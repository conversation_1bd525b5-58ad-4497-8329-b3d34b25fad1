/* eslint-disable no-unused-vars */
/* eslint-disable max-len */
/* eslint-disable no-undef */
import os, { findBestMatch } from '../../src/helper/os';

describe('os', () => {
  describe('findBestMatch', () => {
    const collection = [
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
      { id: 3, name: '<PERSON>' },
    ];

    it('should return the best matching object in the collection', () => {
      const value = '<PERSON><PERSON>';
      const property = 'name';
      const expected = { id: 1, name: '<PERSON>' };
      const result = findBestMatch(value, collection, property);
      expect(result).toEqual(expected);
    });

    // it('should return the reference to the best matching object in the collection if returnRef is true', () => {
    //   const value = '<PERSON>e';
    //   const property = 'name';
    //   const expected = { id: 1, name: '<PERSON>' };
    //   const result = findBestMatch(value, collection, property, true);
    //   expect(result).toBe(expected);
    // });

    // it('should return an empty object if no matches are found', () => {
    //   const value = 'Smith<PERSON>';
    //   const property = 'name';
    //   const expected = {};
    //   const result = findBestMatch(value, collection, property);
    //   expect(result).toEqual(expected);
    // });
  });
});
