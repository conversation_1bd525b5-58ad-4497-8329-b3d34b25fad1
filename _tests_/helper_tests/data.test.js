/* eslint-disable no-undef */
import {
  getStrLen,
  convertNumToLetter,
  toPercent,

} from '../../src/helper/data';

describe('getStrLen', () => {
  it('should return the correct length of a string', () => {
    expect(getStrLen('Hello, world!')).toBe(13);
    expect(getStrLen('你好，世界！')).toBe(12);
    expect(getStrLen('')).toBe(0);
  });
});

describe('convertNumToLetter', () => {
  it('should return A for 1', () => {
    expect(convertNumToLetter(1)).toBe('A');
  });

  it('should return Z for 26', () => {
    expect(convertNumToLetter(26)).toBe('Z');
  });

  it('should return AA for 27', () => {
    expect(convertNumToLetter(27)).toBe('AA');
  });

  it('should return AB for 28', () => {
    expect(convertNumToLetter(28)).toBe('AB');
  });

  it('should return AZ for 52', () => {
    expect(convertNumToLetter(52)).toBe('AZ');
  });

  it('should return BA for 53', () => {
    expect(convertNumToLetter(53)).toBe('BA');
  });

  // Add more test cases if needed
});

describe('toPercent', () => {
  it('should return "25.00%" for 0.25', () => {
    expect(toPercent(0.25)).toBe('25%');
  });

  it('should return "100.00%" for 1', () => {
    expect(toPercent(1)).toBe('100%');
  });

  it('should return "50.00%" for 0.5 and 0 decimal places', () => {
    expect(toPercent(0.5, 0)).toBe('50%');
  });

  it('should return "50.00%" for 0.5 and 0 decimal places', () => {
    expect(toPercent(0.5, 2)).toBe('50.00%');
  });
});
