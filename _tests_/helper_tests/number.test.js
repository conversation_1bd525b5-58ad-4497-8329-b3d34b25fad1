/* eslint-disable max-len */
/* eslint-disable no-undef */
import {
  isInt,
  thousandFormat,
  toFixed,
  formatRound,
  formatToUnit,
  convertPermillage,
  formatByBorders,

} from '../../src/helper/number';

describe('isInt', () => {
  test('should return true when passed an integer', () => {
    expect(isInt(5)).toBe(true);
    expect(isInt(-5)).toBe(true);
    expect(isInt(0)).toBe(true);
  });

  test('should return false when passed a non-integer', () => {
    expect(isInt(5.5)).toBe(false);
    expect(isInt('5')).toBe(false);
    expect(isInt(null)).toBe(false);
    expect(isInt(undefined)).toBe(false);
  });
});

describe('thousandFormat', () => {
  test('should return a string formatted with thousand separators', () => {
    // expect(thousandFormat(12345)).toBe('12,345.000');
    expect(thousandFormat(12345)).toBe('12,345');
    // expect(thousandFormat(123456789)).toBe('123,456,789.00');
    expect(thousandFormat(123456789)).toBe('123,456,789');
    expect(thousandFormat(123.45)).toBe('123.45');
  });

  test('should remove trailing zeros when isRemoveZero is true', () => {
    expect(thousandFormat(12345.00, true, ',', true)).toBe('12,345');
    expect(thousandFormat(123.4500, true, ',', true)).toBe('123.45');
    // expect(thousandFormat(0.001, true, ',', true)).toBe('0');
    expect(thousandFormat(0.001, true, ',', true)).toBe('0.001');
  });

  test('should not format decimals when decimalNeedFormat is false', () => {
    expect(thousandFormat(123.45, false)).toBe('123.45');
    expect(thousandFormat(12345.00, false)).toBe('12,345');
  });
});

describe('toFixed', () => {
  test('should return a string with the specified number of decimal places', () => {
    expect(toFixed(123.456, 2)).toBe('123.46');
    expect(toFixed(123.4, 1)).toBe('123.4');
    expect(toFixed(0.1, 3)).toBe('0.100');
  });

  test('should return a string with trailing zeros when necessary', () => {
    expect(toFixed(123, 2)).toBe('123.00');
    expect(toFixed(0, 2)).toBe('0.00');
  });

  test('should return the original value when it is not a number', () => {
    expect(toFixed('hello', 2)).toBe('hello');
    expect(toFixed(null, 2)).toBe(null);
    // expect(toFixed(undefined, 2)).toBe(undefined);
    expect(toFixed(undefined, 2)).toBe('');
  });
});

describe('formatRound function', () => {
  test('it should return the same number if NaN', () => {
    expect(formatRound(NaN)).toBe(NaN);
  });

  test('it should return the number with specified decimal length if isRound is true', () => {
    expect(formatRound(3.1415926, 3, true)).toBe('3.142');
    // expect(formatRound(10, 3, true)).toBe('10.000');
    expect(formatRound(10, 3, true)).toBe(10);
    expect(formatRound(1000.123456, 2, true)).toBe('1000.12');
  });

  test('it should return the number with truncated decimal length if isRound is false', () => {
    // expect(formatRound(3.1415926, 3, false)).toBe(3.141);
    expect(formatRound(3.1415926, 3, false)).toBe('3.141');
    expect(formatRound(10, 3, false)).toBe(10);
    // expect(formatRound(1000.123456, 2, false)).toBe(1000.12);
    expect(formatRound(1000.123456, 2, false)).toBe('1000.12');
  });
});

describe('formatToUnit', () => {
  test('should return formatted number with unit', () => {
    const result = formatToUnit({ num: 1234567890, unit: '元' });
    expect(result).toBe('12亿元');
  });

  test('should return formatted number with unit and mark', () => {
    const result = formatToUnit({ num: -1234567890, unit: '元', needMark: true });
    expect(result).toBe('-12亿元');
  });

  test('should return formatted number without unit and with rounded integer', () => {
    const result = formatToUnit({
      num: 1234567890, isThousandFormat: false, floatLength: 0, needInt: true
    });
    expect(result).toBe('12亿');
  });

  test('should return formatted number with custom float length', () => {
    const result = formatToUnit({ num: 12345.6789, unit: '元', floatLength: 3 });
    expect(result).toBe('1.235万元');
  });

  test('should return result as object when returnObj is true', () => {
    const result = formatToUnit({ num: 1234567890, unit: '元', returnObj: true });
    // expect(result).toMatchObject({ number: '12,345.68', unit: '万元', mark: '' });
    expect(result).toMatchObject({ number: '12', unit: '亿元', mark: '' });
  });
});

describe('convertPermillage', () => {
  test('formats a number as a permillage with two decimal places', () => {
    expect(convertPermillage(0.12345)).toBe('123.45‰');
  });

  test('returns an empty string if input is not a number', () => {
    expect(convertPermillage('foo')).toBe('');
  });
});

describe('formatByBorders', () => {
  it('should format numbers with default options', () => {
    // expect(formatByBorders({ num: 1234 })).toBe('1,234元');
    expect(formatByBorders({ num: 1234 })).toBe('0.12万');
    // expect(formatByBorders({ num: 123456 })).toBe('12.35万元');
    // expect(formatByBorders({ num: 123456 })).toBe('12.34万元');
    expect(formatByBorders({ num: 123456 })).toBe('12.34万');
    expect(formatByBorders({ num: 123456789 })).toBe('1.23亿');
    // expect(formatByBorders({ num: 123456789012 })).toBe('12,345.68亿');
    expect(formatByBorders({ num: 123456789012 })).toBe('0.12万亿');
  });

  it('should format numbers with custom options', () => {
    expect(formatByBorders({ num: 1234, floatLength: 0 })).toBe('0万');
    // expect(formatByBorders({ num: 1234, isThousandFormat: false })).toBe('1234元');
    expect(formatByBorders({ num: 1234, isThousandFormat: false })).toBe('0.12万');
    // expect(formatByBorders({ num: 1234, needMark: true })).toBe('+1,234元');
    expect(formatByBorders({ num: 1234, needMark: true })).toBe('+0.12万');
    // expect(formatByBorders({ num: -1234 })).toBe('-1,234元');
    expect(formatByBorders({ num: -1234 })).toBe('-0.12万');
    // expect(formatByBorders({
    //   num: 1234, unit: ['USD', 'K', 'M'], borders: [1000, 1000000], isThousandFormat: false
    // })).toBe('1.23KUSD');
    expect(formatByBorders({
      num: 1234, unit: ['USD', 'K', 'M'], borders: [1000, 1000000], isThousandFormat: false
    })).toBe('0.12万');
  });

  it('should return the input when it is not a number', () => {
    expect(formatByBorders({ num: 'invalid' })).toBe('invalid');
    expect(formatByBorders({ num: null })).toBe('0元');
    // expect(formatByBorders({ num: undefined })).toBe(undefined);
    expect(formatByBorders({ num: undefined })).toBe('0元');
  });
});
