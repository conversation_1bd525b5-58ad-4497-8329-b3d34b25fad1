/* eslint-disable no-undef */
import moment from 'moment';
import { calculateDuration, generateDate } from '../../src/helper/date';

describe('calculateDuration', () => {
  test('should return "0秒" when start or end is not provided', () => {
    expect(calculateDuration(null, null)).toBe('0秒');
    expect(calculateDuration(moment(), null)).toBe('0秒');
    expect(calculateDuration(null, moment())).toBe('0秒');
  });

  test('should return formatted duration string based on intervalMs', () => {
    const start = moment('2022-01-01T12:00:00.000Z');
    const end = moment('2022-01-01T12:01:30.000Z');
    expect(calculateDuration(start, end)).toBe('01分30秒');

    const start2 = moment('2022-01-01T12:00:00.000Z');
    const end2 = moment('2022-01-01T13:01:30.000Z');
    expect(calculateDuration(start2, end2)).toBe('01时01分30秒');
  });
});

describe('generateDate', () => {
  test('should return date string in the format of "年月日"', () => {
    const momentTime = moment('2022-01-01T00:00:00.000Z');
    expect(generateDate(momentTime)).toBe('2022年1月1日');

    const momentTime2 = moment('2023-05-07T00:00:00.000Z');
    expect(generateDate(momentTime2)).toBe('2023年5月7日');
  });
});
