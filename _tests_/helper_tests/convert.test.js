/* eslint-disable import/named */
/* eslint-disable no-undef */
import convert, { getStatusByCode } from '../../src/helper/convert';

describe('getStatusByCode', () => {
  it('should return status object when given valid code', () => {
    const code = '01';
    const statusObject = getStatusByCode(code);
    expect(statusObject).toBeDefined();
    expect(statusObject.code).toBe(code);
  });

  it('should return undefined when given invalid code', () => {
    const code = 999;
    const statusObject = getStatusByCode(code);
    expect(statusObject).toBeUndefined();
  });
});

describe('isFinishStatus', () => {
  it('should return true when given a finish status code', () => {
    const code = '02';
    const isFinish = convert.isFinishStatus(code);
    expect(isFinish).toBe(true);
  });

  it('should return false when given a non-finish status code', () => {
    const code = 404;
    const isFinish = convert.isFinishStatus(code);
    expect(isFinish).toBe(false);
  });
});
