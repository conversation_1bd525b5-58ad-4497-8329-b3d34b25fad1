/* eslint-disable import/no-named-as-default-member */
/* eslint-disable no-undef */
import regexp from '../../src/helper/regexp';

describe('Regexp Tests', () => {
  // 整数正则测试
  describe('integer', () => {
    test('should match 0', () => {
      expect(regexp.integer.test('0')).toBe(true);
    });
    test('should match 123', () => {
      expect(regexp.integer.test('123')).toBe(true);
    });
    test('should not match -1', () => {
      expect(regexp.integer.test('-1')).toBe(false);
    });
    test('should not match 0.1', () => {
      expect(regexp.integer.test('0.1')).toBe(false);
    });
  });

  // 中文字符正则测试
  describe('chinese', () => {
    test('should match 你好', () => {
      expect('你好'.match(regexp.chinese)).not.toBeNull();
    });
    test('should not match abc', () => {
      expect('abc'.match(regexp.chinese)).toBeNull();
    });
    test('should match 中文abc', () => {
      expect('中文abc'.match(regexp.chinese)).not.toBeNull();
    });
    test('should match 123中文', () => {
      expect('123中文'.match(regexp.chinese)).not.toBeNull();
    });
  });

  // 手机号码正则测试
  describe('cellPhone', () => {
    test('should match 13800138000', () => {
      expect(regexp.cellPhone.test('13800138000')).toBe(true);
    });
    test('should match 15600000000', () => {
      expect(regexp.cellPhone.test('15600000000')).toBe(true);
    });
    test('should not match 23800138000', () => {
      expect(regexp.cellPhone.test('23800138000')).toBe(false);
    });
    test('should not match 1380013800', () => {
      expect(regexp.cellPhone.test('1380013800')).toBe(false);
    });
  });

  // 座机号码正则测试
  describe('tellPhone', () => {
    test('should match 010-88888888', () => {
      expect(regexp.tellPhone.test('010-88888888')).toBe(true);
    });
    test('should match 02188888888', () => {
      expect(regexp.tellPhone.test('02188888888')).toBe(true);
    });
    test('should match 010-88888888-123', () => {
      expect(regexp.tellPhone.test('010-88888888-123')).toBe(true);
    });
    test('should not match 12345678', () => {
      expect(regexp.tellPhone.test('12345678')).toBe(false);
    });
  });

  // 电子邮箱正则测试
  describe('email', () => {
    test('<NAME_EMAIL>', () => {
      expect(regexp.email.test('<EMAIL>')).toBe(true);
    });
    test('<NAME_EMAIL>', () => {
      expect(regexp.email.test('<EMAIL>')).toBe(true);
    });
    test('should not match test@.com', () => {
      expect(regexp.email.test('test@.com')).toBe(false);
    });
    test('should not match testexample.com', () => {
      expect(regexp.email.test('testexample.com')).toBe(false);
    });
  });

  // 邮政编码正则测试
  describe('zipCode', () => {
    test('should match 100000', () => {
      expect(regexp.zipCode.test('100000')).toBe(true);
    });
    test('should not match 10000', () => {
      expect(regexp.zipCode.test('10000')).toBe(false);
    });
    test('should not match 1000000', () => {
      expect(regexp.zipCode.test('1000000')).toBe(false);
    });
    test('should not match abcdef', () => {
      expect(regexp.zipCode.test('abcdef')).toBe(false);
    });
  });

  // 整数部分千分位格式化正则测试
  describe('thousandInteger', () => {
    test('should format 12345604 to 12,345,604', () => {
      expect('12345604'.replace(regexp.thousandInteger, '$1,'))
        .toBe('12,345,604');
    });
    test('should format 123 to 123', () => {
      expect('123'.replace(regexp.thousandInteger, '$1,'))
        .toBe('123');
    });
    test('should format 1234 to 1,234', () => {
      expect('1234'.replace(regexp.thousandInteger, '$1,'))
        .toBe('1,234');
    });
    test('should format 12345 to 12,345', () => {
      expect('12345'.replace(regexp.thousandInteger, '$1,'))
        .toBe('12,345');
    });
  });

  // 小数部分千分位格式化正则测试
  describe('thousandDecimal', () => {
    test('should format 12345604 to 123,456,04', () => {
      expect('12345604'.replace(regexp.thousandDecimal, '$1,'))
        .toBe('123,456,04');
    });
    test('should format 123 to 123', () => {
      expect('123'.replace(regexp.thousandDecimal, '$1,'))
        .toBe('123');
    });
    test('should format 1234 to 1,234', () => {
      expect('1234'.replace(regexp.thousandDecimal, '$1,'))
        .toBe('1,234');
    });
    test('should format 12345 to 12,345', () => {
      expect('12345'.replace(regexp.thousandDecimal, '$1,'))
        .toBe('12,345');
    });
  });

  // 正整数正则测试
  describe('positiveInteger', () => {
    test('should match 1', () => {
      expect(regexp.positiveInteger.test('1')).toBe(true);
    });
    test('should match 123', () => {
      expect(regexp.positiveInteger.test('123')).toBe(true);
    });
    test('should not match 0', () => {
      expect(regexp.positiveInteger.test('0')).toBe(false);
    });
    test('should not match -1', () => {
      expect(regexp.positiveInteger.test('-1')).toBe(false);
    });
  });

  // 非负数正则测试
  describe('positiveNum', () => {
    test('should match 0', () => {
      expect(regexp.positiveNum.test('0')).toBe(true);
    });
    test('should match 123', () => {
      expect(regexp.positiveNum.test('123')).toBe(true);
    });
    test('should match 123.45', () => {
      expect(regexp.positiveNum.test('123.45')).toBe(true);
    });
    test('should not match -1', () => {
      expect(regexp.positiveNum.test('-1')).toBe(false);
    });
  });
});
// 非负数（另一种规则）正则测试
describe('positiveNum1', () => {
  test('should match 0', () => {
    expect(regexp.positiveNum1.test('0')).toBe(true);
  });
  test('should match 123', () => {
    expect(regexp.positiveNum1.test('123')).toBe(true);
  });
  test('should match 123.45', () => {
    expect(regexp.positiveNum1.test('123.45')).toBe(true);
  });
  test('should not match 1.', () => {
    expect(regexp.positiveNum1.test('1.')).toBe(false);
  });
});

// 非零正数（包含小数）正则测试
describe('positiveNumber', () => {
  test('should match 1', () => {
    expect(regexp.positiveNumber.test('1')).toBe(true);
  });
  test('should match 1.23', () => {
    expect(regexp.positiveNumber.test('1.23')).toBe(true);
  });
  test('should not match 0', () => {
    expect(regexp.positiveNumber.test('0')).toBe(false);
  });
  test('should not match -1', () => {
    expect(regexp.positiveNumber.test('-1')).toBe(false);
  });
});

// 路径分割正则测试
describe('matchPathList', () => {
  test('should match /a/b/c', () => {
    const result = '/a/b/c'.match(regexp.matchPathList);
    expect(result).toEqual(['/a', '/b', '/c']);
  });
  test('should match /a', () => {
    const result = '/a'.match(regexp.matchPathList);
    expect(result).toEqual(['/a']);
  });
  test('should not match a', () => {
    const result = 'a'.match(regexp.matchPathList);
    expect(result).toBeNull();
  });
  test('should match /a/', () => {
    const result = '/a/'.match(regexp.matchPathList);
    expect(result).toEqual(['/a']);
  });
});

// 换行符正则测试
describe('returnLine', () => {
  test('should match \n', () => {
    expect('\n'.match(regexp.returnLine)).not.toBeNull();
  });
  test('should match \r', () => {
    expect('\r'.match(regexp.returnLine)).not.toBeNull();
  });
  test('should match \r\n', () => {
    expect('\r\n'.match(regexp.returnLine)).not.toBeNull();
  });
  test('should not match abc', () => {
    expect('abc'.match(regexp.returnLine)).toBeNull();
  });
});

// URL正则测试
describe('url', () => {
  test('should match https://www.example.com', () => {
    expect(regexp.url.test('https://www.example.com')).toBe(true);
  });
  test('should match http://example.com', () => {
    expect(regexp.url.test('http://example.com')).toBe(true);
  });
  test('should not match example.com', () => {
    expect(regexp.url.test('example.com')).toBe(false);
  });
  test('should match https://example.com/path?query=1', () => {
    expect(regexp.url.test('https://example.com/path?query=1')).toBe(true);
  });
});

// 统一社会信用码正则测试
describe('uscc', () => {
  test('should match 91110108MA01J2X72Y', () => {
    expect(regexp.uscc.test('91110108MA01J2X72Y')).toBe(true);
  });
  test('should not match 91110108MA01J2X72', () => {
    expect(regexp.uscc.test('91110108MA01J2X72')).toBe(false);
  });
  test('should not match 91110108MA01J2X72YZ', () => {
    expect(regexp.uscc.test('91110108MA01J2X72YZ')).toBe(false);
  });
  test('should not match abcdefghijklmnopqrst', () => {
    expect(regexp.uscc.test('abcdefghijklmnopqrst')).toBe(false);
  });
});

// 18位身份证号码正则测试
describe('idNo18Digit', () => {
  test('should match 11010519491231002X', () => {
    expect(regexp.idNo18Digit.test('11010519491231002X')).toBe(true);
  });
  test('should match 11010519491231002x', () => {
    expect(regexp.idNo18Digit.test('11010519491231002x')).toBe(true);
  });
  test('should not match 11010519491231002', () => {
    expect(regexp.idNo18Digit.test('11010519491231002')).toBe(false);
  });
  test('should not match 11010519491231002XY', () => {
    expect(regexp.idNo18Digit.test('11010519491231002XY')).toBe(false);
  });
});

// 15位身份证号码正则测试
describe('idNo15Digit', () => {
  test('should match 110105491231002', () => {
    expect(regexp.idNo15Digit.test('110105491231002')).toBe(true);
  });
  test('should not match 11010549123100', () => {
    expect(regexp.idNo15Digit.test('11010549123100')).toBe(false);
  });
  test('should not match 110105491231002X', () => {
    expect(regexp.idNo15Digit.test('110105491231002X')).toBe(false);
  });
  test('should not match 110105491231002XY', () => {
    expect(regexp.idNo15Digit.test('110105491231002XY')).toBe(false);
  });
});

// 只含字母与数字正则测试
describe('onlyAlphabetAndNumber', () => {
  test('should match abc123', () => {
    expect(regexp.onlyAlphabetAndNumber.test('abc123')).toBe(true);
  });
  test('should match ABC123', () => {
    expect(regexp.onlyAlphabetAndNumber.test('ABC123')).toBe(true);
  });
  test('should not match abc@123', () => {
    expect(regexp.onlyAlphabetAndNumber.test('abc@123')).toBe(false);
  });
  test('should not match abc 123', () => {
    expect(regexp.onlyAlphabetAndNumber.test('abc 123')).toBe(false);
  });
});

// 只含字母、数字和汉字正则测试
describe('onlyWordNumAlphabet', () => {
  test('should match abc123你好', () => {
    expect(regexp.onlyWordNumAlphabet.test('abc123你好')).toBe(true);
  });
  test('should match ABC123你好', () => {
    expect(regexp.onlyWordNumAlphabet.test('ABC123你好')).toBe(true);
  });
  test('should not match abc@123你好', () => {
    expect(regexp.onlyWordNumAlphabet.test('abc@123你好')).toBe(false);
  });
  test('should not match abc 123你好', () => {
    expect(regexp.onlyWordNumAlphabet.test('abc 123你好')).toBe(false);
  });
});

// 匹配所有的html标签正则测试
describe('htmlTags', () => {
  test('should match <div>', () => {
    expect('<div>'.match(regexp.htmlTags)).not.toBeNull();
  });
  test('should match <p>Hello</p>', () => {
    expect('<p>Hello</p>'.match(regexp.htmlTags)).not.toBeNull();
  });
  test('should not match Hello', () => {
    expect('Hello'.match(regexp.htmlTags)).toBeNull();
  });
  test('should match <a href="#">Link</a>', () => {
    expect('<a href="#">Link</a>'.match(regexp.htmlTags)).not.toBeNull();
  });
});

// 第一位数字不能为0正则测试
describe('noFirstZero', () => {
  test('should match 123', () => {
    expect(regexp.noFirstZero.test('123')).toBe(true);
  });
  test('should not match 012', () => {
    expect(regexp.noFirstZero.test('012')).toBe(false);
  });
  test('should match 987', () => {
    expect(regexp.noFirstZero.test('987')).toBe(true);
  });
  test('should not match 0', () => {
    expect(regexp.noFirstZero.test('0')).toBe(false);
  });
});

// 正数，小数位不能超过两位正则测试
describe('twoDecimals', () => {
  test('should match 123', () => {
    expect(regexp.twoDecimals.test('123')).toBe(true);
  });
  test('should match 123.45', () => {
    expect(regexp.twoDecimals.test('123.45')).toBe(true);
  });
  test('should not match 123.456', () => {
    expect(regexp.twoDecimals.test('123.456')).toBe(false);
  });
  test('should not match -123.45', () => {
    expect(regexp.twoDecimals.test('-123.45')).toBe(false);
  });
});

// 判断是否只含有字母正则测试
describe('onlyAlphabet', () => {
  test('should match abc', () => {
    expect(regexp.onlyAlphabet.test('abc')).toBe(true);
  });
  test('should match ABC', () => {
    expect(regexp.onlyAlphabet.test('ABC')).toBe(true);
  });
  test('should not match abc123', () => {
    expect(regexp.onlyAlphabet.test('abc123')).toBe(false);
  });
  test('should not match abc@', () => {
    expect(regexp.onlyAlphabet.test('abc@')).toBe(false);
  });
});

// 判断是否只含有字母数字和空格正则测试
describe('alphabetAndSpaceAndNumber', () => {
  test('should match abc 123', () => {
    expect(regexp.alphabetAndSpaceAndNumber.test('abc 123')).toBe(true);
  });
  test('should match ABC123', () => {
    expect(regexp.alphabetAndSpaceAndNumber.test('ABC123')).toBe(true);
  });
  test('should not match abc@123', () => {
    expect(regexp.alphabetAndSpaceAndNumber.test('abc@123')).toBe(false);
  });
  test('should not match abc 123!', () => {
    expect(regexp.alphabetAndSpaceAndNumber.test('abc 123!')).toBe(false);
  });
});

// 判断是否只含有字母数字和空格和一些英文符号#.,-/正则测试
describe('alphabetAndSpaceAndNumberAndSymbol', () => {
  test('should match abc 123#.,-/', () => {
    expect(regexp.alphabetAndSpaceAndNumberAndSymbol.test('abc 123#.,-/')).toBe(true);
  });
  test('should match ABC123', () => {
    expect(regexp.alphabetAndSpaceAndNumberAndSymbol.test('ABC123')).toBe(true);
  });
  test('should not match abc@123', () => {
    expect(regexp.alphabetAndSpaceAndNumberAndSymbol.test('abc@123')).toBe(false);
  });
  test('should not match abc 123!', () => {
    expect(regexp.alphabetAndSpaceAndNumberAndSymbol.test('abc 123!')).toBe(false);
  });
});

// 判断是否含有字母/正则测试
describe('hasAlphabet', () => {
  test('should match abc', () => {
    expect(regexp.hasAlphabet.test('abc')).toBe(true);
  });
  test('should match ABC', () => {
    expect(regexp.hasAlphabet.test('ABC')).toBe(true);
  });
  test('should not match 123', () => {
    expect(regexp.hasAlphabet.test('123')).toBe(false);
  });
  test('should match abc123', () => {
    expect(regexp.hasAlphabet.test('abc123')).toBe(true);
  });
});

// 11位电话号码脱敏正则测试
describe('phoneDesensitization', () => {
  test('should match 13800138000', () => {
    expect('13800138000'.replace(regexp.phoneDesensitization, '$1****$2')).toBe('138****8000');
  });
  test('should match 15600000000', () => {
    expect('15600000000'.replace(regexp.phoneDesensitization, '$1****$2')).toBe('156****0000');
  });
  test('should not match 123', () => {
    expect('123'.replace(regexp.phoneDesensitization, '$1****$2')).toBe('123');
  });
  test('should match 1380013800', () => {
    expect('1380013800'.replace(regexp.phoneDesensitization, '$1****$2')).toBe('1380013800');
  });
});

// 常用的一些会影响富文本效果的html标签正则测试
describe('regularHtmlTag', () => {
  test('should match <script>', () => {
    expect('<script>'.match(regexp.regularHtmlTag)).not.toBeNull();
  });
  test('should match <img src="#">', () => {
    expect('<img src="#">'.match(regexp.regularHtmlTag)).not.toBeNull();
  });
  test('should not match Hello', () => {
    expect('Hello'.match(regexp.regularHtmlTag)).toBeNull();
  });
  test('should match <div>Content</div>', () => {
    expect('<div>Content</div>'.match(regexp.regularHtmlTag)).not.toBeNull();
  });
});

// 判断是否为非数字正则测试
describe('notNumber', () => {
  test('should match abc', () => {
    expect('abc'.match(regexp.notNumber)).not.toBeNull();
  });
  test('should match abc123', () => {
    expect('abc123'.match(regexp.notNumber)).not.toBeNull();
  });
  test('should not match 123', () => {
    expect('123'.match(regexp.notNumber)).toBeNull();
  });
  test('should match abc@123', () => {
    expect('abc@123'.match(regexp.notNumber)).not.toBeNull();
  });
});

// 两位小数正则测试
describe('newTwoDecimals', () => {
  test('should match 123', () => {
    expect(regexp.newTwoDecimals.test('123')).toBe(true);
  });
});
