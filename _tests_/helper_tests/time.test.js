/* eslint-disable no-unused-vars */
/* eslint-disable no-undef */
import moment from 'moment';
import time, {
  format,
  weekDay,
  getDurationString,
  convertToTimeStamp,
} from '../../src/helper/time';

describe('time', () => {
  describe('format', () => {
    test('should format a date correctly', () => {
      const str = '2021-01-01';
      const formatter = 'YYYY-MM-DD';
      expect(format(str, formatter)).toBe('2021-01-01');
    });

    test('should use default formatter if none is provided', () => {
      const str = '2021-01-01';
      expect(format(str)).toBe('2021-01-01');
    });

    test('should return empty string if no argument is provided', () => {
      expect(format()).toBe('');
    });
  });

  describe('weekDay', () => {
    test('should return the correct weekday for a given number', () => {
      expect(weekDay(0)).toBe('周日');
      expect(weekDay(1)).toBe('周一');
      expect(weekDay(2)).toBe('周二');
      expect(weekDay(3)).toBe('周三');
      expect(weekDay(4)).toBe('周四');
      expect(weekDay(5)).toBe('周五');
      expect(weekDay(6)).toBe('周六');
    });

    test('should return the correct weekday for a given date', () => {
      const date = new Date('2021-01-01');
      expect(weekDay(date)).toBe('周五');
    });
  });

  describe('getDurationString function', () => {
    test('returns correct duration string for cycle type "beforeLastMonth"', () => {
      const maxDataDt = '2022/04/30';
      const expectedDuration = {
        cycleType: 'beforeLastMonth',
        durationStr: '2022/02/01-2022/02/28',
        // begin: '2022/02/01',
        // end: '2022/02/28',
        begin: '20220201',
        end: '20220228',
      };
      const actualDuration = getDurationString('beforeLastMonth', maxDataDt);
      expect(actualDuration).toEqual(expectedDuration);
    });

    test('returns correct duration string for cycle type "lastMonth"', () => {
      const maxDataDt = '2022/04/30';
      const expectedDuration = {
        cycleType: 'lastMonth',
        durationStr: '2022/03/01-2022/03/31',
        // begin: '2022/03/01',
        // end: '2022/03/31',
        begin: '20220301',
        end: '20220331',
      };
      const actualDuration = getDurationString('lastMonth', maxDataDt);
      expect(actualDuration).toEqual(expectedDuration);
    });

    test('returns correct duration string for cycle type "lastQuarter"', () => {
      const maxDataDt = '2022/04/30';
      const expectedDuration = {
        cycleType: 'lastQuarter',
        durationStr: '2023/01/01-2023/03/30',
        begin: '20230101',
        end: '20230330',
      };
      const actualDuration = getDurationString('lastQuarter', maxDataDt);
      expect(actualDuration).toEqual(expectedDuration);
    });

    test('returns correct duration string for cycle type "lastYear"', () => {
      const maxDataDt = '2022/04/30';
      const expectedDuration = {
        cycleType: 'lastYear',
        durationStr: '2022/01/01-2022/12/31',
        begin: '20220101',
        end: '20221231',
      };
      const actualDuration = getDurationString('lastYear', maxDataDt);
      expect(actualDuration).toEqual(expectedDuration);
    });

    test('returns correct duration string for custom cycle type', () => {
      const maxDataDt = '2022/04/30';
      const expectedDuration = {
        cycleType: 'week',
        durationStr: '2022/04/24-2022/04/30',
        begin: '20220424',
        end: '20220430',
      };
      const actualDuration = getDurationString('week', maxDataDt);
      expect(actualDuration).toEqual(expectedDuration);
    });
  });

  //   describe('transformTime function', () => {
  //     test('returns last month time range', () => {
  //       const result = transformTime('month');
  //       const expected = {
  //         startDate: moment().subtract(1, 'months').format('YYYYMMDD'),
  //         endDate: moment().format('YYYYMMDD'),
  //       };
  //       expect(result).toEqual(expected);
  //     });

  //     test('returns last three months time range', () => {
  //       const result = transformTime('season');
  //       const expected = {
  //         startDate: moment().subtract(3, 'months').format('YYYYMMDD'),
  //         endDate: moment().format('YYYYMMDD'),
  //       };
  //       expect(result).toEqual(expected);
  //     });

  //     test('returns last six months time range', () => {
  //       const result = transformTime('halfYear');
  //       const expected = {
  //         startDate: moment().subtract(6, 'months').format('YYYYMMDD'),
  //         endDate: moment().format('YYYYMMDD'),
  //       };
  //       expect(result).toEqual(expected);
  //     });

  //     test('returns current year time range', () => {
  //       const result = transformTime('currentYear');
  //       const expected = {
  //         startDate: moment().startOf('year').format('YYYYMMDD'),
  //         endDate: moment().format('YYYYMMDD'),
  //       };
  //       expect(result).toEqual(expected);
  //     });

  //     test('returns last month time range as default', () => {
  //       const result = transformTime('invalidKey');
  //       const expected = {
  //         startDate: moment().subtract(1, 'months').format('YYYYMMDD'),
  //         endDate: moment().format('YYYYMMDD'),
  //       };
  //       expect(result).toEqual(expected);
  //     });
  //   });

  describe('convertToTimeStamp function', () => {
    test('returns timestamp for valid date string', () => {
      const result = convertToTimeStamp('2022-04-22');
      const expected = moment('2022-04-22').valueOf();
      expect(result).toEqual(expected);
    });

    test('returns null for null input', () => {
      const result = convertToTimeStamp(null);
      expect(result).toBeNull();
    });
  });
});
