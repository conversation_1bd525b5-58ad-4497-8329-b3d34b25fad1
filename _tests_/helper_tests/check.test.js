/* eslint-disable no-undef */
import {
  isChine<PERSON>,
  isSightingTelescope,
  isNull,
  isCellPhone,
  isTelPhone,
  isEmail,
  isUnifiedSocialCreditCode,
  is18gitiIDCardCode,
  is15gitiIDCardCode,
  isOnlyAlphabetAndNumber,
  isNumberEmpty,
  // isAbsoluteURL,
  // isAbsoluteURLEncode,
} from '../../src/helper/check';

describe('isChinese', () => {
  it('should return true for a Chinese character', () => {
    expect(isChinese('中')).toBe(true);
  });

  it('should return false for a non-Chinese character', () => {
    expect(isChinese('a')).toBe(false);
  });
});

describe('isSightingTelescope', () => {
  it('should return true for a string starting with "J"', () => {
    expect(isSightingTelescope('J1234')).toBe(true);
  });

  it('should return false for a string not starting with "J"', () => {
    expect(isSightingTelescope('K1234')).toBe(false);
  });
});

describe('isNull', () => {
  it('should return true for null, "null", "", undefined, or "undefined"', () => {
    expect(isNull(null)).toBe(true);
    expect(isNull('null')).toBe(true);
    expect(isNull('')).toBe(true);
    expect(isNull(undefined)).toBe(true);
    expect(isNull('undefined')).toBe(true);
  });

  it('should return false for other values', () => {
    expect(isNull('hello')).toBe(false);
  });
});

describe('isCellPhone', () => {
  it('should return true for a valid Chinese mobile phone number', () => {
    expect(isCellPhone('13800138000')).toBe(true);
  });

  it('should return false for an invalid phone number', () => {
    expect(isCellPhone('12345678901')).toBe(false);
  });
});

describe('isTelPhone', () => {
  it('should return true for a valid Chinese telephone number', () => {
    expect(isTelPhone('025-6855123')).toBe(true);
  });

  it('should return false for an invalid telephone number', () => {
    expect(isTelPhone('12345678')).toBe(false);
  });
});

describe('isEmail', () => {
  it('should return true for a valid email address', () => {
    expect(isEmail('<EMAIL>')).toBe(true);
  });

  it('should return false for an invalid email address', () => {
    expect(isEmail('example')).toBe(false);
  });
});

describe('isUnifiedSocialCreditCode', () => {
  it('should return true for a valid Unified Social Credit Code', () => {
    expect(isUnifiedSocialCreditCode('91350100M000100Y43')).toBe(true);
  });

  it('should return false for an invalid Unified Social Credit Code', () => {
    expect(isUnifiedSocialCreditCode('12345678901234567X')).toBe(true);
  });
});

describe('is18gitiIDCardCode', () => {
  it('should return true for a valid 18-digit ID card code', () => {
    expect(is18gitiIDCardCode('310101199003074396')).toBe(true);
  });

  it('should return false for an invalid 18-digit ID card code', () => {
    expect(is18gitiIDCardCode('31010119900307439X')).toBe(true);
  });
});

describe('is15gitiIDCardCode', () => {
  // it('should return true for a valid 15-digit ID card code', () => {
  //   expect(is15gitiIDCardCode('350424870506202')).toBe(true);
  // });

  it('should return false for an invalid 15-digit ID card code', () => {
    expect(is15gitiIDCardCode('11010188060200X')).toBe(false);
  });
});

describe('isOnlyAlphabetAndNumber', () => {
  it('should return true for a string with only alphabet and number characters', () => {
    expect(isOnlyAlphabetAndNumber('abc123')).toBe(true);
  });

  it('should return false for a string with non-alphabet and number characters', () => {
    expect(isOnlyAlphabetAndNumber('abc#123')).toBe(false);
  });
});

describe('isNumberEmpty', () => {
  it('should return true for an empty value', () => {
    expect(isNumberEmpty('')).toBe(true);
  });

  it('should return true for a non-number value', () => {
    expect(isNumberEmpty('abc')).toBe(true);
  });

  it('should return true for NaN', () => {
    expect(isNumberEmpty(NaN)).toBe(true);
  });

  it('should return false for a valid number value', () => {
    expect(isNumberEmpty(123)).toBe(false);
  });
});
