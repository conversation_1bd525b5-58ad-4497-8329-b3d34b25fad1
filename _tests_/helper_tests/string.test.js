/* eslint-disable no-undef */
import string from '../../src/helper/string';

describe('string', () => {
  describe('replaceHTMLTags', () => {
    test('should remove HTML tags from a string', () => {
      const input = '<h1>Hello, world!</h1>';
      const expected = 'Hello, world!';
      expect(string.replaceHTMLTags(input)).toBe(expected);
    });

    test('should handle empty string input', () => {
      const input = '';
      const expected = '';
      expect(string.replaceHTMLTags(input)).toBe(expected);
    });

    test('should handle null input', () => {
      const input = null;
      const expected = '';
      expect(string.replaceHTMLTags(input)).toBe(expected);
    });
  });

  describe('substr', () => {
    test('should return the first character of a string by default', () => {
      const input = 'Hello';
      const expected = 'H';
      expect(string.substr(input)).toBe(expected);
    });

    test('should return the first two characters of a string', () => {
      const input = 'Hello';
      const expected = 'He';
      expect(string.substr(input, 2)).toBe(expected);
    });

    test('should handle null input', () => {
      const input = null;
      const expected = null;
      expect(string.substr(input)).toBe(expected);
    });

    test('should handle non-string input', () => {
      const input = 123;
      const expected = null;
      expect(string.substr(input)).toBe(expected);
    });
  });
});
