/* eslint-disable no-undef */
import moment from 'moment';
import time, {
  format as timeFormat,
  weekDay,
  getDurationString,
  convertToTimeStamp,
} from '../../src/helper/time';

describe('time utility functions', () => {
  describe('format', () => {
    it('should format a valid date string', () => {
      const str = '2023-10-15';
      const result = timeFormat(str);
      expect(result).toBe(moment(str).format('YYYY-MM-DD'));
    });

    it('should return an empty string for a falsy input', () => {
      const result = timeFormat(null);
      expect(result).toBe('');
    });
  });

  describe('weekDay', () => {
    it('should return 周日 for input 0', () => {
      const result = weekDay(0);
      expect(result).toBe('周 日');
    });
    it('should return 周一 for input 1', () => {
      const result = weekDay(1);
      expect(result).toBe('周 一');
    });
    it('should return 周二 for input 2', () => {
      const result = weekDay(2);
      expect(result).toBe('周 二');
    });
    it('should return 周三 for input 3', () => {
      const result = weekDay(3);
      expect(result).toBe('周 三');
    });
    it('should return 周四 for input 4', () => {
      const result = weekDay(4);
      expect(result).toBe('周 四');
    });
    it('should return 周五 for input 5', () => {
      const result = weekDay(5);
      expect(result).toBe('周 五');
    });
    it('should return 周六 for input 6', () => {
      const result = weekDay(6);
      expect(result).toBe('周 六');
    });

    it('should return the correct weekday for a Date object', () => {
      const date = new Date();
      const weekLocals = ['日', '一', '二', '三', '四', '五', '六'];
      const result = weekDay(date);
      expect(result).toBe(`周${weekLocals[date.getDay()]}`);
    });
  });

  describe('getDurationString', () => {
    const maxDataDt = '2023/10/15';
    const outputFomater = 'YYYYMMDD';
    const isFullPeriod = false;

    it('should return the correct duration for beforeLastMonth', () => {
      const result = getDurationString('beforeLastMonth', maxDataDt, outputFomater, isFullPeriod);
      expect(result.cycleType).toBe('beforeLastMonth');
      expect(typeof result.durationStr).toBe('string');
      expect(typeof result.begin).toBe('string');
      expect(typeof result.end).toBe('string');
    });
    it('should return the correct duration for lastMonth', () => {
      const result = getDurationString('lastMonth', maxDataDt, outputFomater, isFullPeriod);
      expect(result.cycleType).toBe('lastMonth');
      expect(typeof result.durationStr).toBe('string');
      expect(typeof result.begin).toBe('string');
      expect(typeof result.end).toBe('string');
    });
    it('should return the correct duration for lastQuarter', () => {
      const result = getDurationString('lastQuarter', maxDataDt, outputFomater, isFullPeriod);
      expect(result.cycleType).toBe('lastQuarter');
      expect(typeof result.durationStr).toBe('string');
      expect(typeof result.begin).toBe('string');
      expect(typeof result.end).toBe('string');
    });
    it('should return the correct duration for lastYear', () => {
      const result = getDurationString('lastYear', maxDataDt, outputFomater, isFullPeriod);
      expect(result.cycleType).toBe('lastYear');
      expect(typeof result.durationStr).toBe('string');
      expect(typeof result.begin).toBe('string');
      expect(typeof result.end).toBe('string');
    });
    it('should return the correct duration for day', () => {
      const result = getDurationString('day', maxDataDt, outputFomater, isFullPeriod);
      expect(result.cycleType).toBe('day');
      expect(typeof result.durationStr).toBe('string');
      expect(typeof result.begin).toBe('string');
      expect(typeof result.end).toBe('string');
    });
    it('should return the correct duration for week', () => {
      const result = getDurationString('week', maxDataDt, outputFomater, isFullPeriod);
      expect(result.cycleType).toBe('week');
      expect(typeof result.durationStr).toBe('string');
      expect(typeof result.begin).toBe('string');
      expect(typeof result.end).toBe('string');
    });
    it('should return the correct duration for month', () => {
      const result = getDurationString('month', maxDataDt, outputFomater, isFullPeriod);
      expect(result.cycleType).toBe('month');
      expect(typeof result.durationStr).toBe('string');
      expect(typeof result.begin).toBe('string');
      expect(typeof result.end).toBe('string');
    });
    it('should return the correct duration for quarter', () => {
      const result = getDurationString('quarter', maxDataDt, outputFomater, isFullPeriod);
      expect(result.cycleType).toBe('quarter');
      expect(typeof result.durationStr).toBe('string');
      expect(typeof result.begin).toBe('string');
      expect(typeof result.end).toBe('string');
    });
    it('should return the correct duration for year', () => {
      const result = getDurationString('year', maxDataDt, outputFomater, isFullPeriod);
      expect(result.cycleType).toBe('year');
      expect(typeof result.durationStr).toBe('string');
      expect(typeof result.begin).toBe('string');
      expect(typeof result.end).toBe('string');
    });
  });

  describe('convertToTimeStamp', () => {
    it('should return null for a null input', () => {
      const result = convertToTimeStamp(null);
      expect(result).toBe(null);
    });

    it('should return the correct timestamp for a valid date string', () => {
      const dateString = '2023-10-15';
      const result = convertToTimeStamp(dateString);
      expect(result).toBe(moment(dateString, 'YYYY-MM-DD').valueOf());
    });
  });

  describe('transformTime', () => {
    const timeFormatStr = 'YYYYMMDD';

    it('should return the correct time range for month', () => {
      const result = time.transformTime('month', timeFormatStr);
      expect(typeof result.startDate).toBe('string');
      expect(typeof result.endDate).toBe('string');
    });
    it('should return the correct time range for season', () => {
      const result = time.transformTime('season', timeFormatStr);
      expect(typeof result.startDate).toBe('string');
      expect(typeof result.endDate).toBe('string');
    });
    it('should return the correct time range for halfYear', () => {
      const result = time.transformTime('halfYear', timeFormatStr);
      expect(typeof result.startDate).toBe('string');
      expect(typeof result.endDate).toBe('string');
    });
    it('should return the correct time range for currentYear', () => {
      const result = time.transformTime('currentYear', timeFormatStr);
      expect(typeof result.startDate).toBe('string');
      expect(typeof result.endDate).toBe('string');
    });
    it('should return the correct time range for default', () => {
      const result = time.transformTime('default', timeFormatStr);
      expect(typeof result.startDate).toBe('string');
      expect(typeof result.endDate).toBe('string');
    });
  });
});
