/*
 * @Description: 权限申请home页面
 * @Author: honggaunqging
 * @Date: 2019-06-19 19:44:06
 * @Last Modified by: sunweibin
 * @Last Modified time: 2020-03-17 10:44:45
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { routerRedux } from 'dva/router';
import { connect } from 'dva';
import _ from 'lodash';
import { emp } from '@aorta-pc/utils';

import seibelHelper from '@/helper/page/seibel';
import SplitPanel from '@/components/common/splitPanel/CutScreen';
import ConnectedSeibelHeader from '@/components/common/biz/ConnectedSeibelHeader';
import Detail from '@/components/permission/Detail';
import PermissionList from '@/components/common/appList';
import { seibelConfig } from '@/config';
import ApplyItem from '@/components/common/appList/ApplyItem';
import CreatePrivateClient from '@/components/permission/CreatePrivateClient_';
import Barable from '@/decorators/selfBar';
import withRouter from '@/decorators/withRouter';
import logable, { logPV, logCommon } from '@/decorators/logable';
import { convert } from '@/helper';

import styles from './home.less';

const OMIT_ARRAY = ['isResetPageNum', 'currentId'];

const {
  permission: {
    pageType,
    subType: subTypeOptions,
    status,
    basicFilters,
    moreFilters,
    moreFilterData,
  },
} = seibelConfig;

const fetchDataFunction = (globalLoading, type) => (query) => ({
  type,
  payload: query || {},
  loading: globalLoading,
});

const mapStateToProps = (state) => ({
  // 右侧详情
  detailMessage: state.permission.detailMessage,
  // 左侧列表数据
  list: state.app.newSeibleList,
  // 服务人员列表
  searchServerPersonList: state.permission.searchServerPersonList,
  // 可申请客户
  canApplyCustList: state.app.canApplyCustList,
  // 按照条件 查询下一审批人列表
  nextApproverList: state.permission.nextApproverList,
  // 获取btnlist
  bottonList: state.permission.bottonList,
  // 获取修改私密客户申请 的结果
  modifyCustApplication: state.permission.modifyCustApplication,
  // 监听 修改私密客户申请 过程
  addListenModify: state.loading.effects['permission/getModifyCustApplication'] || false,
  // 获取创建私密客户申请 的结果
  createCustApplication: state.permission.createCustApplication,
  // 监听 创建私密客户申请 过程
  addListenCreate: state.loading.effects['permission/getCreateCustApplication'] || false,
  // 列表loading
  seibelListLoading: state.loading.effects['app/getNewSeibleList'],
  //  获取子类型
  subTypeList: state.permission.subTypeList,
  // 校验数据
  validateData: state.permission.validateData,
});

const mapDispatchToProps = {
  replace: routerRedux.replace,
  // 获取右侧详情
  getDetailMessage: fetchDataFunction(true, 'permission/getDetailMessage'),
  // 获取左侧列表
  getPermissionList: fetchDataFunction(true, 'app/getNewSeibleList'),
  // 获取服务人员列表
  getServerPersonelList: fetchDataFunction(false, 'permission/getSearchServerPersonList'),
  // 获取可申请客户列表
  getCanApplyCustList: fetchDataFunction(false, 'app/getCanApplyCustList'),
  // 查询已有服务任务列表
  getHasServerPersonList: fetchDataFunction(false, 'permission/getHasServerPersonList'),
  // 按照条件 查询下一审批人列表
  getNextApproverList: fetchDataFunction(false, 'permission/getNextApproverList'),
  // 获取btnlist
  getBottonList: fetchDataFunction(false, 'permission/getBottonList'),
  // 获取修改私密客户申请 的结果
  getModifyCustApplication: fetchDataFunction(false, 'permission/getModifyCustApplication'),
  // 获取创建私密客户申请 的结果
  getCreateCustApplication: fetchDataFunction(false, 'permission/getCreateCustApplication'),
  // 获取子类型
  getSubTypeList: fetchDataFunction(false, 'permission/getSubTypeList'),
  // 校验权重信息
  validateWeights: fetchDataFunction(false, 'permission/validateWeights'),
  // 客户服务关系跨境校验
  checkCustCityLimit: fetchDataFunction(true, 'permission/checkCustCityLimit'),
  // 删除附件
  deleteAttachment: fetchDataFunction(true, 'app/deleteAttachment'),
};

@connect(mapStateToProps, mapDispatchToProps)
@withRouter
@Barable
export default class Permission extends PureComponent {
  static propTypes = {
    list: PropTypes.object.isRequired,
    seibelListLoading: PropTypes.bool,
    getPermissionList: PropTypes.func.isRequired,
    location: PropTypes.object.isRequired,
    getDetailMessage: PropTypes.func.isRequired,
    detailMessage: PropTypes.object,
    replace: PropTypes.func.isRequired,
    getCanApplyCustList: PropTypes.func.isRequired,
    searchServerPersonList: PropTypes.array.isRequired,
    getSearchServerPersonList: PropTypes.func,
    canApplyCustList: PropTypes.array.isRequired,
    getHasServerPersonList: PropTypes.func.isRequired,
    getNextApproverList: PropTypes.func.isRequired,
    nextApproverList: PropTypes.array.isRequired,
    bottonList: PropTypes.object.isRequired,
    getBottonList: PropTypes.func.isRequired,
    getModifyCustApplication: PropTypes.func.isRequired,
    modifyCustApplication: PropTypes.object.isRequired,
    addListenModify: PropTypes.bool.isRequired,
    getCreateCustApplication: PropTypes.func.isRequired,
    createCustApplication: PropTypes.object.isRequired,
    addListenCreate: PropTypes.bool.isRequired,
    getSubTypeList: PropTypes.func.isRequired,
    subTypeList: PropTypes.array.isRequired,
    validateWeights: PropTypes.func.isRequired,
    validateData: PropTypes.bool.isRequired,
    deleteAttachment: PropTypes.func.isRequired,
    getServerPersonelList: PropTypes.func.isRequired,
    // 客户服务关系跨境校验
    checkCustCityLimit: PropTypes.func.isRequired,
  }

  static defaultProps = {
    detailMessage: {},
    seibelListLoading: false,
    getSearchServerPersonList: _.noop,
  }

  static contextTypes = {
    empInfo: PropTypes.object.isRequired,
  }

  static childContextTypes = {
    getCanApplyCustList: PropTypes.func.isRequired,
    getSearchServerPersonList: PropTypes.func.isRequired,
    getSubTypeList: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      // 是否显示新建私密客户弹框
      isShowCreateModal: false,
      // 高亮项的下标索引
      activeRowIndex: 0,
    };
  }

  // NOTE: SWB 2024-02-06 不知道他们以前的开发为什么通过这种方式去传
  getChildContext() {
    return {
      getCanApplyCustList: (data) => {
        this.props.getCanApplyCustList({
          keyword: data,
          type: pageType,
          postnId: emp.getPstnId(),
        });
      },
      // 获取 查询服务人员列表
      getSearchServerPersonList: (data) => {
        this.props.getServerPersonelList({
          keyword: data,
          pageSize: 20,
          pageNum: 1,
        });
      },
      getSubTypeList: (data) => this.props.getSubTypeList(data),
    };
  }

  componentDidMount() {
    const {
      location: {
        query,
        query: {
          pageNum,
          pageSize,
        },
      },
    } = this.props;
    this.queryAppList(query, pageNum, pageSize);
  }

  componentDidUpdate(prevProps) {
    const { location: { query: prevQuery } } = prevProps;
    const {
      location: { query },
    } = this.props;
    const otherQuery = _.omit(query, ['currentId']);
    const otherPrevQuery = _.omit(prevQuery, ['currentId']);
    // query和prevQuery，不等时需要重新获取列表，但是首次进入页面获取列表在componentDidMount中调用过，所以不需要重复获取列表
    if (!_.isEqual(otherQuery, otherPrevQuery) && !_.isEmpty(prevQuery)) {
      const { pageNum, pageSize } = query;
      this.queryAppList(query, pageNum, pageSize);
    }
  }

  // 获取列表后再获取某个Detail
  @autobind
  getRightDetail() {
    const {
      replace,
      list,
      location: { pathname, query, query: { currentId } },
    } = this.props;
    if (!_.isEmpty(list.resultData)) {
      // 表示左侧列表获取完毕
      // 因此此时获取Detail
      const { pageNum, pageSize } = list.page;
      let item = list.resultData[0];
      let itemIndex = _.findIndex(list.resultData, (o) => o.id.toString() === currentId);
      if (!_.isEmpty(currentId) && itemIndex > -1) {
        // 此时url中存在currentId
        item = _.filter(list.resultData, (o) => String(o.id) === String(currentId))[0];
      } else {
        // 不存在currentId
        replace({
          pathname,
          query: {
            ...query,
            currentId: item.id,
            pageNum,
            pageSize,
          },
        });
        itemIndex = 0;©
      }
      this.setState({
        activeRowIndex: itemIndex,
      });
      this.props.getDetailMessage({
        type: pageType,
        id: item.id,
      });
    }
  }

  @autobind
  queryAppList(query, pageNum = 1, pageSize = 20) {
    const params = seibelHelper.constructSeibelPostBody(query, pageNum, pageSize);
    // 新需求需要增加一个编号过滤
    // 并且当从其他页面跳转过来指定显示某一条申请单详情的数据的时候，
    // 需要告诉后端不要鉴权，如果只是用户输入编号，则需要鉴权
    // authFlag='N'不鉴权，authFlag='Y'鉴权
    const applyIdParams = {
      id: query.applyId || '',
      authFlag: query.authFlag || 'Y',
    };
    // 默认筛选条件
    this.props.getPermissionList({
      type: pageType,
      ...params,
      ...applyIdParams,
    }).then(this.getRightDetail);
  }

  // 头部筛选后调用方法
  @autobind
  handleHeaderFilter(obj) {
    // 1.将值写入Url
    const { replace, location } = this.props;
    const { query, pathname } = location;
    replace({
      pathname,
      query: {
        ...query,
        pageNum: 1,
        ...obj,
      },
    });
  }

  @autobind
  clearModal(name) {
    // 清除模态框组件
    this.setState({ [name]: false });
  }

  // 头部新建页面
  @autobind
  @logPV({ pathname: '/modal/createPermission', title: '新建权限申请' })
  creatPermossionModal() {
    // 打开模态框 发送获取服务人员列表请求
    this.setState({ isShowCreateModal: true });
  }

  // 点击列表每条的时候对应请求详情
  @autobind
  @logable({
    type: 'ViewItem',
    payload: {
      name: '权限申请左侧列表项',
      type: '$props.location.query.type',
      subType: '$props.location.query.subType',
    },
  })
  handleListRowClick(record, index) {
    const { id } = record;
    const {
      replace,
      location: { pathname, query, query: { currentId } },
    } = this.props;
    if (currentId === String(id)) return;
    replace({
      pathname,
      query: {
        ...query,
        currentId: id,
      },
    });
    this.setState({ activeRowIndex: index });
    this.props.getDetailMessage({
      id,
      type: pageType,
    });
  }

  /**
   * 检查部分属性是否相同
   * @param {*} prevQuery 前一次query
   * @param {*} nextQuery 后一次query
   */
  diffObject(prevQuery, nextQuery) {
    const prevQueryData = _.omit(prevQuery, OMIT_ARRAY);
    const nextQueryData = _.omit(nextQuery, OMIT_ARRAY);
    if (!_.isEqual(prevQueryData, nextQueryData)) {
      return false;
    }
    return true;
  }

  get detailComponent() {
    if (_.isEmpty(this.props.detailMessage)) {
      return null;
    }

    const {
      canApplyCustList,
      searchServerPersonList,
      nextApproverList,
      getNextApproverList,
      getBottonList,
      bottonList,
      getModifyCustApplication,
      modifyCustApplication,
      addListenModify,
      subTypeList,
      location,
      checkCustCityLimit,
    } = this.props;

    return (
      <Detail
        {...this.props.detailMessage}
        location={location}
        canApplyCustList={canApplyCustList}
        searchServerPersonList={searchServerPersonList}
        nextApproverList={nextApproverList}
        getNextApproverList={getNextApproverList}
        getBottonList={getBottonList}
        bottonList={bottonList}
        getModifyCustApplication={getModifyCustApplication}
        modifyCustApplication={modifyCustApplication}
        addListenModify={addListenModify}
        subTypeList={subTypeList}
        onEmitClearModal={this.clearModal}
        checkCustCityLimit={checkCustCityLimit}
      />
    );
  }

  // 切换页码
  @autobind
  handlePageNumberChange(nextPage, currentPageSize) {
    const { replace, location } = this.props;
    const { query, pathname } = location;
    replace({
      pathname,
      query: {
        ...query,
        pageNum: nextPage,
        pageSize: currentPageSize,
      },
    });
  }

  // 切换每一页显示条数
  @autobind
  handlePageSizeChange(currentPageNum, changedPageSize) {
    const { replace, location } = this.props;
    const { query, pathname } = location;
    replace({
      pathname,
      query: {
        ...query,
        pageNum: 1,
        pageSize: changedPageSize,
      },
    });
  }

  // 创建私密客户申请
  @autobind
  handleCreatePrivateApp(params) {
    const { location: { query } } = this.props;
    // log日志---创建私密客户申请
    let logSubType = '';
    switch (params.subType) {
      case '0103':
        logSubType = '私密客户设置';
        break;
      case '0102':
        logSubType = '私密客户取消';
        break;
      case '0101':
        logSubType = '私密客户交易信息权限分配';
        break;
      default:
        break;
    }
    logCommon({
      type: 'Submit',
      payload: {
        name: '',
        type: '权限申请',
        subType: logSubType,
        value: JSON.stringify(params),
      },
    });
    this.props.getCreateCustApplication(params).then(
      () => this.queryAppList(query, query.pageNum, query.pageSize),
    );
  }

  // 修改私密客户申请
  @autobind
  handleModifyPrivateApp(params) {
    const { location: { query } } = this.props;
    // log日志---修改私密客户申请
    logCommon({
      type: 'Submit',
      payload: {
        name: '',
        type: '权限申请',
        subType: params.subType,
        value: JSON.stringify(params),
      },
    });
    this.props.getModifyCustApplication(params).then(
      () => this.queryAppList(query, query.pageNum, query.pageSize),
    );
  }

  // 后台返回的子类型字段、状态字段转化为对应的中文显示
  @autobind
  getSubTypeName(st, options) {
    if (st && !_.isEmpty(st)) {
      const nowStatus = _.find(options, (o) => o.value === st) || {};
      return nowStatus.label || '无';
    }
    return '无';
  }

  // 渲染列表项里面的每一项
  @autobind
  renderListRow(record, index) {
    const { activeRowIndex } = this.state;
    const { status: statusData, subType } = record;
    const statusTags = [convert.getStatusByCode(statusData)];
    const subTypeName = this.getSubTypeName(subType, subTypeOptions);
    return (
      <ApplyItem
        key={record.id}
        data={record}
        active={index === activeRowIndex}
        onClick={this.handleListRowClick}
        index={index}
        typeName={subTypeName}
        statusTags={statusTags}
      />
    );
  }

  render() {
    const {
      list,
      location,
      replace,
      canApplyCustList,
      searchServerPersonList,
      getHasServerPersonList,
      nextApproverList,
      getNextApproverList,
      createCustApplication,
      addListenCreate,
      subTypeList,
      validateWeights,
      validateData,
      deleteAttachment,
    } = this.props;

    const {
      empInfo: {
        empInfo = {},
      },
    } = this.context;

    const isEmpty = _.isEmpty(list.resultData);
    const { isShowCreateModal } = this.state;
    const topPanel = (
      <ConnectedSeibelHeader
        location={location}
        replace={replace}
        page="premissionPage"
        pageType={pageType}
        subtypeOptions={subTypeOptions}
        stateOptions={status}
        creatSeibelModal={this.creatPermossionModal}
        filterCallback={this.handleHeaderFilter}
        basicFilters={basicFilters}
        moreFilters={moreFilters}
        moreFilterData={moreFilterData}
      />
    );

    // 生成页码器，此页码器配置项与Antd的一致
    const { location: { query: { pageNum = 1, pageSize = 20 } } } = this.props;
    const { resultData = [], page = {} } = list;
    const paginationOptions = {
      current: parseInt(pageNum, 10),
      total: page.totalCount,
      pageSize: parseInt(pageSize, 10),
      onChange: this.handlePageNumberChange,
      onShowSizeChange: this.handlePageSizeChange,
    };

    const leftPanel = (
      <PermissionList
        list={resultData}
        renderRow={this.renderListRow}
        pagination={paginationOptions}
      />
    );

    return (
      <div className={styles.premissionbox}>
        <SplitPanel
          isEmpty={isEmpty}
          topPanel={topPanel}
          leftPanel={leftPanel}
          rightPanel={this.detailComponent}
          leftListClassName="premissionList"
        />
        {
          isShowCreateModal
            ? (
              <CreatePrivateClient
                location={location}
                canApplyCustList={canApplyCustList}
                searchServerPersonList={searchServerPersonList}
                onEmitClearModal={this.clearModal}
                getHasServerPersonList={getHasServerPersonList}
                nextApproverList={nextApproverList}
                getNextApproverList={getNextApproverList}
                getCreateCustApplication={this.handleCreatePrivateApp}
                createCustApplication={createCustApplication}
                addListenCreate={addListenCreate}
                subTypeList={subTypeList}
                empInfo={empInfo}
                validateWeights={validateWeights}
                validateData={validateData}
                deleteAttachment={deleteAttachment}
                checkCustCityLimit={this.props.checkCustCityLimit}
              />
            )
            : null
        }
      </div>
    );
  }
}
