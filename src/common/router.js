/* eslint-disable max-len */
import { createElement, cloneElement } from 'react';
import dynamic from 'dva/dynamic';

import recommendedLabel from '../components/platformParameterSetting/routers/RecommendedLabel';

let routerDataCache;
const modelNotExisted = (app, model) => (
  // eslint-disable-next-line
  !app._models.some(({ namespace }) => {
    return namespace === model.substring(model.lastIndexOf('/') + 1);
  })
);
// wrapper of dynamic
export const dynamicWrapper = (app, models, WrapperComponent) => {
  // () => require('module')
  // transformed by babel-plugin-dynamic-import-node-sync
  if (WrapperComponent?.toString()?.indexOf('.then(') < 0) {
    models.forEach((model) => {
      if (modelNotExisted(app, model)) {
        // eslint-disable-next-line
        app.model(require(`../models/${model}`).default);
      }
    });
    // eslint-disable-next-line react/react-in-jsx-scope
    return (props) => cloneElement(<WrapperComponent />, { ...props, routerData: routerDataCache });
  }
  return dynamic({
    app,
    models: () =>
      // eslint-disable-next-line
      models.filter(model => modelNotExisted(app, model)).map(m => import(`../models/${m}.js`)),
    // add routerData prop
    component: () => {
      if (!routerDataCache) {
        // eslint-disable-next-line
        routerDataCache = getRouterData(app);
      }
      // eslint-disable-next-line
      return WrapperComponent().then(raw => {
        const Component = raw.default || raw;
        return (props) => createElement(Component, {
          ...props,
          routerData: routerDataCache,
        });
      });
    },
  });
};

// 新增路由必须增加前缀：/webfsp
export const getRouterData = (app) => {
  const routerConfig = {
    '/empty': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "empty" */ '../routes/empty/Home')),
    },
    '/phone': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "phone" */ '../routes/phone/Home')),
    },
    // 直接进入
    '/statisticalQuery/report': {
      component: dynamicWrapper(app, ['report'], () => import(/* webpackChunkName: "report" */ '../routes/reports/Home')),
    },
    // 直接进入
    '/boardManage': {
      component: dynamicWrapper(app, ['manage'], () => import(/* webpackChunkName: "boardManage" */ '../routes/boardManage/Home')),
    },
    // 从 boardManage 页面点击看板进入
    '/boardEdit': {
      component: dynamicWrapper(app, ['edit'], () => import(/* webpackChunkName: "boardEdit" */ '../routes/boardEdit/Home')),
    },
    // 在 boardEdit 页面右下角点击预览进入
    '/preview': {
      component: dynamicWrapper(app, ['preview', 'edit'], () => import(/* webpackChunkName: "preview" */ '../routes/reports/PreviewReport')),
    },
    // 再 report 页面左上角下拉列表-自定义看板-选择一个点击进入
    '/history': {
      component: dynamicWrapper(app, ['history'], () => import(/* webpackChunkName: "history" */ '../routes/history/Home')),
    },
    // 直接进入
    '/businessApplyment/commission': {
      component: dynamicWrapper(app, ['commission'], () => import(/* webpackChunkName: "commission" */ '../routes/commission/connectedHome')),
    },
    // 直接进入
    '/businessApplyment/bizapply/excesscache': {
      component: dynamicWrapper(app, ['excessCache'], () => import(/* webpackChunkName: "excessCache" */ '../routes/excessCache/Home')),
    },
    // ['资讯订阅', '资讯退订']
    // const arr = ['SUBSCRIBE', 'UNSUBSCRIBE']
    // 从 commission 页面左侧列表中选择一条类型在 arr 中的数据，找到返回数据中的 flowCode 或 flowId
    // localhost:9088/#/commissionChange?flowId=xxxxxx&type=SINGLE
    // type 为对应的类型值
    '/commissionChange': {
      component: dynamicWrapper(app, ['commissionChange'], () => import(/* webpackChunkName: "commissionChange" */'../routes/commissionChange/connectedHome')),
    },
    // 直接进入没有数据，需要一个 custid，不知道是什么
    '/commissionAdjustment': {
      component: dynamicWrapper(app, ['commission'], () => import(/* webpackChunkName: "commissionAdjustment" */'../routes/commissionAdjustment/Home')),
    },
    // 可直接进入看页面，所需数据未知
    '/preSaleQuery': {
      component: dynamicWrapper(app, ['preSaleQuery'], () => import(/* webpackChunkName: "preSaleQuery" */'../routes/preSaleQuery/Home')),
    },
    // 可直接进入，部分公用组件的展示路由
    '/modal': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "modal" */ '../routes/templeModal/Home')),
    },
    // 需要有权限的角色进入
    '/sysOperate/crossDepartment/relation': {
      component: dynamicWrapper(app, ['relation'], () => import(/* webpackChunkName: "relation" */ '../routes/relation/Home')),
    },
    '/taskCenter/taskList': {
      component: dynamicWrapper(app,
        [
          'taskList/performerView',
          'customerPool',
          'taskList/tasklist',
          'taskList/managerView',
          'investmentAdvice',
          'taskApproval',
          'serviceScenes'
        ],
        () => import(/* webpackChunkName: "taskList" */ '../routes/TaskList__/ConnectedHome')),
    },
    // 服务记录管理
    '/taskCenter/taskManage': {
      component: dynamicWrapper(app,
        [
          'customerPool',
          'orderManage',
          'taskManage',
          'customer360Detail/productOrder',
          'serviceScenes'
        ], () => import(/* webpackChunkName: "taskManage" */ '../routes/taskManage/Home')),
    },
    // CRM工单管理
    '/workOrderManage': {
      component: dynamicWrapper(app,
        [
          'workOrderManage',
        ], () => import(/* webpackChunkName: "sheetManage" */ '../routes/workOrderManage/Home')),
    },
    // 直接进入
    '/statisticalQuery/exchange': {
      component: dynamicWrapper(app, ['pointsExchange'], () => import(/* webpackChunkName: "exchange" */ '../routes/pointsExchange/Home')),
    },
    // 权限申请-私密客户申请
    // 直接进入
    '/businessApplyment/permission/privateCustApplication': {
      component: dynamicWrapper(app, ['permission'], () => import(/* webpackChunkName: "permission" */ '../routes/permission/Home')),
    },
    // 从 permission 页面左侧列表中选择一条数据，找到请求回来的 flowId,
    // 拼接路由 /permission/edit?flowId=xxxxxxxx&empId=xxxx，
    // empId 需要设置为 edit 获取到的详情里的审批人
    // 由此进入为有数据页面
    '/permission/edit': {
      component: dynamicWrapper(app, ['permission'], () => import(/* webpackChunkName: "permission_edit" */ '../routes/permission/Edit')),
    },
    // 权限申请-客户公司级分配事项申报
    // 直接进入
    '/businessApplyment/permission/custCompanyLevelAllotApply': {
      component: dynamicWrapper(app, ['custCompanyLevelAllot'], () => import(/* webpackChunkName: "permission" */ '../routes/custCompanyLevelAllot/connectedHome')),
    },
    // 权限申请-客户公司级分配事项申报-驳回后修改
    // 从 custCompanyLevelAllotApply 页面左侧列表中选择一条数据，找到请求回来的 flowId,
    // 拼接路由 /businessApplyment/permission/custCompanyLevelAllotApplyEdit?flowId=xxxxxxxx&empId=xxxx，
    // empId 需要设置为 edit 获取到的详情里的审批人
    // 由此进入为有数据页面
    '/businessApplyment/permission/custCompanyLevelAllotApplyEdit': {
      component: dynamicWrapper(app, ['custCompanyLevelAllot'], () => import(/* webpackChunkName: "permission" */ '../routes/custCompanyLevelAllot/connectedEdit')),
    },
    // 直接进入
    '/businessApplyment/contract': {
      component: dynamicWrapper(app, ['contract'], () => import(/* webpackChunkName: "contract" */ '../routes/contract/Home')),
    },
    // 从 contract 页面左侧列表中选择一条数据，找到请求回来的 flowId,
    // 拼接路由 /contract/form?flowId=xxxxxxxx&empId=xxxx,
    // empId 需要设置为 edit 获取到的详情里的审批人
    // 由此进入为有数据页面
    '/contract/form': {
      component: dynamicWrapper(app, ['contract'], () => import(/* webpackChunkName: "contract_form" */ '../routes/contract/Form')),
    },
    // 直接进入
    '/businessApplyment/channelsTypeProtocol': {
      component: dynamicWrapper(app, ['channelsTypeProtocol'], () => import(/* webpackChunkName: "channelsTypeProtocol" */ '../routes/channelsTypeProtocol/Home')),
    },
    // 从 channelsTypeProtocol 页面左侧列表中选择一条数据，找到请求回来的 flowId,
    // 拼接路由 /channelsTypeProtocol/edit?flowId=xxxxxxxx&empId=xxxx,
    // empId 需要设置为 edit 获取到的详情里的审批人
    // 由此进入为有数据页面
    '/channelsTypeProtocol/edit': {
      component: dynamicWrapper(app, ['channelsTypeProtocol', 'channelsEdit'], () => import(/* webpackChunkName: "channelsTypeProtocol_edit" */ '../routes/channelsTypeProtocol/Edit')),
    },
    '/channelsTypeProtocol/arbitrageEdit': {
      component: dynamicWrapper(app, ['arbitrageUnSubscribeEdit'], () => import(/* webpackChunkName: "channelsTypeProtocol_arbitrageEdit" */ '../routes/channelsTypeProtocol/ArbitrageUnSubscribeEdit')),
    },
    // 直接进入
    '/customerPool': {
      component: dynamicWrapper(app, ['customerPool', 'morningBoradcast'], () => import(/* webpackChunkName: "customerPool" */ '../routes/newHome_/Home')),
      isPrimary: true,
    },
    // 从 customerPool 搜索框下方--任务概览--第三个选项【代办流程】进入
    '/customerPool/todo': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist'], () => import(/* webpackChunkName: "customerPool_todo" */ '../routes/customerPool/ToDo')),
      isPrimary: true,
    },
    // 从 customerPool 页面中上部的搜索框输入搜索条件、或搜索框下方--猜你感兴趣进入
    '/customerPool/list': {
      component: dynamicWrapper(app, ['customerPool', 'customerLabel', 'serviceScenes'], () => import(/* webpackChunkName: "customerPool_list" */ '../routes/customerPool/customerList/ConnectedHome')),
      isPrimary: true,
    },
    // 从客户360详情-账户信息-账户分析进入
    '/investAnalyze': {
      component: dynamicWrapper(
        app,
        [
          'customerDetail',
          'customer360Detail/investAnalyze',
        ],
        () => import(/* webpackChunkName: "customerDetail_investAnalyze" */ '../routes/investAnalyze/connectedHome'),
      ),
      isPrimary: true,
    },
    '/investAnalyze/edit': {
      component: dynamicWrapper(
        app,
        [
          'customerDetail',
          'customer360Detail/investAnalyze',
        ],
        () => import(/* webpackChunkName: "customerDetail_investAnalyze" */ '../routes/investAnalyzeEdit/connectedHome'),
      ),
      isPrimary: true,
    },
    // 从客户360详情-账户信息-账户分析-投资账户分析进入, 所需数据为custId
    '/analysisReportHistoryList': {
      component: dynamicWrapper(app, ['customer360Detail/investAnalyze'], () => import(/* webpackChunkName: "empty" */ '../routes/investAnalyze/AnalysisReportHistoryList')),
    },
    // customerPool/customerGroup 直接进入，所需数据未知
    '/customerPool/customerGroup': {
      component: dynamicWrapper(app, ['customerPool'], () => import(/* webpackChunkName: "customerPool_customerGroup" */ '../routes/customerPool/CustomerGroup')),
      isPrimary: true,
    },
    // 分组管理发起任务
    // customerPool/createTaskFromCustGroup 直接进入，所需数据未知
    '/customerPool/createTaskFromCustGroup': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 管理者视图详情发起任务
    '/customerPool/createTaskFromDetail': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 管理者视图进度条发起任务
    '/customerPool/createTaskFromProgress': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 管理者视图饼图发起任务
    '/customerPool/createTaskFromPie': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 从代办流程进去，任务驳回修改
    '/customerPool/createTaskFromTaskRejection1': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 从任务管理，创建者视图驳回中的任务，进行任务驳回修改
    '/customerPool/createTaskFromTaskRejection2': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 从管理者视图服务经理维度发起任务
    '/customerPool/createTaskFromCustScope': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 客户列表发起任务
    '/customerPool/createTask': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 执行者视图服务结果客户明细的地方发起任务
    '/customerPool/createTaskFromServiceResultCust': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 标签管理页面发起任务
    '/customerPool/createTaskFromLabelManagement': {
      component: dynamicWrapper(app, ['customerPool', 'taskList/tasklist', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_createTask" */ '../routes/customerPool/CreateTask')),
      isPrimary: true,
    },
    // 客户分组管理
    '/customerPool/customerGroupManage': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "customerGroupManage" */ '../routes/customerPool/CustomerGroupManage')),
      isPrimary: true,
    },
    '/customerPool/serviceLog': {
      component: dynamicWrapper(app, ['customerPool'], () => import(/* webpackChunkName: "customerPool_serviceLog" */ '../routes/customerPool/ServiceLog')),
      isPrimary: true,
    },
    // 从 /taskList 页面，点击右上角新建进入
    '/customerPool/taskFlow': {
      component: dynamicWrapper(app, ['customerPool', 'taskFeedback'], () => import(/* webpackChunkName: "customerPool_taskFlow" */ '../routes/customerPool/TaskFlow')),
      isPrimary: true,
    },

    // 从 FSP 消息提醒进入，亦可直接进入，需要数据需后台配置
    '/demote': {
      component: dynamicWrapper(app, ['demote'], () => import(/* webpackChunkName: "demote" */ '../routes/demote/Home')),
    },
    // 从 FSP 消息提醒进入

    // 消息通知提醒
    '/messageCenter': {
      component: dynamicWrapper(app, ['messageCenter'], () => import(/* webpackChunkName: "messgeCenter" */ '../routes/messageCenter/Home')),
    },
    // 直接进入
    '/customerFeedback': {
      component: dynamicWrapper(app, ['customerFeedback'], () => import(/* webpackChunkName: "customerFeedback" */ '../routes/customerFeedback/Home')),
    },
    // 直接进入
    '/taskFeedback': {
      component: dynamicWrapper(app, ['taskFeedback'], () => import(/* webpackChunkName: "taskFeedback" */ '../routes/taskFeedback/Home')),
    },
    // 直接进入
    '/sysOperate/crossDepartment/mainPosition': {
      component: dynamicWrapper(app, ['mainPosition', 'customerPool'], () => import(/* webpackChunkName: "mainPosition" */ '../routes/mainPosition/Home')),
    },
    // 从 mainPosition 页面左侧列表中选择一条数据，找到请求回来的 flowId,
    // 拼接路由 /mainPosition/edit?flowId=xxxxxxxx&empId=xxxx,
    // empId 需要设置为 edit 获取到的详情里的审批人
    // 由此进入为有数据页面
    '/mainPosition/edit': {
      component: dynamicWrapper(app, ['mainPosition'], () => import(/* webpackChunkName: "mainPosition_edit" */ '../routes/mainPosition/Edit')),
    },
    // 从 fsp 消息提醒对应类型进入，本地可直接进入，如需要数据，需向后端要一个 appId
    '/mainPosition/notifies': {
      component: dynamicWrapper(app, ['mainPosition'], () => import(/* webpackChunkName: "mainPosition_notifies" */ '../routes/mainPosition/Notifies')),
    },

    // 晨间播报
    // 直接进入，或从 customerPool 页面右侧-晨间播报-更多进入
    '/strategyCenter/broadcastList': {
      component: dynamicWrapper(app, ['morningBoradcast'], () => import(/* webpackChunkName: "broadcastList" */ '../routes/morningBroadcast/BroadcastList')),
    },
    // 从 broadcastList 点击任意记录进入
    '/broadcastDetail': {
      component: dynamicWrapper(app, ['morningBoradcast'], () => import(/* webpackChunkName: "broadcastDetail" */ '../routes/morningBroadcast/BroadcastDetail')),
    },
    // 个股点评
    // 直接进入
    '/strategyCenter/stock': {
      component: dynamicWrapper(app, ['stock'], () => import(/* webpackChunkName: "stock" */ '../routes/stock/Home')),
    },
    // 在 stock 页面的列表中点击任意记录进入
    '/strategyCenter/stock/detail': {
      component: dynamicWrapper(app, ['stock'], () => import(/* webpackChunkName: "stock_detail" */ '../routes/stock/Detail')),
      isPrimary: true,
    },
    // 平台参数设置
    '/sysOperate/platformParameterSetting': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "platformParameterSetting" */ '../routes/platformParameterSetting/Home')),
    },
    '/sysOperate/platformParameterSetting/taskOperation/customerFeedback': {
      component: dynamicWrapper(app, ['customerFeedback'], () => import(/* webpackChunkName: "taskOperationCustomerFeedback" */ '../routes/customerFeedback/Home')),
    },
    '/sysOperate/platformParameterSetting/taskOperation/taskFeedback': {
      component: dynamicWrapper(app, ['taskFeedback'], () => import(/* webpackChunkName: "taskOperationTaskFeedback" */ '../routes/taskFeedback/Home')),
    },
    '/sysOperate/platformParameterSetting/taskOperation/investmentAdvice': {
      component: dynamicWrapper(app, ['investmentAdvice'], () => import(/* webpackChunkName: "taskOperationInvestmentAdvice" */ '../routes/investmentAdvice/Home')),
    },
    '/sysOperate/platformParameterSetting/labelManager': {
      component: dynamicWrapper(app, ['userCenter'], () => import(/* webpackChunkName: "LabelManager" */ '../components/platformParameterSetting/routers/LabelManager')),
    },
    // 直接进入
    '/sysOperate/platformParameterSetting/contentOperate/recommendedLabel': {
      component: dynamicWrapper(app, ['operationCenter'], recommendedLabel),
    },
    // '/sysOperate/platformParameterSetting/contentOperate/activityColumn': {
    //   component: dynamicWrapper(app, ['activityColumn'], () => import('../components/platformParameterSetting/routers/contentOperate/NewActivityColumn/index' /* webpackChunkName: "ActivityColumn" */)),
    // },
    // 运维管理-平台参数管理-首页内容-首页公告
    '/sysOperate/platformParameterSetting/contentOperate/homePageAnnouncement': {
      component: dynamicWrapper(app, ['homePageAnnouncement'], () => import(/* webpackChunkName: "HomePageAnnouncement" */ '../components/platformParameterSetting/routers/contentOperate/homepageAnnouncement/HomePageAnnouncement')),
    },
    // 运维管理-平台参数管理-首页内容-重点关注标签
    '/sysOperate/platformParameterSetting/contentOperate/keyAttentionSetting': {
      component: dynamicWrapper(app, ['keyAttentionSetting'], () => import(/* webpackChunkName: "KeyAttentionSetting" */ '../components/platformParameterSetting/routers/contentOperate/KeyAttentionSetting/Home')),
    },
    '/sysOperate/platformParameterSetting/customerLabel': {
      component: dynamicWrapper(app, ['customerLabel'], () => import(/* webpackChunkName: "customerLabel" */ '../components/platformParameterSetting/routers/customerLabel/index')),
    },
    '/sysOperate/platformParameterSetting/productSale/keyFirstPublicOffering': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "productSaleKeyFirstPublicOffering" */ '../routes/productSale/Home')),
    },
    '/sysOperate/platformParameterSetting/productSale/filialeAnnualTarget': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "productSaleFilialeAnnualTarget" */ '../routes/productSale/Home')),
    },
    '/sysOperate/platformParameterSetting/taskOperation/scenarioAndLabel': {
      component: dynamicWrapper(app, ['scenarioAndLabel', 'serviceScenes'], () => import(/* webpackChunkName: "scenarioAndLabel" */ '../routes/scenarioAndLabel/connectedHome')),
    },
    // 灰度建设
    '/sysOperate/platformParameterSetting/grayscale': {
      component: dynamicWrapper(app, ['grayscale'], () => import(/* webpackChunkName: "grayscale" */ '../routes/grayscale')),
    },
    // 直接进入
    // '/customerFeedback': {
    //   component: dynamicWrapper(app, ['customerFeedback'], () => import(/* webpackChunkName: "customerFeedback" */ '../routes/customerFeedback/Home')),
    // },
    // 公务手机和电话卡号管理
    '/sysOperate/telephoneNumberManage': {
      component: dynamicWrapper(app, ['telephoneNumberManage'], () => import(/* webpackChunkName: "telephoneNumberManage" */ '../routes/telephoneNumberManage/Home')),
    },
    '/sysOperate/telephoneNumberManage/distribute': {
      component: dynamicWrapper(app, ['telephoneNumberManage'], () => import(/* webpackChunkName: "telephoneManageDistribute" */ '../routes/telephoneNumberManage/DistributeHome')),
    },
    '/sysOperate/telephoneNumberManage/apply': {
      component: dynamicWrapper(app, ['telephoneNumberManage'], () => import(/* webpackChunkName: "telephoneManageApplyHome" */ '../routes/telephoneNumberManage/ApplyHome')),
    },
    // 公务手机和电话卡号管理修改页面
    '/sysOperate/telephoneNumberManageEdit': {
      component: dynamicWrapper(app, ['telephoneNumberManage'], () => import(/* webpackChunkName: "telephoneNumberManageEdit" */ '../routes/telephoneNumberManage/ApplyEdit')),
    },
    // 精选组合，直接进入
    '/strategyCenter/choicenessCombination': {
      component: dynamicWrapper(app, ['choicenessCombination'], () => import(/* webpackChunkName: "choicenessCombination" */ '../routes/choicenessCombination/Home')),
    },
    // 组合详情 /choicenessCombination/combinationDetail?id=xxx  id为组合id
    '/strategyCenter/choicenessCombination/combinationDetail': {
      component: dynamicWrapper(app, ['combinationDetail'], () => import(/* webpackChunkName: "choicenessCombination_combinationDetail" */ '../routes/choicenessCombination/CombinationDetail')),
      isPrimary: true,
    },
    // 历史报告详情 /choicenessCombination/reportDetail?id=xxx&combinationCode=xxx
    // id为报告 id，combinationCode 为组合 id
    '/strategyCenter/choicenessCombination/reportDetail': {
      component: dynamicWrapper(app, ['choicenessCombination'], () => import(/* webpackChunkName: "choicenessCombination_reportDetail" */ '../routes/choicenessCombination/ReportDetail')),
      isPrimary: true,
    },
    // 投顾业务能力竞赛
    '/investmentConsultantRace': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "investmentConsultantRace" */ '../routes/investmentConsultantRace/Home')),
    },
    // 客户关联关系信息申请，直接进入
    '/businessApplyment/appropriate/custRelationships': {
      component: dynamicWrapper(app, ['custRelationships'], () => import(/* webpackChunkName: "custRelationships" */ '../routes/custRelationships/Home')),
    },
    // 客户关联关系信息申请，传递参数flowId
    '/custRelationshipsReject': {
      component: dynamicWrapper(app, ['custRelationships'], () => import(/* webpackChunkName: "custRelationshipsReject" */ '../routes/custRelationships/RejectUpdateHome')),
    },
    // 直接进入
    '/customerPool/customerPartition/custAllot': {
      component: dynamicWrapper(app, ['custAllot'], () => import(/* webpackChunkName: "custAllot" */ '../routes/custAllot/connectedHome')),
      isPrimary: true,
    },
    // 从 fsp 消息提醒对应类型进入，本地可直接进入，如需要数据，需向后端要一个 appId 以及 type
    '/customerPool/customerPartition/custAllot/notifies': {
      component: dynamicWrapper(app, ['custAllot'], () => import(/* webpackChunkName: "custAllot_notifies" */ '../routes/custAllot/Notifies')),
      isPrimary: true,
    },
    // 更多-线上签约(灰度菜单)
    '/businessApplyment/onlineSignUp': {
      component: dynamicWrapper(app, ['onlineSignUp'], () => import(/* webpackChunkName: "onlineSignUp" */ '../routes/onlineSignUp/connectedHome')),
      isPrimary: true,
    },
    // 线上签约-驳回修改（从代办进入，url参数：flowId，taskId）
    '/businessApplyment/onlineSignUpReject': {
      component: dynamicWrapper(app, ['onlineSignUp'], () => import(/* webpackChunkName: "onlineSignUp" */ '../routes/onlineSignUp/RejectHome')),
      isPrimary: true,
    },
    // 直接进入
    '/customerPool/customerPartition/departmentCustAllot': {
      component: dynamicWrapper(app, ['departmentCustAllot'], () => import(/* webpackChunkName: "departmentCustAllot" */ '../routes/departmentCustAllot/connectedHome')),
      isPrimary: true,
    },
    // 从 fsp 消息提醒对应类型进入，本地可直接进入，如需要数据，需向后端要一个 appId 以及 type
    '/customerPool/customerPartition/departmentCustAllot/notifies': {
      component: dynamicWrapper(app, ['departmentCustAllot'], () => import(/* webpackChunkName: "departmentCustAllot_notifies" */ '../routes/departmentCustAllot/Notifies')),
      isPrimary: true,
    },
    // 直接进入,总部客户分配
    '/customerPool/customerPartition/headquartersCustAllot': {
      component: dynamicWrapper(app, ['headquartersCustAllot'], () => import(/* webpackChunkName: "headquartersCustAllot" */ '../routes/headquartersCustAllot/connectedHome')),
      isPrimary: true,
    },
    // 总部客户分配审批页面
    '/customerPool/customerPartition/headquartersCustAllot/approval': {
      component: dynamicWrapper(app, ['headquartersApprovalCustAllot'], () => import(/* webpackChunkName: "headquartersCustAllot" */ '../routes/headquartersCustAllot/Approval')),
      isPrimary: true,
    },
    // 从 fsp 消息提醒对应类型进入，本地可直接进入，如需要数据，需向后端要一个 appId 以及 type
    '/customerPool/customerPartition/headquartersCustAllot/notifies': {
      component: dynamicWrapper(app, ['headquartersApprovalCustAllot'], () => import(/* webpackChunkName: "headquartersCustAllot_notifies" */ '../routes/headquartersCustAllot/Notifies')),
      isPrimary: true,
    },
    // 跨分公司客户分配-直接进入
    '/customerPool/customerPartition/acrossBranchCustAllot': {
      component: dynamicWrapper(app, ['acrossBranchCustAllot'], () => import(/* webpackChunkName: "acrossBranchCustAllot" */ '../routes/acrossBranchCustAllot/connectedHome')),
      isPrimary: true,
    },
    // 直接进入，重点监控账户
    '/customerPool/keyMonitorAccount': {
      component: dynamicWrapper(app, ['keyMonitorAccount'], () => import(/* webpackChunkName: "keyMonitorAccount" */ '../routes/keyMonitorAccount/Home')),
      isPrimary: true,
    },
    // 最新观点，直接进入
    '/strategyCenter/latestView': {
      component: dynamicWrapper(app, ['latestView'], () => import(/* webpackChunkName: "latestView" */ '../routes/latestView/Home')),
    },
    // 首席观点列表页面
    '/strategyCenter/latestView/viewpointList': {
      component: dynamicWrapper(app, ['latestView'], () => import(/* webpackChunkName: "latestViewpointList" */ '../routes/latestView/ViewpointList')),
      isPrimary: true,
    },
    // 首席观点详情页
    '/strategyCenter/latestView/viewpointDetail': {
      component: dynamicWrapper(app, ['latestView'], () => import(/* webpackChunkName: "latestViewpointDetail" */ '../routes/latestView/ViewpointDetail')),
      isPrimary: true,
    },
    // 大类资产配置分析列表
    '/strategyCenter/latestView/majorAssetsList': {
      component: dynamicWrapper(app, ['latestView'], () => import(/* webpackChunkName: "majorAssetsList" */ '../routes/latestView/MajorAssetsList')),
      isPrimary: true,
    },
    // 行业主题调整信息列表
    '/strategyCenter/latestView/industryThemeList': {
      component: dynamicWrapper(app, ['latestView'], () => import(/* webpackChunkName: "industryThemeList" */ '../routes/latestView/IndustryThemeList')),
      isPrimary: true,
    },
    // 股票期权评估申请
    '/businessApplyment/option/stockOptionEvaluation': {
      component: dynamicWrapper(app, ['stockOptionEvaluation'], () => import(/* webpackChunkName: "stockOptionEvaluation" */'../routes/stockOptionEvaluation/Home')),
    },
    // 股票期权评估申请修改
    '/stockOptionEvaluationEdit': {
      component: dynamicWrapper(app, ['stockOptionEvaluation'], () => import(/* webpackChunkName: "stockOptionEvaluationEdit" */'../routes/stockOptionEvaluation/ApplyEdit')),
    },
    // 线上销户
    '/businessApplyment/smallCrowd/cancelAccountOL': {
      component: dynamicWrapper(app, ['cancelAccountOL'], () => import(/* webpackChunkName: "cancelAccountOL" */'../routes/cancelAccountOL/Home')),
    },
    // 线上销户
    '/cancelAccountOLReject': {
      component: dynamicWrapper(app, ['cancelAccountOL'], () => import(/* webpackChunkName: "cancelAccountOLReject" */'../routes/cancelAccountOL/RejectHome')),
    },
    // 账号限制管理
    '/businessApplyment/smallCrowd/accountLimit': {
      component: dynamicWrapper(app, ['accountLimit'], () => import(/* webpackChunkName: "accountLimit" */ '../routes/accountLimit/Home')),
    },
    // 从 accountLimit 页面左侧列表中选择一条数据，找到请求回来的 flowId,
    // 拼接路由 /accountLimit/edit?flowId=xxxxxxxx&empId=xxxx,
    // empId 需要设置为 edit 获取到的详情里的审批人
    // 由此进入为有数据页面
    '/accountLimit/edit': {
      component: dynamicWrapper(app, ['accountLimitEdit'], () => import(/* webpackChunkName: "accountLimit_edit" */ '../routes/accountLimit/Edit')),
    },
    // 从 fsp 消息提醒对应类型进入，本地可直接进入，如需要数据，需向后端要一个 appId 以及 type
    '/accountLimit/notifies': {
      component: dynamicWrapper(app, ['accountLimit'], () => import(/* webpackChunkName: "accountLimit_notifies" */ '../routes/accountLimit/Notifies')),
    },
    // 标签管理
    '/customerPool/labelManagement': {
      component: dynamicWrapper(app, ['labelManagement'], () => import(/* webpackChunkName: "labelManagement" */ '../routes/labelManagement/Home')),
      isPrimary: true,
    },
    // 临时委托他人处理任务Home页面
    '/taskCenter/tempDepute': {
      component: dynamicWrapper(app, ['tempDepute'], () => import(/* webpackChunkName: "tempDepute" */ '../routes/tempDepute/Home')),
    },
    // 临时委托他人处理任务驳回后修改的页面
    '/tempDeputeReject': {
      component: dynamicWrapper(app, ['tempDepute'], () => import(/* webpackChunkName: "tempDepute_reject" */ '../routes/tempDepute/RejectHome')),
    },
    // 投顾空间申请
    '/businessApplyment/advisorSpace': {
      component: dynamicWrapper(app, ['advisorSpace'], () => import(/* webpackChunkName: "advisorSpace" */ '../routes/advisorSpace/Home')),
    },
    // 订单管理
    '/statisticalQuery/orderManage': {
      component: dynamicWrapper(app, ['orderManage', 'customer360Detail/productOrder'], () => import(/* webpackChunkName: "orderManage" */ '../routes/orderManage/Home')),
    },
    // smart任务分析报表
    '/statisticalQuery/taskTable/taskAnalysisReport': {
      component: dynamicWrapper(app, ['taskAnalysisReport'], () => import(/* webpackChunkName: "taskAnalysisReport" */ '../routes/taskAnalysisReport/Home')),
    },
    '/demo': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "demo" */ '../routes/demo/Home')),
    },
    // 服务中心-预约服务-直接进入
    '/serviceCenter/reservation': {
      component: dynamicWrapper(app, ['reservation'], () => import(/* webpackChunkName: "reservation" */ '../routes/reservation/Home')),
    },
    // 更多-预约服务-直接进入(灰度菜单)
    '/businessApplyment/reservation': {
      component: dynamicWrapper(app, ['reservation'], () => import(/* webpackChunkName: "reservation" */ '../routes/reservation/Home')),
    },
    // 客户中心-客户分配-权重比例设置
    '/customerPool/customerPartition/weightSetting': {
      component: dynamicWrapper(app, ['weightSetting', 'cooperationWeight'], () => import(/* webpackChunkName: "weightSetting" */ '../routes/weightSetting/Home')),
      isPrimary: true,
    },
    // 客户中心-客户分配-权重比例设置-驳回后修改
    '/customerPool/customerPartition/weightSetting/edit': {
      component: dynamicWrapper(app, ['weightSetting'], () => import(/* webpackChunkName: "weightSetting_edit" */ '../routes/weightSetting/Edit')),
      isPrimary: true,
    },
    // 客户中心-客户分配-合作权重设置-驳回后修改
    '/customerPool/customerPartition/cooperationWeight/edit': {
      component: dynamicWrapper(app, ['weightSetting', 'cooperationWeight'], () => import(/* webpackChunkName: "cooperationWeight_edit" */ '../routes/weightSetting/CooperationWeightEdit')),
      isPrimary: true,
    },
    // 客户中心-潜客商机
    '/customerPool/prospectiveCustomer': {
      component: dynamicWrapper(app, ['prospectiveCustomer'], () => import(/* webpackChunkName: "prospectiveCustomer" */ '../routes/prospectiveCustomer/Home')),
      isPrimary: true,
    },
    // 客户中心-潜在客户
    '/customerPool/potentialCustomer': {
      component: dynamicWrapper(app, ['potentialCustomer'], () => import(/* webpackChunkName: "potentialCustomer" */ '../routes/potentialCustomer/Home')),
      isPrimary: true,
    },
    // 客户中心-潜在客户-转化追踪
    '/customerPool/potentialCustomer/transformTrack': {
      component: dynamicWrapper(app, ['transformTrack'], () => import(/* webpackChunkName: "transformTrack" */ '../routes/potentialCustomer/transformTrack/connectedHome')),
      isPrimary: true,
    },
    // 客户中心-潜在客户-转化追踪-管理层
    '/customerPool/potentialCustomer/transformTrackManager': {
      component: dynamicWrapper(app, ['transformTrack'], () => import(/* webpackChunkName: "transformTrackManager" */ '../routes/potentialCustomer/transformTrackManager/connectedHome')),
      isPrimary: true,
    },
    // 客户中心-潜客客户-潜客抢单
    '/customerPool/potentialGrab': {
      component: dynamicWrapper(app, ['prospectiveCustomer', 'potentialGrab'], () => import(/* webpackChunkName: "potentialGrab" */ '../routes/potentialGrab/Home')),
      isPrimary: true,
    },
    // 留言反馈-客户留言-直接进入
    '/feedback/customerMessage': {
      component: dynamicWrapper(app, ['customerMessage'], () => import(/* webpackChunkName: "customerMessage" */ '../routes/customerMessage/Home')),
    },
    // 运维管理-任务维护-新建维护申请
    '/sysOperate/createTaskMaintain': {
      component: dynamicWrapper(app, ['taskMaintain'], () => import(/* webpackChunkName: "MaintainNew" */ '../routes/taskMaintain/MaintainNew')),
    },
    // 运维管理-移动启动广告
    '/sysOperate/mobileStartAD': {
      component: dynamicWrapper(app, ['mobileStartAD'], () => import(/* webpackChunkName: "MobileStartAD" */ '../routes/mobileStartAD/Home')),
    },
    // 运维管理-任务维护-详情首页
    '/sysOperate/taskMaintainHome': {
      component: dynamicWrapper(app, ['taskMaintain'], () => import(/* webpackChunkName: "MaintainHome" */ '../routes/taskMaintain/MaintainHome')),
    },
    // 运维管理-任务维护-驳回修改页面
    '/sysOperate/taskMaintainReject': {
      component: dynamicWrapper(app, ['taskMaintain'], () => import(/* webpackChunkName: "MaintainReject" */ '../routes/taskMaintain/MaintainReject')),
    },
    // 运维管理-产品销售经办人管理
    '/sysOperate/productOperatorManage': {
      component: dynamicWrapper(app, ['productOperatorManage'], () => import(/* webpackChunkName: "productOperatorManage" */ '../routes/productOperatorManage/Home')),
    },
    // 直接进入
    '/productCenter/productPool': {
      component: dynamicWrapper(app, ['productPool'], () => import(/* webpackChunkName: "productPool" */ '../routes/productPool/connectedHome')),
      isPrimary: true,
    },
    // 消息提醒页面进入，需要 appId 以及 flowId
    '/productCenter/productPool/notifies': {
      component: dynamicWrapper(app, ['productPool'], () => import(/* webpackChunkName: "productPool_notifies" */ '../routes/productPool/Notifies')),
      isPrimary: true,
    },
    // 直接进入
    '/taskCenter/productOperation': {
      component: dynamicWrapper(app, ['productOperation'], () => import(/* webpackChunkName: "productOperation" */ '../routes/productOperation/connectedHome')),
      isPrimary: true,
    },
    // 新建投顾产品运营
    '/taskCenter/productPushFlow': {
      component: dynamicWrapper(app, ['productPushFlow'], () => import(/* webpackChunkName: "productPushFlow" */ '../routes/productPushFlow/Home')),
    },
    // 新建线上签约
    '/businessApplyment/createOnlineContract': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "createOnlineContract" */ '../routes/createOnlineContract/Home')),
    },
    // 共享财富管理
    '/businessApplyment/bookMeeting': {
      component: dynamicWrapper(app, ['meetingRoom'], () => import(/* webpackChunkName: "meeting" */ '../routes/meeting/Home')),
    },
    // 共享财富管理 - 重点关注
    '/businessApplyment/bookMeeting/focus': {
      component: dynamicWrapper(app, ['meetingRoom'], () => import(/* webpackChunkName: "meetingFocus" */ '../routes/meeting/meetingFocus/Home')),
    },
    // 共享财富管理 - 会议室预定详情
    '/businessApplyment/bookMeeting/meetingDetail': {
      component: dynamicWrapper(app, ['meetingRoom'], () => import(/* webpackChunkName: "meetingDetail" */ '../routes/meeting/meetingDetail/Home')),
    },
    // 共享财富管理 - 会议预定列表
    '/businessApplyment/bookMeeting/meetingList': {
      component: dynamicWrapper(app, ['meetingRoom'], () => import(/* webpackChunkName: "MeetingList" */ '../routes/meeting/meetingList/Home')),
    },
    // 共享财富管理 - 会议室列表
    '/businessApplyment/bookMeeting/meetingRoomList': {
      component: dynamicWrapper(app, ['meetingRoom'], () => import(/* webpackChunkName: "MeetingRoom" */ '../routes/meeting/meetingRoom/Home')),
    },
    // 共享财富管理 - 预定会议室
    '/businessApplyment/bookMeeting/meetingBook': {
      component: dynamicWrapper(app, ['meetingRoom'], () => import(/* webpackChunkName: "MeetingBook" */ '../routes/meeting/meetingBook/Home')),
    },
    // 公务号通话管理明细查询
    '/taskCenter/recordsManage': {
      component: dynamicWrapper(app, ['recordsManage'], () => import(/* webpackChunkName: "recordsManage" */ '../routes/recordsManage/Home')),
    },
    // 公务号通话管理视图
    '/taskCenter/recordsManageView': {
      component: dynamicWrapper(app, ['recordsManageView'], () => import(/* webpackChunkName: "recordsManageView" */ '../routes/recordsManageView/Home')),
    },
    // smart 任务审批
    '/taskCenter/taskApproval': {
      component: dynamicWrapper(app, ['taskApproval'], () => import(/* webpackChunkName: "taskApproval" */ '../routes/taskApproval/Home')),
      isPrimary: true,
    },
    // smart 任务审批-详情
    '/taskCenter/taskApproval/detail': {
      component: dynamicWrapper(app, ['taskApproval'], () => import(/* webpackChunkName: "taskApprovalDetail" */ '../routes/taskApproval/detail/Home')),
      isPrimary: true,
    },
    // smart 任务审批-驳回后修改
    '/taskCenter/taskApproval/edit': {
      component: dynamicWrapper(app, ['taskApproval'], () => import(/* webpackChunkName: "taskApprovalEdit" */ '../routes/taskApproval/edit/Home')),
      isPrimary: true,
    },
    // 投顾直接间 - 我的直播间
    '/live': {
      component: dynamicWrapper(app, ['live'], () => import(/* webpackChunkName: "live" */ '../routes/live/Home')),
      isPrimary: true,
    },
    // 投顾直接间 - 我的直播间
    '/live/create': {
      component: dynamicWrapper(app, ['live'], () => import(/* webpackChunkName: "createLive" */ '../routes/live/create/Home')),
      isPrimary: true,
    },
    // 当前直播房间，/currentLive?liveId=XXXX
    '/currentLive': {
      component: dynamicWrapper(app, ['live'], () => import(/* webpackChunkName: "currentLive" */ '../routes/live/currentLive/Home')),
    },
    // 从客户360详情-账户信息-期货账户-账户分析进入
    '/futuresAnalyze': {
      component: dynamicWrapper(
        app,
        [
          'customer360Detail/futuresAnalyze',
        ],
        () => import(/* webpackChunkName: "customerDetail_futuresAnalyze" */ '../routes/futuresAnalyze/Home'),
      ),
      isPrimary: true,
    },
    // 消息中心管理-手工消息推送-消息列表页
    '/messageManageCenter/manualMessage/list': {
      component: dynamicWrapper(
        app,
        ['manualMessage', 'orderManage'],
        () => import(/* webpackChunkName: "currentLive" */ '../routes/messageManageCenter/manualMessageList/Home'),
      ),
    },
    // 消息中心管理-手工消息推送-新建消息
    '/messageManageCenter/manualMessage/edit': {
      component: dynamicWrapper(
        app,
        [
          'manualMessage',
        ],
        () => import(/* webpackChunkName: "manualMessage_edit" */ '../routes/messageManageCenter/manualMessageEdit/Home'),
      )
    },
    // 总分零距离管理-列表
    '/zeroDistance/list': {
      component: dynamicWrapper(
        app,
        [
          'zeroDistance',
        ],
        () => import(/* webpackChunkName: "zeroDistance_list" */ '../routes/zeroDistance/list/Home'),
      )
    },
    // 总分零距离管理-详情
    '/zeroDistance/detail': {
      component: dynamicWrapper(
        app,
        [
          'zeroDistance',
        ],
        () => import(/* webpackChunkName: "zeroDistance_detail" */ '../routes/zeroDistance/detail/Home'),
      )
    },
    // 总分零距离管理-批注添加列表
    '/zeroDistance/ApproveAddList': {
      component: dynamicWrapper(
        app,
        [
          'zeroDistance',
        ],
        () => import(/* webpackChunkName: "zeroDistance_ApproveAddList" */ '../routes/zeroDistance/approveAddList/Home'),
      )
    },
    // 总分零距离管理-添加编辑页面
    '/zeroDistance/bulletinEdit': {
      component: dynamicWrapper(
        app,
        [
          'zeroDistance',
        ],
        () => import(/* webpackChunkName: "zeroDistance_bulletinEdit" */ '../routes/zeroDistance/bulletinEdit/Home'),
      )
    },
    // 总分零距离管理-审批页面
    '/zeroDistance/bulletinApproval': {
      component: dynamicWrapper(
        app,
        [
          'zeroDistance',
        ],
        () => import(/* webpackChunkName: "bulletinApproval" */ '../routes/zeroDistance/bulletinApproval/Home'),
      ),
      isPrimary: true,
    },
    // 总分零距离管理-驳回修改页面
    '/zeroDistance/bulletinApproval/edit': {
      component: dynamicWrapper(
        app,
        [
          'zeroDistance',
        ],
        () => import(/* webpackChunkName: "bulletinApprovalEdit" */ '../routes/zeroDistance/bulletinApprovalEdit/Home'),
      ),
      isPrimary: true,
    },
    // 总分零距离管理-批注审批详情
    '/zeroDistance/notationApproval/detail': {
      component: dynamicWrapper(
        app,
        ['zeroDistance'],
        () => import(/* webpackChunkName: "notationApproval_detail" */ '../routes/zeroDistance/notationApproval/Detail'),
      )
    },
    // 总分零距离管理-批注驳回
    '/zeroDistance/notationApproval/edit': {
      component: dynamicWrapper(
        app,
        ['zeroDistance'],
        () => import(/* webpackChunkName: "notationApproval_edit" */ '../routes/zeroDistance/notationApproval/Edit'),
      )
    },
    // 乐有圈-流程消息提示页面
    '/moments/workflowDetail': {
      component: dynamicWrapper(
        app, [],
        () => import(/* webpackChunkName: "workflowDetail_Home" */ '../routes/moments/workflowDetail/Home'),
      )
    },
    // 短视频-流程消息提示页面
    '/feedCenter/workflowDetail': {
      component: dynamicWrapper(
        app, [],
        () => import(/* webpackChunkName: "workflowDetail_feedCenter" */ '../routes/feedCenter/workflowDetail/Home'),
      )
    },
    // 联合服务渠道潜客归属关系配置（从原来的平台参数配置改到运维管理下）
    '/platformParameterSetting/potentialDistribution': {
      component: dynamicWrapper(app, ['potentialDistribution'], () => import(/* webpackChunkName: "potentialDistribution" */ '../routes/potentialDistribution')),
    },
    // 联合服务渠道潜客列表页
    '/channelPotential/list': {
      component: dynamicWrapper(
        app, ['channelPotential'],
        () => import(/* webpackChunkName: "channelPotential_List" */ '../routes/channelPotential/list/Home'),
      )
    },
    // 联合服务渠道潜客编辑新增页
    '/channelPotential/bulletinEdit': {
      component: dynamicWrapper(
        app, ['channelPotential'],
        () => import(/* webpackChunkName: "channelPotential_List" */ '../routes/channelPotential/bulletinEdit/Home'),
      )
    },
    // 特殊佣金驳回修改页面
    '/specialCommissionChange': {
      component: dynamicWrapper(app, ['commissionChange'], () => import(/* webpackChunkName: "specialCommissionChange" */ '../routes/commissionChange/SpecialCommissionChange')),
    },
    // 单佣金调整-新建页面
    '/businessApplyment/commissionNew/singleCommissionNew': {
      component: dynamicWrapper(app, ['singleCommission'], () => import(/* webpackChunkName: "singleCommissionNew" */ '../routes/SingleCommissionView/SingleNew')),
    },
    // 单佣金调整-驳回修改页面
    '/businessApplyment/commissionNew/singleCommissionReject': {
      component: dynamicWrapper(app, ['singleCommission'], () => import(/* webpackChunkName: "singleCommissionReject" */ '../routes/SingleCommissionView/SingleReject')),
    },
    // 特殊佣金调整-新建页面
    '/businessApplyment/commissionNew/specialCommissionNew': {
      component: dynamicWrapper(app, ['specialCommission'], () => import(/* webpackChunkName: "specialCommissionNew" */ '../routes/SpecialCommissionView/SpecialNew')),
    },
    // 特殊佣金调整-驳回修改页面
    '/businessApplyment/commissionNew/specialCommissionReject': {
      component: dynamicWrapper(app, ['specialCommission'], () => import(/* webpackChunkName: "specialCommissionReject" */ '../routes/SpecialCommissionView/SpecialReject')),
    },
    // 非标客户特殊佣金调整-新建页面
    '/businessApplyment/commissionNew/unStandardCommissionNew': {
      component: dynamicWrapper(app, ['unStandardCommission'], () => import(/* webpackChunkName: "unStandardCommissionNew" */ '../routes/UnStandardCommissionView/UnStandardNew')),
    },
    // 非标客户特殊佣金调整-驳回修改页面
    '/businessApplyment/commissionNew/unStandardCommissionReject': {
      component: dynamicWrapper(app, ['unStandardCommission'], () => import(/* webpackChunkName: "unStandardCommissionReject" */ '../routes/UnStandardCommissionView/UnStandardReject')),
    },
    // 批量佣金调整-新建页面
    '/businessApplyment/commissionNew/batchCommissionNew': {
      component: dynamicWrapper(app, ['batchCommission'], () => import(/* webpackChunkName: "batchCommissionNew" */ '../routes/BatchCommissionView/BatchNew')),
    },
    // 【自动外呼调佣申请】-新建页面
    '/businessApplyment/commissionNew/autoCallCommissionNew': {
      component: dynamicWrapper(app, ['autoCallCommission'], () => import(/* webpackChunkName: "autoCallCommissionNew" */ '../routes/AutoCallCommissionView/AutoCallNew')),
    },
    // 佣金配置管理
    '/businessApplyment/commissionConfig': {
      component: dynamicWrapper(app, ['commissionConfig'], () => import(/* webpackChunkName: "commissionConfig" */ '../routes/CommissionConfig')),
    },
    // 客户分配--分公司集中分配
    '/customerPool/customerPartition/custDistribute': {
      component: dynamicWrapper(app, ['custDistribute', 'branchCentralAllot'], () => import(/* webpackChunkName: "custDistribute" */ '../routes/custDistribute/Home')),
      isPrimary: true,
    },
    // 客户分配--分公司集中分配-特殊需求分配-新建
    '/customerPool/customerPartition/custDistribute/specialCreate': {
      component: dynamicWrapper(app, ['custDistribute', 'branchCentralAllot'], () => import(/* webpackChunkName: "custDistribute_specialCreate" */ '../routes/custDistributeSpecialCreate/Home')),
      isPrimary: true,
    },
    // 客户分配--分公司集中分配-统一集中分配-新建
    '/customerPool/customerPartition/custDistribute/centralCreate': {
      component: dynamicWrapper(app, ['custDistribute', 'branchCentralAllot'], () => import(/* webpackChunkName: "custDistribute_specialCreate" */ '../routes/custDistributeCentralCreate/Home')),
      isPrimary: true,
    },
    // 客户分配--参与客户自动流转申请
    '/customerPool/customerPartition/customerAutoCirculation': {
      component: dynamicWrapper(app, ['customerAutoCirculation'], () => import(/* webpackChunkName: "customerAutoCirculation" */ '../routes/CustomerAutoCirculation')),
      isPrimary: true,
    },
    // 从 fsp 消息提醒对应类型进入，参与客户自动流转申请消息提醒
    '/customerPool/customerPartition/customerAutoCirculation/notifies': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "customerAutoCirculation_notifies" */ '../routes/CustomerAutoCirculation/Notifies')),
      isPrimary: true,
    },
    // 客户分配--投顾服务半径设置
    '/customerPool/customerPartition/serviceRadiusSetting': {
      component: dynamicWrapper(app, ['serviceRadiusSetting'], () => import(/* webpackChunkName: "serviceRadiusSetting" */ '../routes/ServiceRadiusSetting')),
      isPrimary: true,
    },
    // 客户分配--取消客户选投顾标识申请--办结消息提醒跳转的详情页
    '/customerPool/customerPartition/cancelLabelApply/detail': {
      component: dynamicWrapper(app, ['cancelLabelApply'], () => import(/* webpackChunkName: "cancelLabelApply_detail" */ '../routes/CancelLabelApply/Detail')),
      isPrimary: true,
    },
    // 客户分配--取消客户选投顾标识申请--多客户办结消息提醒跳转的详情页
    '/customerPool/customerPartition/cancelLabelApply/detailMuti': {
      component: dynamicWrapper(app, ['cancelLabelApplyMulti'], () => import(/* webpackChunkName: "cancelLabelApply_detail_muti" */'../routes/CancelLabelApply/DetailMulti')),
      isPrimary: true,
    },
    // 客户分配--取消客户选投顾标识申请
    '/customerPool/customerPartition/cancelLabelApply': {
      component: dynamicWrapper(app, ['cancelLabelApply','cancelLabelApplyMulti'], () => import(/* webpackChunkName: "cancelLabelApply" */'../routes/CancelLabelApply')),
      isPrimary: true,
    },
    // 客户分配--取消客户选投顾标识申请-新建
    '/customerPool/customerPartition/cancelLabelApply/create': {
      component: dynamicWrapper(app, ['cancelLabelApply'], () => import(/* webpackChunkName: "cancelLabelApply_create" */ '../routes/CancelLabelApply/Create')),
      isPrimary: true,
    },
    // 客户分配--取消客户选投顾标识申请-新建-多客户
    '/customerPool/customerPartition/cancelLabelApply/createMulti': {
      component: dynamicWrapper(app, ['cancelLabelApplyMulti'], () => import(/* webpackChunkName: "cancelLabelApply_create_multi" */'../routes/CancelLabelApply/CreateMulti')),
      isPrimary: true,
    },
    // 客户分配--取消客户选投顾标识申请-修改
    '/customerPool/customerPartition/cancelLabelApply/revise': {
      component: dynamicWrapper(app, ['cancelLabelApply'], () => import(/* webpackChunkName: "cancelLabelApply_revise" */ '../routes/CancelLabelApply/Revise')),
      isPrimary: true,
    },
    // 客户分配--取消客户选投顾标识申请-修改
    '/customerPool/customerPartition/cancelLabelApply/reviseMulti': {
      component: dynamicWrapper(app, ['cancelLabelApplyMulti'], () => import(/* webpackChunkName: "cancelLabelApply_revise_multi" */'../routes/CancelLabelApply/ReviseMulti')),
      isPrimary: true,
    },
    // 分公司集中分配-分公司驳回修改页面(特殊需求分配)
    '/customerPool/branchSpecialAllotChange': {
      component: dynamicWrapper(app, ['branchSpecialAllotChange'], () => import(/* webpackChunkName: "branchSpecialAllotChange" */ '../routes/BranchAllotChange/BranchSpecialAllotChange')),
      isPrimary: true,
    },
    // 分公司集中分配-营业部驳回修改页面(特殊需求分配)
    '/customerPool/departmentSpecialAllotChange': {
      component: dynamicWrapper(app, ['branchSpecialAllotChange'], () => import(/* webpackChunkName: "departmentSpecialAllotChange" */ '../routes/BranchAllotChange/BranchSpecialDepartChange')),
      isPrimary: true,
    },
    // 分公司集中分配驳回回修改页面(统一集中分配)
    '/customerPool/branchCentralAllotChange': {
      component: dynamicWrapper(app, ['branchCentralAllotChange'], () => import(/* webpackChunkName: "branchCentralAllotChange" */ '../routes/BranchAllotChange/BranchCentralAllotChange')),
      isPrimary: true,
    },
    // 从 fsp 消息提醒对应类型进入,分公司集中分配消息提醒页面
    '/customerPool/customerPartition/custDistribute/notifies': {
      component: dynamicWrapper(app, ['custDistribute'], () => import(/* webpackChunkName: "custDistribute_notifies" */ '../routes/custDistribute/Notifies')),
      isPrimary: true,
    },
    // 从 fsp 消息提醒对应类型进入,佣金调整消息提醒页面
    '/businessApplyment/commissionNew/notifies': {
      component: dynamicWrapper(app, ['commissionNotifies'], () => import(/* webpackChunkName: "commission_notifies" */ '../routes/commissionNotifies/Home')),
      isPrimary: true,
    },
    // 合格投资者认定列表
    '/qualifyInvestor/home': {
      component: dynamicWrapper(app, ['qualifyInvestor'], () => import(/* webpackChunkName: "qualifyInvestorHome" */ '../routes/qualifyInvestor/connectedHome')),
      isPrimary: true,
    },
    // 合格投资者认定审批
    '/qualifyInvestor/approve': {
      component: dynamicWrapper(app, ['qualifyInvestor'], () => import(/* webpackChunkName: "qualifyInvestorApprove" */ '../routes/qualifyInvestor/Approve')),
      isPrimary: true,
    },
    // 合格投资者认定详情页
    '/qualifyInvestor/detail': {
      component: dynamicWrapper(app, ['qualifyInvestor'], () => import(/* webpackChunkName: "qualifyInvestorDetail" */'../routes/qualifyInvestor/Detail')),
      isPrimary: true,
    },
    // 热门活动-星投营销活动
    '/sysOperate/hotActivitiesConfig/investmentMarket': {
      component: dynamicWrapper(app, ['investmentMarket'], () => import(/* webpackChunkName: "investmentMarket" */'../routes/hotActivitiesConfig/investmentMarket/Home')),
      isPrimary: true,
    },
    // 热门活动-权益私募销售活动
    '/sysOperate/hotActivitiesConfig/privateEquitySale': {
      component: dynamicWrapper(app, ['privateEquitySale'], () => import(/* webpackChunkName: "privateEquitySale" */'../routes/hotActivitiesConfig/privateEquitySale/Home')),
      isPrimary: true,
    },
    // 问候海报配置
    '/customizePoster': {
      component: dynamicWrapper(app, ['customizePoster'], () => import(/* webpackChunkName: "customizePoster" */'../routes/customizePoster/Home')),
      isPrimary: true,
    },
    // 编辑节日海报列表
    '/customizePoster/editFestivalPosterList': {
      component: dynamicWrapper(app, ['customizePoster'], () => import(/* webpackChunkName: "EditFestivalPosterList" */'../routes/customizePoster/EditFestivalPosterList')),
      isPrimary: true,
    },
    // 行知鉴道
    '/practiceLearn': {
      component: dynamicWrapper(app, [], () => import(/* webpackChunkName: "practiceLearn" */'../routes/practiceLearn/Home')),
      isPrimary: true,
    },
    // 资产配置-新增
    '/assetConfig/add': {
      component: dynamicWrapper(app, ['assetConfig'], () => import(/* webpackChunkName: "assetConfigAdd" */'../routes/assetConfigAdd/Home')),
      isPrimary: true,
    },
    // 资产配置列表
    '/assetConfig/list': {
      component: dynamicWrapper(app, ['assetConfig'], () => import(/* webpackChunkName: "assetConfig" */'../routes/AssetConfigList/Home')),
      isPrimary: true,
    },
    // 资产配置审批页面
    '/assetConfig/approval': {
      component: dynamicWrapper(app, ['assetConfig'], () => import(/* webpackChunkName: "assetConfigApproval" */'../routes/AssetConfigApproval/Home')),
      isPrimary: true,
    },
    // 期权佣金调整(新)列表
    '/webfsp/businessApplyment/optionCommission/list': {
      component: dynamicWrapper(app, ['optionCommission'], () => import(/* webpackChunkName: "optionCommission_list" */'../routes/OptionCommission/Home')),
      isPrimary: true,
    },
    // 期权佣金调整(新)-新建
    '/webfsp/businessApplyment/optionCommission/createApply': {
      component: dynamicWrapper(app, ['optionCommission'], () => import(/* webpackChunkName: "optionCommission_createApply" */'../routes/OptionCommission/CreateApply')),
      isPrimary: true,
    },
    // 期权佣金调整(新)-驳回修改
    '/webfsp/businessApplyment/optionCommission/rejectApply': {
      component: dynamicWrapper(app, ['optionCommission'], () => import(/* webpackChunkName: "optionCommission_rejectApply" */'../routes/OptionCommission/RejectApply')),
      isPrimary: true,
    },
    // 留资手机号线索派发页面
    '/webfsp/reservedPhone': {
      component: dynamicWrapper(app, ['reservedPhone'], () => import(/* webpackChunkName: "reservedPhone" */'../routes/reservedPhone/Home')),
      isPrimary: true,
    },
    // 资产配置-新增
    '/webfsp/newAccountAnalysis/add': {
      component: dynamicWrapper(app, ['newAssetConfig'], () => import(/* webpackChunkName: "assetConfigAdd" */'../routes/newAccountAnalysis/Home')),
      isPrimary: true,
    },
    // 佣金授权申请-新建页面
    '/webfsp/businessApplyment/commissionNew/commissionAuthorizationNew': {
      component: dynamicWrapper(app, ['commissionAuthorization'], () => import('../routes/commissionAuthorization/CreateApply' /* webpackChunkName: "commissionAuthorizationNew" */)),
    },
    // 佣金授权申请-驳回修改页面
    '/webfsp/businessApplyment/commissionNew/commissionAuthorizationReject': {
      component: dynamicWrapper(app, ['commissionAuthorization'], () => import('../routes/commissionAuthorization/RejectApply' /* webpackChunkName: "commissionAuthorizationReject" */)),
    },
    // 佣金授权申请-消息提醒跳转客户列表页
    '/businessApplyment/commissionNew/commissionAuthorizationCustomerList': {
      component: dynamicWrapper(app, ['commissionAuthorization'], () => import('../routes/commissionAuthorization/CustomerList' /* webpackChunkName: "commissionAuthorizationCustomerList" */)),
    },
    // 佣金调整-详情页面
    '/businessApplyment/commissionNew/commissionDetail': {
      component: dynamicWrapper(app, ['singleCommission', 'specialCommission', 'unStandardCommission'], () => import(/* webpackChunkName: "commissionDetail" */ '../routes/CommissionDetail')),
    },
  };
  routerDataCache = routerConfig;
  return routerConfig;
};
// 需要进行重定向的路由配置
export const redirectRoutes = [
  {
    from: '/invest',
    to: '/statisticalQuery/report',
    name: '管理者视图',
  },
  {
    from: '/report',
    to: '/statisticalQuery/report',
    name: '管理者视图',
  },
  {
    from: '/custAllot',
    to: '/customerPool/customerPartition/custAllot',
    name: '分公司客户分配',
  },
  {
    from: '/departmentCustAllot',
    to: '/customerPool/customerPartition/departmentCustAllot',
    name: '营业部客户分配',
  },
  {
    from: '/telephoneNumberManageEdit',
    to: '/sysOperate/telephoneNumberManageEdit',
    name: '公务手机管理',
  },
  {
    from: '/latestView/viewpointList',
    to: '/strategyCenter/latestView/viewpointList',
    name: '资讯列表',
  },
  {
    from: '/latestView/viewpointDetail',
    to: '/strategyCenter/latestView/viewpointDetail',
    name: '资讯详情',
  },
  {
    from: '/latestView/majorAssetsList',
    to: '/strategyCenter/latestView/majorAssetsList',
    name: '大类资产配置分析列表',
  },
  {
    from: '/latestView/industryThemeList',
    to: '/strategyCenter/latestView/industryThemeList',
    name: '行业主题调整信息列表',
  },
  {
    from: '/stock/detail',
    to: '/strategyCenter/stock/detail',
    name: '个股详情',
  },
  {
    from: '/choicenessCombination/combinationDetail',
    to: '/strategyCenter/choicenessCombination/combinationDetail',
    name: '组合详情',
  },
  {
    from: '/choicenessCombination/reportDetail',
    to: '/strategyCenter/choicenessCombination/reportDetail',
    name: '历史报告',
  },
  {
    from: '/statisticalQuery/taskAnalysisReport',
    to: '/statisticalQuery/taskTable/taskAnalysisReport',
    name: '任务分析图表',
  },
  {
    from: '/statisticalQuery/taskAnalysisReport',
    to: '/statisticalQuery/taskTable/taskAnalysisReport',
    name: '任务分析图表',
  },
];

// 特殊的子路由需要使用的配置
export const childrenRoutes = [
  {
    from: '/sysOperate/platformParameterSetting',
    name: '平台参数设置',
  },
  {
    from: '/sysOperate/telephoneNumberManage',
    name: '公务手机管理',
  },
  {
    from: '/fsp/customerCenter/contractSelectOperate',
    name: '新增投顾签约向导',
  },
  {
    from: '/fsp/customerCenter/360OperateType',
    name: '投顾签约变更向导',
  },
];


export const fspWebRouteQuery = () => {
  const normalRouteKeys = Object.keys(routerDataCache);
  const redirectRouteKey = redirectRoutes.map(item => item.from);
  return {
    keys: [...normalRouteKeys, ...redirectRouteKey],
    regx: [/\/jump(\/.*)*/, /\/fspjump(\/.*)*/, /\/webfsp(\/.*)*/],
  };
}

window.fspWebRouteQuery = fspWebRouteQuery;
