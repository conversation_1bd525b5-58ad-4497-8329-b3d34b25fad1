/**
 * 该文件为能力中心输出，供HOST调用
 */
import React from 'react';
import ReactDOM from 'react-dom';
import { exposeApplication } from '@bigbox/bridge-react';
import { setHost } from '@/utils/host';
import getRoot from '../getRoot';
import appRouter from './appRouter';
import { singleAppName } from './config';

const { rootComponent, dvaInstance: dvaApp } = getRoot(appRouter);

const app = exposeApplication({
  name: singleAppName,
  React,
  ReactDOM,
  rootComponent,
  extraExpose: {
    setHost,
    dvaApp,
  },
});

export default app;
