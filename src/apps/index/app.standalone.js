import React from 'react';
import ReactDOM from 'react-dom';
import qs from 'query-string';
import _ from 'lodash';
import { sensors } from '@lego/bigbox-utils';
import { ALPHABET_ARRAY } from '@/config/other';
import { setHost, getHost } from '@/utils/host';
import { post } from '../../api';
import getRoot from '../getRoot';
import appRouter from './appRouter';
// 独立运行时mock平台数据

import '@/css/antd.less';

// 投资偏好加上ABCDE的处理
const preTreatDict = (dict) => {
  if (dict) {
    const newInvestVarietyDictionary = _.map(dict.investVarietyDictionary, (item, index) => ({
      key: item.key,
      value: `${ALPHABET_ARRAY[index]}${item.value}`
    }));
    return {
      ...dict,
      investVarietyDictionary: newInvestVarietyDictionary,
    };
  }

  return {};
};

const { rootComponent: Root } = getRoot(appRouter);

function getMockEmpId() {
  return qs.parse(window.location.search).empId || '002332';
}
// 设置神策
const { env } = sensors;
env.setDistinctId(getMockEmpId());

Promise.all([
  post('/groovynoauth/emp/queryEmpResInfo', { empId: getMockEmpId() }),
  post('/groovynoauth/fsp/dictionary'),
]).then((data) => {
  const [
    empInfo,
    dict,
    ecifDict
  ] = data;
  // Mock 平台的数据
  setHost({
    ...getHost(),
    getState() {
      return {
        global: {
          empInfo: empInfo && empInfo.resultData,
          dict: dict && dict.resultData,
          ecifDict: preTreatDict(ecifDict && ecifDict.resultData),
          breadcrumbRoutes: [],
        },
      };
    },
  });
  ReactDOM.render(<Root />, document.querySelector('#singlePageApp'));
});
