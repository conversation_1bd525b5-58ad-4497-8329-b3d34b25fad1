/**
 * @file app.js
 * <AUTHOR>
 */

import dva from 'dva';
import React from 'react';
import createHistory from 'history/createHashHistory';
import createLoading from 'dva-loading';
import _ from 'lodash';
import {
  Button, message, Modal, notification
} from 'antd';
import reduxCreateLogger from 'redux-logger';
import createActivityIndicator from '@/middlewares/createActivityIndicator';
import { sensors, withErrorBoundary, createLogger } from '@lego/bigbox-utils';
import { persistStore, autoRehydrate } from 'redux-persist';
import { dva as dvaHelper } from '@/helper';
import { ERROR_SEPARATOR } from '@/config/request';
import { persist as persistConfig } from '@/config';
import logConfig from '@/config/log';
import pollingWorker, { checkVersionNotice } from '../utils/pollingRequestWorker';
import { getHost } from '../utils/host';
import { appName } from '../../config';

// 神策埋点配置
const { logCommon, createSensorsLogger } = sensors;
sensors.mergeConfig(logConfig);

function navToUserLogin() {
  const redirectUrl = `${window.location.origin}/gateway/web/index.html?url=${encodeURIComponent(window.location.href)}`;
  window.location.href = redirectUrl;
}

const extraEnhancers = [];
if (persistConfig.active) {
  extraEnhancers.push(autoRehydrate());
}

// 错误处理
const onError = (e) => {
  const { message: msg, stack, duration = 3 } = e;
  // 如果存在分隔符，认为是业务错误
  // 否则根据情况判定为代码错误或者登录超时
  // 后端暂时没有登录超时概念
  // 都走门户的验证，门户返回的html，JSON parse报错即认为超时
  if (msg.indexOf(ERROR_SEPARATOR) > -1) {
    const [errorMessage, messageType, code, url] = msg.split(ERROR_SEPARATOR);
    if (messageType === '0' || message === '2') {
      // 错误类型是0 / 2，用message.error
      message.error(errorMessage);
    } else if (messageType === '1') {
      // 错误类型是1，用dialog
      Modal.error({
        title: '业务错误',
        content: errorMessage
      });
    }
    // 业务错误
    logCommon({
      type: 'bizError',
      payload: {
        name: '业务错误',
        value: errorMessage,
        url: url.lastIndexOf('?') === -1 ? url : url.slice(0, url.lastIndexOf('?')),
        code,
      },
    });
    e.preventDefault();
    return;
  }
  if (e.name === 'SyntaxError'
    && (msg.indexOf('<') > -1 || msg.indexOf('JSON') > -1)) {
    navToUserLogin();
    return;
  }
  if (stack && stack.indexOf('SyntaxError') > -1) {
    navToUserLogin();
    return;
  }
  message.error(msg, duration);
  e.preventDefault();
};

// 兼容bigtool-utils中不含createLogger方法版本
const onAction = createLogger ? [createLogger(appName)] : [reduxCreateLogger({
  predicate: (getState, action) => !_.find(logConfig.blacklist, (type) => action.type === type)
}), createSensorsLogger()];

// 1. Initialize
const getRoot = (Router) => {
  const dvaApp = dva({
    history: createHistory(),
    onAction,
    extraEnhancers,
    onError,
  });

  // 2. Plugins
  dvaApp.use(createLoading({ effects: true })); // dva-loading
  dvaApp.use(createActivityIndicator());
  // 3. Model
  dvaApp.model(require('../models/global').default); // eslint-disable-line
  dvaApp.model(require('../models/app').default); // eslint-disable-line
  dvaApp.model(require('../models/customerPool').default); // eslint-disable-line
  dvaApp.model(require('../models/taskList/performerView').default); // eslint-disable-line
  dvaApp.model(require('../models/telephoneNumberManage').default); // eslint-disable-line
  dvaApp.model(require('../models/investmentAdvice').default); // eslint-disable-line
  dvaApp.model(require('../models/customerLabel').default); // eslint-disable-line
  dvaApp.model(require('../models/morningBoradcast').default); // eslint-disable-line
  dvaApp.model(require('../models/newHome').default); // eslint-disable-line
  dvaApp.model(require('../models/feedback').default); // eslint-disable-line
  dvaApp.model(require('../models/homePageAnnouncement').default); // eslint-disable-line

  // 4. Router
  dvaApp.router(Router);

  // 5. Start
  const AppRoot = dvaApp.start();

  dvaHelper.initApp(dvaApp, createHistory());
  // start后_store才被初始化
  const store = dvaApp._store; // eslint-disable-line

  // 6. redux-persist
  if (persistConfig.active) {
    persistStore(store, persistConfig);
  }
  const customLog = ({ error, info }) => {
    // eslint-disable-next-line no-unused-expressions
    getHost().errorComponentLog && getHost().errorComponentLog({ module: `${appName}ReactError`, error, info });
  };
  return {
    rootComponent: withErrorBoundary(undefined, customLog)(AppRoot),
    dvaInstance: dvaApp,
  };
};

pollingWorker.onmessage = (e) => {
  switch (e.data.type) {
    case 'versionChange': {
      notification.warning({
        prefixCls: `${appName}-notification`,
        message: '新版本发布通知',
        className: 'notification-version',
        description: (
          <div>
            <div>当前页面内容已经发布了最新版本，请刷新当前页面</div>
            <Button
              className={`${appName}-btn ${appName}-btn-primary`}
              type="primary"
              style={{ float: 'right' }}
              onClick={() => window.location.reload()}
            >刷新
            </Button>
          </div>
        ),
        onClick: () => {
          console.log('Notification Clicked!');
        },
        duration: 0,
      });

      try {
        localStorage.setItem(checkVersionNotice(e.data?.version), e.data?.time);
      } catch (err) {
        console.error('checkVersionNotice error', err);
      }
      // eslint-disable-next-line no-unused-expressions
      window.xlog && window.xlog.reportEvent({
        name: '版本通知',
        message: `${appName}:版本通知`,
        labels: [],
        ext: e.data?.data,
      });

      break;
    }
    case 'relogin': {
      notification.warning({
        prefixCls: `${appName}-notification`,
        message: '登录态超时，请重新登录',
        className: 'notification-version',
        description: (
          <div>
            <div>当前页面登录态超时，请刷新当前页面或点击登录按钮，重新登录！</div>
            <Button
              className={`${appName}-btn ${appName}-btn-primary`}
              type="primary"
              style={{ float: 'right' }}
              onClick={() => navToUserLogin()}
            >登录
            </Button>
          </div>
        ),
        onClick: () => {
          console.log('Notification Clicked!');
        },
        duration: 0,
      });
      // eslint-disable-next-line no-unused-expressions
      window.xlog && window.xlog.reportEvent({
        name: '自动登录',
        message: `${appName}:自动登录`,
        labels: [],
        ext: '',
      });
      break;
    }
    default: {
      console.log('未处理消息', e);
    }
  }
};
const versionTime = localStorage.getItem(checkVersionNotice());
if (versionTime) {
  pollingWorker.postMessage({ type: 'versionTimeUpdate', data: versionTime });
}

export default getRoot;
