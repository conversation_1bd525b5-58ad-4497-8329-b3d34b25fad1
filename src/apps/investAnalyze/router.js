import { createElement } from 'react';
import dynamic from 'dva/dynamic';
import mapKeys from 'lodash/mapKeys';
import { singleAppName } from './config';

let routerDataCache;
const modelNotExisted = (app, model) => (
  // eslint-disable-next-line
  !app._models.some(({ namespace }) => {
    return namespace === model.substring(model.lastIndexOf('/') + 1);
  })
);
// wrapper of dynamic
const dynamicWrapper = (app, models, component) => {
  // () => require('module')
  // transformed by babel-plugin-dynamic-import-node-sync
  if (component?.toString()?.indexOf('.then(') < 0) {
    models.forEach((model) => {
      if (modelNotExisted(app, model)) {
        // eslint-disable-next-line
        app.model(require(`../../models/${model}`).default);
      }
    });
    return (props) => {
      if (!routerDataCache) {
        // eslint-disable-next-line
        routerDataCache = getRouterData(app);
      }
      return createElement(component().default, {
        ...props,
        routerData: routerDataCache,
      });
    };
  }
  // () => import('module')
  return dynamic({
    app,
    models: () =>
      // eslint-disable-next-line
      models.filter(model => modelNotExisted(app, model)).map(m => import(`../../models/${m}.js`)),
    // add routerData prop
    component: () => {
      if (!routerDataCache) {
        // eslint-disable-next-line
        routerDataCache = getRouterData(app);
      }
      // eslint-disable-next-line
      return component().then(raw => {
        const Component = raw.default || raw;
        return (props) => createElement(Component, {
          ...props,
          routerData: routerDataCache,
        });
      });
    },
  });
};

export const getRouterData = (app) => {
  const routerData = {
    // 当只有一个页面时，使用：'*': { component: dynamicWrapper(...) }
    // 当有多个页面时，正常写路由
    '*': {
      component: dynamicWrapper(
        app,
        ['app', 'customerDetail', 'customer360Detail/investAnalyze'],
        () => import('../../routes/investAnalyze/connectedHome' /* webpackChunkName: "investAnalyzeApp" */)
      ),
    },
  };
  // 独立入口存在路由，需要在每条路由上添加app的名称
  return mapKeys(
    routerData,
    (value, key) => (key !== '*' ? `/${singleAppName}${key}` : '*'),
  );
};
