import React from 'react';
import ReactDOM from 'react-dom';
import { setHost, getHost } from '@/utils/host';
import getRoot from '../getRoot';
import appRouter from './appRouter';
// 独立运行时mock平台数据

const { rootComponent: Root } = getRoot(appRouter);

// Mock 平台的数据
setHost({
  ...getHost(),
  getState() {
    return {
      global: {
        empInfo: {
          empInfo: {
            cellPhNum: '18951810511',
            city: 'DF1501',
            conAsstName: null,
            country: '111156',
            emailAddr: '<EMAIL>',
            empJb: null,
            empL0: null,
            empL1: null,
            empL2: '南京分公司',
            empL3: '南京长江路证券营业部',
            empName: '1-OXZ5',
            empNum: '002332',
            empQualification: null,
            empStatus: '在职',
            empTdbm: null,
            faxPhNum: '025-84798657',
            hireDt: '2005-09-19',
            jobTitle: '总经理',
            login: '002332',
            occDivnNum: 'ZZ001041051',
            occupation: '南京长江路证券营业部',
            overtimeCd: null,
            parOccDivnNum: 'ZZ001041093',
            parOccupation: '南京分公司',
            postnId: '1-OXZ8',
            rowId: '1-OXZ5',
            sex: '女',
            srvRoleCd: null,
            state: 'DF15',
            terminationDt: null,
            tgFlag: true,
            tgNum: 'S0570610120372',
            tgQyFlag: true,
            workPhNum: '+8602552821688',
            xEmpFrozenCd: null,
            zipcode: null,
          },
          empPostnList: [],
          empRespList: [],
        },
      },
    };
  },
});

ReactDOM.render(<Root />, document.querySelector('#singlePageApp'));
