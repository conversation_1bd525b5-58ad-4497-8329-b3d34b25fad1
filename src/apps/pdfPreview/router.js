import { createElement } from 'react';
import dynamic from 'dva/dynamic';

let routerDataCache;
const dynamicWrapper = (app, models, component) => (
  dynamic({
    app,
    models,
    component: () => {
      if (!routerDataCache) {
        // eslint-disable-next-line
        routerDataCache = getRouterData(app);
      }
      // eslint-disable-next-line
      return component().then(raw => {
        const Component = raw.default || raw;
        return (props) => createElement(Component, {
          ...props,
          routerData: routerDataCache,
        });
      });
    },
  })
);

export const getRouterData = (app) => {
  const routerData = {
    // 当只有一个页面时，使用：'*': { component: dynamicWrapper(...) }
    // 当有多个页面时，正常写路由
    '*': {
      component: dynamicWrapper(
        app,
        [],
        () => import('../../routes/pdfPreview/Home' /* webpackChunkName: "PdfPreviewApp" */)
      ),
    },
  };
  // 独立入口存在路由，需要在每条路由上添加app的名称
  return routerData;
};
