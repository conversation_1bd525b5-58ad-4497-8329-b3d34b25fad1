/**
 * @file layouts/AppMain.js
 * 独立入口外层框架
 * <AUTHOR>
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'dva';
import {
  Route,
  Switch,
  routerRedux,
} from 'dva/router';
import map from 'lodash/map';
import { ConfigProvider } from 'antd';
import ErrorBoundary from '@/layouts/ErrorBoundary';
import Loading from '@/layouts/Loading';

const mapStateToProps = (state) => ({
  loading: state.activity.global,
});

const mapDispatchToProps = {
  push: routerRedux.push,
  replace: routerRedux.replace,
};

@connect(mapStateToProps, mapDispatchToProps)
export default class AppMain extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    prefixCls: PropTypes.string,
    routerData: PropTypes.object.isRequired,
    loading: PropTypes.number.isRequired,
  };

  static defaultProps = {
    prefixCls: 'ant',
  };

  render() {
    const {
      prefixCls,
      location,
      routerData,
      loading,
    } = this.props;

    const routerKeys = Object.keys(routerData);
    const mapRouterData = map(routerKeys, (item) => (
      {
        ...routerData[item],
        key: item,
        path: item,
      }
    ));

    return (
      <ConfigProvider prefixCls={prefixCls}>
        <ErrorBoundary location={location}>
          <>
            <Switch>
              {
                map(mapRouterData, (item) => (
                  <Route
                    key={item.key}
                    path={item.path}
                    exact
                    render={(props) => <item.component {...props} />}
                  />
                ))
              }
            </Switch>
            <Loading id="api-loading" loading={loading} />
          </>
        </ErrorBoundary>
      </ConfigProvider>
    );
  }
}
