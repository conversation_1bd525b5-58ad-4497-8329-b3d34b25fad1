import { createElement } from 'react';
import dynamic from 'dva/dynamic';

let routerDataCache;
const dynamicWrapper = (app, models, component) => (
  dynamic({
    app,
    models,
    component: () => {
      if (!routerDataCache) {
        // eslint-disable-next-line
        routerDataCache = getRouterData(app);
      }
      // eslint-disable-next-line
      return component().then(raw => {
        const Component = raw.default || raw;
        return (props) => createElement(Component, {
          ...props,
          routerData: routerDataCache,
        });
      });
    },
  })
);

export const getRouterData = (app) => {
  const routerData = {
    // 特殊佣金调整-审批页面
    '/specialCommission': {
      component: dynamicWrapper(
        app, [], // 审批页面不需要走model了
        // 原有的方式Build的产物有点问题，TODO需要细查原因
        () => import('../../routes/Approval/SpecialCommission/Home' /* webpackChunkName: "approval_specialCommission" */)
      ),
    },
    // 单佣金调整-审批页面
    // 针对异地限价 - 单佣金调整审批流程做调整
    // 新流程的审批页面不在放到screenshot项目中，改放在fsp - web - new中
    '/singleCommissionNewApproval': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/SingleCommission/Home' /* webpackChunkName: "approval_singleCommissionNewApproval" */)
      ),
    },
    // 批量佣金调整-审批页面
    '/batchCommissionApproval': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/BatchCommission' /* webpackChunkName: "approval_batchCommission" */)
      ),
    },
    // 自动外呼调佣申请-审批页面
    '/autoCallCommissionApproval': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/AutoCallCommission' /* webpackChunkName: "approval_autoCallCommissionn" */)
      ),
    },
    // 分公司集中分配-分公司特殊需求分配-审批页面
    '/specialCustDistribute': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/SpecialCustDistribute/Home' /* webpackChunkName: "approval_specialCustDistribute" */)
      ),
    },
    // 分公司集中分配-营业部特殊需求分配-审批页面
    '/departSpecialCustDistribute': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/DepartSpecialCustDistribute/Home' /* webpackChunkName: "approval_departSpecialCustDistribute" */)
      ),
    },
    // 分公司集中分配-统一集中分配-审批页面
    '/focusCustDistribute': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/FocusCustDistribute/Home' /* webpackChunkName: "approval_focusCustDistribute" */)
      ),
    },
    // 客户分配--取消客户选投顾标识申请-审批页面
    '/cancelLabelApplyApproval': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/CancelLabelApply' /* webpackChunkName: "approval_cancelLabelApply" */)
      ),
    },
    // 客户分配--取消客户选投顾标识申请-审批页面-多客户
    '/cancelLabelApplyApprovalMulti': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/CancelLabelApplyMulti' /* webpackChunkName: "approval_cancelLabelApplyMulti" */)
      ),
    },
    // 非标客户特殊佣金调整-审批页面
    '/unStandardCommissionApproval': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/UnStandardCommission' /* webpackChunkName: "approval_unStandardCommissionApproval" */)
      ),
    },
    // 合作权重审批页面
    '/cooperationWeightApproval': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/CooperationWeight' /* webpackChunkName: "approval_cooperationWeightApproval" */)
      ),
    },
    // 期权佣金申请-审批页面
    '/optionCommissionApproval': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/OptionCommission' /* webpackChunkName: "approval_optionCommissionApproval" */)
      ),
    },
    // 部门协作收入申报审批页面
    '/departCooperationApproval': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/DepartCooperation' /* webpackChunkName: "approval_departCooperationApproval" */)
      ),
    },
    // 部门协作收入申报二期审批页面
    '/departCooperationApprovalV2': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/DepartCooperationV2' /* webpackChunkName: "approval_departCooperationApproval" */)
      ),
    },
    // 佣金授权申请-审批页面
    '/commissionAuthorizationApproval': {
      component: dynamicWrapper(
        app, [],
        () => import('../../routes/Approval/CommissionAuthorizationApproval' /* webpackChunkName: "approval_commissionAuthorizationApproval" */)
      ),
    },
  };
  // 独立入口存在路由，需要在每条路由上添加app的名称
  return routerData;
};
