/*
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-02-01 09:34:21
 * @LastEditors: yuan<PERSON>jie
 * @LastEditTime: 2021-02-02 15:09:30
 * @Description: file content
 */

import React from 'react';
import PropTypes from 'prop-types';
import {
  Switch,
  Route,
} from 'dva/router';
import ConnectedRouter from '@/components/common/ConnectedRouter';
import AppMain from './AppMain';
import { getRouterData } from './router';

export default function Routers({ history, app }) {
  const routerData = getRouterData(app);
  // 原有页面改造成独立的入口，为了支持withRouter，在外面包一层ConnectRouter
  return (
    <ConnectedRouter history={history}>
      <Switch>
        <Route
          path="/"
          render={(props) => <AppMain {...props} routerData={routerData} />}
        />
      </Switch>
    </ConnectedRouter>
  );
}

Routers.propTypes = {
  history: PropTypes.object.isRequired,
  app: PropTypes.object.isRequired,
};
