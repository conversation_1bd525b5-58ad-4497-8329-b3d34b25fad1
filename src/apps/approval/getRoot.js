/**
 * @file app.js
 * <AUTHOR>
 */

import dva from 'dva';
import createHistory from 'history/createHashHistory';
import createLoading from 'dva-loading';
import { message, Modal } from 'antd';
import qs from 'query-string';
import createActivityIndicator from '@/middlewares/createActivityIndicator';
import { sensors, withErrorBoundary, createLogger } from '@lego/bigbox-utils';
import { initApp } from '@/helper/dva';
import { ERROR_SEPARATOR } from '@/config/request';
import logConfig from '@/config/log';
import config from '../../../config';
import { getHost } from '../../utils/host';

// 设置神策
const { env, logCommon } = sensors;
env.setDistinctId(qs.parse(window.location.search).empId || 'unset');
sensors.mergeConfig(logConfig);

// 错误处理
const onError = (e) => {
  const { message: msg, stack, duration = 3 } = e;
  // 如果存在分隔符，认为是业务错误
  // 否则根据情况判定为代码错误或者登录超时
  // 后端暂时没有登录超时概念
  // 都走门户的验证，门户返回的html，JSON parse报错即认为超时
  if (msg.indexOf(ERROR_SEPARATOR) > -1) {
    const [errorMessage, messageType, code, url] = msg.split(ERROR_SEPARATOR);
    if (messageType === '0' || message === '2') {
      // 错误类型是0 / 2，用message.error
      message.error(errorMessage);
    } else if (messageType === '1') {
      // 错误类型是1，用dialog
      Modal.error({
        title: '业务错误',
        content: errorMessage
      });
    }
    // 业务错误
    logCommon({
      type: 'bizError',
      payload: {
        name: '业务错误',
        value: errorMessage,
        url: url.lastIndexOf('?') === -1 ? url : url.slice(0, url.lastIndexOf('?')),
        code,
      },
    });
    return;
  }
  if (e.name === 'SyntaxError'
    && (msg.indexOf('<') > -1 || msg.indexOf('JSON') > -1)) {
    return;
  }
  if (stack && stack.indexOf('SyntaxError') > -1) {
    return;
  }
  message.error(msg, duration);
};

const onAction = [createLogger('approval')];

// 1. Initialize
const getRoot = (Router) => {
  const dvaApp = dva({
    history: createHistory(),
    onAction,
    onError,
  });

  // 2. Plugins
  dvaApp.use(createLoading({ effects: true })); // dva-loading
  dvaApp.use(createActivityIndicator());

  // 4. Router
  dvaApp.router(Router);

  // 5. Start
  const AppRoot = dvaApp.start();

  initApp(dvaApp, createHistory());
  // start后_store才被初始化
  const store = dvaApp._store; // eslint-disable-line
  const customLog = ({ error, info }) => {
    // eslint-disable-next-line no-unused-expressions
    getHost().errorComponentLog && getHost().errorComponentLog({ module: `${config.appName}ReactError`, error, info });
  };
  return {
    rootComponent: withErrorBoundary(undefined, customLog)(AppRoot),
    dvaInstance: dvaApp,
  };
};

export default getRoot;
