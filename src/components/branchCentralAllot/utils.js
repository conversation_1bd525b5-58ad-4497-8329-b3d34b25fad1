/*
 * @Description: 公用方法提取
 * @Author: zousheng
 * @Date: 2021-07-21 10:39:20
 * @Last Modified by:
 * @Last Modified time:
 */
import _ from 'lodash';

export function viewPdf(fileInfo) {
  const { attachId } = fileInfo;
  const host = window.location.origin;
  const pdrviewerUrl = `${host}/fspa/fsp-host/static/public/pdf/web/viewer.html`;
  const params = `${host}/fspa/mcrm/api/storage/download?fileId=${attachId}`;
  const url = `${pdrviewerUrl}?&file=${encodeURIComponent(params)}`;
  window.open(
    url,
    '_blank'
  );
}

// 针对大文件通用接口返回的客户信息字段进行映射
export function fieldMapping(bigFileCustList) {
  if (_.isEmpty(bigFileCustList)) {
    return [];
  }

  return _.map(bigFileCustList, (item) => ({
    custName: item.customerName,
    custId: item.customerId,
    totalAsset: (_.isNil(item.totalAsset) || item.totalAsset === '') ? null : item.totalAsset,
    serveManagerName: item.oldServiceName,
    serveManagerType: item.oldServiceType,
    serveManagerId: item.oldServiceId,
    departmentName: item.oldDepartOrgName,
    branchName: item.oldBranchOrgName,
    newServeManagerName: item.newServiceName,
    newServeManagerId: item.newServiceId,
    newServeManagerType: item.newServiceType,
    newDepartmentName: item.newDepartOrgName,
    newBranchName: item.newBranchOrgName,
    reason: item.reason,
  }));
}
