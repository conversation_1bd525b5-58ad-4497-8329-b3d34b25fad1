/*
 * @Description: 分公司集中分配通用Table
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2021-07-18 10:39:20
 * @Last Modified by:
 * @Last Modified time:
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { Popconfirm } from 'antd';
import _ from 'lodash';

import Table, { ToolTipCell } from '@/components/common/table';
import Icon from '@/components/common/Icon';
import { number } from '@/helper';

import {
  KEY_CUSTNAME,
  TOTAL_ASSET,
  SERVE_MANAGER_NAME,
  NEW_SERVE_MANAGER_NAME,
  REASON,
  CUSTOMER_PLACEHOLDER_PROPS,
  SPACE_20,
  DETAIL_CUSTOMER_COLUMNS,
} from './config';

import styles from './table.less';

export default class BranchCentralCommonTable extends PureComponent {
  static propTypes = {
    // 是否需要操作列
    needOperation: PropTypes.bool,
    // list数据
    list: PropTypes.array,
    // pagination数据
    pagination: PropTypes.object,
    // 分页器onChange事件
    onChange: PropTypes.func,
    // 删除客户
    onDelete: PropTypes.func,
    // 调整原因是否需要设为固定列
    reasonFixed: PropTypes.bool,
  }

  static defaultProps = {
    needOperation: false,
    list: [],
    pagination: {},
    onChange: () => {},
    onDelete: () => {},
    reasonFixed: false,
  }

  // 生成客户表格标题列表
  @autobind
  getCustColumns() {
    const { needOperation, reasonFixed } = this.props;
    const columns = _.map(DETAIL_CUSTOMER_COLUMNS, (column) => {
      if (column.key === KEY_CUSTNAME) {
        return this.renderCustNameColumn(column);
      }
      if (column.key === TOTAL_ASSET) {
        return this.renderTotalAssetColumn(column);
      }
      if (column.key === SERVE_MANAGER_NAME) {
        return this.renderOldEmpColumn(column);
      }
      if (column.key === NEW_SERVE_MANAGER_NAME) {
        return this.renderNewEmpColumn(column);
      }
      if (reasonFixed && column.key === REASON) {
        return {
          ...column,
          fixed: 'right',
          align: 'center',
          render(text) {
            return (
              <ToolTipCell cellClass={styles.reason} tipContent={text} cellText={text} />
            );
          },
        };
      }
      return this.renderCommonColumn(column);
    });
    if (needOperation) {
      columns.push({
        dataIndex: 'operate',
        key: 'operate',
        title: '操作',
        render: (text, record) => this.renderPopconfirm(record),
        width: 28,
        fixed: 'right',
        align: 'center',
      });
    }
    return columns;
  }

  // 渲染客户姓名
  @autobind
  renderCustNameColumn(column) {
    return {
      ...column,
      render(text, record) {
        const showText = text ? `${text}(${record.custId})` : '';
        return (
          <ToolTipCell cellClass={styles.name} tipContent={showText} cellText={showText} />
        );
      },
    };
  }

  // 渲染总资产
  @autobind
  renderTotalAssetColumn(column) {
    return {
      ...column,
      render(text) {
        const showText = _.isNumber(text)
          ? number.thousandFormat(_.toNumber(text).toFixed(2))
          : '';
        return (
          <ToolTipCell tipContent={showText} cellText={showText} />
        );
      },
    };
  }

  // 渲染原服务经理列
  @autobind
  renderOldEmpColumn(column) {
    return {
      ...column,
      render(text, record) {
        const { serveManagerId } = record;
        const showText = text ? `${text}(${serveManagerId})` : '--';
        return (
          <ToolTipCell tipContent={showText} cellText={showText} />
        );
      },
    };
  }

  // 渲染新服务经理列
  @autobind
  renderNewEmpColumn(column) {
    return {
      ...column,
      render(text, record) {
        const { newServeManagerId } = record;
        const showText = text ? `${text}(${newServeManagerId})` : '--';
        return (
          <ToolTipCell tipContent={showText} cellText={showText} />
        );
      },
    };
  }

  // 通用列渲染
  @autobind
  renderCommonColumn(column) {
    return {
      ...column,
      render(text) {
        return (
          <ToolTipCell tipContent={text} cellText={text} />
        );
      },
    };
  }

  // 渲染点击删除按钮后的确认框
  @autobind
  renderPopconfirm(record) {
    return (
      <Popconfirm
        placement="top"
        onConfirm={() => this.props.onDelete(record)}
        okText="是"
        cancelText="否"
        title="是否删除此条数据？"
      >
        <Icon type="shanchu" />
      </Popconfirm>
    );
  }

  render() {
    const { list, pagination, onChange } = this.props;
    const paginationProps = {
      ...pagination,
      onChange,
    };

    const columns = this.getCustColumns();
    // 表格需要滚动的宽度
    const scrollWidth = _.sum(_.map(columns, 'width'));

    return (
      <Table
        fixedTableWidth={166}
        dataSource={list}
        columns={columns}
        pagination={paginationProps}
        rowKey="custId"
        placeHolderImageProps={CUSTOMER_PLACEHOLDER_PROPS}
        spaceColumnProps={SPACE_20}
        scroll={{ x: scrollWidth }}
        useNewUI
      />
    );
  }
}
