/**
 * @Description: 总部客户分配配置项
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-05-23 17:03:23
 * @Last Modified by: z<PERSON><PERSON><PERSON><PERSON>-k0171276
 * @Last Modified time: 2021-10-21 14:38:30
 */

// 所有条数限制为 5000
export const LIMIT_ALL_COUNT = 5000;
// 详情以及审批页面客户列表标题
export const DETAIL_CUSTOMER_COLUMNS = [
  {
    dataIndex: 'custName',
    key: 'custName',
    title: '客户',
    width: 126,
    fixed: 'left',
  },
  {
    dataIndex: 'totalAsset',
    key: 'totalAsset',
    title: '总资产(元)',
    width: 121,
    align: 'right',
  },
  {
    dataIndex: 'serveManagerName',
    key: 'serveManagerName',
    title: '原服务经理',
    width: 101,
  },
  {
    dataIndex: 'serveManagerType',
    key: 'serveManagerType',
    title: '原服务经理类型',
    width: 84,
  },
  {
    dataIndex: 'departmentName',
    key: 'departmentName',
    title: '原营业部',
    width: 140,
  },
  {
    dataIndex: 'branchName',
    key: 'branchName',
    title: '原分公司',
    width: 70,
  },
  {
    dataIndex: 'newServeManagerName',
    key: 'newServeManagerName',
    title: '新服务经理',
    width: 101,
  },
  {
    dataIndex: 'newServeManagerType',
    key: 'newServeManagerType',
    title: '新服务经理类型',
    width: 84,
  },
  {
    dataIndex: 'newDepartmentName',
    key: 'newDepartmentName',
    title: '新营业部',
    width: 140,
  },
  {
    dataIndex: 'newBranchName',
    key: 'newBranchName',
    title: '新分公司',
    width: 70,
  },
  {
    dataIndex: 'reason',
    key: 'reason',
    title: '调整原因',
    width: 98,
  },
];
// 审批人的 columns
export const APPROVAL_COLUMNS = [
  {
    title: '工号',
    dataIndex: 'empId',
    key: 'empId',
  }, {
    title: '姓名',
    dataIndex: 'empName',
    key: 'empName',
  }, {
    title: '所属部门',
    dataIndex: 'orgName',
    key: 'orgName',
  },
];

// pageFlag 区分编辑页面和只读页面
export const EDIT_FLAG = 'editPage';

// 分配场景-普通分配key
export const COMMON_TRANSFER = 'COMMON_TRANSFER';
// 新增-分配场景默认选项
export const TRANSFERTYPE_DEFAULT = COMMON_TRANSFER;

export const DEFAULT_PAGE_NUM = 1;
// 客户
export const KEY_CUSTNAME = 'custName';
// 总资产(元)
export const TOTAL_ASSET = 'totalAsset';
// 原服务经理
export const SERVE_MANAGER_NAME = 'serveManagerName';
// 新服务经理姓名
export const NEW_SERVE_MANAGER_NAME = 'newServeManagerName';
// 调整原因
export const REASON = 'reason';
// 取消按钮的值
export const CANCEL_BTN_VALUE = 'cancel';
// 客户列表空数据
export const CUSTOMER_PLACEHOLDER_PROPS = {
  title: '暂无客户数据',
};
export const SPACE_20 = {
  width: 20,
};
export const PAGE_SIZE_FIFTEEN = 15;
export const PAGE_SIZE_MAX = 5000;
export const XLSX_CONFIG = [['custId', 'custName', 'newServeManagerName', 'orgCd', 'newServeManagerId', 'reason']];
export const HIGH_LIGHT_TEXT_LIST = ['同意', '通过', '办结', '提交', '重新提交'];
