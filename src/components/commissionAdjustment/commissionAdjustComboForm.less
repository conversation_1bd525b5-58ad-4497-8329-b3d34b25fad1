.adjustComboContentBox {
  padding-top: 20px;
  .formItem {
    display: inline-block;
    width: 33.33%;
    margin-bottom: 5px;
    &:nth-child(1), &:nth-child(4), &:nth-child(7) {
        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .ant-form-item-label {
          width: 126px;
        }
      }
    }
  }
  .blockTip {
    font-size: 14px;
    padding-bottom: 20px;
    display: flex;
    .icon {
      color: #108ee9;
      margin-right: 8px;
    }
    .text {
      color: #999;
      p {
        margin: 0;
      }
    }
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-select-arrow {
      &::before {
        content: '\E606';
      }
    }
    .ant-form-item {
      margin-bottom: 13px;
    }
    .ant-form-item-label {
      width: 123px;
      overflow: inherit;
      vertical-align: top;
      label {
        margin: 0;
        font-size: 14px;
        color: #999;
        font-style: normal;
        font-weight: normal;
        &::after {
          margin: 0 4px;
        }
      }
    }
    .ant-form-item-control-wrapper {
      display: inline-block;
      vertical-align: top;
    }
    .ant-select, .ant-input {
      width: 200px;
      color: #333;
      border-radius: 2px;
      height: 30px;
      .ant-select-selection {
        border-radius: 2px;
        height: 30px;
      }
    }
    .has-error {
      .commissionComboFormcheckApprover {
        border-color: #f5222d;
      }
      .lego-filter-filterWrapper {
        button.ant-btn {
          border-color: #f5222d;
          &:hover {
            border-color: #f5222d;
          }
        }
      }
    }
    .lego-filter-filterWrapper {
      .lego-filter-customerFilterValue {
        padding-left: 8px;
      }
      button.ant-btn {
        width: 200px;
        height: 30px;
        text-align: left;
        border: 1px solid #d9d9d9;
        border-radius: 2px;
        &:hover {
          border-color: #40a9ff;
        }
      }
      & > button:first-child .lego-filter-contentShowOnButton {
        padding-left: 0;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.65);
        & > span:nth-child(1) {
          display: none;
        }
        .lego-filter-valueShowOnButton {
          max-width: 170px;
          color: #333;
          font-size: 14px;
        }
      }
      .lego-single-filter-searchInput {
        width: 200px;
        border: 1px solid #d9d9d9;
        &:focus {
          box-shadow: none;
        }
        &:hover {
          border-color: #d9d9d9;
        }
      }
      .lego-filter-multi-withSearch-multiFilterMenu-search {
        .ant-input {
          width: 232px;
        }
      }
    }
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .commissionComboFormcheckApprover {
      display: inline-block;
      width: 200px;
      height: 32px;
      border: 1px solid #e9e9e9;
      cursor: pointer;
      line-height: 32px;
      padding-left: 8px;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      &:hover {
        border-color: #49a9ee;
      }
    }
  }
}
.defaultValue {
  font-size: 14px;
  font-style: normal;
}
.searchIcon {
  font-size: 14px;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  float: right;
}
.channelTips {
  background-color: #fdf7d3;
  margin: 4px 0;
  padding: 6px;
  .text {
    display: inline-block;
    font-size: 12px;
    width: 170px;
    color: #333;
    vertical-align: top;
  }
  .close {
    position: relative;
    right: -5px;
    cursor: pointer;
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .anticon-exclamation-circle {
      margin-right: 4px;
      color: #f5aa1d;
    }
  }
}
.hide {
  display: none;
}
