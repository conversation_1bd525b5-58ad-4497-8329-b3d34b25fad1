/**
 * @file components/commissionAdjustment/SpecialOtherCommissionSelect.js
 * @description 特殊佣金其他佣金率Select选择列表
 * <AUTHOR>
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-09-03 16:51:21
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import Icon from '@/components/common/Icon';

import { disabledMap, specialOtherCommission } from '../../config/otherCommissionDictionary';
import OtherCommissionSelect from './SpecialOtherCommissionSelect';
import { NORMAL_KEY, CREDIT_KEY } from './config';

import styles from './otherCommissionSelectList.less';

export default class SpecialOtherCommissionSelectList extends PureComponent {
  static propTypes = {
    // 其他佣金费率列表
    otherRatios: PropTypes.array,
    // 切换下拉框选项值
    onChange: PropTypes.func,
    // 其他佣金费率对应的值
    baseCommission: PropTypes.object,
    // 客户信息
    custInfo: PropTypes.object,
  };

  static defaultProps = {
    otherRatios: [],
    onChange: () => { },
    baseCommission: {},
    custInfo: {},
  };

  @autobind
  getWrapRef() {
    return this.wrap;
  }

  @autobind
  wrapRef(input) {
    this.wrap = input;
  }

  @autobind
  makeSelect(item) {
    const {
      baseCommission,
      onChange,
      custInfo,
      custInfo: {
        openRzrq = 'Y',
        minimumCharge = 'Y',
      }
    } = this.props;
    // 下拉框是否置灰不可选
    let disabled = false;
    const { code, options } = item;
    const { brief, paramName } = specialOtherCommission[code];
    // 是否是需要置灰的选项
    const isDisabledName = _.includes(disabledMap, paramName);
    if (code === CREDIT_KEY) {
      // 信用账户收取最低费用（是否满足收取最低费用规则且客户开通两融，不满足则置灰）
      disabled = !(isDisabledName && minimumCharge === 'Y' && openRzrq === 'Y');
    } else if (code === NORMAL_KEY) {
      // 普通账户收取最低费用（是否满足收取最低费用规则，不满足则置灰）
      disabled = isDisabledName && minimumCharge === 'N';
    } else if (openRzrq === 'N' && isDisabledName) {
      // 担保股基、信用股基（是否客户开通两融，未开通则置灰）
      disabled = true;
    }

    const newOptions = options.map((option) => ({
      ...option,
      label: option.codeDesc,
      value: option.codeValue,
      show: true,
    }));
    const otherProps = {
      onChange,
      custInfo,
    };
    return (
      <OtherCommissionSelect
        value={baseCommission[paramName] || ''}
        disabled={disabled}
        key={code}
        label={brief}
        name={paramName}
        options={newOptions}
        getPopupContainer={this.getWrapRef}
        {...otherProps}
      />
    );
  }

  render() {
    const { otherRatios } = this.props;
    const compactRatios = _.compact(otherRatios);
    const orderRatios = _.sortBy(compactRatios, (o) => specialOtherCommission[o.code].order);
    const oddCommissionArray = _.filter(orderRatios, (v, index) => index % 2 === 1);
    const evenCommissionArray = _.filter(orderRatios, (v, index) => index % 2 === 0);
    return (
      <div className={styles.otherComsBoxWrap} ref={this.wrapRef}>
        <div className={styles.otherComsBox}>
          {
            evenCommissionArray.map(this.makeSelect)
          }
        </div>
        <div className={styles.otherComsBox}>
          {
            oddCommissionArray.map(this.makeSelect)
          }
        </div>
        <div className={styles.blockTip}>
          <div className={styles.icon}><Icon type="tishi2" /></div>
          <div className={styles.text}>
            <p>特别提醒：深圳私募债、公司债、政策性金融债成本佣金为万0.15，深圳可转债成本佣金为万0.4，低于成本一律按成本收取佣金；</p>
            <p>
              港股通初始佣金同步规则：客户首次开通港股通时点A股非现场交易佣金-万0.687得出的数值作为港股通初始净佣金，
              之后调整A股佣金不影响港股通佣金。如需调整港股通佣金建议先检查恒生系统客户港股通初始佣金数值，避免误操作。
            </p>
          </div>
        </div>
      </div>
    );
  }
}
