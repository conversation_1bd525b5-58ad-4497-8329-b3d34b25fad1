@import '~@/css/commonDetail.less';

.detailBox {
  background-color: #fff;
  padding-bottom: 47px;

  .title {
    margin-bottom: 14px;
    padding: 0 0 10px;
    color: #333;
    font-size: 14px;
    font-weight: bold;
    border-bottom: 1px solid #ebebeb;

    .tipText {
      position: relative;
      display: inline-block;
      text-indent: 20px;
      font-size: 14px;
      color: #f0af41;
      margin-left: 10px;
      font-weight: normal;

      .warningIcon {
        position: absolute;
        left: -8px;
        top: -3px;
        width: 28px;
        height: 28px;
        margin-right: 5px;
      }
    }
  }

  .commissionType {
    font-size: 14px;
    color: #333;
    line-height: 20px;
    background: linear-gradient(270deg, rgba(16, 142, 233, 0) 0%, rgba(16, 142, 233, 0.4) 100%);
    background-position: 0 12px;
    background-repeat: no-repeat;
    display: inline-block;

    &:not(:first-child) {
      margin-top: 6px;
    }
  }

  .description {
    word-break: break-all;
  }
  /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
    .ant-bizDetailBlock:first-child {
      margin-top: 0;
    }

    .ant-bizDetailBlock:not(:first-child) {
      margin-top: 16px;
    }

    .ant-bizDetailBlock-body {
      margin-left: 14px;
    }

    .ant-bizInfoGroup-infoCells {
      margin-left: 0 !important;
    }

    .ant-bizInfoGroup {
      padding: 0;
    }

    .ant-bizInfoCell-label {
      display: flex;
      justify-content: flex-end;
    }

    .ant-bizInfoCell-label-text {
      font-size: 14px;
    }

    .ant-bizInfoCell {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    .ant-uploader {
      min-height: 0;
    }

    .ant-uploader-file {
      margin-bottom: 14px;
    }

    .ant-radio-input, .ant-radio-inner {
      top: -2px;
    }
  }

  .custListWrap {
    padding-bottom: 14px;
  }

  .fileWrap {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-bizInfoCell-label-text {
        font-size: 14px;
        color: #999;
      }
    }
  }

  .tableWrap {
    box-shadow: 0 1px 0 0 #e9e9e9;
  }
}

.viewLink {
  color: #348cf0;
  cursor: pointer;
}

.detailBox {
  /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
    .ant-bizInfoCell-label-text {
      max-height: 40px;
      width: calc(100% - 20px);
      text-align: right;
    }

    .ant-bizInfoCell-label {
      display: flex;
      max-height: 40px;
      justify-content: right;
      height: auto;
    }
  }
}

.filedText {
  color: #f8505b;
  text-decoration: underline;
  cursor: pointer;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-tooltip-content {
      max-width: 257px;
    }

    .ant-tooltip-inner {
      padding: 14px;
      cursor: default;
      background-color: #fff;
    }
  }
}

.filedBox {
  padding: 14px;
  width: 257px;
  background: #fff;
  z-index: 10;
  box-shadow: 0 2px 6px 0 #ccc;
  text-align: left;
  cursor: default;

  h4 {
    margin-bottom: 14px;
    font-size: 14px;
    color: #333;
    font-weight: bold;
  }

  span.text {
    display: block;
    max-height: 123px;
    overflow: auto;
    line-height: 20px;
    font-size: 12px;
    color: #333;
    white-space: normal;
    word-break: break-all;
  }
}

.close {
  cursor: pointer;
  right: 14px;
  position: absolute;
  top: 12px;
  color: #666;
}

.dept {
  max-width: 90%;
}

.textOver {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toolTipBox {
  height: 0;
}

.tableModule {
  .filterWarp {
    display: flex;
    align-items: center;
    padding-bottom: 14px;
  }

  .filterItem {
    display: inline-block;
    margin-right: 10px;
  }
}
.detailTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .endBtn {
    border: 1px solid #108ee9;
    width: 154px;
    height: 30px;
    text-align: center;
    border-radius: 2px;
    svg {
      vertical-align: middle;
      margin-right: 2px;
      color: #108ee9;
    }
    span {
      font-weight: normal;
      vertical-align: middle;
    }
    &.disabledEndBtn {
      border: 1px solid #ccc;
      background-color: #eee;
      cursor: default;
      span {
        color: #999;
      }
    }
  }
}

.fileName {
  cursor: pointer;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #108ee9;
}

.popover {
  width: 274px;
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-popover-inner-content {
      padding: 10px !important;
    }
  }
}

.statusName {
  color: #4169c1;
}

.statusWarningIcon {
  position: relative;
  top: -2px;
  width: 28px;
  height: 28px;
}

.approvalIcon {
  position: relative;
  top: -1px;
  width: 14px;
  height: 14px;
}

.submitIcon {
  position: relative;
  top: -2px;
  width: 16px;
  height: 16px;
}
