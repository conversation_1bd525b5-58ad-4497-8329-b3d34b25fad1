/**
 * @file components/common/Select/SelectAssembly.js
 * <AUTHOR>
 * @Last Modified by: yanfaping
 * @Last Modified time: 2021-05-14 8:59:40
 * @description 特殊佣金申请-搜索客户输入框
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { AutoComplete } from 'antd';

import { seibelConfig } from '@/config';
import logable, { logCommon } from '@/decorators/logable';
import SimilarAutoComplete from '../common/similarAutoComplete';
import confirm from '../common/confirm';

import styles from './selectAssembly.less';

const Option = AutoComplete.Option;
const { comsubs: commadj } = seibelConfig;

export default class SpecialSelectAssembly extends PureComponent {
  static propTypes = {
    dataSource: PropTypes.array.isRequired,
    onSearchValue: PropTypes.func.isRequired,
    onSelectValue: PropTypes.func.isRequired,
    unfinishRoute: PropTypes.func, // 选择客户，弹出未完成订单，点击确认的跳转路由
    onValidateCust: PropTypes.func,
    width: PropTypes.number,
    subType: PropTypes.string,
    validResult: PropTypes.object,
    shouldeCheck: PropTypes.bool,
    // 佣金调整校验客户如果有被驳回订单，终止订单
    terminalOrderFlow: PropTypes.func,
    // 佣金调整校验客户如果有新建订单，修改订单状态
    changeOrderStatus: PropTypes.func,
  }

  static defaultProps = {
    width: 300,
    subType: '',
    validResult: {},
    shouldeCheck: true,
    onValidateCust: _.noop,
    terminalOrderFlow: _.noop,
    changeOrderStatus: _.noop,
    unfinishRoute: () => {},
  }

  constructor(props) {
    super(props);
    this.state = {
      isUnfinish: false, // 标识 选中的客户，是否有未完成订单
      isRejected: false, // 标识 选中的客户，是否有被驳回订单
      isNew: false, // 标识 选中的客户，是否有新建订单
      canSelected: false,
    };
  }

  @autobind
  clearCust() {
    this.setState({
      canSelected: false,
    });
    this.custSearch.clearValue();
  }

  @autobind
  custSearchRef(input) {
    this.custSearch = input;
  }

  @autobind
  handleOrder(selectItem, otherParams) {
    const { unfinishRoute, terminalOrderFlow, changeOrderStatus } = this.props;
    const { isUnfinish, isRejected, isNew } = this.state;
    const params = {
      custEcom: selectItem.custId,
      ...otherParams
    };
    // 是否是fsp流程
    const isFspFlow = otherParams.fspFlow;
    // 选择的客户，有未完成订单，点击确定，跳转到360视图订单列表页面
    if (isUnfinish) {
      unfinishRoute(params);
    }
    // 有被驳回订单，点击确定,如果是fsp流程则掉接口终止订单，否则就更改状态
    if (isRejected) {
      if (isFspFlow) {
        terminalOrderFlow(params);
      } else {
        changeOrderStatus(params);
      }
    }
    // 有新建订单，点击确定则掉接口修改“新建”状态为“已失败”，继续发起调佣流程
    if (isNew) {
      changeOrderStatus(params);
    }
    // 把选择的客户传到外层，提交时二次校验
    this.props.onSelectValue(selectItem);
  }

  @autobind
  handleOKAfterValidate(selectItem, otherParams) {
    if (this.state.canSelected) {
      // 可以选中
      const { onSelectValue, validResult: { openRzrq, minimumCharge } } = this.props;
      onSelectValue({ ...selectItem, openRzrq, minimumCharge });
    } else {
      // 是否需要清空所选客户,如果是有在途订单（待审批，新建，驳回）,确认之后不清空所选客户
      if (otherParams) {
        this.handleOrder(selectItem, otherParams);
        return;
      }
      // 干掉客户
      this.clearCust();
    }
  }

  @autobind
  handleCancelAfterValidate() {
    this.clearCust();
  }

  // 校验不通过，弹框
  @autobind
  fail2Validate(obj) {
    const {
      shortCut,
      content,
      selectItem,
      params,
    } = obj;
    confirm({
      shortCut,
      content,
      title: '提示',
      onOk: () => { this.handleOKAfterValidate(selectItem, params); },
      onCancel: this.handleCancelAfterValidate,
    });
    this.setState({
      canSelected: false,
    });
  }

  // 客户校验
  @autobind
  afterValidateSingleCust(selectItem) {
    if (_.isEmpty(this.props.validResult)) {
      confirm({ content: '客户校验失败' });
      return;
    }
    const {
      subType,
      hasOrder, // 是否有在途订单
      orderStatus, // 订单状态
      fspFlow, // 是否是fsp流程
      orderId, // 订单id
      flowId, // 流程id
      flowStarter, // 流程发起者工号
      validmsg, // 校验信息
    } = this.props.validResult;
    const params = {
      fspFlow,
      orderId,
      flowId,
      flowStarter
    };
    this.setState({
      isUnfinish: false,
      isRejected: false,
      isNew: false,
    });
    // 投顾签约客户不允许调整佣金
    if (selectItem?.despiteSigning) {
      this.fail2Validate({ shortCut: 'isDespiteSigning' });
      return;
    }
    // 有待审批订单时
    if (hasOrder === 'Y' && orderStatus === 'hasCheck') {
      this.setState({ isUnfinish: true });
      this.fail2Validate({ shortCut: 'unfinish', selectItem, params });
      return;
    }
    // 有被驳回订单时
    if (hasOrder === 'Y' && orderStatus === 'hasRejected') {
      this.setState({ isRejected: true });
      this.fail2Validate({ shortCut: 'rejected', selectItem, params });
      return;
    }
    // 有新建订单时
    if (hasOrder === 'Y' && orderStatus === 'hasNew') {
      this.setState({ isNew: true });
      this.fail2Validate({ shortCut: 'new', selectItem, params });
      return;
    }
    // 当前客户有自动外呼调佣时
    if (hasOrder === 'Y' && subType === commadj.commissionAdjustCombo) {
      this.fail2Validate({ shortCut: 'hasAutoDial' });
      return;
    }
    // 当前客户是否有在途的投顾签约和线上签约时
    if (hasOrder === 'Y' && !_.isEmpty(validmsg)) {
      this.fail2Validate({ content: validmsg });
      return;
    }
    this.setState({
      canSelected: true,
    });
    this.handleOKAfterValidate(selectItem);
  }

  // 选择某个客户
  @autobind
  handleSelectCust(cust) {
    if (!_.isEmpty(cust)) {
      // 选中值了
      const { shouldeCheck, onValidateCust } = this.props;
      const { custId, custType } = cust;
      if (shouldeCheck) {
        onValidateCust({
          custId,
          custType,
        }).then(() => {
          this.afterValidateSingleCust(cust);
        });
      } else {
        this.props.onSelectValue(cust);
      }
      // 记录选择的客户信息日志
      logCommon({
        type: 'DropdownSelect',
        payload: {
          name: '选择客户',
          value: JSON.stringify(cust),
        },
      });
    } else {
      // 此时有可能客户搜索组件会传递一个空对象
      // 将空值传递出去以便可以让父组件更新其他组件数据
      // 删除客户
      // 向父组件传递一个空对象
      this.props.onSelectValue(null);
    }
  }

  // 搜索客户列表
  @autobind
  @logable({ type: 'Click', payload: { name: '搜索客户', value: '$args[0]' } })
  handleSearchCustList(value) {
    this.props.onSearchValue(value);
  }

  @autobind
  renderOption(cust) {
    const { custName, custId, riskLevelText = '' } = cust;
    const text = `${custName}（${custId}） - ${riskLevelText}`;
    return (
      <Option key={custId} value={custId}>
        <span className={styles.prodValue} title={text}>{text}</span>
      </Option>
    );
  }

  render() {
    const { width, dataSource } = this.props;
    return (
      <SimilarAutoComplete
        ref={this.custSearchRef}
        placeholder="经纪客户号/客户名称"
        optionList={dataSource}
        style={{ width }}
        optionKey="custId"
        onSelect={this.handleSelectCust}
        onSearch={this.handleSearchCustList}
        renderOptionNode={this.renderOption}
      />
    );
  }
}
