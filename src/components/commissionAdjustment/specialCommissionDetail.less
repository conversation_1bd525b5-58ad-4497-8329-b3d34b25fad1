.detailWrap {
  min-width: 650px;
  padding-bottom: 30px;

  /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
    .ant-bizInfoCell-label-text {
      max-height: 40px;
    }

    .ant-bizInfoCell-label {
      display: flex;
      max-height: 40px;
      justify-content: right;
      height: auto;

      .ant-bizInfoCell-label-text {
        width: calc(100% - 20px);
        text-align: right;
      }
    }
  }

  .title {
    height: 44px;
    font-size: 14px;
    color: #333;
    line-height: 43px;
    text-align: left;
    font-weight: bold;
    border-bottom: 1px solid #ebebeb;
    box-sizing: border-box;
    margin-bottom: 14px;

    .tipText {
      position: relative;
      display: inline-block;
      text-indent: 20px;
      font-size: 14px;
      color: #f0af41;
      margin-left: 10px;
      font-weight: normal;

      .warningIcon {
        position: absolute;
        left: -8px;
        top: 8px;
        width: 28px;
        height: 28px;
        margin-right: 5px;
      }
    }
  }

  .custTable {
    width: 100%;
    overflow: auto;
  }
}
