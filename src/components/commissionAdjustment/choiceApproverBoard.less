.approverBox {
  position: relative;
  padding: 20px 0;
}
.serarhApprover {
  margin-bottom: 15px;
  width: 300px;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-input-affix-wrapper .ant-input-suffix {
      display: inline-block;
      line-height: 30px;
      right: 0;
      background-color: #348cf0;
      border-radius: 0 4px 4px 0;
      text-align: center;
      font-size: 14px;
      i {
        &:before {
          color: #fff;
        }
      }
    }
    .ant-input-affix-wrapper .ant-input:not(:last-child) {
      padding-right: 60px;
    }
    .ant-input-search-icon,
    .ant-input-search-icon:hover {
      color: #fff;
    }
  }
}
