/*
 * @Author: yanfaping
 * @Date: 2022-07-26 15:07:28
 * @Last Modified by: yanfaping
 * @Last Modified time: 2022-07-26 15:07:28
 * @description 非标客户特殊佣金调整-详情页面
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { Alert, Tooltip, Radio } from 'antd';
import {
  DetailContainer,
  DetailBlock,
  InfoGroup,
  InfoCell,
  ApprovalHistory,
} from '@crm/biz-ui';
import { logCommon } from '@/decorators/logable';
import Upload from '@/newUI/storageUpload';
import Table, { ToolTipCell } from '@/components/common/table';
import IFWrap from '@/components/common/IFWrap';
import { number, url } from '@/helper';
import InfoIcon from './svg/info.svg';
import warningIcon from './svg/warning.svg';

import {
  UN_STANDARD_CUST_TABLE_COLUMNS,
  SPACE_20,
  FUTURE_TIME_30,
  SELECT,
  NO_SELECT,
  SHOW_RADIO_CUSTS
} from './config';

import styles from './unStandardCommissionDetail.less';

const RadioGroup = Radio.Group;
const unStandards = ['不达标', '未达标'];

export default class UnStandardCommissionDetail extends PureComponent {
  static propTypes = {
    data: PropTypes.object.isRequired,
  }

  constructor(props) {
    super(props);

    this.custColumns = this.updateColumns(UN_STANDARD_CUST_TABLE_COLUMNS);
  }

  @autobind
  getValue(data, keyPath, defaultVlaue = '--') {
    const value = _.get(data, keyPath);
    if (_.isNumber(value)) {
      return value;
    }
    return _.get(data, keyPath) || defaultVlaue;
  }

  @autobind
  updateColumns(columns) {
    return _.map(columns, (column) => {
      const { dataIndex } = column;
      // 服务经理需要携带名称和id
      if (dataIndex === 'empId') {
        return this.updateEmpColumn(column);
      }

      // 客户承诺未来达标时间
      if (dataIndex === 'futureStandardTime') {
        return this.updateFutureStandardTimeColumn(column);
      }

      // 是否达标列
      if (dataIndex === 'standardFlag') {
        return this.updateStandardFlagColumns(column);
      }

      // T-1日总资产
      if (dataIndex === 'timeTotalAsset') {
        return this.updateTimeTotaAssetColumn(column);
      }

      // 金额类格式化
      if (dataIndex === 'sumGjAmt') {
        return this.updateMoneyColumn(column);
      }

      return this.updateWordColumn(column);
    });
  }

  @autobind
  updateEmpColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        const { empId, empName } = record;
        const empInfo = `${empName}(${empId})`;
        return (
          <ToolTipCell
            cellText={empInfo || '--'}
            tipContent={empInfo || '--'}
          />
        );
      }
    };
  }

  @autobind
  updateFutureStandardTimeColumn(column) {
    return {
      ...column,
      render: (text) => {
        if (_.isEmpty(text)) {
          return '--';
        }
        return text === FUTURE_TIME_30 ? '30日' : '60日';
      }
    };
  }

  @autobind
  renderStandardFlagTootip(text, record) {
    const { unStandardCommsion } = record;
    // 不达标、未达标悬浮展示未达标佣金品种信息
    if (_.includes(unStandards, text) && !_.isEmpty(unStandardCommsion)) {
      const unStandardComStr = _.join(unStandardCommsion, '、');
      return (
        <div className={styles.tootipWarp}>
          <div className={styles.tootipTitle}>未达标费率</div>
          <span>{unStandardComStr}</span>
        </div>
      );
    }
    return '';
  }

  @autobind
  updateStandardFlagColumns(column) {
    return {
      ...column,
      render: (text, record) => {
        if (text === '' || _.isNull(text)) {
          return '--';
        }
        const node = (<span className={styles.standardFlag}>{text}</span>);
        const tipContent = this.renderStandardFlagTootip(text, record);
        return (
          <ToolTipCell
            cellText={node}
            tipContent={tipContent}
          />
        );
      }
    };
  }

  @autobind
  updateTimeTotaAssetColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        if (_.isNumber(text)) {
          const formatMoney = number.thousandFormat(text);
          const peakTime = `峰值时间：${record?.peakTime || '--'}`;
          return (
            <div className={styles.assetTotal}>
              <div>{formatMoney}</div>
              <Tooltip placement="top" title={peakTime}>
                <img src={InfoIcon} className={styles.infoIcon} />
              </Tooltip>
            </div>
          );
        }
        return '--';
      }
    };
  }

  @autobind
  updateMoneyColumn(column) {
    return {
      ...column,
      render: (text) => {
        let formatMoney = '--';
        if (_.isNumber(text)) {
          formatMoney = number.thousandFormat(text);
        }
        return formatMoney;
      }
    };
  }

  @autobind
  updateWordColumn(column) {
    return {
      ...column,
      render: (text) => (
        <ToolTipCell
          cellText={text || '--'}
          tipContent={text || '--'}
        />
      )
    };
  }

  @autobind
  handleViewPdf(fileInfo) {
    // TODO: 后期添加 window.open 打开 PDF 预览
    logCommon({
      type: 'Click',
      payload: {
        name: '非标客户特殊佣金调整详情-PDF预览',
        value: JSON.stringify(fileInfo)
      }
    });
    return url.viewPdf(fileInfo);
  }

  @autobind
  showRadioGroup(multiMiniChargeSet, custType) {
    // 1、判断multiMiniChargeSet是否有值，有值判断2
    // 2、判断客户类型是否为机构户、产品户，是则展示“是否设置多档最低收费”，否则不展示
    if (multiMiniChargeSet) {
      return _.includes(SHOW_RADIO_CUSTS, custType);
    }

    // 不展示“是否设置多档最低收费”
    return false;
  }

  render() {
    const { data } = this.props;
    const isEmpty = _.isEmpty(data);
    // 标题
    const applyId = this.getValue(data, 'applyId');
    const title = `编号：${applyId}`;
    // 申请事由
    const applayReason = this.getValue(data, 'applayReason');
    // 拟稿人
    const empName = this.getValue(data, 'drafterInfo.empName');
    const empId = this.getValue(data, 'drafterInfo.empId');
    const orgName = this.getValue(data, 'drafterInfo.orgName');
    const createTime = this.getValue(data, 'createTime');
    const statusText = this.getValue(data, 'statusText');
    const empInfo = `${empName}(${empId})-${orgName}`;
    // 客户信息
    const custInfo = this.getValue(data, 'custInfo', []);
    // 当前股基佣金率
    const originCommission = this.getValue(data, 'commissions.originCommission');
    // 目标股基佣金率
    const newCommission = this.getValue(data, 'commissions.newCommission');
    // 其他佣金费率
    const otherCommissions = data?.commissions?.otherCommissions;
    // 债券
    const zqCommission = this.getValue(otherCommissions, 'zqCommission.desc', '不变');
    // 担保股基
    const stkCommission = this.getValue(otherCommissions, 'stkCommission.desc', '不变');
    // 信用股基
    const creditCommission = this.getValue(otherCommissions, 'creditCommission.desc', '不变');
    // 担保品大宗交易
    const ddCommission = this.getValue(otherCommissions, 'ddCommission.desc', '不变');
    // 担保债券
    const dzCommission = this.getValue(otherCommissions, 'dzCommission.desc', '不变');
    // 信用场内基金
    const coCommission = this.getValue(otherCommissions, 'coCommission.desc', '不变');
    // 股转
    const stbCommission = this.getValue(otherCommissions, 'stbCommission.desc', '不变');
    // 场内基金
    const oCommission = this.getValue(otherCommissions, 'oCommission.desc', '不变');
    // 担保场内基金
    const doCommission = this.getValue(otherCommissions, 'doCommission.desc', '不变');
    // 港股通(净佣金)
    const hkCommission = this.getValue(otherCommissions, 'hkCommission.desc', '不变');
    // B股
    const bgCommission = this.getValue(otherCommissions, 'bgCommission.desc', '不变');
    // 大宗交易
    const dCommission = this.getValue(otherCommissions, 'dCommission.desc', '不变');
    // 普通账户收取最低费用
    const normalMinimumCharge = this.getValue(otherCommissions, 'normalMinimumCharge.desc', '不变');
    // 信用账户收取最低费用
    const creditMinimumCharge = this.getValue(otherCommissions, 'creditMinimumCharge.desc', '不变');
    // 信用债券
    const creditBondsCommission = this.getValue(otherCommissions, 'creditBondsCommission.desc', '不变');
    // 普通账户股票最低收费
    const gpMinimumCommission = this.getValue(otherCommissions, 'gpMinimumCommission.desc', '不变');
    // 信用账户股票最低收费
    const creditgpMinimumCommission = this.getValue(otherCommissions, 'creditgpMinimumCommission.desc', '不变');
    // 普通账户场内基金
    const onExchangeFundCommission = this.getValue(otherCommissions, 'onExchangeFundCommission.desc', '不变');
    // 信用账户场内基金
    const creditOnExchangeFundCommission = this.getValue(otherCommissions, 'creditOnExchangeFundCommission.desc', '不变');
    // 普通账户上海债券（不含可转债）
    const shNonConvertibleBondsCommission = this.getValue(otherCommissions, 'shNonConvertibleBondsCommission.desc', '不变');
    // 信用账户上海债券（不含可转债）
    const shCreditNonConvertibleBondsCommission = this.getValue(otherCommissions, 'shCreditNonConvertibleBondsCommission.desc', '不变');
    // 普通账户上海可转债
    const shConvertibleBondsCommission = this.getValue(otherCommissions, 'shConvertibleBondsCommission.desc', '不变');
    // 信用账户上海可转债
    const shCreditConvertibleBondsCommission = this.getValue(otherCommissions, 'shCreditConvertibleBondsCommission.desc', '不变');
    // 普通账户深圳债券（不含可转债）
    const szNonConvertibleBondsCommission = this.getValue(otherCommissions, 'szNonConvertibleBondsCommission.desc', '不变');
    // 信用账户深圳债券（不含可转债）
    const szCreditNonConvertibleBondsCommission = this.getValue(otherCommissions, 'szCreditNonConvertibleBondsCommission.desc', '不变');
    // 普通账户深圳可转债
    const szConvertibleBondsCommission = this.getValue(otherCommissions, 'szConvertibleBondsCommission.desc', '不变');
    // 信用账户深圳可转债
    const szCreditConvertibleBondsCommission = this.getValue(otherCommissions, 'szCreditConvertibleBondsCommission.desc', '不变');
    // 回购
    const hCommission = this.getValue(otherCommissions, 'hCommission.desc', '不变');
    // 审批历史
    const flowHistory = this.getValue(data, 'flowHistory', {});
    const currentStepName = this.getValue(data, 'flowHistory.currentStepName', '');
    // 附件信息
    const attachList = this.getValue(data, 'attachList', []);
    // 调佣状态
    const commissionStatus = this.getValue(data, 'commissionStatus');
    // 调佣状态是否调佣失败
    const isFail = commissionStatus === '调佣失败';
    const commissionStatusText = isFail && statusText === '处理中' ? '处理中' : commissionStatus;
    // 终止原因
    const terminateReason = `终止原因：${data.terminateReason}`;

    // 是否设置多档最低收费
    const multiMiniChargeSet = custInfo?.[0]?.multiMiniChargeSet;
    // 客户类型
    const custType = custInfo?.[0]?.custType;
    // 是否展示设置多档最低收费radio
    const showRadio = this.showRadioGroup(multiMiniChargeSet, custType);
    // 是否展示多档最低收费佣金项
    const showMultiMiniRadio = multiMiniChargeSet === 'Y';
    const requiredCommissionFields = data?.requiredCommissionFields || [];

    return (
      <DetailContainer isEmpty={isEmpty}>
        <div className={styles.detailWrap}>
          <div className={styles.title}>
            {title}
            <IFWrap when={commissionStatus === '模板已匹配'}>
              <span className={styles.tipText}>
                <img src={warningIcon} className={styles.warningIcon} />
                调佣设置已完成，佣金模板已匹配，请跟踪客户首笔交易。
              </span>
            </IFWrap>
            <IFWrap when={commissionStatus === '部分失败'}>
              <span className={styles.tipText}>
                <img src={warningIcon} className={styles.warningIcon} />
                调佣设置已完成，普通账户佣金模板已匹配，请跟踪客户首笔交易。信用账户未开通，佣金设置失败。
              </span>
            </IFWrap>
          </div>
          <IFWrap when={isFail && statusText === '终止'}>
            <Alert message={terminateReason} type="warning" showIcon />
          </IFWrap>
          <DetailBlock title="基本信息">
            <InfoGroup labelWidth="190px">
              <InfoCell label="子类型" content="非标客户特殊佣金调整" />
              <InfoCell label="申请事由" content={applayReason} />
              <InfoCell
                label="调佣状态"
                content={
                  commissionStatus === '部分失败'
                    ? (
                      <Tooltip title="信用账户未开通" placement="top">
                        {commissionStatusText}
                      </Tooltip>
                    )
                    : commissionStatusText
                }
              />
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="客户信息">
            <div className={styles.custTable}>
              <Table
                dataSource={custInfo}
                columns={this.custColumns}
                pagination={false}
                rowKey="custId"
                useNewUI
                spaceColumnProps={SPACE_20}
                scroll={{ x: 1340 }}
                withBorder={_.isEmpty(custInfo)}
              />
            </div>
          </DetailBlock>
          <DetailBlock title="股基佣金费率">
            <InfoGroup labelWidth="190px">
              <InfoCell
                span={50}
                label={`当前股基佣金率(${number.permillage})`}
                content={originCommission}
              />
              <InfoCell
                span={50}
                label={`目标股基佣金率(${number.permillage})`}
                content={newCommission}
              />
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="其他佣金费率">
            <InfoGroup labelWidth="190px">
              <InfoCell span={50} label="债券" content={zqCommission} />
              <InfoCell span={50} label="担保债券" content={dzCommission} />
              <InfoCell span={50} label="B股" content={bgCommission} />
              <InfoCell span={50} label="担保股基" content={stkCommission} />
              <InfoCell span={50} label="大宗交易" content={dCommission} />
              <InfoCell span={50} label="担保品大宗交易" content={ddCommission} />
              <InfoCell span={50} label="场内基金" content={oCommission} required={requiredCommissionFields?.['oCommission']} />
              <InfoCell span={50} label="担保场内基金" content={doCommission} required={requiredCommissionFields?.['doCommission']} />
              <InfoCell span={50} label="回购" content={hCommission} />
              <InfoCell span={50} label="信用场内基金" content={coCommission} required={requiredCommissionFields?.['coCommission']} />
              <InfoCell span={50} label="股转" content={stbCommission} />
              <InfoCell span={50} label="信用股基" content={creditCommission} />
              <InfoCell span={50} label="港股通(净佣金)" content={hkCommission} />
              <InfoCell span={50} label="信用债券" content={creditBondsCommission} />
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="账户最低收费设置">
            <InfoGroup labelWidth="190px">
              <IFWrap when={showRadio}>
                <InfoCell
                  span={100}
                  label="是否定制化设置最低收费"
                  labelWidth="190px"
                  content={(
                    <RadioGroup
                      value={multiMiniChargeSet}
                      size="normal"
                      disabled
                    >
                      <Radio key={SELECT} value={SELECT}>是</Radio>
                      <Radio key={NO_SELECT} value={NO_SELECT}>否</Radio>
                    </RadioGroup>
                  )}
                />
              </IFWrap>
              <IFWrap when={showMultiMiniRadio}>
                <InfoCell span={50} labelWidth="190px" label="普通账户股票" content={gpMinimumCommission} />
                <InfoCell span={50} labelWidth="190px" label="信用账户股票" content={creditgpMinimumCommission} />
                <InfoCell span={50} labelWidth="190px" label="普通账户场内基金" content={onExchangeFundCommission} />
                <InfoCell span={50} labelWidth="190px" label="信用账户场内基金" content={creditOnExchangeFundCommission} />
                <InfoCell span={50} labelWidth="190px" label="普通账户上海债券（不含可转债）" content={shNonConvertibleBondsCommission} />
                <InfoCell span={50} labelWidth="190px" label="信用账户上海债券（不含可转债）" content={shCreditNonConvertibleBondsCommission} />
                <InfoCell span={50} labelWidth="190px" label="普通账户上海可转债" content={shConvertibleBondsCommission} />
                <InfoCell span={50} labelWidth="190px" label="信用账户上海可转债" content={shCreditConvertibleBondsCommission} />
                <InfoCell span={50} labelWidth="190px" label="普通账户深圳债券（不含可转债）" content={szNonConvertibleBondsCommission} />
                <InfoCell span={50} labelWidth="190px" label="信用账户深圳债券（不含可转债）" content={szCreditNonConvertibleBondsCommission} />
                <InfoCell span={50} labelWidth="190px" label="普通账户深圳可转债" content={szConvertibleBondsCommission} />
                <InfoCell span={50} labelWidth="190px" label="信用账户深圳可转债 " content={szCreditConvertibleBondsCommission} />
              </IFWrap>
              <IFWrap when={!showMultiMiniRadio}>
                <InfoCell span={50} labelWidth="190px" label="普通账户收取最低费用" content={normalMinimumCharge} />
                <InfoCell span={50} labelWidth="190px" label="信用账户收取最低费用" content={creditMinimumCharge} />
              </IFWrap>
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="附件信息">
            <Upload
              key={applyId}
              disabled
              previewable
              removeable={false}
              onViewFile={this.handleViewPdf}
              defaultFileList={attachList}
            />
          </DetailBlock>
          <DetailBlock title="拟稿信息">
            <InfoGroup labelWidth="190px">
              <InfoCell label="拟稿人" content={empInfo} />
              <InfoCell label="申请时间" content={createTime} />
              <InfoCell label="状态" content={statusText} />
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="审批记录">
            <ApprovalHistory
              history={flowHistory}
              visibleCurrentNode={_.isEmpty(currentStepName)}
            />
          </DetailBlock>
        </div>
      </DetailContainer>
    );
  }
}
