@import "../../../css/util.less";
.pullleft() {
  float: left;
  text-align: left;
}
.pullright() {
  float: right;
  text-align: right;
}
.applyItem {
  padding: 14px 20px;
  width: 420px;
  max-height: 95px;
  background-color: #fff;
  cursor: pointer;
  box-sizing: border-box;
  border-bottom: 1px solid #e9e9e9;
  &:hover {
    background-color: #eaeef1;
  }

  &.active {
    background-color: #4897f1;
    &:hover {
      background-color: #4897f1;
    }
    .subType {
      color: #fff;
    }
    .operateType {
      color: #fff;
    }
    .date {
      color: #fff;
    }
    .drafter {
      color: #fff;
      .label {
        color: #fff;
      }
    }
    .tagArea {
      span {
        background-color: #fff;
        color: #108ee9;
      }
    }
    .customer {
      color: #fff;
      .label {
        color: #fff;
      }
        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .apply-item-cust {
          color: #fff;
        }
      }
    }
  }
}

.itemHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 20px;
  line-height: 20px;
  .titleArea {
    width: 60%;
  }
  .tagArea {
    width: 39%;
    text-align: right;
    line-height: initial;
    span {
      display: inline-block;
      width: 50px;
      height: 18px;
      text-align: center;
      line-height: 18px;
      padding: 0;
      font-size: 12px;
      border: none;
      margin: 0;
      border-radius: 2px;
    }

    .status {
      width: auto;
      height: auto;
      margin-right: 14px;
      padding: 0 4px;
      font-size: 12px;
      line-height: 18px;
      border-radius: 2px;
    }

    .success {
      color: #00a985;
      border: 1px solid rgba(0, 169, 133, 0.4);
    }

    .fail {
      color: #f3444a;
      border: 1px solid #fbb2b2;
    }

    .activeGhost {
      color: #fff !important;
      border: 1px solid #fff;
      background: transparent !important;
    }
  }
}

.serialNumber {
  font-weight: bold;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  .one-line-ellipsis;
}

.type {
  color: #333;
  font-size: 12px;
}

.textEllipse {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.secondLine,
.thirdLine {
  margin-top: 5px;
  font-size: 12px;
  color: #aaa;
  &:after {
    content: " ";
    display: table;
    clear: both;
  }
}

.secondLine {
  line-height: 20px;
}

.thirdLine {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 18px;
}

.subType {
  font-size: 12px;
  color: #333;
  display: inline-block;
}

.operateType {
  color: #d07345;
}

.date {
  width: 33%;
  font-size: 12px;
  color: #333;
  .pullright;
}

.drafter {
  width: 59%;
  .textEllipse;
  font-size: 12px;
  color: #333;
  .label {
    color: #999;
  }
}

.drafterName {
  margin-right: 10px;
}

.customer {
  text-align: right;
  width: 39%;
  .textEllipse;
  font-size: 12px;
  color: #333;
  .label {
    color: #a7a7a7;
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .apply-item-cust {
      color: #a7a7a7;
    }
  }
}

.serialNumber,
.secondLine,
.thirdLine,
.type {
  &.purple {
    color: #dc8f4c;
  }
  &.active {
    color: #fff;

    .subType {
      color: #fff;
    }
  }
}

.cutLine {
  display: inline-block;
  margin: 0 10px;
  width: 1px;
  height: 10px;
  background-color: #ddd;
}
