/**
 * @Author: sunweibin
 * @Date: 2018-07-06 15:36:38
 * @Last Modified by: yanfaping-K0050194
 * @Last Modified time: 2021-12-16 13:41:04
 * @description 服务订购-左侧列表-申请单组件
 */

import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _ from 'lodash';

import Tag from '@/components/common/tag';
import IfWrap from '@/components/common/IFWrap';

import { data as dataHelper } from '@/helper';
import { seibelConfig } from '@/config';

import styles from './index.less';

const { comsubs } = seibelConfig;

const StatusTag = Tag.statusTag;

const IMPORT_CUST_COMMISSIONS = [comsubs.batch, comsubs.authorizeCommission];

export default function ApplyItem(props) {
  const {
    data,
    index,
    operateTypeName,
    typeName,
    typeNameClass,
    statusTags,
    subTypeName,
    active,
    onClick,
    showThirdLineInfo,
    showSecondLineInfo,
    showThirdLine,
  } = props;

  // 给组件添加选中状态下的 className
  const activeCls = { [styles.active]: active };
  const typeNameCls = typeNameClass ? { [styles[typeNameClass]]: true } : '';
  const applyItemCls = cx(styles.applyItem, activeCls);
  const serialCls = cx(styles.serialNumber, activeCls);
  const typeCls = cx({
    [styles.type]: true,
    ...activeCls,
    ...typeNameCls,
  });
  const secondLineCls = cx(styles.secondLine, activeCls);
  const thirdLineCls = cx(styles.thirdLine, activeCls);

  // 第二行信息
  const secondLineInfo = showSecondLineInfo(data);
  // 展示客户信息
  const thirdLineInfo = showThirdLineInfo(data) || {};
  const secondLineValue = showSecondLineInfo === _.noop ? `${(data.createTime && data.createTime.slice(0, 10)) || '无'}` : secondLineInfo;
  const thirdLineValue = showThirdLineInfo === _.noop
    ? {
      title: `客户：${data.custName || '无'}(${data.custNumber || '无'})`,
      content: (
        <span>
          <span className="apply-item-cust">客户：</span>
          {`${data.custName || '无'}(${data.custNumber || '无'})`}
        </span>
      ),
    } : thirdLineInfo;
  // 针对选中状态下的申请单状态标签type做处理，如果申请单是选中状态则type为ghost
  const tags = _.map(statusTags, (tag) => {
    if (active) {
      return {
        key: dataHelper.uuid(),
        ...tag,
        type: 'ghost',
      };
    }
    return {
      ...tag,
      key: dataHelper.uuid(),
    };
  });

  const drafterName = `${data.empName || ''}(${data.empId || ''})${data.orgName || ''}`;

  // 渲染调佣状态值
  const renderOrderStatus = () => {
    const orderStatus = data?.singleBatchOrderStatus || '';
    if (_.isEmpty(orderStatus)) {
      return null;
    }
    let statusClass = '';
    // 单佣金、批量佣金添加调佣状态展示
    // 调佣失败：批量佣金展示“存在调佣失败”, 单佣金展示“调佣失败”
    // 单佣金部分失败时展示“存在调佣失败”
    const failName = (_.includes(IMPORT_CUST_COMMISSIONS, data?.subType) || data?.adjustCommissionWayStatus === '部分失败')
      ? '存在调佣失败' : '调佣失败';
    const statusName = orderStatus === 'success' ? '模板已匹配' : failName;
    if (active) {
      statusClass = styles.activeGhost;
    } else {
      statusClass = styles[`${orderStatus}`];
    }
    return (
      <span className={`${styles.status} ${statusClass}`}>{statusName}</span>
    );
  };

  return (
    <div className={applyItemCls} onClick={() => onClick(data, index)}>
      {/* 第一行 */}
      <div className={styles.itemHeader}>
        <div className={styles.titleArea}>
          <span className={serialCls}>
            编号
            {data.id || '暂无'}
          </span>
        </div>
        <div className={styles.tagArea}>
          {renderOrderStatus()}
          {
            _.map(tags, (tagProps) => <StatusTag {...tagProps} />)
          }
        </div>
      </div>
      {/* 第二行 */}
      <div className={secondLineCls}>
        <span className={typeCls}>{typeName}</span>
        <IfWrap when={!_.isEmpty(subTypeName)}>
          <span className={styles.subType}>-{subTypeName}</span>
        </IfWrap>
        <IfWrap when={!_.isEmpty(operateTypeName)}>
          <span className={styles.operateType}>
            <i className={styles.cutLine} />
            {operateTypeName}
          </span>
        </IfWrap>
        <div className={styles.date}>{secondLineValue}</div>
      </div>
      {/* 第三行 */}
      <IfWrap when={showThirdLine}>
        <div className={thirdLineCls}>
          <div className={styles.drafter}>
            <span className={styles.label}>拟稿人：</span>
            <span className={styles.drafterName} title={drafterName}>
              {drafterName}
            </span>
          </div>
          <div
            className={styles.customer}
            title={thirdLineValue.title}
          >
            {thirdLineValue.content}
          </div>
        </div>
      </IfWrap>
    </div>
  );
}

ApplyItem.propTypes = {
  // 申请单项的基本数据
  data: PropTypes.object.isRequired,
  // 数据在申请单列表数据中的小标索引值
  index: PropTypes.number.isRequired,
  // 操作类型，展示在申请单项第二行文字
  operateTypeName: PropTypes.string,
  // 当前页面的功能名称
  typeName: PropTypes.string,
  // 类型文字的样式类型
  typeNameClass: PropTypes.string,
  // 子类型，展示在申请单项第二行的类型文本
  subTypeName: PropTypes.string,
  // 申请单项的右侧展示状态标签 Props 的数组
  // [{ size: 'normal', type: 'processing',text: '处理中', style: {} }, ...]
  statusTags: PropTypes.array,
  // 是否选中状态,选中状态需要展示镂空样式
  active: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
  // 展示第二行中的信息，返回值为字符串
  showSecondLineInfo: PropTypes.func,
  // 展示第三行中的客户信息，由于部分申请单有多个客户，展示多个客户和单个不一样所以提供函数由用户自己控制
  // 其返回值为字符串
  showThirdLineInfo: PropTypes.func,
  // 是否显示第三行
  showThirdLine: PropTypes.bool,
};

ApplyItem.defaultProps = {
  typeName: '',
  subTypeName: '',
  typeNameClass: '',
  operateTypeName: '',
  active: false,
  showSecondLineInfo: _.noop,
  showThirdLineInfo: _.noop,
  showThirdLine: true,
  statusTags: [],
};
