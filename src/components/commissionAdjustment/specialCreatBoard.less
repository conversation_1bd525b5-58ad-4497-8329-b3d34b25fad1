.contentBox {
  width: 100%;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .@{ant-prefix}-input-group-wrapper {
      width: 300px;
    }

    .ant-bizDetailBlock {
      margin-top: 0;
    }
  }
}

.hint {
  color: #999;
}

.attachmentBox {
  padding-left: 14px;
  margin-bottom: 14px;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .@{ant-prefix}-uploader {
      min-height: max-content;

      .@{ant-prefix}-uploader-attachList {
        .@{ant-prefix}-uploader-file {
          margin: 0 20px 0 0;
        }

        .@{ant-prefix}-uploader-button {
          margin-bottom: 0;
          color: #108ee9;
        }
      }
    }
  }
}

.checkApprover {
  display: inline-block;
  width: 200px;
  height: 32px;
  border: 1px solid #ccc;
  cursor: pointer;
  line-height: 32px;
  padding-left: 8px;
  border-radius: 2px;

  &:hover {
    border-color: #49a9ee;
  }

  .searchIcon {
    color: #999;
    font-size: 14px;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    float: right;
  }
}

.selectWarp {
  width: 228px;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .@{ant-prefix}-select-selection {
      border-radius: 2px;
    }
  }
}

.specialApprovalModal {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .@{ant-prefix}-input-search-enter-button +.@{ant-prefix}-input-group-addon .@{ant-prefix}-input-search-button,
    .@{ant-prefix}-input-search-enter-button input +.@{ant-prefix}-input-group-addon .@{ant-prefix}-input-search-button {
      height: 30px;
    }
  }
}
