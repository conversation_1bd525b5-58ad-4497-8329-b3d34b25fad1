@import '~@/css/commonDetail.less';

.viewLink {
  color: #348cf0;
  cursor: pointer;
}

.detailBox {
  /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
    .ant-bizInfoCell-label-text {
      max-height: 40px;
    }

    .ant-bizInfoCell-label {
      display: flex;
      max-height: 40px;
      justify-content: right;
      height: auto;
    }
  }
}

.filedText {
  color: #f8505b;
  text-decoration: underline;
  cursor: pointer;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-tooltip-content {
      max-width: 257px;
    }

    .ant-tooltip-inner {
      padding: 14px;
      cursor: default;
      background-color: #fff;
    }
  }
}

.filedBox {
  padding: 14px;
  width: 257px;
  background: #fff;
  z-index: 10;
  box-shadow: 0 2px 6px 0 #ccc;
  text-align: left;
  cursor: default;

  h4 {
    margin-bottom: 14px;
    font-size: 14px;
    color: #333;
    font-weight: bold;
  }

  span.text {
    display: block;
    max-height: 123px;
    overflow: auto;
    line-height: 20px;
    font-size: 12px;
    color: #333;
    white-space: normal;
    word-break: break-all;
  }
}

.close {
  cursor: pointer;
  right: 14px;
  position: absolute;
  top: 12px;
  color: #666;
}

.dept {
  max-width: 90%;
}

.textOver {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toolTipBox {
  height: 0;
}

.tableModule {
  .filterWarp {
    display: flex;
    align-items: center;
    padding-bottom: 14px;
  }

  .filterItem {
    display: inline-block;
    margin-right: 10px;
  }
}
.detailTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .endBtn {
    border: 1px solid #108ee9;
    width: 154px;
    height: 30px;
    text-align: center;
    border-radius: 2px;
    svg {
      vertical-align: middle;
      margin-right: 2px;
      color: #108ee9;
    }
    span {
      font-weight: normal;
      vertical-align: middle;
    }
    &.disabledEndBtn {
      border: 1px solid #ccc;
      background-color: #eee;
      cursor: default;
      span {
        color: #999;
      }
    }
  }
}

.halfLine {
  display: inline-flex;
  align-items: center;

  .detailLabel {
    display: flex;
    justify-content: right;
  }

  .radioGroup {
    width: 120%;
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-radio-wrapper {
        margin-right: 10px;
      }
    }
  }

  .adjustRadioGroup {
    width: 90%;
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-radio-wrapper {
        margin-right: 10px;
      }
    }
  }
}

.custFilter {
  display: flex;
  justify-content: space-between;
  margin-bottom: 14px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-btn {
        border-radius: 2px;
      }
    }
}

.fileName {
  cursor: pointer;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #108ee9;
}

.popover {
  width: 274px;
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-popover-inner-content {
      padding: 10px !important;
    }
  }
}

.statusName {
  color: #4169c1;
}

.statusWarningIcon {
  position: relative;
  top: -2px;
  width: 28px;
  height: 28px;
}

.title {
  margin-bottom: 14px;
  padding: 0 0 10px;
  color: #333;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #ebebeb;

  .tipText {
    position: relative;
    display: inline-block;
    text-indent: 20px;
    font-size: 14px;
    color: #f0af41;
    margin-left: 10px;
    font-weight: normal;

    .warningIcon {
      position: absolute;
      left: -8px;
      top: -3px;
      width: 28px;
      height: 28px;
      margin-right: 5px;
    }
  }
}
