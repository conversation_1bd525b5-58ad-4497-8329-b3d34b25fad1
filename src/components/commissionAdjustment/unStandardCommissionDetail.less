.detailWrap {
  min-width: 650px;
  padding-bottom: 30px;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .@{ant-prefix}-bizDetailBlock-body {
      margin-bottom: 0;
    }

    .@{ant-prefix}-uploader {
      min-height: auto;
      padding: 0 0 6px 14px;

      .@{ant-prefix}-uploader-attachList {
        .@{ant-prefix}-uploader-file {
          margin: 0 20px 0 0;
        }

        .@{ant-prefix}-uploader-button {
          margin-bottom: 0;
          color: #108ee9;
        }
      }
    }

      .ant-bizInfoCell-label-text {
        max-height: 40px;
        width: calc(100% - 20px);
        text-align: right;
      }

      .ant-bizInfoCell-label {
        display: flex;
        max-height: 40px;
        justify-content: right;
        height: auto;
      }
  }

  .title {
    height: 44px;
    font-size: 14px;
    color: #333;
    line-height: 43px;
    text-align: left;
    font-weight: bold;
    border-bottom: 1px solid #ebebeb;
    box-sizing: border-box;
    margin-bottom: 14px;

    .tipText {
      position: relative;
      display: inline-block;
      text-indent: 20px;
      font-size: 14px;
      color: #f0af41;
      margin-left: 10px;
      font-weight: normal;

      .warningIcon {
        position: absolute;
        left: -8px;
        top: 8px;
        width: 28px;
        height: 28px;
        margin-right: 5px;
      }
    }
  }

  .custTable {
    width: 100%;
    overflow: auto;
  }

  .standardFlag {
    color: #108ee9;
  }

  .assetTotal {
    display: flex;
    align-items: center;
  }

  .infoIcon {
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-left: 8px;
  }
}

.tootipTitle {
  font-size: 12px;
  margin-bottom: 4px;
  font-weight: bold;
}
