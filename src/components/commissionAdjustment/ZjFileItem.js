/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 线上调佣-紫金理财计划书用到的附件展示
 * @Date: 2021-09-16 17:12:14
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-17 08:54:58
 */

import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { Popover } from 'antd';

import Icon from '@/components/common/Icon';
import FileInfo from './FileInfo';
import styles from './zjFileItem.less';

// 解决提示pop位置随页面滚动的问题
const getPopupContainer = () => document.querySelector(`.${styles.fileName}`);

export default function ZjFileItem(props) {
  const {
    fileInfo,
  } = props;
  if (_.isEmpty(fileInfo)) {
    return (
      <div className={styles.emptyFile}>
        <Icon type="fujian" className={styles.icon} />
        <span className={styles.text}>暂无数据</span>
      </div>
    );
  }
  // 紫金理财计划书只要取第一个附件就行，也只有一个
  const file = fileInfo[0] || {};
  const PopContent = (
    <FileInfo fileInfo={file} />
  );
  return (
    <Popover
      placement="topLeft"
      content={PopContent}
      trigger="click"
      overlayClassName={styles.popover}
      getPopupContainer={getPopupContainer}
    >
      <div className={styles.fileName}>
        <Icon type="fujian" className={styles.icon} />
        <span className={styles.text}>{file?.name}</span>
      </div>
    </Popover>
  );
}

ZjFileItem.propTypes = {
  fileInfo: PropTypes.array,
};

ZjFileItem.defaultProps = {
  fileInfo: [],
};
