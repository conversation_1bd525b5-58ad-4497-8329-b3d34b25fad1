/*
 * @Author: yanfaping
 * @Date: 2022-08-08 13:38:45
 * @Last Modified by: yanfaping
 * @Last Modified time: 2022-08-08 13:38:45
 * @description 服务订购头部过滤器
 */
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import PropTypes from 'prop-types';
import _ from 'lodash';
import moment from 'moment';
import { autobind } from 'core-decorators';
import { dva } from '@/helper';
import logable, { logCommon } from '@/decorators/logable';

import DateRangePick from '@lego/date/src';
import TreeFilter from '@lego/treeSelect/src';
import { SingleFilter, MoreFilter } from '@lego/filters/src';
import IFWrap from '@/components/common/IFWrap';
import HtFilter from '@/components/common/htFilter';

import styles from './index.less';

import {
  commission,
  DROPDOWN_STYLE,
  MORE_DROPDOWN,
  DEFAULT_OPTION,
  DATE_FORMAT,
  COM_SUBTYPES
} from './config';

const {
  subTypeData,
  statusData,
  adjustCommissionWayData,
  importantMatterApplyData,
  moreFilterData,
  isMultiComissionData
} = commission;

// 服务订购对应code码
const pageType = '02';

const effect = dva.generateEffect;

const mapStateToProps = (state) => ({
  // 审批人列表
  approvePersonList: state.app.approvePersonList,
  // 拟稿人列表
  drafterList: state.app.drafterList,
  // 部门
  custRange: state.app.custRange,
  // 客户列表
  custList: state.app.custList,
});

const mapDispatchToProps = {
  // 获取审批人列表
  getApprovePersonList: effect('app/getApprovePersonList', { loading: false }),
  // 获取拟稿人列表
  getDrafterList: effect('app/getDrafterList', { loading: false }),
  // 获取部门
  getCustRange: effect('app/getCustRange', { loading: false }),
  // 获取客户列表
  queryCustomerList: effect('app/queryCustomerList', { loading: false }),
};

@connect(mapStateToProps, mapDispatchToProps)
export default class CommissionHeader extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    // 获取客户列表
    queryCustomerList: PropTypes.func.isRequired,
    // 客户列表信息
    custList: PropTypes.array.isRequired,
    // 获取部门信息
    getCustRange: PropTypes.func.isRequired,
    // 部门信息
    custRange: PropTypes.array.isRequired,
    // 获取拟稿人列表
    getDrafterList: PropTypes.func.isRequired,
    // 拟稿人列表
    drafterList: PropTypes.array.isRequired,
    // 获取审批人列表
    getApprovePersonList: PropTypes.func.isRequired,
    // 审批人列表
    approvePersonList: PropTypes.array.isRequired,
    // 头部筛选回调
    filterCallback: PropTypes.func.isRequired,
  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  }

  componentDidMount() {
    this.props.getCustRange({
      type: pageType
    });
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '服务订购筛选-客户',
      value: '$args[0].value.custName',
    },
  })
  handleCustChange(item) {
    const { value: { custNumber, custName } } = item;
    this.props.filterCallback({
      custNumber,
      custName,
    });
  }

  @autobind
  handleCustSearch(value) {
    this.props.queryCustomerList({
      pageNum: 1,
      pageSize: 10,
      keyword: value,
    });
  }

  @autobind
  handleChangeSubType(option) {
    const { value } = option;
    const { location: { query } } = this.props;
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '服务订购筛选-子类型',
        value: '$args[0].value.value',
      },
    });
    const adjustCommissionWay = this.showCommissionWay(value) ? query?.adjustCommissionWay : '';
    this.props.filterCallback({
      subType: value,
      adjustCommissionWay,
    });
  }

  @autobind
  handleChangeAdjustCommissionWay(option) {
    const { value } = option;
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '服务订购筛选-调佣方式',
        value,
      },
    });
    this.props.filterCallback({
      adjustCommissionWay: value,
    });
  }

  @autobind
  handleChangeStatus(option) {
    const { value } = option;
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '服务订购筛选-状态',
        value: '$args[0].value.value',
      },
    });
    this.props.filterCallback({
      status: value,
    });
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '服务订购筛选-拟稿人',
      value: '$args[1].value.ptyMngName',
    },
  })
  handleDrafterChange(item) {
    const { value: { ptyMngId, ptyMngName } } = item;
    this.props.filterCallback({
      drafterId: ptyMngId,
      drafterName: ptyMngName,
    });
  }

  @autobind
  handleDrafterSearch(value) {
    this.props.getDrafterList({
      keyword: value,
      type: pageType,
    });
  }

  @autobind
  @logable({
    type: 'CalendarSelect',
    payload: {
      name: '服务订购筛选-申请时间',
      value: {
        min: (instance, args) => moment(args[0].value[0]).format(DATE_FORMAT),
        max: (instance, args) => moment(args[0].value[1]).format(DATE_FORMAT),
      }
    },
  })
  handleChangeAprovalTime(date) {
    const { value } = date;
    const startDate = value[0] || '';
    const endDate = value[1] || '';
    this.props.filterCallback({
      createTime: startDate,
      createTimeTo: endDate,
    });
  }

  @autobind
  setDisableStart(startDate) {
    return startDate >= moment();
  }

  @autobind
  setDisableEnd(startDate, endDate) {
    return endDate >= moment() || endDate < startDate;
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '服务订购筛选-部门',
      value: '$args[0]',
    },
  })
  handleSelectDepartment(value) {
    this.props.filterCallback({
      orgId: value,
    });
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '服务订购筛选-审批人',
      value: '$args[1].value.ptyMngName',
    },
  })
  handleApprovalChange(item) {
    const { value: { ptyMngId, ptyMngName } } = item;
    this.props.filterCallback({
      approvalId: ptyMngId,
      approvalName: ptyMngName,
    });
  }

  @autobind
  handleApprovalSearch(value) {
    this.props.getApprovePersonList({
      keyword: value,
      type: pageType,
    });
  }

  @autobind
  handleApplyIdEnterEvent({ key, value = '' }) {
    this.props.filterCallback({
      [key]: value,
    });
  }

  @autobind
  handleChangeImportantMatter(option) {
    const { value } = option;
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '服务订购筛选-OA重要事项特殊佣金申请',
        value: '$args[0].value.value',
      },
    });
    this.props.filterCallback({
      importantMatterApply: value,
    });
  }

  @autobind
  handleChangeIsMultiCommission(option) {
    const { value } = option;
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '服务订购筛选-是否多档佣金',
        value: '$args[0].value.value',
      },
    });
    this.props.filterCallback({
      isMultiComission: value,
    });
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '服务订购筛选-更多',
      value: '$args[0].id',
    },
  })
  handleChangeMoreFilter(obj) {
    const {
      location: {
        pathname,
        query,
      },
    } = this.props;
    const { id, isDeleteFilterFromLocation } = obj;
    const currentFilterItem = _.find(moreFilterData, (item) => item.key === id);
    const filterOption = currentFilterItem && currentFilterItem.filterOption;
    let finalQuery = query;
    if (isDeleteFilterFromLocation && currentFilterItem) {
      finalQuery = _.omit(query, filterOption);
    } else {
      const filterMap = _.reduce(filterOption,
        (filterQuery, itemQuery) => ({ ...filterQuery, [itemQuery]: '' }), {});
      finalQuery = _.merge(query, filterMap);
    }
    this.context.replace({
      pathname,
      query: finalQuery,
    });
  }

  @autobind
  selectMoreFilter() {
    const {
      location: { query },
    } = this.props;
    return _.map(moreFilterData, (itemFilter) => {
      const hasFilterItem = _.every(itemFilter.filterOption, (item) => _.hasIn(query, item));
      if (hasFilterItem) {
        return itemFilter.key;
      }
      return null;
    });
  }

  @autobind
  getFilterOnClose(key) {
    this.handleChangeMoreFilter({ id: key, isDeleteFilterFromLocation: true });
  }

  // 获取部门
  @autobind
  transformCustRangeData(list) {
    return list.map((item) => {
      const obj = {
        label: item.name,
        value: item.id,
        key: item.id,
      };
      if (item.children && item.children.length) {
        obj.children = this.transformCustRangeData(item.children);
      }
      return obj;
    });
  }

  @autobind
  getTreeData() {
    const { custRange } = this.props;
    if (_.isEmpty(custRange)) {
      return [DEFAULT_OPTION];
    }
    const treeCustRange = this.transformCustRangeData(custRange);
    return [DEFAULT_OPTION, ...treeCustRange];
  }

  // 判断子类型是否选择单佣金调整/批量佣金调整，是则展示调佣方式筛选项，否则不展示
  @autobind
  showCommissionWay(subType) {
    const { single, batch } = COM_SUBTYPES;
    return _.includes([single, batch], subType);
  }

  render() {
    const {
      location: { query },
      custList,
      drafterList,
      approvePersonList,
    } = this.props;
    const {
      custNumber,
      custName,
      drafterId,
      drafterName,
      approvalId,
      approvalName,
      orgId,
      subType,
      status,
      createTime,
      createTimeTo,
      applyId,
      // 调佣方式
      adjustCommissionWay,
      // OA重要事项特殊佣金申请
      importantMatterApply,
      // 是否多档佣金
      isMultiComission,
    } = query || {};
    // 申请时间-开始时间
    const startDate = createTime ? moment(createTime, DATE_FORMAT) : null;
    // 申请时间-结束时间
    const endDate = createTimeTo ? moment(createTimeTo, DATE_FORMAT) : null;
    // 客户信息
    const customer = custNumber ? [custNumber, custName] : ['', ''];
    // 拟稿人
    const drafter = drafterId ? [drafterId, drafterName] : ['', ''];
    // 审批人
    const approval = approvalId ? [approvalId, approvalName] : ['', ''];
    return (
      <div className={styles.filterArea}>
        <div className={styles.filterLeft}>
          <div className={styles.filterItem}>
            <SingleFilter
              filterId="custId"
              filterName="客户"
              placeholder="经济客户号或客户名称"
              dataMap={['custId', 'custName']}
              data={custList}
              value={customer}
              onChange={this.handleCustChange}
              onInputChange={this.handleCustSearch}
              dropdownStyle={DROPDOWN_STYLE}
              showSearch
              needItemObj
            />
          </div>
          <div className={styles.filterItem}>
            <SingleFilter
              filterId="subType"
              filterName="子类型"
              dataMap={['value', 'label']}
              data={subTypeData}
              value={subType}
              dropdownStyle={{
                width: 170,
              }}
              onChange={this.handleChangeSubType}
            />
          </div>
          <IFWrap when={this.showCommissionWay(subType)}>
            <div className={styles.filterItem}>
              <SingleFilter
                filterId="adjustCommissionWay"
                filterName="调佣方式"
                dataMap={['value', 'label']}
                data={adjustCommissionWayData}
                value={adjustCommissionWay}
                onChange={this.handleChangeAdjustCommissionWay}
              />
            </div>
          </IFWrap>
          <div className={styles.filterItem}>
            <SingleFilter
              filterId="status"
              filterName="状态"
              dataMap={['value', 'label']}
              data={statusData}
              value={status}
              onChange={this.handleChangeStatus}
            />
          </div>
          <div className={styles.filterItem}>
            <SingleFilter
              filterId="ptyMngId"
              filterName="拟稿人"
              placeholder="工号或名称"
              dataMap={['ptyMngId', 'ptyMngName']}
              data={drafterList}
              value={drafter}
              onChange={this.handleDrafterChange}
              onInputChange={this.handleDrafterSearch}
              dropdownStyle={DROPDOWN_STYLE}
              showSearch
              needItemObj
            />
          </div>
          <div className={styles.filterItem}>
            <DateRangePick
              filterId="aprovalTime"
              filterName="申请时间"
              filterValue={[startDate, endDate]}
              onChange={this.handleChangeAprovalTime}
              disabledStart={this.setDisableStart}
              disabledEnd={this.setDisableEnd}
              allowClear
            />
          </div>
          <div className={styles.filterItem}>
            <SingleFilter
              filterId="isMultiComissionData"
              filterName="是否多档佣金调整流程"
              dataMap={['value', 'label']}
              data={isMultiComissionData}
              value={isMultiComission}
              onChange={this.handleChangeIsMultiCommission}
              // onClose={() => this.getFilterOnClose('importantMatterApply')}
              // isCloseable
            />
          </div>
          <IFWrap when={!_.isNil(orgId)}>
            <div className={styles.filterItem}>
              <TreeFilter
                key="id"
                filterId="id"
                filterName="部门"
                searchPlaceholder="搜索"
                value={orgId || ''}
                treeData={this.getTreeData()}
                onSelect={this.handleSelectDepartment}
                onClose={() => this.getFilterOnClose('orgId')}
                isCloseable
                showSearch
                treeDefaultExpandAll
              />
            </div>
          </IFWrap>
          <IFWrap when={!_.isNil(approvalId)}>
            <div className={styles.filterItem}>
              <SingleFilter
                filterId="ptyMngId"
                filterName="审批人"
                placeholder="工号或名称"
                dataMap={['ptyMngId', 'ptyMngName']}
                data={approvePersonList}
                value={approval}
                onChange={this.handleApprovalChange}
                onInputChange={this.handleApprovalSearch}
                onClose={() => this.getFilterOnClose('approvalId')}
                dropdownStyle={DROPDOWN_STYLE}
                showSearch
                needItemObj
                isCloseable
              />
            </div>
          </IFWrap>
          <IFWrap when={!_.isNil(applyId)}>
            <div className={styles.filterItem}>
              <HtFilter
                filterId="applyId"
                filterName="编号"
                placeholder="输入编号后按回车"
                type="input"
                value={applyId}
                onPressEnter={this.handleApplyIdEnterEvent}
                onClose={() => this.getFilterOnClose('applyId')}
                isCloseable
              />
            </div>
          </IFWrap>
          <IFWrap when={!_.isNil(importantMatterApply)}>
            <div className={styles.filterItem}>
              <SingleFilter
                filterId="importantMatterApply"
                filterName="OA重要事项特殊佣金申请"
                dataMap={['value', 'label']}
                data={importantMatterApplyData}
                value={importantMatterApply}
                onChange={this.handleChangeImportantMatter}
                onClose={() => this.getFilterOnClose('importantMatterApply')}
                isCloseable
              />
            </div>
          </IFWrap>
        </div>
        <div className={styles.moreItem}>
          <MoreFilter
            data={moreFilterData}
            value={this.selectMoreFilter()}
            onChange={this.handleChangeMoreFilter}
            dropdownStyle={MORE_DROPDOWN}
          />
        </div>
      </div>
    );
  }
}
