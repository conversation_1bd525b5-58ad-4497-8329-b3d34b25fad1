/*
 * @Author: yanfaping
 * @Date: 2022-08-08 13:38:45
 * @Last Modified by: yanfaping
 * @Last Modified time: 2022-08-08 13:38:45
 * @description 服务订购头部过滤器-配置项
 */
// 服务订购涉及筛选头部-配置项
export const commission = {
  subTypeData: [
    {
      show: true,
      label: '不限',
      value: '',
    },
    {
      show: true,
      label: '佣金调整',
      value: '0201',
    },
    {
      show: true,
      label: '批量佣金调整',
      value: '0202',
    },
    {
      show: true,
      label: '自动外呼调佣申请',
      value: '0205',
    },
    {
      show: true,
      label: '特殊佣金调整',
      value: '0206',
    },
    {
      show: true,
      label: '非标客户特殊佣金调整',
      value: '0209',
    },
    {
      show: true,
      label: '佣金授权申请',
      value: '0220',
    },
    {
      show: true,
      label: '资讯订阅',
      value: '0203',
    },
    {
      show: true,
      label: '资讯退订',
      value: '0204',
    },
  ],
  statusData: [
    {
      show: true,
      label: '不限',
      value: '',
    },
    {
      show: true,
      label: '处理中',
      value: '01',
    },
    {
      show: true,
      label: '完成',
      value: '02',
    },
    {
      show: true,
      label: '终止',
      value: '03',
    },
    {
      show: true,
      label: '驳回',
      value: '04',
    },
  ],
  adjustCommissionWayData: [
    {
      show: true,
      label: '不限',
      value: '',
    },
    {
      show: true,
      label: '线上',
      value: 'ONLINE',
    },
    {
      show: true,
      label: '线下',
      value: 'OFFLINE',
    },
  ],
  importantMatterApplyData: [
    {
      show: true,
      label: '不限',
      value: '',
    },
    {
      show: true,
      label: '是',
      value: 'Y',
    },
    {
      show: true,
      label: '否',
      value: 'N',
    },
  ],
  moreFilterData: [
    {
      key: 'orgId',
      value: '部门',
      filterOption: ['orgId'],
    },
    {
      key: 'approvalId',
      value: '审批人',
      filterOption: ['approvalId', 'approvalName'],
    },
    {
      key: 'applyId',
      value: '编号',
      filterOption: ['applyId', 'authFlag', 'disableFilterId'], // 用于写入URL的参数key
    },
    {
      key: 'importantMatterApply',
      value: 'OA重要事项特殊佣金申请',
      filterOption: ['importantMatterApply'],
    },
  ],
  isMultiComissionData: [
    {
      show: true,
      label: '不限',
      value: '',
    },
    {
      show: true,
      label: '是',
      value: 'Y',
    },
    {
      show: true,
      label: '否',
      value: 'N',
    },
  ],
};
// 下拉框样式配置
export const DROPDOWN_STYLE = {
  maxHeight: 324,
  overflowY: 'auto',
  width: 250,
};
// 更多下拉框样式配置
export const MORE_DROPDOWN = {
  minWidth: 150,
  width: 'auto'
};
// 部门-默认下拉选项配置
export const DEFAULT_OPTION = {
  label: '不限',
  value: '',
  key: 0,
};
// 日期格式化配置
export const DATE_FORMAT = 'YYYY/MM/DD';
// 佣金调整子类型配置
export const COM_SUBTYPES = {
  single: '0201', // 单佣金调整
  batch: '0202', // 批量佣金调整
};
