/**
 * @file components/commissionAdjustment/SpecialOtherCommissionSelect.js
 * @description 新建特殊佣金调整中其他佣金费率下拉框
 * <AUTHOR>
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-06-02 13:41:49
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import newConfirm from '@/components/common/newUI/confirm';
import { specialOtherCommission } from '@/config/otherCommissionDictionary';
import Select from '../common/Select';
import styles from './specialOtherCommissionSelect.less';
import logable from '../../decorators/logable';

const minimumChargeMap = ['normalMinimumCharge', 'creditMinimumCharge'];

export default class SpecialOtherCommissionSelect extends PureComponent {
  static propTypes = {
    // 佣金名称
    label: PropTypes.string,
    // 佣金可选列表
    options: PropTypes.array,
    // 切换下拉框选项值
    onChange: PropTypes.func.isRequired,
    // 客户信息
    custInfo: PropTypes.object,
  }

  static defaultProps = {
    label: '',
    options: [],
    custInfo: {},
  }

  // 将所选的option的总资产、近一年股基交易量、近一年股基毛佣金与所选客户的值做比较
  @autobind
  isPassThreshold(value, option) {
    if (_.isString(value) && _.isEmpty(value)) {
      return true;
    }
    // 首先判断需要需要对哪些阈值进行判断
    const thresholdKeys = ['totalAsset', 'yearStockExchange', 'yearStockProfit'];
    const thresholdObject = _.reduce(option, (result, optionValue, key) => {
      if (_.includes(thresholdKeys, key) && !_.isNil(option[key])) {
        const item = result;
        item[key] = optionValue;
        return item;
      }
      return result;
    }, {});
    // 判断如果没有需要判断的阈值对象（即三个阈值指标都是null），则该选项可选
    if (_.isEmpty(thresholdObject)) {
      return true;
    }
    // 判断阈值与客户指标的关系,只要有一个指标满足就可选该选项
    const canSelect = _.some(thresholdObject, (optionValue, key) => {
      const custValue = this.props.custInfo[key];
      return custValue >= optionValue;
    });
    return canSelect;
  }

  // 判断客户费率是否在可选列表中
  @autobind
  isInOptions(custValue) {
    const { options } = this.props;
    const option = _.find(options, (item) => item.value === custValue);

    return !_.isEmpty(option);
  }

  // 判断所选的option是否大于客户当前的佣金费率,
  // 返回 true, 表示可以通过校验
  @autobind
  isPassTooHighCompare(name, value, option) {
    const { custInfo } = this.props;
    // 所选佣金对应的客户当前佣金值
    const currentValue = custInfo[name];

    if (_.isEmpty(currentValue)) {
      // 客户信息为空，不做校验
      return true;
    }

    // 如果客户对应的佣金费 code 值, 不在可选的费率选项中就不做过高的校验
    if (!this.isInOptions(currentValue)) {
      return true;
    }

    return +value <= +currentValue;
  }

  @autobind
  judgeThreshold(name, value, option) {
    let canChange = true;
    let content = '';
    if (!this.isPassThreshold(value, option)) { // option阈值判断
      canChange = false;
      content = '客户暂不符合申请该档佣金条件，请选择其他佣金档位';
    } else if (!this.isPassTooHighCompare(name, value, option)) { // option与客户当前佣金费率比较
      const commissionName = _.find(specialOtherCommission, ['paramName', name])?.brief || '';
      canChange = false;
      content = `${commissionName}本次新设费率值高于客户当前费率，请确认。`;
    }
    if (canChange) {
      this.props.onChange(name, value, option);
    } else {
      newConfirm({
        title: '提示',
        content,
        okText: '确定',
        cancelVisible: false,
        onOk: () => false,
      });
    }
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '$props.label',
      value: '$args[1]',
    },
  })
  onChange(name, value, option) {
    const { custInfo } = this.props;
    // 普通账户收取最低费用、信用账户收取最低费用不做比较
    const isMinimumCharge = _.includes(minimumChargeMap, name);
    if (isMinimumCharge) {
      this.props.onChange(name, value, option);
      return;
    }

    if (!_.isEmpty(custInfo)) {
      // NOTE: 此处需要针对具体的客户数据进行阈值、与之前的值比较大小判断
      this.judgeThreshold(name, value, option);
    } else {
      this.props.onChange(name, value, option);
    }
  }

  render() {
    const {
      label,
      options,
      ...resetProps
    } = this.props;
    const newOptions = _.cloneDeep(options);
    newOptions.unshift({
      label: '请选择',
      value: '',
      show: true,
    });

    return (
      <div className={styles.lineInputWrap}>
        <div className={styles.label}>
          {label}
          <span className={styles.colon}>:</span>
        </div>
        <div className={`${styles.componentBox} ${styles.selectBox}`}>
          <Select
            {...resetProps}
            data={newOptions}
            onChange={this.onChange}
          />
        </div>
      </div>
    );
  }
}
