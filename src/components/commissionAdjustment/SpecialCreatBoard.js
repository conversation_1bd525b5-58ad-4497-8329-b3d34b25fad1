/**
 * @Author: yanfaping
 * @Date: 2021-02-04 14:49:33
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-04-20 16:52:49
 * @description 特殊佣金申请内容区域
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'dva';
import { autobind } from 'core-decorators';

import Uploader from '@/newUI/storageUpload';
import { data as dh, dva, emp } from '@/helper';
import {
  Icon,
} from 'antd';
import {
  InfoCell,
  InfoGroup,
  DetailBlock,
} from '@crm/biz-ui';
import _ from 'lodash';
import Select from '@/components/common/Select';
import confirm from '@/components/common/confirm';
import newConfirm from '@/components/common/newUI/confirm';
import logable, { logCommon, logPV } from '@/decorators/logable';
import ChoiceApproverBoard from './ChoiceApproverBoard';
import OtherCommissionSelectList from './SpecialOtherCommissionSelectList';
import { APPROVAL_COLUMNS, DEFAULT_VALUE, FLOW_BPMNAME } from './config';
import styles from './specialCreatBoard.less';

// 使用helper里面封装的生成effects的方法
const dispatch = dva.generateEffect;

const mapStateToProps = (state) => ({
  // 审批人员列表
  approverList: state.commission.specialApproverList,
});

const mapDispatchToProps = {
  // 客户当前股基佣金率
  queryCustCurrentCommission: dispatch('commission/queryCustCurrentCommission', { loading: false }),
  // 查询下一步审批人
  queryFlowButton: dispatch('commission/querySpecialCommissionApprovalList'),
  // 清除Redux
  clearRedux: dispatch('commission/clearReduxState', { loading: false }),
};

@connect(mapStateToProps, mapDispatchToProps, null, { withRef: true })
export default class SpecialCreatBoard extends PureComponent {
  static propTypes = {
    // 其他佣金费率选项
    otherRations: PropTypes.object,
    // 目标股基佣金率
    specialGJCommission: PropTypes.object,
    // 客户当前其他佣金费率
    currentOtherCommission: PropTypes.object,
    // 客户信息
    customer: PropTypes.object,
    // 查询客户的当前股基佣金率
    queryCustCurrentCommission: PropTypes.func.isRequired,
    custCurrentCommission: PropTypes.object.isRequired,
    // 查询下一步审批人
    queryFlowButton: PropTypes.func.isRequired,
    approverList: PropTypes.array.isRequired,
    // 清除Redux
    clearRedux: PropTypes.func.isRequired,
    // 特殊佣金提交按钮置灰
    handleSubmitBtnDisabled: PropTypes.func.isRequired,
  }

  static defaultProps = {
    customer: {},
    otherRations: {},
    specialGJCommission: {},
    currentOtherCommission: {},
  }

  constructor(props) {
    super(props);
    this.state = {
      // 用于重置上传组件
      uploaderUUID: dh.uuid(),
      // 审批人
      approverName: '',
      // 审批人id
      approverId: '',
      // 附件信息
      attachList: [],
      // 是否显示审批弹框，默认false
      showApprovalModal: false,
      // 在目标股基佣金率输入框填的值
      inputCommission: '',
      // 其他佣金费率值
      otherCommission: {},
    };
  }

  componentDidMount() {
    this.props.handleSubmitBtnDisabled(true);
  }

  componentDidUpdate(prevProps) {
    const { customer, queryCustCurrentCommission, queryFlowButton } = this.props;
    if (!_.isEqual(customer, prevProps.customer)) {
      this.resetData();
      if (!_.isEmpty(customer)) {
        queryCustCurrentCommission({
          brokerNumber: customer.custId,
        });
        queryFlowButton({
          bpmnName: FLOW_BPMNAME[customer.custType] || '',
          orgId: emp.getOrgId(),
        });
      }
    }
  }

  @autobind
  handleChangeGJCommission(name, value, option) {
    // 如果没有选择客户，提示用户选择客户
    const {
      customer,
      custCurrentCommission: {
        currentCommission = '',
      },
    } = this.props;
    if (_.isEmpty(customer)) {
      confirm({
        content: '请先选择需要申请的客户',
      });
    } else {
      const {
        openOrgId,
        serviceOrgId,
        deptCurrentPrice,
        totalAsset,
        yearStockExchange,
        yearStockProfit,
      } = customer;
      if (_.isEmpty(value)) {
        this.setInputCommission();
      } else if (openOrgId !== serviceOrgId && deptCurrentPrice > +value) {
        newConfirm({
          title: '提示',
          content: '当前客户开户与服务营业部不一致，且开户营业部所在地区设置了佣金限价，当前佣金不可选',
          okText: '确定',
          cancelVisible: false,
          onOk: () => false,
        });
      } else if (totalAsset < option.totalAsset
        && yearStockExchange < option.yearStockExchange
        && yearStockProfit < option.yearStockProfit) {
        newConfirm({
          title: '提示',
          content: '该客户暂不满足特殊佣金申请要求',
          okText: '确定',
          cancelVisible: false,
          onOk: () => false,
        });
      } else if (!_.isNull(currentCommission) && +value > currentCommission) {
        // 新佣金与客户当前佣金对比，如新设置的目标股基佣金率高于客户当前佣金，则弹出提示
        newConfirm({
          title: '提示',
          content: '目标股基佣金率本次新设费率值高于客户当前费率，请确认。',
          okText: '确定',
          cancelVisible: false,
          onOk: () => false,
        });
      } else {
        this.setState({
          inputCommission: value,
        }, () => {
          this.setInputCommission(value);
        });
      }
      logCommon({
        type: 'Select',
        payload: {
          name: '服务订购-特殊佣金调整-目标股基佣金率',
          type: '服务订购',
          subType: '特殊佣金调整',
          value,
        },
      });
    }
  }

  // 目标股基佣金率
  @autobind
  setInputCommission(value = '') {
    this.setState({
      inputCommission: value,
    }, () => {
      const btnDisabled = this.checkOtherComsIsNull();
      this.props.handleSubmitBtnDisabled(btnDisabled);
    });
  }

  // 获取用户选择的数据
  @autobind
  getData() {
    return this.state;
  }

  // 设置其他佣金率的值
  @autobind
  setOtherCommission(name, value = '') {
    this.setState((preState) => ({
      otherCommission: {
        ...preState.otherCommission,
        [name]: value,
      },
    }), () => {
      const btnDisabled = this.checkOtherComsIsNull();
      this.props.handleSubmitBtnDisabled(btnDisabled);
      logCommon({
        type: 'Select',
        payload: {
          name: '服务订购-特殊佣金调整-其他佣金费率',
          type: '服务订购',
          subType: '特殊佣金调整',
          value,
        },
      });
    });
  }

  // 上传成功后的数据
  @autobind
  handleUploadSuccess(apiResult) {
    // 新的存储网关附件上传接口，上传后，直接将附件信息返回
    const { resultData } = apiResult;
    const { attachList } = this.state;
    this.setState({
      attachList: [...attachList, resultData],
    });
    return Promise.resolve(resultData);
  }

  // 删除已经上传成功的附件
  @autobind
  handleFileRemove({ attachId }) {
    const { attachList } = this.state;
    // 因为附件在本组件里面只保存上传成功的，所以不需要判断上传失败的附件的删除处理
    // 删除上传失败的附件由 Uploader 组件内部处理
    const notDealAttaches = _.filter(attachList, (item) => {
      if (!_.isEmpty(attachId)) {
        return item.attachId !== attachId;
      }
      return true;
    });
    this.setState({
      attachList: notDealAttaches,
    });
    // 默认表示删除成功
    return Promise.resolve(true);
  }

  @autobind
  handlePreviewPdf(fileInfo) {
    logCommon({
      type: 'Click',
      payload: {
        name: '服务订购-特殊佣金调整-PDF预览',
        value: JSON.stringify(fileInfo)
      }
    });
    const { attachId } = fileInfo;
    const host = window.location.origin;
    const pdrviewerUrl = `${host}/fspa/fsp-host/static/public/pdf/web/viewer.html`;
    const params = `${host}/fspa/mcrm/api/storage/download?fileId=${attachId}`;
    const url = `${pdrviewerUrl}?&file=${encodeURIComponent(params)}`;
    window.open(
      url,
      '_blank'
    );
  }

  @autobind
  @logPV({ pathname: '/modal/spcialApprovalModal', title: '服务订购-特殊佣金调整-新建-选择审批人' })
  handleOpenApproverModal() {
    this.setState({
      showApprovalModal: true,
    });
  }

  @autobind
  @logable({ type: 'Click', payload: { name: '服务订购-特殊佣金调整-新建-关闭审批人弹框' } })
  handleCloseApproverModal() {
    this.setState({
      showApprovalModal: false,
    });
  }

  @autobind
  @logable({ type: 'Select', payload: { name: '服务订购-特殊佣金调整-新建-选择审批人' } })
  handleConfirmApproverModal(approver) {
    this.setState({
      approverName: approver.empName,
      approverId: approver.empId,
    });
  }

  // 获取其他佣金率数据
  @autobind
  getCommission(custType, list) {
    return custType ? list[custType] : [];
  }

  // 目标股基佣金率数据
  @autobind
  targetGjCommission(custType, data) {
    const defaultOptions = [{ label: '请选择', value: '' }];
    if (custType && !_.isEmpty(data)) {
      const options = data[custType] || [];
      if (!_.isEmpty(options)) {
        const newOptions = options.map((item) => ({
          label: item.codeDesc,
          value: item.codeValue,
          ...item,
        }));
        return _.concat(defaultOptions, newOptions);
      }
      return defaultOptions;
    }
    return defaultOptions;
  }

  @autobind
  clearRedux() {
    this.props.clearRedux({
      clearList: [
        { name: 'specialGJCommission' },
        { name: 'currentOtherCommission' },
      ],
    });
  }

  @autobind
  resetData() {
    this.clearRedux();
    // 用户选择的其他佣金率的值
    this.setState({
      approverName: '',
      approverId: '',
      inputCommission: '',
      uploaderUUID: dh.uuid(),
      otherCommission: {},
      attachList: [],
    }, () => {
      this.props.handleSubmitBtnDisabled(true);
    });
  }

  // 这里检查其他佣金费率是否为null或者目标股基佣金为空
  @autobind
  checkOtherComsIsNull() {
    const { inputCommission, otherCommission } = this.state;
    const isNull = _.isEmpty(otherCommission) || _.every(otherCommission, _.isEmpty);
    return isNull && _.isEmpty(inputCommission);
  }

  render() {
    const {
      otherRations,
      customer,
      specialGJCommission,
      approverList,
      custCurrentCommission,
      currentOtherCommission,
    } = this.props;
    const {
      uploaderUUID,
      approverName,
      approverId,
      showApprovalModal,
      inputCommission,
      otherCommission,
    } = this.state;
    // 客户类型
    const custType = customer?.custType || '';
    // 其它佣金率
    const specialOtherCom = this.getCommission(custType, otherRations);
    // 目标股基佣金率
    const specialGJCom = this.targetGjCommission(custType, specialGJCommission);
    // 当前股基佣金率
    const newCurrentCom = !_.isEmpty(customer) && !_.isNil(custCurrentCommission?.currentCommission)
      ? `${custCurrentCommission?.currentCommission}‰` : DEFAULT_VALUE;
    return (
      <div className={styles.contentBox}>
        {/* 股基佣金费率 */}
        <DetailBlock title="股基佣金费率">
          <InfoGroup labelWidth="140px">
            <InfoCell
              span={50}
              label="当前股基佣金率"
              content={newCurrentCom}
            />
            <InfoCell
              span={50}
              label="目标股基佣金率"
              style={{ alignItems: 'center' }}
              content={(
                <Select
                  name="newComm"
                  data={specialGJCom}
                  value={inputCommission}
                  onChange={this.handleChangeGJCommission}
                  className={styles.selectWarp}
                  needShowKey={false}
                />
              )}
            />
          </InfoGroup>
        </DetailBlock>
        <DetailBlock title="佣金费率选择">
          {
            _.isEmpty(customer) ? null
              : (
                <OtherCommissionSelectList
                  otherRatios={specialOtherCom}
                  onChange={this.setOtherCommission}
                  baseCommission={otherCommission}
                  custInfo={{ ...customer, ...currentOtherCommission }}
                />
              )
          }
        </DetailBlock>
        <DetailBlock title={(
          <>
            附件信息
            <span className={styles.hint}>(必传)</span>
          </>
          )}
        >
          <div className={styles.attachmentBox}>
            <Uploader
              key={uploaderUUID}
              previewable
              onSuccess={this.handleUploadSuccess}
              onRemove={this.handleFileRemove}
              onViewFile={this.handlePreviewPdf}
            />
          </div>
        </DetailBlock>
        <DetailBlock title="审批人">
          <InfoGroup labelWidth="140px">
            <InfoCell
              label="选择审批人"
              style={{ lineHeight: '30px' }}
              content={(
                <div className={styles.checkApprover} onClick={this.handleOpenApproverModal}>
                  {approverName === '' ? '' : `${approverName}(${approverId})`}
                  <div className={styles.searchIcon}>
                    <Icon type="search" />
                  </div>
                </div>
              )}
            />
          </InfoGroup>
        </DetailBlock>
        <ChoiceApproverBoard
          visible={showApprovalModal}
          approverList={approverList}
          onClose={this.handleCloseApproverModal}
          onOk={this.handleConfirmApproverModal}
          column={APPROVAL_COLUMNS}
          rowKey="empId"
        />
      </div>
    );
  }
}
