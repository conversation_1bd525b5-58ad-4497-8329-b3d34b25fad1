/**
 * @Author: sunweibin
 * @Date: 2017-11-08 15:51:25
 * @Last Modified by: sunweibin
 * @Last Modified time: 2020-03-24 10:33:19
 * @description 资讯订阅新建的内容组件
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { connect } from 'dva';
import { Icon } from 'antd';
import _ from 'lodash';

import confirm from '../common/confirm';
import Uploader from '@/newUI/upload';
import { emp, data as dataHelper } from '@/helper';
import ChoiceApproverBoard from './ChoiceApproverBoard';
import InfoTitle from '../common/InfoTitle';
import Transfer from '../common/biz/TableTransfer';
import CommissionLine from './CommissionLine';
import ThreeMatchTip from './ThreeMatchTip';
import { seibelConfig } from '../../config';
import {
  pagination,
  subScribeProColumns,
} from './commissionTransferHelper/transferPropsHelper';
import { getApprovalBtnID, changeSubmitSubProList } from './commissionCreateCommon/common';
import logable, { logPV } from '../../decorators/logable';

import styles from './createNewApprovalBoard.less';

const { comsubs: commadj } = seibelConfig;

// 表示为最低风险的产品
const PRODCUT_LOW_RISK_LEVEL = '5';
// 低风险保守型客户
const CUST_LOW_RISK_LEVEL = '704040';

const getDataFunction = (loading, type) => query => ({
  type,
  payload: query || {},
  loading,
});

// redux effects方法
const effects = {
  approver: 'commission/getAprovalUserList',
  threeMatchInfo: 'commission/queryThreeMatchInfo',
  clearReduxState: 'commission/clearReduxState',
  deleteAttachment: 'app/deleteAttachment',
};

// redux store
const mapStateToProps = state => ({
  // 客户与产品的三匹配信息
  threeMatchInfo: state.commission.threeMatchInfo,
  // 审批人员列表
  approvalUserList: state.commission.approvalUserList,
});

// redux dispatch
const mapDispatchToProps = {
  // 查询产品与客户的三匹配信息
  queryThreeMatchInfo: getDataFunction(false, effects.threeMatchInfo),
  // 清空redux保存的state
  clearReduxState: getDataFunction(false, effects.clearReduxState),
  // 查询审批人员列表
  getAprovalUserList: getDataFunction(false, effects.approver),
  // 删除附件
  deleteAttachment: getDataFunction(true, effects.deleteAttachment),
};

@connect(mapStateToProps, mapDispatchToProps, null, { withRef: true })
export default class SubscribeCreateBoard extends PureComponent {
  static propTypes = {
    // 登录人信息
    empInfo: PropTypes.object,
    // 客户
    customer: PropTypes.object,
    // 新建资讯订阅可选产品列表
    subscribelProList: PropTypes.array.isRequired,
    // 产品与客户的三匹配信息
    threeMatchInfo: PropTypes.object.isRequired,
    queryThreeMatchInfo: PropTypes.func.isRequired,
    // 审批人列表
    approvalUserList: PropTypes.array.isRequired,
    getAprovalUserList: PropTypes.func.isRequired,
    clearReduxState: PropTypes.func.isRequired,
    // 删除附件
    deleteAttachment: PropTypes.func.isRequired,
  }

  static defaultProps = {
    customer: {},
    empInfo: {},
  }

  constructor(props) {
    super(props);
    this.state = {
      // 客户选择的产品列表
      subProList: [],
      // 三匹配信息
      subscribelProductMatchInfo: [],
      // 选择审批人的弹出层显示或隐藏
      choiceApprover: false,
      // 审批人
      approverName: '',
      approverId: '',
      // 附件编号
      attachment: '',
      canShowAppover: false,
      // 用于重置上传组件
      uploaderUUID: dataHelper.uuid(),
    };
  }

  componentDidMount() {
    // 进入页面查一把子类型的审批人列表
    const { empInfo: { empNum } } = this.props;
    const btnId = getApprovalBtnID(commadj.subscribe);
    this.props.getAprovalUserList({
      loginUser: empNum,
      btnId,
    });
  }

  // 获取用户选择的数据
  @autobind
  getData() {
    const {
      subProList,
      subscribelProductMatchInfo,
      approverId,
      attachment,
      canShowAppover,
    } = this.state;
    const newSubProList = changeSubmitSubProList(
      subProList,
      subscribelProductMatchInfo,
    );
    return {
      newSubProList,
      approverId,
      attachment,
      canShowAppover,
    };
  }

  @autobind
  clearRedux() {
    // 清空redux的state
    this.props.clearReduxState({
      clearList: [
        { name: 'threeMatchInfo', value: {} },
        { name: 'subscribelProList', value: [] },
      ],
    });
  }

  @autobind
  resetData() {
    this.clearRedux();
    this.setState({
      subProList: [],
      subscribelProductMatchInfo: [],
      choiceApprover: false,
      approverName: '',
      approverId: '',
      attachment: '',
      uploaderUUID: dataHelper.uuid(),
    });
  }

  // 咨讯订阅选择产品时进行三匹配
  @autobind
  merge3MatchSubInfo() {
    const { threeMatchInfo: info } = this.props;
    const {
      riskRankMhrt, investProdMhrt, investTypeMhrt, productCode
    } = info;
    const matchInfo = {
      productCode,
      riskMatch: riskRankMhrt,
      prodMatch: investTypeMhrt,
      termMatch: investProdMhrt,
    };
    const { subscribelProductMatchInfo } = this.state;
    const exsit = _.findIndex(subscribelProductMatchInfo, o => o.productCode === productCode) > -1;
    if (!exsit) {
      this.setState({
        subscribelProductMatchInfo: [matchInfo, ...subscribelProductMatchInfo],
      });
    }
  }


  @autobind
  changeSubscriProList(product) {
    const { prodRowId, prodId, prodName } = product;
    return {
      key: prodRowId,
      // 产品代码
      prodCode: prodId,
      // 产品名称
      prodName,
      ...product,
    };
  }

  // 重组资讯订阅可选产品List
  @autobind
  createSubscribelProList(data) {
    const newSubscriProList = data.map((product) => {
      const newSubscribel = this.changeSubscriProList(product);
      const { children } = product;
      if (!_.isEmpty(children)) {
        newSubscribel.children = children.map((item) => {
          const { prodRowid } = item;
          return {
            key: prodRowid,
            xDefaultOpenFlag: 'Y',
            canNotBeChoice: 'Y',
            ...item,
          };
        });
      }
      return newSubscribel;
    });
    return newSubscriProList;
  }

  // 资讯订阅调整穿梭变化的时候处理程序
  @autobind
  @logable({
    type: 'ViewItem',
    payload: {
      name: '资讯订阅产品',
    },
  })
  handleSubscribelTransferChange(flag, item, array) {
    this.setState({
      subProList: array,
    });
    const appList = array.map(pro => pro.approvalFlg);
    const approvFlag = _.includes(appList, 'Y');
    if (approvFlag) {
      this.setState({
        canShowAppover: true,
      });
    } else {
      this.setState({
        canShowAppover: false,
        approverId: '',
        approverName: '',
      });
    }
    const { prodCode, channelTypeProduct } = item;
    if (flag === 'add' && !channelTypeProduct) {
      // 如果是左侧列表添加到右侧列表,则需要查询三匹配信息
      // NOTE: 新增，如果是通道类产品，则不需要进行三匹配信息
      const { id, custType } = this.props.customer;
      this.props.queryThreeMatchInfo({
        custRowId: id,
        custType,
        prdCode: prodCode,
      }).then(this.merge3MatchSubInfo);
    }
  }

  // 资讯订阅选择子产品的时候的处理程序
  @autobind
  @logable({
    type: 'ViewItem',
    payload: {
      name: '资讯订阅通过check选择子产品',
    },
  })
  handleSubscribelTransferSubProductCheck(item, array) {
    this.setState({
      subProList: array,
    });
  }

  // 打开选择审批人弹窗
  @autobind
  @logPV({ pathname: '/modal/choiceApproval', title: '选择审批人' })
  openApproverBoard() {
    this.setState({
      choiceApprover: true,
    });
  }

  // 关闭审批人员选择弹出窗
  @autobind
  closeChoiceApproverModal() {
    this.setState({
      choiceApprover: false,
    });
  }

  // 审批人弹出框确认按钮
  @autobind
  handleApproverModalOK(approver) {
    this.setState({
      approverName: approver.empName,
      approverId: approver.empNo,
    });
  }

  // 上传成功后的数据
  @autobind
  handleUploadSuccess(apiResult) {
    const { attachment, attaches } = apiResult.resultData;
    this.setState({ attachment });
    return Promise.resolve(_.last(attaches));
  }

  // 删除已经上传成功的附件
  @autobind
  handleFileRemove({ attachId, status }) {
    // 如果要删除的文件没有attachId，或者status的状态为error，则表示该附件没有上传到服务器中
    const { attachment } = this.state;
    if (attachment && !_.isEmpty(attachId) && status !== 'error') {
      return this.props.deleteAttachment({
        empId: emp.getId(),
        attachId,
        attachment,
      });
    }
    // 默认表示删除成功
    return Promise.resolve(true);
  }

  @autobind
  handleBeforeAction(action, product) {
    if (action === 'remove') {
      return true;
    }
    /**
     *  NOTE: 新增需求，在添加的时候，需要判断客户的风险等级是否与产品风险等级匹配
     * 如果客户为低风险保守型客户，则只能添加【低风险】产品
     */
    const { customer: { riskLevel } } = this.props;
    const { prdtRiskLevel } = product;
    if (riskLevel === CUST_LOW_RISK_LEVEL) {
      if (prdtRiskLevel !== PRODCUT_LOW_RISK_LEVEL) {
        confirm({
          content: '客户风险承受能力为保守型（最低类别），不可订购相关服务',
        });
        return false;
      }
    }
    return true;
  }

  render() {
    const {
      approvalUserList,
      subscribelProList,
    } = this.props;

    const {
      choiceApprover,
      approverName,
      approverId,
      canShowAppover,
      subscribelProductMatchInfo,
      subProList,
      attachment,
      uploaderUUID,
    } = this.state;

    const newApproverList = approvalUserList.map((item, index) => {
      const key = `${new Date().getTime()}-${index}`;
      return {
        ...item,
        key,
      };
    });

    const newSubscribelProList = this.createSubscribelProList(subscribelProList);
    // 资讯订阅中的产品选择配置
    const subScribetransferProps = {
      firstTitle: '可选服务',
      secondTitle: '已选服务',
      firstData: newSubscribelProList,
      firstColumns: subScribeProColumns,
      secondColumns: subScribeProColumns,
      transferChange: this.handleSubscribelTransferChange,
      checkChange: this.handleSubscribelTransferSubProductCheck,
      beforeAction: this.handleBeforeAction,
      rowKey: 'key',
      showSearch: true,
      placeholder: '产品代码/产品名称',
      pagination,
      defaultCheckKey: 'xDefaultOpenFlag',
      supportSearchKey: [['prodCode'], ['prodName']],
    };
    return (
      <div className={styles.contentBox}>
        <div className={styles.approvalBlock}>
          <InfoTitle head="资讯产品选择" />
          <Transfer {...subScribetransferProps} />
        </div>
        <ThreeMatchTip info={subscribelProductMatchInfo} userList={subProList} />
        <div className={styles.approvalBlock}>
          <InfoTitle head="附件信息" />
          <Uploader
            key={uploaderUUID}
            attachment={attachment || ''}
            onSuccess={this.handleUploadSuccess}
            onRemove={this.handleFileRemove}
          />
        </div>
        {
          // 资讯订阅选择审批人
          canShowAppover
            ? (
              <div className={styles.approvalBlock}>
                <InfoTitle head="审批人" />
                <CommissionLine label="选择审批人" labelWidth="110px">
                  <div className={styles.checkApprover} onClick={this.openApproverBoard}>
                    {approverName === '' ? '' : `${approverName}(${approverId})`}
                    <div className={styles.searchIcon}>
                      <Icon type="search" />
                    </div>
                  </div>
                </CommissionLine>
              </div>
            ) : null
        }
        <ChoiceApproverBoard
          visible={choiceApprover}
          approverList={newApproverList}
          onClose={this.closeChoiceApproverModal}
          onOk={this.handleApproverModalOK}
        />
      </div>
    );
  }
}
