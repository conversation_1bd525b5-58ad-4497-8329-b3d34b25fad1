.multiMiniAlert {
  background: linear-gradient(270deg, #f4f9ff 0%, #e9f3ff 100%);
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  padding: 10px 10px 11px 32px;
  position: relative;
  margin: 10px 0 20px 14px;

  .content {
    height: 54px;
    font-size: 12px;
    color: #666;
    line-height: 18px;
  }

  .infoIcon {
    width: 14px;
    height: 14px;
    position: absolute;
    top: 12px;
    left: 10px;
  }

  .overbold {
    color: #333;
    font-weight: bold;
  }
}