.smartCallResultModal {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .@{ant-prefix}-modal-body {
      padding-top: 24px;
    }
    table > thead > tr > th, table > tbody > tr > td {
      padding: 8px 24px;
    }
    .@{ant-prefix}-modal-footer {
      .@{ant-prefix}-btn {
        background-color: #108ee9;
        color: #fff;
      }
    }
    .@{ant-prefix}-table-placeholder {
      border: 1px solid #e8e8e8;
      border-top: none;
    }
  }
  .tableCellWrap {
    white-space: normal;
  }
  .audioArea {
    padding: 14px 14px 20px;
    & > div {
      display: inline-block;
    }
    .audioTitle {
      width: 70px;
      line-height: 20px;
      vertical-align: top;
    }
    .audioPlay {
      width: 205px;
      height: 20px;
      background-color: #f6fafe;
    }
  }
  .tableArea {
    padding: 0 14px;
  }
}
