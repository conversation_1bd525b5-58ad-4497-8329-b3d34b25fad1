.fileInfo {
  background-color: #fff;

  .headerInfo {
    display: flex;
    justify-content: space-between;

    .leftName {
      color: #108ee9;
      font-size: 12px;
      line-height: 18px;
      max-width: 180px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;

      .fujian {
        color: #666;
        margin-right: 8px;
      }
    }

    .operate {
      .download {
        margin-left: 20px;
      }

      .icon {
        color: #108ee9;
        font-size: 12px;
        cursor: pointer;
      }

      a {
        text-decoration: none;
      }
    }
  }

  .uploaderInfo {
     display: flex;
     justify-content: space-between;
     margin-top: 8px;

    .leftInfo {
      color: #666;
      font-size: 12px;
      line-height: 22px;
    }

    .size {
      color: #f5a623;
      font-size: 12px;
      line-height: 18px;
    }
  }
}
