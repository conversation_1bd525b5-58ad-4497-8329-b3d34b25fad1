/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-18 15:13:20
 * @description 资产配置审批记录tab页
 */

import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import {
  ApprovalHistory,
  DetailBlock,
  InfoCell,
  InfoGroup,
} from '@crm/biz-ui';

import { number } from '@/helper';
import { NODATA, LABEL_112 } from '@/components/AssetConfigApproval/config';
import ProductInfoTable from '@/components/AssetConfigApproval/ProductInfoTable';

import styles from './approvalRecord.less';

export default function ApprovalRecord(props) {
  const {
    detail: {
      custInfo,
      baseInfo,
      investDemand,
      assetProductList,
      custCenterDetail,
    },
    flowHistory,
  } = props;

  const formatMoney = (value) => (_.isNumber(value)
    ? number.formatToUnit({
      num: value,
      unit: '元',
      floatLength: 2,
    })
    : '--');

  const renderNormalCell = (value) => (
    _.isEmpty(value) ? NODATA : value
  );

  const renderNormalCellWithTitle = (value) => (
    _.isEmpty(value) ? NODATA : <div className={styles.value} title={value}>{value}</div>
  );

  return (
    <div className={styles.wrap}>
      <div className={styles.custInfo}>
        <div className={styles.custName}>
          {`${renderNormalCell(custInfo?.custName)} ${renderNormalCell(custInfo?.custId)}`}
        </div>
        <div className={styles.item}>{renderNormalCell(custInfo?.gender)}</div>
        <div className={styles.splitLine} />
        <div className={styles.item}>{`${renderNormalCell(custInfo?.age)}岁`}</div>
        <div className={styles.splitLine} />
        <div className={styles.item}>{renderNormalCell(custInfo?.openYear)}</div>
        <div className={styles.splitLine} />
        <div className={styles.item}>{renderNormalCell(custInfo?.riskLevel)}</div>
      </div>
      <DetailBlock title="基本信息">
        <InfoGroup>
          <InfoCell
            span={35}
            labelWidth={LABEL_112}
            label="总资产"
            ellipsis
            content={formatMoney(Number(custCenterDetail?.totalAssets || 0))}
          />
          <InfoCell
            span={35}
            labelWidth={LABEL_112}
            label="年日均资产"
            ellipsis
            content={formatMoney(Number(custCenterDetail?.dayAssets) || 0)}
          />
          <InfoCell
            span={30}
            labelWidth={LABEL_112}
            label="历史峰值资产"
            ellipsis
            content={formatMoney(Number(custCenterDetail?.historyTopAssets) || 0)}
          />
        </InfoGroup>
        <InfoCell
          span={30}
          labelWidth={LABEL_112}
          label="计划投资期限"
          ellipsis
          content={renderNormalCellWithTitle(baseInfo?.planInvestPeriod || 0)}
        />
        <InfoCell
          span={90}
          labelWidth={LABEL_112}
          label="偏好品种"
          ellipsis
          content={renderNormalCellWithTitle(baseInfo?.productInvestType)}
        />
        <InfoCell
          span={90}
          labelWidth={LABEL_112}
          label="合格投资者类型"
          ellipsis
          content={renderNormalCellWithTitle(baseInfo?.qualifiedInvestorType)}
        />
      </DetailBlock>
      <DetailBlock title="客户投资需求">
        <InfoCell
          span={30}
          labelWidth={LABEL_112}
          label="配置场景"
          ellipsis
          content={renderNormalCellWithTitle(investDemand?.configScenarios)}
        />
        <InfoGroup>
          <InfoCell
            span={35}
            labelWidth={LABEL_112}
            label="新增配置资金"
            ellipsis
            content={renderNormalCellWithTitle(investDemand?.capital)}
          />
          <InfoCell
            span={35}
            labelWidth={LABEL_112}
            label="预期年化收益率"
            ellipsis
            content={renderNormalCellWithTitle(investDemand?.expectRate)}
          />
          <InfoCell
            span={30}
            labelWidth={LABEL_112}
            label="可接受最大回撤"
            ellipsis
            content={renderNormalCellWithTitle(investDemand?.maxReturn)}
          />
        </InfoGroup>
        <InfoGroup>
          <InfoCell
            span={35}
            labelWidth={LABEL_112}
            label="投资范围"
            ellipsis
            content={renderNormalCellWithTitle(investDemand?.investScope)}
          />
          <InfoCell
            span={35}
            labelWidth={LABEL_112}
            label="资产偏好"
            ellipsis
            content={renderNormalCellWithTitle(investDemand?.preferAsset)}
          />
          <InfoCell
            span={30}
            labelWidth={LABEL_112}
            label="赛道偏好"
            ellipsis
            content={renderNormalCellWithTitle(investDemand?.preferTrack)}
          />
        </InfoGroup>
        <InfoCell
          span={30}
          labelWidth={LABEL_112}
          label="风格偏好"
          ellipsis
          content={renderNormalCellWithTitle(investDemand?.preferStyle)}
        />
      </DetailBlock>
      <DetailBlock title="已选配置产品">
        <ProductInfoTable
          data={assetProductList}
        />
      </DetailBlock>
      <DetailBlock title="审批记录">
        <ApprovalHistory
          history={flowHistory}
          visibleCurrentNode={_.isEmpty(flowHistory?.currentStepName)}
        />
      </DetailBlock>
    </div>
  );
}

ApprovalRecord.propTypes = {
  detail: PropTypes.object.isRequired,
  flowHistory: PropTypes.object.isRequired,
};
