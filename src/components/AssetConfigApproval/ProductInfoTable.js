/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-18 15:13:20
 * @description 资产配置审批记录tab页-已选配置产品
 */

import React from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';

import Table, { ToolTipCell } from '@/components/common/table';
import {
  TABLE_COLUMN,
  NODATA,
  SPACE_20,
  TABLE_PLACE_HOLDER,
} from './config';

import styles from './productInfoTable.less';

export default function ProductInfoTable(props) {
  const { data } = props;

  const renderNoData = () => <span className={styles.noData}>{NODATA}</span>;

  const renderCell = (text, record) => {
    if (record.flag) {
      return null;
    }
    if (text === '' || _.isNull(text)) {
      return renderNoData();
    }
    return <ToolTipCell tipContent={text} cellText={text} />;
  };

  const renderColumns = () => {
    const newColumns = _.map(TABLE_COLUMN, (column) => {
      const { key } = column;
      // 产品名称列
      if (key === 'productCode') {
        return {
          ...column,
          render: (text, record) => (text
            ? (
              <ToolTipCell tipContent={`${record?.productName}(${text})`} cellText={`${record?.productName}(${text})`} />
            )
            : (
              renderNoData()
            )),
        };
      }
      // 是否适配列
      if (key === 'ifAdaptation') {
        return {
          ...column,
          render: (text) => (_.isBoolean(text)
            ? <div className={!text ? styles.notAdaptation : null}>{text ? '是' : '否' }</div>
            : renderNoData()),
        };
      }
      return {
        ...column,
        render: (text, record) => renderCell(text, record),
      };
    });
    return newColumns;
  };

  // 不适配的这行高亮显示
  const getRowClass = (record) => (_.isBoolean(record.ifAdaptation) && !record.ifAdaptation
    ? styles.notAdaptationLine
    : null);

  return (
    <div className={styles.tableArea}>
      <Table
        useNewUI
        columns={renderColumns()}
        dataSource={data}
        rowKey={(record) => record?.productCode}
        spaceColumnProps={SPACE_20}
        placeHolderImageProps={TABLE_PLACE_HOLDER}
        rowClassName={getRowClass}
      />
    </div>
  );
}

ProductInfoTable.propTypes = {
  data: PropTypes.array.isRequired,
};
