.wrap {
  padding: 0 20px;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-bizDetailBlock {
      margin-top: 30px;
    }
  }

  .custInfo {
    height: 50px;
    line-height: 50px;
    margin: -20px 20px -10px 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ddd;

    .custName {
      font-size: 15px;
      font-weight: bold;
      color: #333;
      margin-right: 40px;
    }

    .item {
      font-size: 14px;
      color: #4a4a4a;
      line-height: 19px;
    }

    .splitLine {
      width: 1px;
      height: 10px;
      background: #4a4a4a;
      margin: 0 8px;
    }
  }

  .value {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}
