/**
 * @Description: 策略中心-精选组合-组合亮点
 * @Author: Liujianshu-K0240007
 * @Date: 2019-01-23 14:56:54
 * @Last Modified by: Liujianshu-K0240007
 * @Last Modified time: 2019-01-25 13:18:28
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { Tabs, Select } from 'antd';
import _ from 'lodash';

import InfoTitle from '../../common/InfoTitle';
import CombinationTopFive from './CombinationTopFive';
import StockTopFive from './StockTopFive';
import logable, { logCommon } from '@/decorators/logable';
import {
  COMBINATION_TOP_FIVE_TAB,
  STOCK_TOP_FIVE_TAB,
  SORT_BY_TYPE,
  DEFAULT_TOP_FIVE_PAGE_SIZE,
  WEEK_EARNINGS_KEY,
} from '../config';
import styles from './index.less';

// 近 7 天收益率
const DEFAULT_TYPE = SORT_BY_TYPE[0].key;
const Option = Select.Option;
const titleStyle = {
  fontSize: '14px',
  fontWeight: 'bold',
};
const warpStyle = {
  marginBottom: 0,
  marginLeft: '-20px',
};
const TabPane = Tabs.TabPane;

export default class Highlight extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    combinationData: PropTypes.array.isRequired,
    stockData: PropTypes.array.isRequired,
    openDetailPage: PropTypes.func.isRequired,
    openStockPage: PropTypes.func.isRequired,
    openCustomerListPage: PropTypes.func.isRequired,
    queryStockList: PropTypes.func.isRequired,
    queryCombinationList: PropTypes.func.isRequired,
  }

  static defaultProps = {

  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      sortBy: WEEK_EARNINGS_KEY,
    };
  }

  // tab 切换
  @autobind
  handleTabChange(activeKey) {
    const {
      location: { query },
      queryStockList,
      queryCombinationList,
    } = this.props;
    const {
      sortBy,
    } = this.state;
    const isCombinationTopFive = activeKey === COMBINATION_TOP_FIVE_TAB;
    if (sortBy !== WEEK_EARNINGS_KEY) {
      if (isCombinationTopFive) {
        queryCombinationList({
          sortBy,
        });
      } else {
        queryStockList({
          sortBy,
          pageSize: DEFAULT_TOP_FIVE_PAGE_SIZE,
        });
      }
    }
    this.context.replace({
      query: {
        ...query,
        highlightTab: activeKey,
      }
    });
    logCommon({
      type: 'Click',
      payload: {
        name: '组合 tab 切换',
        value: isCombinationTopFive ? '组合TOP5' : '个股TOP5',
      },
    });
  }

  // 收益率切换
  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '选择收益率',
      value: '$args[1].props.children',
    },
  })
  handleSelectChange(value, option) {
    const {
      location: {
        query: {
          highlightTab = COMBINATION_TOP_FIVE_TAB,
        },
      },
      queryStockList,
      queryCombinationList,
    } = this.props;
    this.setState({
      sortBy: value,
    }, () => {
      if (highlightTab === STOCK_TOP_FIVE_TAB) {
        queryStockList({
          sortBy: value,
          pageSize: DEFAULT_TOP_FIVE_PAGE_SIZE,
        });
      } else {
        queryCombinationList({
          sortBy: value,
        });
      }
    });
  }

  render() {
    const {
      combinationData,
      stockData,
      openDetailPage,
      openStockPage,
      openCustomerListPage,
      location: {
        query: {
          highlightTab = COMBINATION_TOP_FIVE_TAB,
        },
      },
    } = this.props;
    return (
      <div className={styles.highlight}>
        <InfoTitle
          head="组合亮点"
          titleStyle={titleStyle}
          warpStyle={warpStyle}
        />
        <Tabs
          activeKey={highlightTab}
          animated={false}
          onChange={this.handleTabChange}
        >
          <TabPane tab="组合TOP5" key={COMBINATION_TOP_FIVE_TAB}>
            <CombinationTopFive
              data={combinationData}
              openDetailPage={openDetailPage}
            />
          </TabPane>
          <TabPane tab="个股TOP5" key={STOCK_TOP_FIVE_TAB}>
            <StockTopFive
              data={stockData}
              openDetailPage={openDetailPage}
              openStockPage={openStockPage}
              openCustomerListPage={openCustomerListPage}
            />
          </TabPane>
        </Tabs>
        <div className={styles.sortBy}>
          <Select
            defaultValue={DEFAULT_TYPE}
            style={{ width: 120 }}
            onChange={this.handleSelectChange}
          >
            {
              _.map(SORT_BY_TYPE, type => (
                <Option
                  value={type.key}
                  key={type.key}
                >
                  {type.value}
                </Option>
              ))
            }
          </Select>
        </div>
      </div>
    );
  }
}
