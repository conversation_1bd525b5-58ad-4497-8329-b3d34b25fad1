.list {
  li {
    line-height: 14px;
    position: relative;
    text-indent: 23px;
    &:before {
      position: absolute;
      left: -20px;
      font-size: 14px;
    }
    &:first-child:before,
    &:nth-child(2):before,
    &:nth-child(3):before {
      color: #333;
      font-weight: bold;
    }
    &:first-child:before {
      content: '1';
    }
    &:nth-child(2):before {
      content: '2';
    }
    &:nth-child(3):before {
      content: '3';
    }
    &:nth-child(4):before {
      content: '4';
      color: #999;
    }
    &:nth-child(5):before {
      content: '5';
      color: #999;
    }
    .firstLine, .secondLine {
      span {
        display: inline-block;
        text-indent: 0;
      }
    }
  }
}
.combinationTopFive {
  .list {
    li {
      margin-top: 35px;
      .name {
        color: #108ee9;
        font-weight: bold;
        font-size: 14px;
      }
      .percent {
        font-size: 12px;
        margin-left: 4px;
        i {
          font-size: 13px;
          vertical-align: baseline;
        }
      }
      .red {
        color: #e33c39;
      }
      .blue {
        color: #59ae85;
      }
      .producter {
        font-size: 12px;
        color: #333;
        float: right;
      }
    }
  }
}
.stockTopFive {
  .list {
    li {
      margin-top: 18px;
      .name {
        color: #108ee9;
        font-weight: bold;
        font-size: 14px;
      }
      .percent {
        font-size: 12px;
        margin-left: 4px;
        color: #e33c39;
        i {
          font-size: 13px;
          vertical-align: baseline;
        }
      }
      .time {
        float: right;
        font-size: 12px;
        color: #999;
      }
      .combinationName {
        font-size: 12px;
        color: #333;
      }
      .cust {
        float: right;
        font-size: 12px;
        color: #108ee9;
        i {
          color: #108ee9;
          margin-right: 6px;
        }
      }
      .secondLine {
        margin-top: 6px;
      }
    }
  }
}
