/**
 * @Description: 策略中心-精选组合-组合亮点-个股 top 5
 * @Author: Liujianshu-K0240007
 * @Date: 2019-01-23 16:51:31
 * @Last Modified by: Liujianshu-K0240007
 * @Last Modified time: 2019-01-23 17:38:14
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import moment from 'moment';

import Icon from '../../common/Icon';
import logable from '@/decorators/logable';
import { number } from '@/helper';
import styles from './topFive.less';
import {
  sourceType,
  securityType as securityTypeConfig,
  formatDateStr,
} from '../config';
// securityType 里股票对应的值
const STOCK_CODE = securityTypeConfig[0].value;

export default class StockTopFive extends PureComponent {
  static propTypes = {
    data: PropTypes.array.isRequired,
    openDetailPage: PropTypes.func.isRequired,
    openStockPage: PropTypes.func.isRequired,
    openCustomerListPage: PropTypes.func.isRequired,
  }

  // 证券名称点击事件
  @autobind
  @logable({
    type: 'ViewItem',
    payload: {
      name: '近一周表现前十的证券',
      value: '$args[2]',
    },
  })
  handleStockNameClick(type, code) {
    if (type === STOCK_CODE) {
      const { openStockPage } = this.props;
      const openPayload = {
        code,
      };
      openStockPage(openPayload);
    }
  }

  // 组合名称点击事件
  @autobind
  @logable({
    type: 'ViewItem',
    payload: {
      name: '组合亮点-个股TOP5-组名名称',
      value: '$args[0].name',
    },
  })
  handleNameClick(obj) {
    const { openDetailPage } = this.props;
    openDetailPage(obj);
  }

  @autobind
  @logable({
    type: 'ViewItem',
    payload: {
      name: '组合亮点-个股TOP5-持仓客户',
      value: '$args[0].name',
    },
  })
  handleOpenCustomerListPage(openPayload) {
    const { openCustomerListPage } = this.props;
    openCustomerListPage(openPayload);
  }

  @autobind
  renderList() {
    const {
      data,
    } = this.props;
    return _.map(data, (item) => {
      const {
        name,
        code,
        priceLimit,
        callInTime,
        combinationName,
        securityType,
      } = item;
      const openDetailPayload = {
        id: code,
        name,
      };
      const openCustPayload = {
        name,
        code,
        type: securityType,
        source: sourceType.security,
      };
      // 收益率是否大于等于 0
      const isBiggerThanZero = Number(priceLimit) >= 0;
      const iconElement = isBiggerThanZero
        ? <Icon type="zhang" />
        : <Icon type="die" />;
      const colorName = isBiggerThanZero ? styles.red : styles.blue;
      return (
        <li key={code}>
          <div className={styles.firstLine}>
            <span
              className={styles.name}
              onClick={() => this.handleStockNameClick(securityType, code, `${name}(${code})`)}
            >
              {`${name} (${code})`}
            </span>
            <span className={`${styles.percent} ${colorName}`}>
              {iconElement}
              {number.toFixed(priceLimit, 2)}%
            </span>
            <span className={styles.time}>调入时间: {moment(callInTime).format(formatDateStr)}</span>
          </div>
          <div className={styles.secondLine}>
            <span
              className={styles.combinationName}
              onClick={() => this.handleNameClick(openDetailPayload)}
            >
              {combinationName}
            </span>
            <a
              className={styles.cust}
              onClick={() => this.handleOpenCustomerListPage(openCustPayload)}
            >
              <Icon type="kehuzu" />持仓客户
            </a>
          </div>
        </li>
      );
    });
  }

  render() {
    return (
      <div className={styles.stockTopFive}>
        <ul className={styles.list}>
          {this.renderList()}
        </ul>
      </div>
    );
  }
}
