.highlight {
  box-sizing: border-box;
  padding: 14px 20px 20px;
  position: relative;
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-tabs-ink-bar {
      margin-left: 4px;
    }
    .ant-tabs-bar {
      margin-bottom: 10px;
      padding-left: 10px;
      border-bottom: 1px solid #a8b6d4;
    }
    .ant-tabs-nav .ant-tabs-tab {
      padding: 12px 0;
      width: 56px;
      margin-right: 40px;
      font-size: 14px;
      color: #8995a5;
      letter-spacing: 0;
    }
    .ant-tabs-tab-active.ant-tabs-tab {
      color: #333;
      font-weight: bold;
    }
    .ant-select-selection-selected-value {
      color: #333;
      float: right;
      padding-right: 18px;
    }
  }
  .sortBy {
    position: absolute;
    top: 44px;
    right: 14px;
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-select {
        float: right;
        margin-top: -4px;
      }
      .ant-select-selection {
        background: transparent;
        border: none;
      }
      .ant-select-selection:hover {
        border: none;
      }
      .ant-select-focused .ant-select-selection,
      .ant-select-selection:focus,
      .ant-select-selection:active {
        box-shadow: none;
      }
      .ant-select-arrow {
        border: none;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 8px solid #c2c2c2;
        height: 0;
        width: 0;
        overflow: hidden;
      }
    }
  }
}
