/**
 * @Description: 策略中心-精选组合-组合亮点-组合 TOP 5
 * @Author: Liujianshu-K0240007
 * @Date: 2019-01-23 15:34:59
 * @Last Modified by: Liujianshu-K0240007
 * @Last Modified time: 2019-01-23 16:24:10
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import Icon from '../../common/Icon';
import logable from '@/decorators/logable';
import { number } from '@/helper';
import styles from './topFive.less';

export default class CombinationTopFive extends PureComponent {
  static propTypes = {
    data: PropTypes.array.isRequired,
    openDetailPage: PropTypes.func.isRequired,
  }

  static defaultProps = {

  }

  // 组合名称点击事件
  @autobind
  @logable({
    type: 'ViewItem',
    payload: {
      name: '组合亮点-组合top5-组合名称',
      value: '$args[0].name',
    },
  })
  handleNameClick(obj) {
    const { openDetailPage } = this.props;
    openDetailPage(obj);
  }

  @autobind
  renderList() {
    const {
      data,
    } = this.props;
    return _.map(data, (item) => {
      const {
        name,
        code,
        empName,
        rateOfReturn,
      } = item;
      const openDetailPayload = {
        id: code,
        name,
      };
      // 收益率是否大于等于0
      const isBiggerThanZero = Number(rateOfReturn) >= 0;
      const iconElement = isBiggerThanZero
        ? <Icon type="zhang" />
        : <Icon type="die" />;
      const colorName = isBiggerThanZero ? styles.red : styles.blue;
      return (
        <li key={name}>
          <div className={styles.firstLine}>
            <span
              className={styles.name}
              onClick={() => this.handleNameClick(openDetailPayload)}
            >
              {name}
            </span>
            <span className={`${styles.percent} ${colorName}`}>
              {iconElement}
              {number.toFixed(rateOfReturn, 2)}%
            </span>
            <span className={styles.producter}>{empName}</span>
          </div>
        </li>
      );
    });
  }

  render() {
    return (
      <div className={styles.combinationTopFive}>
        <ul className={styles.list}>
          {this.renderList()}
        </ul>
      </div>
    );
  }
}
