/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 组合概览组件
 * @Date: 2019-01-23 13:21:28
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-03-22 21:27:00
 */


import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import classnames from 'classnames';
import _ from 'lodash';
import Icon from '@/components/common/Icon';
import IFWrap from '@/components/common/IFWrap';
import logable from '@/decorators/logable';
import { number } from '../../../helper';
import { weekMonthYear } from '../config';
import styles from './overview.less';


const EMPTY_TEXT = '无';
const MAX_HEIGHT = 88;
export default class Overview extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    data: PropTypes.object.isRequired,
    titleName: PropTypes.string.isRequired,
    clearOverview: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      isShowButton: false,
      fold: true,
    };
    this.textBoxRef = React.createRef();
  }

  componentDidUpdate(prevProps, prevState) {
    const dom = this.getRef();
    const height = dom.clientHeight;
    const { isShowButton: oldState } = prevState;
    const { location: { query: { openPoint } } } = this.props;
    // 文本内容超出限定高度，并且当前是否显示按钮状态值是false
    if (height > MAX_HEIGHT && !oldState) {
      // eslint-disable-next-line
      this.setState({
        isShowButton: true,
        // 从首页-组合排名-主板观点-详情点击进来时需要自动展开主办观点全部信息
        fold: !openPoint,
      });
    }
    // 文本内容没有超出限定高度，并且当前是否显示按钮状态值是true
    if (height < MAX_HEIGHT && oldState) {
      // eslint-disable-next-line
      this.setState({
        isShowButton: false,
      });
    }
  }

  componentWillUnmount() {
    // 组件卸载时清空组合概览数据
    this.props.clearOverview();
  }

  @autobind
  getRef() {
    return this.textBoxRef.current;
  }

  @autobind
  getNumberClassName(num) {
    const bigThanZero = num >= 0;
    const numberClassName = classnames({
      [styles.up]: bigThanZero,
      [styles.down]: !bigThanZero,
    });
    return numberClassName;
  }

  // 与零作比较，大于 0 则加上 + 符号
  @autobind
  compareWithZero(value) {
    return value > 0 ? `+${value}` : value;
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '打开主办观点',
    },
  })
  handleOpen() {
    this.setState({
      fold: false,
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '折叠主办观点',
    },
  })
  handleClose() {
    this.setState({
      fold: true,
    });
  }

  @autobind
  getBoxHeight() {
    const { fold, isShowButton } = this.state;
    if (isShowButton) {
      return fold ? MAX_HEIGHT : 'auto';
    }
    return 'auto';
  }

  render() {
    const {
      data,
      data: {
        adjustNumber,
        earnNumber,
        stockName,
        withdraw,
        weekEarnings,
        composeType = '',
        empName = '',
        empId = '',
        viewpoint = '',
      },
      titleName,
    } = this.props;
    const { isShowButton, fold } = this.state;
    const showWeekMonthYear = [...weekMonthYear];
    if (!_.isEmpty(data) && !_.isNumber(weekEarnings)) {
      showWeekMonthYear.shift();
    }
    return (
      <div className={styles.overview}>
        <h2 className={styles.title}>
          {titleName}
          <span className={styles.subhead}>
            {`${composeType || ''} | ${empName || ''} (${empId || ''})`}
          </span>
        </h2>
        <div className={`${styles.contentBox} clearfix`}>
          <h5 className={styles.threeMonthTips}>近3个月数据</h5>
          <div className={styles.left}>
            <div className={styles.line}>
              <span className={styles.shortLabel}>调仓：{adjustNumber}次</span>
              <span>
                涨幅最高：
                <em className={styles.stockName}>{stockName || EMPTY_TEXT}</em>
              </span>
            </div>
            <div className={styles.line}>
              <span className={styles.shortLabel}>
                盈利：
                <em className={styles.earn}>{earnNumber}次</em>
              </span>
              <span>
                最大回撤：
                <em className={`${this.getNumberClassName(withdraw)} ${styles.withdraw}`}>
                  {this.compareWithZero(number.toFixed(withdraw))}%
                </em>
              </span>
            </div>
          </div>
          <div className={styles.right}>
            {
              showWeekMonthYear.map((item) => {
                const {
                  name,
                  key,
                  percent,
                  ranking,
                  total,
                  img
                } = item;
                const num = number.toFixed(data[percent]);
                return (
                  <div className={styles.item} key={key}>
                    <img src={img} alt={name} />
                    <div className={styles.textBox}>
                      <div className={styles.textLine}>
                        收益率：
                        <span className={this.getNumberClassName(num)}>
                          {this.compareWithZero(num)}%
                          <Icon type="zhang" />
                          <Icon type="die" />
                        </span>
                      </div>
                      <div className={styles.textLine}>
                        同类排名：
                        <span>{data[ranking]}<em>/{data[total]}</em></span>
                      </div>
                    </div>
                  </div>
                );
              })
            }
          </div>
        </div>
        <div className={styles.point}>
          <h4>主办观点</h4>
          <div className={styles.pointTextBox} style={{ height: this.getBoxHeight() }}>
            <div className={styles.text} ref={this.textBoxRef}>
              {viewpoint}
            </div>
          </div>
        </div>
        <IFWrap when={isShowButton}>
          <div className={styles.buttonBox}>
            {
              fold
                ? (
                  <span onClick={this.handleOpen}>查看全部<Icon type="zhankai1" /></span>
                )
                : (
                  <span onClick={this.handleClose}>收起<Icon type="shouqi2" /></span>
                )
            }
          </div>
        </IFWrap>
      </div>
    );
  }
}
