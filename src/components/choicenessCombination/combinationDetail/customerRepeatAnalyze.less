.orderingCustomerBox {
  margin-left: calc(~"49% + 20px");
  padding: 0 20px;
  width: calc(~"51% - 20px");
  height: 270px;
  background-color: #fff;
  box-shadow: 2px 1px 5px #bdbcbc;
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-table {
      height: 178px;
      table {
        table-layout: fixed;
      }
    }
    .ant-table-thead > tr > th {
      padding: 4px 8px;
      background: none;
    }
    .ant-table-tbody > tr > td {
      padding: 6px 8px;
      background: none;
      border-bottom: none;
    }
    .ant-table-placeholder {
      border-bottom: none;
    }
    .ant-table-pagination.ant-pagination {
      margin: 0;
    }
    .ant-pagination-item {
      min-width: 24px;
      height: 24px;
      line-height: 22px;
    }
    .ant-pagination-prev,
    .ant-pagination-next,
    .ant-pagination-jump-prev,
    .ant-pagination-jump-next {
      min-width: 24px;
      height: 24px;
      line-height: 24px;
    }
    .ant-pagination-item-link {
      height: 24px;
    }
  }
}
.headBox {
  height: 54px;
  line-height: 54px;
  h3 {
    float: left;
    line-height: 54px;
    font-size: 14px;
    color: #4a4a4a;
  }
  a {
    float: right;
    font-size: 12px;
  }
}
.tipsBox {
  font-size: 12px;
  text-align: left;
  position: relative;
  top: -7px;
  .total {
    color: #2782d7;
    font-weight: bold;
  }
}
.processBox {
  .processContainer {
    display: inline-block;
    width: 124px;
    height: 3px;
    background-color: #ddd;
    vertical-align: middle;
    span {
      display: block;
      height: 3px;
      background-color: #63d0fc;
    }
  }
  em {
    margin-left: 15px;
    vertical-align: middle;
    font-size: 12px;
    font-style: normal;
  }
}
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
