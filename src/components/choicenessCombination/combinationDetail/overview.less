
//Color
@blue: #2782d7;
.overview {
  box-sizing: border-box;
  padding: 24px 30px 20px;
  background: #fff;
  box-shadow: 1px 1px 4px 0 rgba(0, 0, 0, .18);
  border-radius: 2px;
  position: relative;
  .title {
    margin-bottom: 10px;
    font-weight: bold;
    font-size: 16px;
    color: #333;
    span {
      margin-left: 10px;
      font-size: 14px;
      font-weight: normal;
      color: #999;
    }
  }
  .contentBox {
    padding-bottom: 14px;
    border-bottom: 1px solid #e9e9e9;
    .threeMonthTips {
      margin-bottom: 14px;
      font-size: 12px;
      color: #333;
    }
    em {
      font-style: normal;
    }
    .left {
      float: left;
      font-size: 14px;
      .line {
        line-height: 20px;
        margin-bottom: 10px;
        span {
          display: inline-block;
          color: #666;
          vertical-align: middle;
          &.shortLabel {
            width: 100px;
          }
        }
        .stockName {
          color: #2095ea;
        }
        .earn {
          color: #ff6e02;
        }
        .withdraw {
          color: #e40d0d;
        }
      }
    }
    .right {
      float: right;
      .item {
        display: inline-block;
        margin-left: 84px;
        img {
          display: inline-block;
          width: 36px;
          height: 34px;
          margin-right: 20px;
        }
        .textBox {
          display: inline-block;
          vertical-align: middle;
          .textLine {
            margin-bottom: 10px;
            line-height: 20px;
            font-size: 14px;
            color: #666;
            span {
              font-size: 16px;
              color: #333;
              em {
                font-size: 12px;
                color: #999;
              }
            }
            .up {
              color: #e40d0d;
                /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
                .icon-zhang {
                  display: inline;
                }
                .icon-die {
                  display: none;
                }
                .iconfont {
                  font-size: 13px;
                  position: relative;
                  top: -1px;
                  right: -3px;
                }
              }
            }
            .down {
              color: #1a993b;
                /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
                .icon-zhang {
                  display: none;
                }
                .icon-die {
                  display: inline;
                }
                .iconfont {
                  font-size: 13px;
                  position: relative;
                  top: -1px;
                  right: -3px;
                }
              }
            }
          }
        }
      }
    }
  }
  .point {
    padding-top: 14px;
    h4 {
      font-size: 14px;
      color: #333;
      margin-bottom: 10px;
    }
    .text {
      font-size: 14px;
      color: #666;
      line-height: 22px;
      word-break: break-all;
    }
    .pointTextBox {
      overflow: hidden;
    }
  }
  .buttonBox {
    margin-top: 10px;
    text-align: center;
    font-size: 14px;
    color: #108ee9;
    span {
      cursor: pointer;
    }
    i {
      font-size: 12px;
      position: relative;
      top: -2px;
      margin-left: 5px;
    }
  }
}
