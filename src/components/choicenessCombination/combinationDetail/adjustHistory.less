.adjustHistoryBox {
  position: absolute;
  left: 0;
  top: 0;
  padding: 0 20px;
  width: 49%;
  height: 370px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 2px 1px 5px #bdbcbc;
}
.headBox {
  height: 54px;
  line-height: 54px;
  h3 {
    line-height: 54px;
    float: left;
    font-size: 14px;
    color: #4a4a4a;
  }
  a {
    float: right;
    font-size: 12px;
  }
}
.titleBox {
  margin-bottom: 10px;
  line-height: 26px;
  font-size: 12px;
  color: #4a4a4a;
  border-bottom: 1px solid #ccc;
}
.security {
  float: left;
  width: 38%;
  text-indent: 50px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.time {
  float: left;
  width: 25%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.const {
  float: left;
  width: 15%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.change {
  float: left;
  width: 22%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.itemBox {
  position: relative;
  margin: 3px 0;
  .icon {
    position: absolute;
    top: 10px;
    left: 8px;
    width: 28px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    border-radius: 50%;
    font-size: 14px;
    color: #fff;
    background-color: #2cb7f5;
  }
  .security {
    color: #1890ff;
  }
  .text {
    width: 100%;
    line-height: 25px;
    .top {
      font-size: 12px;
    }
    .reason {
      margin-left: 50px;
      font-size: 12px;
      color: #828282;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  &:nth-of-type(even) {
    background-color: #f2f2f2;
  }
  &.in {
    .icon {
      background-color: #fb8393;
    }
  }
}
