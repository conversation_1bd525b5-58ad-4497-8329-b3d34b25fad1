.combinationFilterBox {
  margin: -10px 0 16px 0;
  .formItem {
    display: inline-block;
    margin-right: 12px;
    .searchFilter, .longSearchFilter {
      max-width: 200px;
    }
    .longSearchFilter {
        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .lego-filter-menuContainer .lego-filter-menu.ant-dropdown-menu {
          width: 190px;
        }
      }
    }
    .moreTextFilter {
      width: 222px;
        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .lego-filter-menuContainer .lego-filter-menu {
          width: 180px;
        }
        .lego-filter-menuContainer .lego-filter-menu.ant-dropdown-menu {
          width: 180px;
        }
        .lego-filter-filterWrapper > button:first-child .lego-filter-contentShowOnButton .lego-filter-valueShowOnButton {
          max-width: 180px;
        }
      }
    }
  }
}
.treeSelectDropdown {
  ul li {
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      span.ant-select-tree-switcher-noop {
        width: 4px;
      }
    }
  }
}
