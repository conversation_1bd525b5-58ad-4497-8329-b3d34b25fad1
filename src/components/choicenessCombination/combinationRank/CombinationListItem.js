/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 精选组合-组合排名-列表项
 * @Date: 2018-04-18 14:26:13
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-25 14:03:49
*/

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { Spin } from 'antd';
import _ from 'lodash';
import classnames from 'classnames';
import Icon from '../../common/Icon';
import IFWrap from '../../common/IFWrap';
import { number } from '../../../helper';
import CombinationYieldChart from '../CombinationYieldChart';
import logable, { logPV } from '../../../decorators/logable';
import styles from './combinationListItem.less';
import {
  yieldRankList,
  sourceType,
  RISK_LEVEL_ICON_MAP,
} from '../config';

const EMPTY_OBJECT = {};
const EMPTY_TEXT = '无';

// 历史报告
const REPORT_TYPE = 'report';

export default class CombinationListItem extends PureComponent {
  static propTypes = {
    // 图表tab切换
    chartTabChange: PropTypes.func.isRequired,
    // 组合item数据
    data: PropTypes.object,
    // 折线图数据
    getCombinationLineChart: PropTypes.func.isRequired,
    combinationLineChartData: PropTypes.object.isRequired,
    // 组合类型当前选择的key
    rankTabActiveKey: PropTypes.string.isRequired,
    // 组合排名收益率排序
    yieldRankValue: PropTypes.string,
    // 打开个股资讯页面
    openStockPage: PropTypes.func.isRequired,
    // 打开持仓查客户页面
    openCustomerListPage: PropTypes.func.isRequired,
    showModal: PropTypes.func.isRequired,
    openDetailPage: PropTypes.func.isRequired,
  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
    push: PropTypes.func.isRequired,
    dict: PropTypes.object.isRequired,
  }

  static defaultProps = {
    data: EMPTY_OBJECT,
    yieldRankValue: '',
  }

  @autobind
  getYieldName() {
    const {
      yieldRankValue,
    } = this.props;
    const result = _.filter(yieldRankList, item => (item.value === yieldRankValue))[0];
    return `${result.showName}: `;
  }

  @autobind
  getYieldNode() {
    const {
      data,
      yieldRankValue,
    } = this.props;
    const result = _.filter(yieldRankList, item => (item.value === yieldRankValue))[0];
    const num = data[result.showNameKey] || 0;
    // 后端返回如 -0.0003这样数据时 四舍五入完会是-0.00 需要特殊处理成 0.00
    const judgeNum = Number(num.toFixed(2));
    const fiexdNum = judgeNum === 0 ? '0.00' : num.toFixed(2);
    const className = this.getNumberClassName(judgeNum);
    return (
      <span className={className}>{`${judgeNum > 0 ? '+' : ''}${fiexdNum}%`}</span>
    );
  }

  @autobind
  @logPV({
    pathname: '/modal/historyReportModal',
    title: '历史报告弹框',
  })
  viewHistoryReport(name) {
    const { showModal } = this.props;
    const payload = {
      combinationCode: name,
      type: REPORT_TYPE,
    };
    showModal(payload);
  }

  // 组合名称点击事件
  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '查看组合详情',
      value: '$args[0].name',
    },
  })
  handleNameClick(obj) {
    const { openDetailPage } = this.props;
    openDetailPage(obj);
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '查看订购该组合的客户',
      value: '$args[0].name',
    },
  })
  handleOpenCustomerListPage(openPayload) {
    const { openCustomerListPage } = this.props;
    openCustomerListPage(openPayload);
  }

  @autobind
  getNumberClassName(num) {
    const bigThanZero = num >= 0;
    const numberClassName = classnames({
      [styles.up]: bigThanZero,
      [styles.down]: !bigThanZero,
    });
    return numberClassName;
  }

  // 与零作比较，大于 0 则加上 + 符号
  @autobind
  compareWithZero(value) {
    return value > 0 ? `+${value}` : value;
  }

  render() {
    const {
      data,
      data: {
        combinationCode,
        combinationName,
        productCode,
        isRecommend,
        categoryName,
        empName,
        empId,
        riskLevel,
        adjustNumber,
        earnNumber,
        stockName,
        withdraw,
        viewpoint,
      },
      chartTabChange,
      getCombinationLineChart,
      combinationLineChartData,
      rankTabActiveKey,
    } = this.props;
    const chartData = combinationLineChartData[combinationCode] || EMPTY_OBJECT;
    const yieldName = this.getYieldName();
    const classNames = classnames({
      [styles.itemBox]: true,
      clearfix: true,
    });
    const openPayload = {
      name: combinationName,
      code: productCode,
      source: sourceType.combination,
      combinationCode,
    };
    const openDetailPayload = {
      id: combinationCode,
      name: combinationName,
    };
    return (
      <div className={classNames}>
        <div className={styles.left}>
          <h3 className={styles.title}>
            <a onClick={() => this.handleNameClick(openDetailPayload)}>{combinationName}</a>
            <span className={styles.riskLevel}><Icon type={RISK_LEVEL_ICON_MAP[riskLevel]} /></span>
            <IFWrap when={isRecommend}>
              <span className={styles.recommend}><Icon type="beituijian" /></span>
            </IFWrap>
          </h3>
          <div className={styles.subhead}>
            {categoryName}&nbsp;&nbsp;|&nbsp;&nbsp;{empName}({empId})
          </div>
          {
            _.isEmpty(chartData)
              ? (
                <div className={styles.chartLoading}>
                  <Spin />
                </div>
              )
              : (
                <CombinationYieldChart
                  combinationItemData={data}
                  combinationCode={combinationCode}
                  chartData={chartData}
                  getCombinationLineChart={getCombinationLineChart}
                  tabChange={chartTabChange}
                  rankTabActiveKey={rankTabActiveKey}
                  ref={ref => this.chartComponent = ref}
                />
              )
          }
        </div>
        <div className={styles.right}>
          <div className={styles.earing}>
            {yieldName}
            {this.getYieldNode()}
          </div>
          <div className={styles.links}>
            <a onClick={() => this.handleNameClick(openDetailPayload)}>组合详情 </a>
            |
            <a onClick={() => this.viewHistoryReport(combinationCode)}> 历史报告 </a>
            |
            <a onClick={() => this.handleOpenCustomerListPage(openPayload)}> 订购客户</a>
          </div>
          <div className={styles.threeMonthData}>
            <div className={styles.title}>近3个月数据</div>
            <div className={styles.line}>
              <span className={styles.shortLabel}>
                调仓：
                <em className={styles.adjustTime}>{adjustNumber}次</em>
              </span>
              <span className={styles.longLabel}>
                <em className={styles.upText}>涨幅最高：</em>
                <em className={styles.stockName}>{stockName || EMPTY_TEXT}</em>
              </span>
            </div>
            <div className={styles.line}>
              <span className={styles.shortLabel}>
                盈利：
                <em className={styles.earnTime}>{earnNumber}次</em>
              </span>
              <span className={styles.longLabel}>
                最大回撤：
                <em className={`${this.getNumberClassName(withdraw)} ${styles.withdraw}`}>
                  {this.compareWithZero(number.toFixed(withdraw))}%
                </em>
              </span>
            </div>
          </div>
          <div className={styles.viewPoint}>
            <span className={styles.title}>主办观点</span>
            <div className={styles.textBox}>
              <div className={styles.textBox}>
                {viewpoint}
              </div>
            </div>
            <a
              onClick={() => this.handleNameClick({ ...openDetailPayload, openPoint: true })}
              className={styles.linkToDetail}
            >
              [详情]
            </a>
          </div>
        </div>
      </div>
    );
  }
}
