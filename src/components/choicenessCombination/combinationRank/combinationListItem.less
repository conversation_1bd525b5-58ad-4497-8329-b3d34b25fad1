.itemBox {
  position: relative;
  margin-bottom: 20px;
  padding: 14px;
  background: #f7fbfe;
  border: 1px solid #d0dfe9;
  border-radius: 3px;
  height: 327px;
  .left {
    margin-right: 48%;
    height: 100%;
    .title {
      margin-bottom: 4px;
      a {
        font-size: 16px;
        color: #108ee9;
        font-weight: bold;
      }
      .riskLevel {
          /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
          .iconfont {
            font-size: 24px;
            color: #7d9be0;
            position: relative;
            top: 1px;
            &.icon-zhongdifengxian, &.icon-zhonggaofengxian {
              margin-left: 4px;
              font-size: 19px;
            }
            &.icon-difengxian,&.icon-zhongfengxian, &.icon-gaofengxian {
              display: inline-block;
              width: 57px;
              overflow: hidden;
            }
          }
        }
      }
      .recommend {
          /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
          .iconfont {
            font-size: 18px;
            color: #ff784e;
            position: relative;
            top: -2px;
            margin-left: 4px;
          }
        }
      }
    }
    .subhead {
      margin-bottom: 20px;
      font-size: 12px;
      color: #999;
    }
    .chartLoading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }
  }
  .right {
    position: absolute;
    right: 14px;
    top: 14px;
    width: 42%;
    height: 100%;
    .earing {
      margin-bottom: 10px;
      font-size: 12px;
      color: #333;
      text-align: right;
      span {
        font-size: 14px;
        font-weight: bold;
      }
    }
    .links {
      margin-bottom: 16px;
      font-size: 12px;
      color: #ccc;
      text-align: right;
      a {
        font-size: 12px;
        color: #108ee9;
      }
    }
    .threeMonthData {
      margin-bottom: 12px;
      .title {
        margin-bottom: 14px;
        font-size: 12px;
        color: #666;
        font-weight: bold;
      }
      .line {
        margin-bottom: 10px;
        font-size: 12px;
        span {
          display: inline-block;
          em {
            display: inline-block;
            font-style: normal;
          }
        }
        .shortLabel {
          width: 38%;
          min-width: 110px;
          .adjustTime {
            color: #383838;
          }
          .earnTime {
            color: #fe7005;
          }
        }
        .longLabel {
          margin-left: 2%;
          width: 60%;
          min-width: 176px;
          .upText {
            vertical-align: middle;
          }
        }
        .stockName {
          color: #2899eb;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
        }
      }
    }
    .viewPoint {
      .title {
        display: inline-block;
        margin-bottom: 10px;
        font-size: 12px;
        color: #666;
        font-weight: bold;
      }
      .textBox {
        margin-bottom: 6px;
        height: 82px;
        overflow: hidden;
        font-size: 12px;
        color: #666;
        line-height: 20px;
        word-break: break-all;
      }
      .linkToDetail {
        float: right;
        font-size: 12px;
        color: #108ee9;
      }
    }
    .up {
      color: #e40d0d;
    }
    .down {
      color: #1a993b;
    }
  }
}
