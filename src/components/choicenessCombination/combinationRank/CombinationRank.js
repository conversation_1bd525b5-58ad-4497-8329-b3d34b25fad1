/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 精选组合-组合排名
 * @Date: 2018-04-18 14:26:13
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-28 13:34:49
*/

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';


import InfoTitle from '../../common/InfoTitle';
import CombinationFilter from './CombinationFilter';
import CombinationListItem from './CombinationListItem';
import Pagination from '@/components/common/Pagination';
import {
  WEEK_EARNINGS_KEY,
} from '../config';
import styles from './combinationRank.less';

const titleStyle = {
  fontSize: '14px',
  fontWeight: 'bold',
};
const EMPTY_LIST = [];
const EMPTY_OBJECT = {};
const PAGE_SIZE = 5;

export default class CombinationRank extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    // 图表tab切换
    chartTabChange: PropTypes.func.isRequired,
    // 组合排名列表数据
    getCombinationRankList: PropTypes.func.isRequired,
    combinationRankListData: PropTypes.object.isRequired,
    // 组合树列表数据
    combinationTreeList: PropTypes.array.isRequired,
    // 折线图数据
    getCombinationLineChart: PropTypes.func.isRequired,
    combinationLineChartData: PropTypes.object.isRequired,
    // 打开个股资讯页面
    openStockPage: PropTypes.func.isRequired,
    // 打开持仓查客户页面
    openCustomerListPage: PropTypes.func.isRequired,
    showModal: PropTypes.func.isRequired,
    // 打开详情页面
    openDetailPage: PropTypes.func.isRequired,
    // 投资顾问
    creatorList: PropTypes.array.isRequired,
  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.listBoxRef = React.createRef();
  }

  componentDidMount() {
    this.queryList();
  }

  componentDidUpdate(prevProps, prevState) {
    // url里保存的四个筛选条件字段，如果有一个发生变化就重新查询列表
    const {
      location: {
        query: {
          adviserId: oldAdviserId = '',
          riskLevel: oldRiskLevel = '',
          combinationType: oldCombinationType = '',
          sortBy: oldSortBy = WEEK_EARNINGS_KEY,
          pageNum: oldPageNum = '1',
        }
      },
    } = prevProps;
    const {
      location: {
        query: {
          adviserId = '',
          riskLevel = '',
          combinationType = '',
          sortBy = WEEK_EARNINGS_KEY,
          pageNum = '1',
        }
      },
    } = this.props;
    if (
      adviserId !== oldAdviserId
      || riskLevel !== oldRiskLevel
      || combinationType !== oldCombinationType
      || sortBy !== oldSortBy
      || pageNum !== oldPageNum
    ) {
      this.queryList();
    }
  }

  @autobind
  queryList() {
    const {
      getCombinationRankList,
      location: {
        query: {
          adviserId = '',
          riskLevel = '',
          combinationType = '',
          sortBy = WEEK_EARNINGS_KEY,
          pageNum = '1',
        }
      },
    } = this.props;
    getCombinationRankList({
      combinationType,
      adviserId,
      riskLevel,
      sortBy,
      pageNum,
      pageSize: PAGE_SIZE,
    });
  }

  @autobind
  getCombinationList() {
    const {
      combinationRankListData,
      chartTabChange,
      combinationLineChartData,
      getCombinationLineChart,
      openStockPage,
      openCustomerListPage,
      showModal,
      openDetailPage,
      location: {
        query: {
          combinationType = '',
          sortBy = WEEK_EARNINGS_KEY,
        }
      },
    } = this.props;
    const {
      list = EMPTY_LIST,
    } = combinationRankListData;
    return list.map(item => (
      <CombinationListItem
        showModal={showModal}
        rankTabActiveKey={combinationType}
        data={item}
        key={item.combinationCode}
        chartTabChange={chartTabChange}
        getCombinationLineChart={getCombinationLineChart}
        combinationLineChartData={combinationLineChartData}
        yieldRankValue={sortBy}
        openStockPage={openStockPage}
        openCustomerListPage={openCustomerListPage}
        openDetailPage={openDetailPage}
      />
    ));
  }

  @autobind
  handlePageChange(pageNum) {
    const {
      location: {
        query,
      },
    } = this.props;
    const { replace } = this.context;
    const dom = this.getRef();
    dom.scrollTop = 0;
    window.scrollTo(0, 0);
    replace({
      query: {
        ...query,
        pageNum,
      }
    });
  }

  @autobind
  getRef() {
    return this.listBoxRef.current;
  }

  render() {
    const {
      location,
      combinationTreeList,
      creatorList,
      combinationRankListData: {
        page = EMPTY_OBJECT,
      },
    } = this.props;
    return (
      <div className={styles.combinationRankBox}>
        <InfoTitle
          head="组合排名"
          titleStyle={titleStyle}
        />
        <div className={styles.containerBox}>
          <CombinationFilter
            location={location}
            composeType={combinationTreeList}
            creatorList={creatorList}
          />
          <div className={styles.combinationListBox} ref={this.listBoxRef}>
            {this.getCombinationList()}
          </div>
          <Pagination
            wrapClassName={styles.page}
            onChange={this.handlePageChange}
            current={page.pageNum}
            total={page.totalCount}
            pageSize={PAGE_SIZE}
          />
        </div>
      </div>
    );
  }
}
