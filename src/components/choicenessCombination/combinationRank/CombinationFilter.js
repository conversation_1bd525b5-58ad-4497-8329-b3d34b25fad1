/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 精选组合-组合排名-筛选
 * @Date: 2018-04-18 14:26:13
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-25 13:58:20
*/

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { SingleFilter } from '@lego/filters/src';

import styles from './combinationFilter.less';
import logable from '../../../decorators/logable';
import {
  yieldRankList,
  WEEK_EARNINGS_KEY,
  RISK_LEVEL_LIST,
} from '../config';


export default class CombinationFilter extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    composeType: PropTypes.array,
    creatorList: PropTypes.array.isRequired,
  }

  static defaultProps = {
    composeType: [],
  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '按收益率排序',
      value: '$args[0].value',
    },
  })
  handleYieldSelect(item) {
    const { replace } = this.context;
    const { location: { query = {} } } = this.props;
    replace({
      query: {
        ...query,
        sortBy: item.value,
        pageNum: 1,
      }
    });
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '按风险等级筛选',
      value: '$args[0].vaue',
    },
  })
  handleRiskChange(item) {
    const { value } = item;
    if (_.isEmpty(value)) {
      return;
    }
    const { replace } = this.context;
    const { location: { query = {} } } = this.props;
    replace({
      query: {
        ...query,
        riskLevel: value,
        pageNum: 1,
      }
    });
  }

  // 投资顾问选项变化
  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '按投资顾问筛选',
      value: '$args[0].value',
    },
  })
  handleCreatorSelectChange({ value }) {
    const {
      location: { query = {} },
    } = this.props;
    const { replace } = this.context;
    replace({
      query: {
        ...query,
        adviserId: value.empId || '',
        pageNum: 1,
      }
    });
  }

  // 切换组合类型
  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '按组合类型筛选',
      value: '$args[0].vaue',
    },
  })
  handleComposeTypeChange(item) {
    const { value } = item;
    const { replace } = this.context;
    const { location: { query = {} } } = this.props;
    replace({
      query: {
        ...query,
        combinationType: value,
        pageNum: 1,
      }
    });
  }

  @autobind
  getOptionItemValue({ value: { empId, empName } }) {
    const showEmpId = empId ? `(${empId})` : '';
    return (
      <span>
        {empName}
        {' '}
        {showEmpId}
      </span>
    );
  }

  render() {
    const {
      composeType,
      creatorList,
      location: {
        query: {
          adviserId = '',
          riskLevel = '',
          combinationType = '',
          sortBy = WEEK_EARNINGS_KEY,
        }
      }
    } = this.props;
    // 所有组合有个 value， 而在请求所有组合数据时，后端需要 value 为空
    // 所以手动将所有组合的 value 值设置为空
    let composeData = [
      {
        ...composeType[0],
        value: '',
        label: '所有组合',
      },
    ];
    if (composeType[0] && !_.isEmpty(composeType[0].children)) {
      composeData = composeData.concat([...composeType[0].children]);
    }
    const creatorData = [
      {
        empId: '',
        empName: '不限',
      },
      ...creatorList,
    ];
    return (
      <div className={styles.combinationFilterBox}>
        <div className={styles.formItem}>
          <SingleFilter
            className={styles.searchFilter}
            filterName="组合类型"
            data={composeData}
            dataMap={['value', 'label']}
            value={combinationType}
            onChange={this.handleComposeTypeChange}
            dropdownStyle={{ width: 190 }}
          />
        </div>
        <div className={styles.formItem}>
          <SingleFilter
            className={styles.longSearchFilter}
            filterName="投资顾问"
            data={creatorData}
            dataMap={['empId', 'empName']}
            useLabelInValue
            needItemObj
            value={adviserId}
            onChange={this.handleCreatorSelectChange}
            getOptionItemValue={this.getOptionItemValue}
            dropdownStyle={{ width: 190 }}
          />
        </div>
        <div className={styles.formItem}>
          <SingleFilter
            className={styles.moreTextFilter}
            filterName="收益率排序"
            filterId="rankValue"
            data={yieldRankList}
            dataMap={['value', 'label']}
            value={sortBy}
            onChange={this.handleYieldSelect}
            dropdownStyle={{ width: 270 }}
          />
        </div>
        <div className={styles.formItem}>
          <SingleFilter
            className={styles.searchFilter}
            filterName="风险等级"
            filterId="riskLevel"
            data={RISK_LEVEL_LIST}
            dataMap={['value', 'label']}
            value={riskLevel}
            onChange={this.handleRiskChange}
            dropdownStyle={{ width: 190 }}
          />
        </div>
      </div>
    );
  }
}
