/**
 * @Description: 调仓组合 tab
 * @Author: Liujianshu-K0240007
 * @Date: 2019-01-22 16:14:32
 * @Last Modified by: Liujianshu-K0240007
 * @Last Modified time: 2019-01-25 17:28:35
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import Icon from '../../common/Icon';
import Tooltip from '../../common/Tooltip';
import { time } from '@/helper';
import {
  directionRange,
  sourceType,
  securityType as configSecurityType,
  typeList,
  formatDateStr,
} from '../config';
import styles from './adjust.less';
import logable, { logPV } from '@/decorators/logable';

// 筛选出页面需要的 调入方向组合
const directionArray = _.filter(directionRange, o => o.value);
// securityType 里股票对应的值
const STOCK_CODE = configSecurityType[0].value;
// 持仓历史
const HISTORY_TYPE = typeList[0];
export default class Adjust extends PureComponent {
  static propTypes = {
    showModal: PropTypes.func.isRequired,
    data: PropTypes.object.isRequired,
    openCustomerListPage: PropTypes.func.isRequired,
    openStockPage: PropTypes.func.isRequired,
    openDetailPage: PropTypes.func.isRequired,
  }

  static defaultProps = {

  }

  // 证券名称点击事件
  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '组合调仓',
      value: '$args[2]',
    },
  })
  handleSecurityClick(type, code) {
    if (type === STOCK_CODE) {
      const { openStockPage } = this.props;
      openStockPage({ code });
    }
  }

  @autobind
  @logPV({
    pathname: '/modal/adjustHistoryModal',
    title: '调仓历史弹框',
  })
  handleMoreClick(value) {
    const { showModal } = this.props;
    showModal({
      directionCode: value,
      type: HISTORY_TYPE,
    });
  }

  // 判断前两条数据的 理由 字段，都有则显示理由，否则不显示理由
  @autobind
  calcReason(list) {
    let reasonFlag = false;
    for (let i = 0; i < 2; i++) {
      if (!_.isEmpty(list[i]) && !_.isEmpty(list[i].reason)) {
        reasonFlag = true;
      }
    }
    return reasonFlag;
  }

  // 组合名称点击事件
  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '组合调仓',
      value: '$args[0].name',
    },
  })
  handleNameClick(obj) {
    const { openDetailPage } = this.props;
    openDetailPage(obj);
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '组合调仓',
      value: '$args[0].name',
    },
  })
  handleOpenCustomerListPage(openPayload) {
    const { openCustomerListPage } = this.props;
    openCustomerListPage(openPayload);
  }

  // 设置 Tooltip
  @autobind
  renderTooltip(value) {
    let reactElement = '调仓理由：暂无';
    if (!_.isEmpty(value)) {
      const trimValue = _.trim(value);
      reactElement = (
        <Tooltip
          placement="bottomLeft"
          content={trimValue}
          trigger="hover"
        >
          <div className={styles.ellipsis}>
            {trimValue}
          </div>
        </Tooltip>
      );
    }
    return reactElement;
  }

  // 渲染调仓内容
  @autobind
  renderContent() {
    const { data } = this.props;
    const { list } = data;
    return directionArray.map((item, idx) => {
      const { icon, title, value } = item;
      const showList = _.filter(list, o => o.directionCode === Number(value));
      const itemKey = `${value}${idx}`;
      return (
        <div className={styles.adjustBox} key={itemKey}>
          <div className={styles.left}>
            <i>{icon}</i>
            <span>{title}</span>
          </div>
          <div className={styles.right}>
            {this.renderList(showList)}
          </div>
          <div className={styles.more}>
            <a onClick={() => this.handleMoreClick(value)}>{'更多 >'}</a>
          </div>
        </div>
      );
    });
  }

  @autobind
  renderList(list) {
    const flag = this.calcReason(list);
    // 如果都有理由字段，则显示两条数据，否则显示三条
    const newList = _.slice(list, 0, flag ? 2 : 3);
    return newList.map((child, index) => {
      const {
        securityName,
        securityCode,
        securityType,
        combinationName,
        combinationCode,
        reason,
      } = child;
      const openPayload = {
        name: securityName,
        code: securityCode,
        type: securityType,
        source: sourceType.security,
      };
      const childKey = `${securityCode}${index}`;
      const openDetailPayload = {
        id: combinationCode,
        name: combinationName,
      };
      return (
        <div className={`${styles.rightItem} clearfix`} key={childKey}>
          <div className={styles.firstLine}>
            <a
              className={styles.securityName}
              title={securityName}
              onClick={() => this.handleSecurityClick(securityType, securityCode, `${securityName}(${securityCode})`)
              }
            >
              {`${securityName} (${securityCode})`}
            </a>
            <span className={styles.time}>{time.format(child.time, formatDateStr)}</span>
          </div>
          <div className={styles.secondLine}>
            <a
              className={styles.combinationName}
              title={combinationName}
              onClick={() => this.handleNameClick(openDetailPayload)}
            >
              {combinationName}
            </a>
            <a
              className={styles.customerLink}
              onClick={() => this.handleOpenCustomerListPage(openPayload)}
            >
              <Icon type="kehuzu" />持仓客户
            </a>
          </div>
          {
            flag
              ? (
                <div className={styles.thirdLine}>
                  {this.renderTooltip(reason)}
                </div>
              )
              : null
          }
        </div>
      );
    });
  }

  render() {
    return (
      <div className={styles.adjust}>
        {this.renderContent()}
      </div>
    );
  }
}
