@import "../../../css/util.less";
.adjust {
  height: 395px;
  font-size: 12px;
  .adjustBox {
    position: relative;
    border-bottom: 1px solid #e9e9e9;
    .more {
      position: absolute;
      bottom: 6px;
      right: 0;
    }
    .left {
      width: 76px;
      text-align: center;
      position: absolute;
      i {
        display: block;
        margin: 60px auto 8px;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        line-height: 36px;
        color: #f00;
        font-size: 18px;
        font-style: normal;
        border: 1px solid #f00;
        background: #fff;
      }
    }
    .right {
      margin-left: 76px;
      height: 194px;
      overflow: hidden;
      .rightItem {
        margin-top: 10px;
        border-bottom: 1px dashed #e9e9e9;
        .firstLine {
          font-size: 14px;
          position: relative;
          .securityName {
            font-weight: bold;
            .one-line-ellipsis;
            display: inline-block;
            position: absolute;
            width: 60%;
            font-size: 14px;
            color: #108ee9;
            line-height: 20px;
          }
          .time {
            display: block;
            text-align: right;
            color: #999;
            font-size: 12px;
          }
        }
        .secondLine {
          margin: 5px 0 4px;
          .customerLink {
            font-size: 12px;
            color: #108ee9;
            line-height: 17px;
            i {
              color: #108ee9;
              margin-right: 6px;
            }
          }
          .combinationName {
            font-size: 12px;
            color: #333;
            line-height: 20px;
          }
          a {
            &:last-child {
              float: right;
            }
          }
        }
        .thirdLine {
          font-size: 12px;
          color: #666;
          line-height: 17px;
          margin-bottom: 10px;
          .one-line-ellipsis;
        }
        &:last-child {
          border-bottom: none;
        }
      }
    }
    &:last-child {
      border-bottom: 0;
      .left {
        i {
          color: #16a49d;
          border: 1px solid #16a49d;
        }
      }
    }
  }
}
.ellipsis {
  color: #828282;
  .one-line-ellipsis;
}
