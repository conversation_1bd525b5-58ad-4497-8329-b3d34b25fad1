/**
 * @Description: 策略中心-精选组合-组合精粹、组合调仓
 * @Author: Liujianshu-K0240007
 * @Date: 2019-01-23 13:36:36
 * @Last Modified by: Liujianshu-K0240007
 * @Last Modified time: 2019-01-25 16:29:02
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
// import _ from 'lodash';
import { Tabs } from 'antd';


import Adjust from './Adjust';
import Essence from './Essence';
import { logCommon } from '@/decorators/logable';
import {
  ESSENCE_TAB,
  ADJUST_TAB,
} from '../config';
import styles from './index.less';

const TabPane = Tabs.TabPane;

export default class EssenceAndAdjust extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    essenceData: PropTypes.array.isRequired,
    adjustData: PropTypes.object.isRequired,
    showModal: PropTypes.func.isRequired,
    openCustomerListPage: PropTypes.func.isRequired,
    openStockPage: PropTypes.func.isRequired,
    openDetailPage: PropTypes.func.isRequired,
  }

  static defaultProps = {

  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    const {
      location: {
        query: {
          combinationTab = ESSENCE_TAB,
        },
      },
    } = props;
    this.state = {
      combinationTab,
    };
  }

  @autobind
  handleTabChange(activeKey) {
    const { location: { query } } = this.props;
    this.setState({
      combinationTab: activeKey,
    }, () => {
      this.context.replace({
        query: {
          ...query,
          combinationTab: activeKey,
        }
      });
      logCommon({
        type: 'Click',
        payload: {
          name: '组合 tab 切换',
          value: 'todo',
        },
      });
    });
  }

  render() {
    const {
      essenceData,
      adjustData,
      showModal,
      openCustomerListPage,
      openStockPage,
      openDetailPage,
    } = this.props;
    const {
      combinationTab,
    } = this.state;
    return (
      <div className={styles.essenceAndAdjust}>
        <Tabs
          activeKey={combinationTab}
          animated={false}
          onChange={this.handleTabChange}
        >
          <TabPane tab="组合精粹" key={ESSENCE_TAB}>
            <Essence
              data={essenceData}
              openDetailPage={openDetailPage}
            />
          </TabPane>
          <TabPane tab="组合调仓" key={ADJUST_TAB}>
            <Adjust
              showModal={showModal}
              data={adjustData}
              openCustomerListPage={openCustomerListPage}
              openStockPage={openStockPage}
              openDetailPage={openDetailPage}
            />
          </TabPane>
        </Tabs>
      </div>
    );
  }
}
