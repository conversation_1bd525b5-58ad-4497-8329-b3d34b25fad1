@import "../../../css/util.less";
.essence {
  height: 395px;
  .essenceItem {
    height: 192px;
    position: relative;
    box-sizing: border-box;
    padding: 14px 0;
    &:first-child {
      border-bottom: 1px solid #e9e9e9;
    }
    &:last-child .left {
      i {
        color: #f5842d;
      }
    }
    .left {
      position: absolute;
      width: 77px;
      text-align: center;
      top: 64px;
      color: #999;
      i {
        display: block;
        width: 37px;
        height: 37px;
        line-height: 30px;
        margin: 0 auto;
        font-size: 37px;
        color: #5286de;
      }
    }
    .right {
      margin-left: 77px;
      h3, h4 {
        line-height: 20px;
        font-size: 12px;
        color: #666;
        font-weight: bold;
      }
      .description {
        margin: 9px 0 14px;
        font-size: 12px;
        color: #666;
        line-height: 17px;
        height: 34px;
        overflow: hidden;
      }
      .list {
        width: 325px;
        margin-top: 9px;
        li {
          display: inline-block;
          width: 149px;
          margin-right: 25px;
          line-height: 17px;
          &:nth-child(2n) {
            margin-right: 0;
          }
        }
        div {
          cursor: pointer;
          .one-line-ellipsis;
          font-size: 12px;
          color: #108ee9;
        }
      }
    }
  }
}
