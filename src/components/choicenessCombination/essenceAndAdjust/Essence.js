/**
 * @Description: 组合精粹
 * @Author: Liujianshu-K0240007
 * @Date: 2019-01-22 15:15:44
 * @Last Modified by: Liujianshu-K0240007
 * @Last Modified time: 2019-01-25 16:22:54
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import Icon from '../../common/Icon';
import logable from '@/decorators/logable';
import styles from './essence.less';
import {
  ESSENCE_TEXT,
} from '../config';

export default class Essence extends PureComponent {
  static propTypes = {
    data: PropTypes.array.isRequired,
    openDetailPage: PropTypes.func.isRequired,
  }

  // 组合名称点击事件
  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '组合精粹-推荐组合',
      value: '$args[0].name',
    },
  })
  handleNameClick(obj) {
    const { openDetailPage } = this.props;
    openDetailPage(obj);
  }

  // 渲染推荐组合列表
  @autobind
  renderIntroduceList(list) {
    return _.map(list, (item) => {
      const {
        code,
        name,
      } = item;
      const openDetailPayload = {
        id: code,
        name,
      };
      return (
        <li key={code}>
          <div onClick={() => this.handleNameClick(openDetailPayload)}>{item.name}</div>
        </li>
      );
    });
  }

  // 渲染精粹列表内容
  @autobind
  renderListContent(item) {
    const {
      name = '',
      description = '',
      introduce = [],
    } = item;
    const iconElement = <Icon type={name === ESSENCE_TEXT ? 'lianghuashaixuan' : 'fenggelundong'} />;
    return (
      <div className={styles.essenceItem} key={name}>
        <div className={styles.left}>
          {iconElement}
          {name}
        </div>
        <div className={styles.right}>
          <h3>指标定义</h3>
          <div className={styles.description} title={description}>{description}</div>
          <h4>推荐组合</h4>
          <ul className={styles.list}>
            {this.renderIntroduceList(introduce)}
          </ul>
        </div>
      </div>
    );
  }

  // 渲染精粹列表
  @autobind
  renderList() {
    const {
      data,
    } = this.props;
    return _.map(data, item => this.renderListContent(item));
  }

  render() {
    return (
      <div className={styles.essence}>
        {this.renderList()}
      </div>
    );
  }
}
