@import "../../css/util.less";
.modal {
  .header {
    display: flex;
    width: calc(~"100% - 60px");
    padding: 20px 0;
    position: absolute;
    margin-bottom: 12px;
    background: #fff;
    z-index: 2;
    .headerItem {
      margin-right: 10px;
      & > span {
        margin-right: 10px;
      }
      &:last-child {
        position: absolute;
        top: 20px;
        right: 6px;
        margin-right: 0;
      }
        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .ant-select {
          width: auto !important;
          min-width: 174px;
          .ant-select-arrow {
            transform: scale(.75);
            border: none;
            border-top: 10px solid #c2c2c2;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            height: 0;
            width: 0;
            overflow: hidden;
          }
        }
        .ant-select-tree-dropdown {
          width: 230px !important;
        }
      }
    }
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      input {
        height: 28px !important;
      }
      .ant-input-suffix {
        button {
          background-color: transparent;
          border-color: transparent;
          color: #333;
          &:visited, &:active {
            background-color: transparent;
            border-color: transparent;
          }
        }
      }
      .ant-select {
        width: 142px;
      }
    }
  }
  .content {
    margin-top: 68px;
    z-index: 1;
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      table {
        table-layout: fixed;
        width: 100%;
        tr td, rt th {
          font-size: 14px;
          line-height: 20px;
          color: #666;
        }
        thead tr {
          height: 38px;
          line-height: 38px;
          th {
            padding: 0 6px;
          }
        }
        tr td {
          padding: 10px 6px;
        }
        .ant-table-tbody {
          tr {
            td {
              div {
                .one-line-ellipsis;
              }
            }
          }
        }
      }
    }
  }
}
.popover {
  box-sizing: border-box;
  width: 210px;
  padding: 10px;
  word-break: break-all;
}
.wrapperTd {
  max-height: 36px;
  overflow: hidden;
  position: relative;
  div {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #fff;
  }
}
.dropdownClassName {
  width: auto !important;
  max-width: 300px;
}
.customerLink {
  color: #7d9be0;
}
.ellipsis {
  .one-line-ellipsis;
}
