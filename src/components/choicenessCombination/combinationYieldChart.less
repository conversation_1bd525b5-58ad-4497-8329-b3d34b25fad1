.yieldChartBox {
  position: relative;
  h3 {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
    font-weight: bold;
  }
  .tabBox {
    position: absolute;
    z-index: 10;
    left: 0;
    top: 30px;
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-tabs-bar {
        margin: 0;
        border-bottom: none;
      }
      .ant-tabs-ink-bar-animated {
        height: 0;
      }
      .ant-tabs-tab {
        margin: 0 10px 0 0;
        padding: 0 4px 3px;
        font-size: 12px;
        color: #666;
        &.ant-tabs-tab-active {
          background-color: #666;
          color: #fff;
          border-radius: 2px;
          transition: none;
        }
      }
    }
  }
  .chartWrapper {
    height: 208px;
  }
}
