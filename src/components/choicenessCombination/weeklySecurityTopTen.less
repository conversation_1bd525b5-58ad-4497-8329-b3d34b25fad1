@import "../../css/util.less";
.weeklySecurityTopTenBox {
  .weeklySecurityTopTenContainer {
    margin-top: 3px;
    padding-left: 14px;
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-table-fixed-header .ant-table-scroll .ant-table-header {
        overflow: hidden !important;
        padding-bottom: 8px !important;
      }
      .ant-table {
        table {
          table-layout: fixed;
          width: 100%;
        }
        .ant-table-thead {
          height: 26px;
          line-height: 26px;
          font-size: 12px;
          color: #4a4a4a;
          tr th {
            background: #fff;
            border-bottom: 2px solid #ccc;
            &:first-child {
              text-indent: 10px;
            }
            &:last-child span {
              margin-right: 12px;
            }
          }
        }
        tr th, tr td {
          padding: 0;
        }
        .ant-table-tbody {
          tr {
            background: #fff;
            position: relative;
            td {
              border-bottom: 0;
              padding: 8px 0 26px;
              div {
                .one-line-ellipsis;
              }
              &:first-child {
                width: 27%;
                text-indent: 10px;
              }
              &:nth-child(2) {
                width: 22%;
              }
              &:nth-child(3) {
                width: 15%;
              }
              &:nth-child(4) {
                width: 24%;
              }
              &:nth-child(5) {
                width: 12%;
              }
            }
            &:nth-child(even) {
              background-color: #f2f2f2;
            }
          }
        }
      }
      .ant-tabs-nav .ant-tabs-tab {
        padding: 0 16px 12px;
      }
    }
    .customerLink {
      margin-right: 10px;
    }
    .reason {
      width: 84%;
      position: absolute;
      left: 0;
      .one-line-ellipsis;
    }
    .name {
      padding-left: 4%;
      a {
        color: rgba(0, 0, 0, .65);
      }
    }
    .customerLink {
      i {
        color: #7d9be0;
      }
    }
    .up {
      padding-left: 4%;
      color: #e33c39;
    }
    .down {
      padding-left: 4%;
      color: #499d37;
    }
  }
}
