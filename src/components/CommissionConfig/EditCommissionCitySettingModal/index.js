/*
 * @Author: sunweibin
 * @Date: 2021-07-27 10:02:27
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-08-13 10:19:51
 * @description 佣金城市地域配置-编辑
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { Input, Alert } from 'antd';

import IFWrap from '@/components/common/IFWrap';
import Modal from '@/newUI/modal';
import { regxp } from '@/helper';
import FormItemWrap from '../FormItemWrap';

import styles from './index.less';

const MODAL_STYLE = {
  height: '406px',
};

const FORM_STYLE = {
  width: '200px',
};

const TEXTAREA_STYLE = {
  width: '446px',
  height: '60px',
};

export default class EditCommissionCitySettingModal extends PureComponent {
  static propTypes = {
    // 编辑的数据
    record: PropTypes.object.isRequired,
    // 确认回调
    onConfirm: PropTypes.func.isRequired,
    // 取消回调
    onCancel: PropTypes.func.isRequired,
    // 关闭回调
    onClose: PropTypes.func.isRequired,
    // 查询是否重复校验接口
    onValidate: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);

    const { record } = props;

    this.state = {
      // 起始佣金率
      startCommission: record?.startCommission,
      commissionError: '',
      // 营业部
      departmentName: record?.departmentName,
      departmentCode: record?.departmentCode,
      // 备注
      remark: record?.remark,
      remarkError: '',
      // 已经存在相关的佣金城市地域配置
      existAlert: false,
    };

    this.footer = [
      {
        key: 'cancel',
        text: '取消',
        onClick: this.props.onCancel,
      },
      {
        key: 'confirm',
        text: '确定',
        type: 'primary',
        onClick: this.handleConfirm,
      },
    ];
  }

  @autobind
  checkBeforeConfirm() {
    const {
      remark,
      startCommission,
    } = this.state;

    const errors = {};

    if (!regxp.positiveNum1.test(startCommission)) {
      errors.commissionError = '必须为数字';
    }

    if (_.size(remark) > 50) {
      errors.remarkError = '最多50个字符';
    }

    return errors;
  }

  @autobind
  handleConfirm() {
    const errors = this.checkBeforeConfirm();

    if (!_.isEmpty(errors)) {
      this.setState(errors);
      return;
    }

    const {
      remark,
      departmentCode,
      departmentName,
      startCommission,
    } = this.state;

    const { record } = this.props;
    const provinceCode = record?.provinceCode;
    const cityCode = record?.cityCode;

    // 点击【确定】按钮时需要，校验下是否重复
    this.props.onValidate({
      provinceCode,
      cityCode,
      departmentCode,
    }).then((result) => {
      if (result === 'Y') {
        this.props.onConfirm({
          id: record?.id,
          remark,
          // NOTE: 为了方便，将原始的营业部门数据传给后端
          departmentCode,
          departmentName,
          startCommission: _.toNumber(startCommission),
        });
      } else {
        this.setState({ existAlert: true });
      }
    });
  }

  @autobind
  handleStartCommissionChange(e) {
    const value = e.target.value;
    this.setState({
      startCommission: value,
      commissionError: '',
    });
  }

  @autobind
  handleRemarkChange(e) {
    const remark = e.target.value;
    this.setState({
      remark,
      remarkError: '',
    });
  }

  render() {
    const {
      record,
    } = this.props;

    const {
      provinceError,
      cityError,
      startCommission,
      commissionError,
      remark,
      remarkError,
      existAlert,
    } = this.state;

    const provinceText = record?.provinceName;
    const cityText = record?.cityName;
    const departmentText = record?.departmentName || '--';

    return (
      <Modal
        title="编辑"
        size="normal"
        visible
        destroyOnClose
        style={MODAL_STYLE}
        onModalClose={this.props.onClose}
        modalFooter={this.footer}
      >
        <div className={styles.container}>
          <IFWrap when={existAlert}>
            <Alert message="记录已存在" type="error" showIcon />
          </IFWrap>
          <FormItemWrap
            title="省/(直辖)市"
            isRequired
            errorMsg={provinceError}
          >
            <div className={styles.textItem}>{provinceText}</div>
          </FormItemWrap>
          <FormItemWrap
            title="城市"
            isRequired
            errorMsg={cityError}
          >
            <div className={styles.textItem}>{cityText}</div>
          </FormItemWrap>
          <FormItemWrap
            title="起始佣金（‰）"
            isRequired
            errorMsg={commissionError}
          >
            <Input
              placeholder="请输入"
              style={FORM_STYLE}
              value={startCommission}
              onChange={this.handleStartCommissionChange}
            />
          </FormItemWrap>
          <FormItemWrap title="营业部">
            <div className={styles.textItem}>{departmentText}</div>
          </FormItemWrap>
          <FormItemWrap
            isTextArea
            title="备注"
            errorMsg={remarkError}
          >
            <Input.TextArea
              placeholder="请输入"
              style={TEXTAREA_STYLE}
              value={remark}
              onChange={this.handleRemarkChange}
            />
          </FormItemWrap>
        </div>
      </Modal>
    );
  }
}
