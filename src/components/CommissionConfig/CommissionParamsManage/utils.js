import _ from 'lodash';

import { TYPE_ALL, MILLION } from './config';

// 根据业务类型的value值获取对应的label
export function getOptionName(optionList, value) {
  return _.find(optionList, (item) => item.value === value)?.label || '';
}

// 编辑数据回填：转客户性质，因为后端返回的客户类型：全部对应的是'',需要转成前端配置的all
export function handleTransCustTypeValue(value) {
  return value === '' ? TYPE_ALL : value;
}

// 新建和编辑提交：转客户性质，前端全部对应的是all，转成后端对应的''
export function handleTransSubmitCustType(value) {
  return value === TYPE_ALL ? '' : value;
}

// 列表和编辑数据回填展示：金额数据单位为万元，后端返回的是元，前端展示需要除以10000
export function handleTransListMoney(value) {
  return _.isNumber(value) ? value / MILLION : value;
}

// 新建和编辑提交：金额前端展示的是万元，需要传给后端的是元，需要乘以10000
export function handleTransSubmitMoney(value) {
  return !_.isEmpty(String(value)) ? value * 10000 : null;
}

// 新建和编辑获取佣金率上下限indexOrder
export function getCommIndexOrder(data, value) {
  return _.find(data, (item) => item?.value === value)?.indexOrder || null;
}
