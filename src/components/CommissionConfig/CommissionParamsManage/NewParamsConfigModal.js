/*
 * @Author: yanfaping
 * @Date: 2021-07-27 18:38:53
 * @Last Modified by: sunweibin
 * @Last Modified time: 2022-03-01 17:36:11
 * @description 佣金率参数条件配置-新建Modal
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import {
  Form, Select, Alert, Input, AutoComplete,
} from 'antd';
import Modal from '@/newUI/modal';
import IFWrap from '@/components/common/IFWrap';
import { regxp } from '@/helper';
import { logCommon } from '@/decorators/logable';
import SimilarAutoComplete from '@/components/common/similarAutoComplete';
import {
  PARAMS_CUST_TYPE_CONFIG,
  SINGLE_KEY,
} from './config';
import {
  getOptionName,
  handleTransSubmitCustType,
  handleTransSubmitMoney,
} from './utils';
import styles from './newParamsConfigModal.less';

const FormItem = Form.Item;
const create = Form.create;
const Option = Select.Option;

@create()
export default class NewParamsConfigModal extends PureComponent {
  static propTypes = {
    // 表单
    form: PropTypes.object.isRequired,
    // 是否显示弹框
    visible: PropTypes.bool.isRequired,
    // 关闭弹框
    onClose: PropTypes.func.isRequired,
    // 业务品种选项列表
    comRateList: PropTypes.array.isRequired,
    // 保存校验
    onValidate: PropTypes.func.isRequired,
    // 保存
    onSave: PropTypes.func.isRequired,
    // 佣金率选项列表
    commissionRateData: PropTypes.array.isRequired,
    // 获取佣金率信息
    queryCommissionRateData: PropTypes.func.isRequired,
    // 搜索查询营业部
    queryDepartmentList: PropTypes.func.isRequired,
    // 场景下拉选项数据
    sceneData: PropTypes.array.isRequired,
  }

  constructor(props) {
    super(props);

    this.state = {
      // 新建时校验提示，如果记录已存在，则无法提交提示报错信息
      showAlert: false,
      // 营业部信息
      departmentList: [],
    };
  }

  @autobind
  handleModalConfirm() {
    const { form } = this.props;
    form.validateFields({ force: true }, (errors, values) => {
      if (!_.isEmpty(errors)) {
        return;
      }
      this.handleSubmit(values);
    });
  }

  @autobind
  handleSubmit(values) {
    const {
      scene,
      businessType,
      custType,
      comRate,
      yearStockProfit,
      yearStockExchange,
      totalAsset,
      departmentCode,
    } = values;
    // 如果选择了全部，转成空
    const newCustType = handleTransSubmitCustType(custType);
    const { comRateList, commissionRateData } = this.props;
    // 业务品种名称
    const businessTypeName = getOptionName(comRateList, businessType);
    // 佣金率名称
    const comRateName = getOptionName(commissionRateData, comRate);
    // 校验和保存公共参数
    const commonParams = {
      scene,
      businessType,
      businessTypeName,
      custType: newCustType,
      comRate,
      comRateName,
      departmentCode,
    };
    // 保存时需校验场景、业务品种、客户性质、佣金率是否重复，如有重复数据则无法新增，提示：同一业务品种不能重复，请修改
    this.props.onValidate(commonParams).then((result) => {
      if (result !== 'Y') {
        this.props.onSave({
          ...values,
          yearStockProfit: handleTransSubmitMoney(yearStockProfit),
          yearStockExchange: handleTransSubmitMoney(yearStockExchange),
          totalAsset: handleTransSubmitMoney(totalAsset),
          ...commonParams,
        });
        this.setState({ showAlert: false });
      } else {
        this.setState({ showAlert: true });
      }
    });
  }

  @autobind
  getModalBtnGroup() {
    return [
      {
        key: 'cancel',
        text: '取消',
        onClick: this.handleCloseModal,
      },
      {
        key: 'confirm',
        text: '确定',
        type: 'primary',
        onClick: this.handleModalConfirm,
      }
    ];
  }

  @autobind
  handleCloseModal() {
    this.setState({ showAlert: false });
    this.props.onClose();
  }

  @autobind
  renderSelectList(list) {
    if (_.isEmpty(list)) {
      return [];
    }
    return _.map(list, this.renderOption);
  }

  @autobind
  renderOption({ value, label }) {
    return <Option key={value} value={value}>{label}</Option>;
  }

  @autobind
  validateMoney(rule, value, callback) {
    if (!_.isEmpty(value) && !regxp.positiveNum1.test(value)) {
      return callback('必须为数字');
    }
    return callback();
  }

  @autobind
  handleChangeScence(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-新建-选择场景',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeBusinessType(value, option) {
    this.props.queryCommissionRateData({
      businessType: value,
    }).then(() => {
      // 因为是联动，重新选择业务品种应该清空佣金率之前的值
      const { form } = this.props;
      form.setFieldsValue({
        comRate: undefined,
      });
    });
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-新建-选择业务品种',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeCustType(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-新建-选择客户性质',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeComRate(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-新建-选择佣金率',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  saveDepartment(departmentList) {
    this.setState({ departmentList });
  }

  @autobind
  handleSearchDepartment(keyword) {
    if (_.isEmpty(keyword)) {
      return;
    }
    this.props.queryDepartmentList({
      keyword,
    }).then(this.saveDepartment);
  }

  @autobind
  handleDepartmentSelect(option) {
    const { form } = this.props;
    if (_.isEmpty(option)) {
      form.setFieldsValue({
        departmentCode: '',
      });
      this.setState({ showAlert: false });
      return;
    }
    form.setFieldsValue({
      departmentCode: option.id
    });
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-新建',
        value: JSON.stringify(option),
      }
    });
  }

  @autobind
  renderDepartmentOption(option) {
    const { id, name } = option;
    return (
      <AutoComplete.Option key={id} value={name}>{name}</AutoComplete.Option>
    );
  }

  render() {
    const {
      visible,
      form: {
        getFieldDecorator,
        getFieldValue,
      },
      comRateList,
      commissionRateData,
      sceneData,
    } = this.props;
    const { showAlert, departmentList } = this.state;
    // 场景默认值，undefined展示placeholder
    const scene = getFieldValue('scene') || undefined;
    // 场景可选项，去除不限
    const transSceneList = !_.isEmpty(sceneData) ? _.drop(sceneData) : [];
    // 是否展示营业部(只有单佣金调整时需要展示营业部)
    const isShowDepart = scene === SINGLE_KEY;
    return (
      <Modal
        title="新建"
        size="normal"
        visible={visible}
        destroyOnClose
        onModalClose={this.handleCloseModal}
        modalFooter={this.getModalBtnGroup()}
      >
        <div className={styles.content}>
          <IFWrap when={showAlert}>
            <Alert message="同一业务品种不能重复，请修改" type="error" showIcon />
          </IFWrap>
          <Form>
            <FormItem label="场景">
              {getFieldDecorator('scene', {
                initialValue: scene,
                rules: [{ required: true, message: '请选择场景' }],
              })(
                <Select placeholder="请选择" onChange={this.handleChangeScence}>
                  {this.renderSelectList(transSceneList)}
                </Select>
              )}
            </FormItem>
            <FormItem label="业务品种">
              {getFieldDecorator('businessType', {
                rules: [{ required: true, message: '请选择业务品种' }],
              })(
                <Select placeholder="请选择" onChange={this.handleChangeBusinessType}>
                  {this.renderSelectList(comRateList)}
                </Select>
              )}
            </FormItem>
            <FormItem label="客户性质">
              {getFieldDecorator('custType', {
                rules: [{ required: true, message: '请选择客户性质' }],
              })(
                <Select placeholder="请选择" onChange={this.handleChangeCustType}>
                  {this.renderSelectList(PARAMS_CUST_TYPE_CONFIG)}
                </Select>
              )}
            </FormItem>
            <IFWrap when={isShowDepart}>
              <FormItem label="营业部">
                {getFieldDecorator('departmentCode', {
                })(
                  <div>
                    <SimilarAutoComplete
                      placeholder="请选择"
                      optionList={departmentList}
                      style={{ width: 200 }}
                      optionKey="id"
                      onSelect={this.handleDepartmentSelect}
                      onSearch={this.handleSearchDepartment}
                      renderOptionNode={this.renderDepartmentOption}
                    />
                  </div>
                )}
              </FormItem>
            </IFWrap>
            <FormItem label="佣金率">
              {getFieldDecorator('comRate', {
                rules: [{ required: true, message: '请选择佣金率' }],
              })(
                <Select placeholder="请选择" onChange={this.handleChangeComRate}>
                  {this.renderSelectList(commissionRateData)}
                </Select>
              )}
            </FormItem>
            <FormItem label="总资产(万元)">
              {getFieldDecorator('totalAsset', {
                rules: [
                  { validator: this.validateMoney },
                ],
              })(
                <Input
                  placeholder="请输入"
                />
              )}
            </FormItem>
            <FormItem label="近一年股基交易量(万元)">
              {getFieldDecorator('yearStockExchange', {
                rules: [{ validator: this.validateMoney }],
              })(
                <Input
                  placeholder="请输入"
                />
              )}
            </FormItem>
            <FormItem label="近一年股基交易毛佣金(万元)">
              {getFieldDecorator('yearStockProfit', {
                rules: [{ validator: this.validateMoney }],
              })(
                <Input
                  placeholder="请输入"
                />
              )}
            </FormItem>
          </Form>
        </div>
      </Modal>
    );
  }
}
