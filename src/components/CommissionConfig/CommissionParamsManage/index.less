.manageWrap {
  height: 100%;
  padding-top: 14px;

  @invest-btn-base-color: #1eaaf1;
  @invest-btn-text-color: #777;
  @invest-btn-text-hvr-color: @invest-btn-base-color;
  @invest-btn-border-color: #ddd;
  @invest-btn-border-hvr-color: @invest-btn-base-color;

  .radioWarp {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-radio-button-wrapper {
        font-size: 14px;
        line-height: 18px;
        padding: 5px 12px;
      }
      .ant-radio-button-wrapper-checked {
        border-color: @invest-btn-base-color;
        color: #fff;
        background-color: @invest-btn-base-color;
        box-shadow: none;
        &:hover {
          color: #fff;
          border-color: @invest-btn-base-color;
          box-shadow: none;
        }
      }
    }
  }

  .listArea {
    .operate {
      display: flex;
      justify-content: space-between;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .iconfont {
          font-size: 14px;
          color: #108ee9;
          cursor: pointer;
        }
      }
    }

    .defaultValue {
      font-size: 14px;
      color: #d8d8d8;
    }

    .tip {
      margin-top: 15px;
    }
  }
}
