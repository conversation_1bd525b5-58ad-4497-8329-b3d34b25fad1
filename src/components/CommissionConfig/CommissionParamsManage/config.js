/*
 * @Author: yanfaping
 * @Date: 2021-07-27 18:38:53
 * @Last Modified by: sunweibin
 * @Last Modified time: 2022-02-21 16:52:28
 * @description 全品种佣金参数管理-配置项
 */
// Tab的名称配置
export const commissionParamsTab = {
  rangeTab: '佣金率范围配置',
  paramsTab: '佣金率参数条件配置',
};
// 佣金率范围配置-表格columns配置
export const RANGE_CONFIG_COLUMNS = [
  {
    title: '场景名称',
    dataIndex: 'sceneName',
    width: 84,
  },
  {
    title: '业务品种',
    dataIndex: 'businessTypeName',
    width: 112,
  },
  {
    title: '客户性质',
    dataIndex: 'custTypeName',
    width: 56,
  },
  {
    title: '营业部',
    dataIndex: 'department',
    width: 140,
  },
  {
    title: '佣金率上限',
    dataIndex: 'comRateMaxName',
    width: 145,
  },
  {
    title: '佣金率下限',
    dataIndex: 'comRateMinName',
    width: 145,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    width: 42,
  },
];
// 佣金率参数条件配置-表格columns配置
export const PARAMS_CONFIG_COLUMNS = [
  {
    title: '场景名称',
    dataIndex: 'sceneName',
    width: 84,
  },
  {
    title: '业务品种',
    dataIndex: 'businessTypeName',
    width: 112,
  },
  {
    title: '客户性质',
    dataIndex: 'custTypeName',
    width: 56,
  },
  {
    title: '营业部',
    dataIndex: 'department',
    width: 140,
  },
  {
    title: '佣金率',
    dataIndex: 'comRateName',
    width: 145,
  },
  {
    title: '总资产(万元)',
    dataIndex: 'totalAsset',
    width: 101,
    align: 'right',
  },
  {
    title: '近一年股基交易量(万元)',
    dataIndex: 'yearStockExchange',
    width: 151,
    align: 'right',
  },
  {
    title: '近一年股基交易毛佣金(万元)',
    dataIndex: 'yearStockProfit',
    width: 179,
    align: 'right',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    width: 42,
  },
];
// 场景配置列表--(佣金范围配置)
export const SCENE_CONFIG = [
  {
    value: '',
    label: '不限',
  },
  {
    value: '0201',
    label: '佣金调整',
  },
  {
    value: '0202',
    label: '批量佣金调整',
  },
  {
    value: '0206',
    label: '特殊佣金调整',
  },
  {
    value: '0205',
    label: '自动外呼调佣',
  },
  {
    value: '0207',
    label: '自动化调佣',
  },
  {
    value: '0208',
    label: '渠道开户调佣',
  },
];
// 场景配置列表--(佣金参数配置)
export const SCENE_CONFIG_PARAMS = [
  {
    value: '',
    label: '不限',
  },
  {
    value: '0201',
    label: '佣金调整',
  },
  {
    value: '0206',
    label: '特殊佣金调整',
  },
  {
    value: '0205',
    label: '自动外呼调佣',
  },
  {
    value: '0207',
    label: '自动化调佣',
  },
  {
    value: '0208',
    label: '渠道开户调佣',
  },
];
// 客户性质配置列表
export const CUST_TYPE_CONFIG = [
  {
    value: 'per',
    label: '个人',
  },
  {
    value: 'org',
    label: '机构',
  },
  {
    value: 'prod',
    label: '产品',
  }
];
// 下拉选项默认值
export const TYPE_ALL = 'all';
export const DEFAULT_OPTION = [{ value: TYPE_ALL, label: '全部' }];
// 佣金参数条件-客户性质配置列表
export const PARAMS_CUST_TYPE_CONFIG = [...DEFAULT_OPTION, ...CUST_TYPE_CONFIG];
// 佣金率参数条件配置-无数据的表格配置
export const PARAMS_PLACEHOLDER_PROPS = {
  title: '暂无佣金率参数条件配置数据',
  style: {
    height: '380px',
  }
};

// 佣金率范围配置-无数据的表格配置
export const RANGE_PLACEHOLDER_PROPS = {
  title: '暂无佣金率范围配置数据',
  style: {
    height: '380px',
  }
};
// 列表间距
export const SPACE_COLUMN_PROPS = {
  width: 20,
};
// 场景-特殊佣金调整key值
export const SPECIAL_KEY = '0206';
// 场景-单佣金调整key值
export const SINGLE_KEY = '0201';
// 场景-非标客户特殊佣金调整key值
export const UN_STANDARD_KEY = '0209';
// 场景-佣金授权调整key值
export const AUTHORIZE_KEY = '0220';
// 万
export const MILLION = 10000;
// 佣金范围配置提示语
export const RANGE_TIP = '批量佣金调整不支持特殊资产校验的费率设置';
// 佣金率参数条件配置提示语
export const PARAMS_TIP = '本功能不提供批量佣金调整的特殊资产校验费率设置';
