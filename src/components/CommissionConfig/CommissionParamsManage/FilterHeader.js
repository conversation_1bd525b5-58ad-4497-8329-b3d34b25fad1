/*
 * @Author: yanfaping
 * @Date: 2021-07-27 18:38:53
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-08-26 09:50:32
 * @description 头部过滤器
 */
import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import SingleFilter from '@lego/filters/src';
import Button from '@/newUI/button';
import styles from './filterHeader.less';

export default function FilterHeader(props) {
  const {
    scene,
    businessType,
    comRateList,
    onCreate,
    handleSelectScene,
    handleSelectBusinessType,
    isRangeConfig,
    sceneData
  } = props;

  const comRateData = () => {
    if (_.isEmpty(comRateList)) {
      return [];
    }
    return _.concat([{ value: '', label: '不限' }], comRateList);
  };

  const dropDownStyle = isRangeConfig ? { width: 170 } : {};
  return (
    <div className={styles.filterArea}>
      <div className={styles.filterItem}>
        <SingleFilter
          filterName="场景"
          filterId="value"
          dataMap={['value', 'label']}
          data={sceneData}
          value={scene}
          dropdownStyle={dropDownStyle}
          onChange={handleSelectScene}
        />
      </div>
      <div className={styles.filterItem}>
        <SingleFilter
          filterName="业务品种"
          filterId="value"
          dataMap={['value', 'label']}
          data={comRateData()}
          value={businessType}
          onChange={handleSelectBusinessType}
        />
      </div>
      <div className={styles.createBtn}>
        <Button
          icon="plus"
          ghost
          type="rpiamry"
          onClick={onCreate}
        >
          添加
        </Button>
      </div>
    </div>
  );
}
FilterHeader.propTypes = {
  // 场景值
  scene: PropTypes.string,
  // 业务品种值
  businessType: PropTypes.string,
  // 业务品种列表
  comRateList: PropTypes.array.isRequired,
  // 添加
  onCreate: PropTypes.func.isRequired,
  // 选择场景
  handleSelectScene: PropTypes.func.isRequired,
  // 选择业务品种
  handleSelectBusinessType: PropTypes.func.isRequired,
  // 是否是佣金范围配置-用来判断对应列表需要展示的筛选项
  isRangeConfig: PropTypes.bool,
  // 场景下拉选项数据
  sceneData: PropTypes.array,
};

FilterHeader.defaultProps = {
  scene: '',
  businessType: '',
  isRangeConfig: false,
  sceneData: []
};
