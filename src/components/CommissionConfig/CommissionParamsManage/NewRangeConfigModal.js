/*
 * @Author: yanfaping
 * @Date: 2021-07-27 18:38:53
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-08-23 11:12:06
 * @description 佣金率范围配置-新建Modal
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import {
  Form, Select, Alert, AutoComplete, message
} from 'antd';
import Modal from '@/newUI/modal';
import IFWrap from '@/components/common/IFWrap';
import SimilarAutoComplete from '@/components/common/similarAutoComplete';
import { logCommon } from '@/decorators/logable';
import {
  PARAMS_CUST_TYPE_CONFIG,
  SPECIAL_KEY,
  SINGLE_KEY,
  UN_STANDARD_KEY,
  // AUTHORIZE_KEY,
} from './config';
import { getOptionName, handleTransSubmitCustType, getCommIndexOrder } from './utils';
import styles from './newRangeConfigModal.less';

const FormItem = Form.Item;
const create = Form.create;
const Option = Select.Option;

@create()
export default class NewRangeConfigModal extends PureComponent {
  static propTypes = {
    // 表单
    form: PropTypes.object.isRequired,
    // 是否显示弹框
    visible: PropTypes.bool.isRequired,
    // 关闭弹框
    onClose: PropTypes.func.isRequired,
    // 业务品种选项列表
    comRateList: PropTypes.array.isRequired,
    // 佣金率选项列表
    commissionRateData: PropTypes.array.isRequired,
    // 保存校验
    onValidate: PropTypes.func.isRequired,
    // 保存
    onSave: PropTypes.func.isRequired,
    // 获取营业部选项列表
    queryDepartmentList: PropTypes.func.isRequired,
    // 获取佣金率信息
    queryCommissionRateData: PropTypes.func.isRequired,
    // 场景下拉选项数据
    sceneData: PropTypes.array.isRequired,
  }

  constructor(props) {
    super(props);

    this.state = {
      // 新建时校验提示，如果记录已存在，则无法提交提示报错信息
      showAlert: false,
      // 营业信息
      departmentList: [],
    };
  }

  @autobind
  handleModalConfirm() {
    const { form } = this.props;
    form.validateFields({ force: true }, (errors, values) => {
      if (!_.isEmpty(errors)) {
        return;
      }
      this.handleSubmit(values);
    });
  }

  @autobind
  handleSubmit(values) {
    const {
      businessType,
      scene,
      departmentCode,
      comRateMinValue,
      comRateMaxValue,
      custType,
    } = values;
    const { comRateList, commissionRateData } = this.props;
    // 如果选择的全部，设置为空
    const newCustType = handleTransSubmitCustType(custType);
    // 业务类型名称
    const businessTypeName = getOptionName(comRateList, businessType);
    // 校验和提交的公共参数(场景、业务品种、营业部、客户性质)
    const commonParams = {
      scene,
      businessType,
      businessTypeName,
      departmentCode,
      custType: newCustType,
    };

    const comRateMaxOrder = getCommIndexOrder(commissionRateData, comRateMaxValue);
    const comRateMinOrder = getCommIndexOrder(commissionRateData, comRateMinValue);
    if (comRateMinOrder > comRateMaxOrder) {
      message.error('佣金下限要小于等于佣金上限');
      return;
    }
    // 保存时需校验场景、业务品种、营业部是否重复，如重复则无法新建，并且提示：记录已存在
    this.props.onValidate(commonParams).then((result) => {
      if (result !== 'Y') {
        this.props.onSave({
          ...values,
          ...commonParams,
        });
        this.setState({ showAlert: false });
      } else {
        this.setState({ showAlert: true });
      }
    });
  }

  @autobind
  getModalBtnGroup() {
    return [
      {
        key: 'cancel',
        text: '取消',
        onClick: this.handleCloseModal,
      },
      {
        key: 'confirm',
        text: '确定',
        type: 'primary',
        onClick: this.handleModalConfirm,
      }
    ];
  }

  @autobind
  renderSelectList(list) {
    if (_.isEmpty(list)) {
      return [];
    }
    return _.map(list, this.renderOption);
  }

  @autobind
  renderOption({ value, label }) {
    return <Option key={value} value={value}>{label}</Option>;
  }

  @autobind
  handleDepartmentSelect(option) {
    const { form } = this.props;
    if (_.isEmpty(option)) {
      form.setFieldsValue({
        departmentCode: '',
      });
      this.setState({ showAlert: false });
      return;
    }
    form.setFieldsValue({
      departmentCode: option.id
    });
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-新建',
        value: JSON.stringify(option),
      }
    });
  }

  @autobind
  handleSearchDepartment(keyword) {
    if (_.isEmpty(keyword)) {
      return;
    }
    this.props.queryDepartmentList({
      keyword,
    }).then(this.saveDepartment);
  }

  @autobind
  saveDepartment(departmentList) {
    this.setState({ departmentList });
  }

  @autobind
  renderDepartmentOption(option) {
    const { id, name } = option;
    return (
      <AutoComplete.Option key={id} value={name}>{name}</AutoComplete.Option>
    );
  }

  @autobind
  handleCloseModal() {
    this.setState({ showAlert: false });
    this.props.onClose();
  }

  @autobind
  handleChangeScence(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-新建-选择场景',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeBusinessType(value, option) {
    this.props.queryCommissionRateData({
      businessType: value,
    }).then(() => {
      // 因为是联动，重新选择业务品种应该清空佣金上限和下限之前的值
      const { form } = this.props;
      form.setFieldsValue({
        comRateMinValue: undefined,
        comRateMaxValue: undefined,
      });
    });
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-新建-选择业务品种',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeComRateMaxData(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-新建-选择佣金上限',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeComRateMinData(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-新建-选择佣金下限',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeCustType(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-新建-选择客户性质',
        value: option?.props?.children,
      }
    });
  }

  render() {
    const {
      visible,
      form: {
        getFieldDecorator,
        getFieldValue,
      },
      comRateList,
      commissionRateData,
      sceneData,
    } = this.props;
    const { showAlert, departmentList } = this.state;
    // 场景
    const scene = getFieldValue('scene') || '';
    // 场景是否是特殊佣金调整，特殊佣金调整展示客户性质，不展示营业部
    const isSpecial = scene === SPECIAL_KEY;
    // 场景是否是单佣金调整
    const isSingle = scene === SINGLE_KEY;
    // 场景是否是非标客户特殊佣金调整
    const isStandard = scene === UN_STANDARD_KEY;
    // 场景是否是佣金授权调整
    // const isAuthroize = scene === AUTHORIZE_KEY;
    // 是否展示客户性质模块（单佣金、特殊佣金、非标客户特殊佣金展示）
    const isShowCustType = isSpecial || isSingle || isStandard;
    // 是否展示营业部(只有单佣金调整需要展示营业部)
    const isShowDepart = isSingle;
    return (
      <Modal
        title="新建"
        size="normal"
        visible={visible}
        destroyOnClose
        onModalClose={this.handleCloseModal}
        modalFooter={this.getModalBtnGroup()}
        wrapClassName={styles.newRangeConfigModal}
      >
        <div className={styles.content}>
          <IFWrap when={showAlert}>
            <Alert message="记录已存在" type="error" showIcon />
          </IFWrap>
          <Form>
            <FormItem label="场景">
              {getFieldDecorator('scene', {
                initialValue: scene,
              })(
                <Select placeholder="请选择" onChange={this.handleChangeScence}>
                  {this.renderSelectList(sceneData)}
                </Select>
              )}
            </FormItem>
            <FormItem label="业务品种">
              {getFieldDecorator('businessType', {
                rules: [{ required: true, message: '请选择业务品种' }],
              })(
                <Select placeholder="请选择" onChange={this.handleChangeBusinessType}>
                  {this.renderSelectList(comRateList)}
                </Select>
              )}
            </FormItem>
            {
              isShowCustType ? (
                <FormItem label="客户性质">
                  {getFieldDecorator('custType', {
                    rules: [{ required: true, message: '请选择客户性质' }],
                  })(
                    <Select placeholder="请选择" onChange={this.handleChangeCustType}>
                      {this.renderSelectList(PARAMS_CUST_TYPE_CONFIG)}
                    </Select>
                  )}
                </FormItem>
              ) : null
            }
            <IFWrap when={isShowDepart}>
              <FormItem label="营业部">
                {getFieldDecorator('departmentCode', {
                })(
                  <div>
                    <SimilarAutoComplete
                      placeholder="请选择"
                      optionList={departmentList}
                      style={{ width: 200 }}
                      optionKey="id"
                      onSelect={this.handleDepartmentSelect}
                      onSearch={this.handleSearchDepartment}
                      renderOptionNode={this.renderDepartmentOption}
                    />
                  </div>
                )}
              </FormItem>
            </IFWrap>
            <FormItem label="佣金率上限">
              {getFieldDecorator('comRateMaxValue', {
                rules: [{ required: true, message: '请选择佣金率上限' }],
              })(
                <Select placeholder="请选择" onChange={this.handleChangeComRateMaxData}>
                  {this.renderSelectList(commissionRateData)}
                </Select>
              )}
            </FormItem>
            <FormItem label="佣金率下限">
              {getFieldDecorator('comRateMinValue', {
                rules: [{ required: true, message: '请选择佣金率下限' }],
              })(
                <Select placeholder="请选择" onChange={this.handleChangeComRateMinData}>
                  {this.renderSelectList(commissionRateData)}
                </Select>
              )}
            </FormItem>
          </Form>
        </div>
      </Modal>
    );
  }
}
