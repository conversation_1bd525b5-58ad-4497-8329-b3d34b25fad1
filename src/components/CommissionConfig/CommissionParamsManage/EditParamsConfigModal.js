/*
 * @Author: yanfaping
 * @Date: 2021-07-27 18:38:53
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-08-Th 03:54:09
 * @description 佣金率参数条件配置-编辑Modal
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import {
  Form, Select, Alert, Input, AutoComplete,
} from 'antd';
import Modal from '@/newUI/modal';
import IFWrap from '@/components/common/IFWrap';
import SimilarAutoComplete from '@/components/common/similarAutoComplete';
import { logCommon } from '@/decorators/logable';
import { regxp } from '@/helper';
import {
  PARAMS_CUST_TYPE_CONFIG,
  SINGLE_KEY,
} from './config';
import {
  getOptionName,
  handleTransCustTypeValue,
  handleTransSubmitCustType,
  handleTransListMoney,
  handleTransSubmitMoney,
} from './utils';
import styles from './newParamsConfigModal.less';

const FormItem = Form.Item;
const create = Form.create;
const Option = Select.Option;

@create()
export default class EditParamsConfigModal extends PureComponent {
  static propTypes = {
    // 表单
    form: PropTypes.object.isRequired,
    // 是否显示弹框
    visible: PropTypes.bool.isRequired,
    // 需要编辑的数据信息
    recordData: PropTypes.object.isRequired,
    // 关闭弹框
    onClose: PropTypes.func.isRequired,
    // 保存校验
    onValidate: PropTypes.func.isRequired,
    // 编辑
    onEdit: PropTypes.func.isRequired,
    // 佣金率选项列表
    commissionRateData: PropTypes.array.isRequired,
    // 获取营业部选项列表
    queryDepartmentList: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);

    this.state = {
      // 新建时校验提示，如果记录已存在，则无法提交提示报错信息
      showAlert: false,
      // 营业信息
      departmentList: [],
    };
  }

  componentDidUpdate(prevProps) {
    const { recordData } = prevProps;
    const { recordData: { departmentCode } } = this.props;
    if (recordData?.departmentCode !== departmentCode) {
      this.handleSearchDepartment(departmentCode);
    }
  }

  @autobind
  handleSearchDepartment(keyword) {
    if (_.isEmpty(keyword)) {
      return;
    }
    this.props.queryDepartmentList({
      keyword,
    }).then(this.saveDepartment);
  }

  @autobind
  saveDepartment(departmentList) {
    this.setState({ departmentList });
  }

  @autobind
  handleDepartmentSelect(option) {
    const { form } = this.props;
    // 无值的时候要清空营业部的值
    if (_.isEmpty(option)) {
      form.setFieldsValue({
        departmentCode: '',
      });
      this.setState({ showAlert: false });
      return;
    }
    form.setFieldsValue({
      departmentCode: option.id
    });
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-编辑',
        value: JSON.stringify(option),
      }
    });
  }

  @autobind
  renderDepartmentOption(option) {
    const { id, name } = option;
    return (
      <AutoComplete.Option key={id} value={name}>{name}</AutoComplete.Option>
    );
  }

  @autobind
  handleModalConfirm() {
    const { form } = this.props;
    form.validateFields({ force: true }, (errors, values) => {
      if (!_.isEmpty(errors)) {
        return;
      }
      this.handleSubmit(values);
    });
  }

  @autobind
  handleSubmit(values) {
    const {
      custType,
      comRate,
      yearStockProfit,
      yearStockExchange,
      totalAsset,
      departmentCode,
    } = values;
    const { commissionRateData } = this.props;
    const newCustType = handleTransSubmitCustType(custType);
    // 业务品种编辑只读，取recordData中的值
    const { recordData } = this.props;
    const { businessTypeId, businessTypeName, sceneId } = recordData;
    // 佣金率名称
    const comRateName = getOptionName(commissionRateData, comRate);
    // 校验和保存的相同参数(场景、业务品种、客户性质, 佣金率)
    const commonParams = {
      scene: sceneId,
      businessType: businessTypeId,
      businessTypeName,
      custType: newCustType,
      comRate,
      comRateName,
      id: recordData?.id,
      departmentCode,
    };
    // 保存时需校验场景、业务品种、客户性质、佣金率是否重复，如有重复数据则无法新增，提示：同一业务品种不能重复，请修改
    this.props.onValidate({
      ...commonParams,
    }).then((result) => {
      if (result !== 'Y') {
        this.props.onEdit({
          ...values,
          ...commonParams,
          yearStockProfit: handleTransSubmitMoney(yearStockProfit),
          yearStockExchange: handleTransSubmitMoney(yearStockExchange),
          totalAsset: handleTransSubmitMoney(totalAsset),
        });
        this.setState({ showAlert: false });
      } else {
        this.setState({ showAlert: true });
      }
    });
  }

  @autobind
  getModalBtnGroup() {
    return [
      {
        key: 'cancel',
        text: '取消',
        onClick: this.handleCloseModal,
      },
      {
        key: 'confirm',
        text: '确定',
        type: 'primary',
        onClick: this.handleModalConfirm,
      }
    ];
  }

  @autobind
  handleCloseModal() {
    this.setState({ showAlert: false });
    this.props.onClose();
  }

  @autobind
  renderSelectList(list) {
    if (_.isEmpty(list)) {
      return [];
    }
    return _.map(list, this.renderOption);
  }

  @autobind
  renderOption({ value, label }) {
    return <Option key={value} value={value}>{label}</Option>;
  }

  @autobind
  validateMoney(rule, value, callback) {
    if (!_.isEmpty(value) && !regxp.positiveNum1.test(value)) {
      return callback('必须为数字');
    }
    return callback();
  }

  @autobind
  getValue(name, defaultValue) {
    const { form, recordData } = this.props;
    const value = form.getFieldValue(name);
    if (!_.isEmpty(value)) {
      return value;
    }

    if (!_.isNil(recordData[name])) {
      return recordData[name];
    }

    if (!_.isNil(defaultValue)) {
      return defaultValue;
    }

    return undefined;
  }

  @autobind
  handleChangeScence(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-编辑-选择场景',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeCustType(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-编辑-选择客户性质',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeComRate(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-编辑-选择佣金率',
        value: option?.props?.children,
      }
    });
  }

  render() {
    const {
      visible,
      form: {
        getFieldDecorator,
      },
      commissionRateData,
      recordData,
    } = this.props;
    const { showAlert, departmentList } = this.state;
    // 客户性质
    const custType = this.getValue('custType');
    const transType = handleTransCustTypeValue(custType);
    // 佣金率
    const comRate = this.getValue('comRate');
    // 总资产
    const totalAsset = handleTransListMoney(this.getValue('totalAsset'));
    // 近一年股基交易量
    const yearStockExchange = handleTransListMoney(this.getValue('yearStockExchange'));
    // 近一年股基交易毛佣金
    const yearStockProfit = handleTransListMoney(this.getValue('yearStockProfit'));
    // 营业部
    const departmentCode = this.getValue('departmentCode');
    // 是否展示营业部(只有单佣金调整时需要展示营业部)
    const isShowDepart = recordData?.sceneId === SINGLE_KEY;
    return (
      <Modal
        title="编辑"
        size="normal"
        visible={visible}
        destroyOnClose
        onModalClose={this.handleCloseModal}
        modalFooter={this.getModalBtnGroup()}
      >
        <div className={styles.content}>
          <IFWrap when={showAlert}>
            <Alert message="同一业务品种不能重复，请修改" type="error" showIcon />
          </IFWrap>
          <Form>
            <FormItem label="场景">
              <div className={styles.label}>{recordData?.sceneName}</div>
            </FormItem>
            <FormItem label="业务品种">
              <div className={styles.label}>{recordData?.businessTypeName}</div>
            </FormItem>
            <FormItem label="客户性质">
              {getFieldDecorator('custType', {
                rules: [{ required: true, message: '请选择客户性质' }],
                initialValue: transType,
              })(
                <Select placeholder="请选择" onChange={this.handleChangeCustType}>
                  {this.renderSelectList(PARAMS_CUST_TYPE_CONFIG)}
                </Select>
              )}
            </FormItem>
            <IFWrap when={isShowDepart}>
              <FormItem label="营业部">
                {getFieldDecorator('departmentCode', {
                  initialValue: departmentCode,
                })(
                  <div>
                    <SimilarAutoComplete
                      placeholder="请选择"
                      optionList={departmentList}
                      style={{ width: 200 }}
                      optionKey="id"
                      defaultValue={recordData?.department}
                      onSelect={this.handleDepartmentSelect}
                      onSearch={this.handleSearchDepartment}
                      renderOptionNode={this.renderDepartmentOption}
                    />
                  </div>
                )}
              </FormItem>
            </IFWrap>
            <FormItem label="佣金率">
              {getFieldDecorator('comRate', {
                rules: [{ required: true, message: '请选择佣金率' }],
                initialValue: comRate,
              })(
                <Select placeholder="请选择" onChange={this.handleChangeComRate}>
                  {this.renderSelectList(commissionRateData)}
                </Select>
              )}
            </FormItem>
            <FormItem label="总资产(万元)">
              {getFieldDecorator('totalAsset', {
                rules: [
                  { validator: this.validateMoney },
                ],
                initialValue: totalAsset,
              })(
                <Input
                  placeholder="请输入"
                />
              )}
            </FormItem>
            <FormItem label="近一年股基交易量(万元)">
              {getFieldDecorator('yearStockExchange', {
                rules: [{ validator: this.validateMoney }],
                initialValue: yearStockExchange,
              })(
                <Input
                  placeholder="请输入"
                />
              )}
            </FormItem>
            <FormItem label="近一年股基交易毛佣金(万元)">
              {getFieldDecorator('yearStockProfit', {
                rules: [{ validator: this.validateMoney }],
                initialValue: yearStockProfit,
              })(
                <Input
                  placeholder="请输入"
                />
              )}
            </FormItem>
          </Form>
        </div>
      </Modal>
    );
  }
}
