/*
 * @Author: yanfaping
 * @Date: 2021-07-27 18:38:53
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-08-23 11:12:26
 * @description 佣金率范围配置-编辑Modal
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import {
  Form, Select, Alert, AutoComplete, message
} from 'antd';
import Modal from '@/newUI/modal';
import IFWrap from '@/components/common/IFWrap';
import SimilarAutoComplete from '@/components/common/similarAutoComplete';
import { logCommon } from '@/decorators/logable';
import {
  SPECIAL_KEY,
  PARAMS_CUST_TYPE_CONFIG,
  SINGLE_KEY,
  UN_STANDARD_KEY,
} from './config';
import { handleTransCustTypeValue, handleTransSubmitCustType, getCommIndexOrder } from './utils';
import styles from './newRangeConfigModal.less';

const FormItem = Form.Item;
const create = Form.create;
const Option = Select.Option;

@create()
export default class EditRangeConfigModal extends PureComponent {
  static propTypes = {
    // 表单
    form: PropTypes.object.isRequired,
    // 是否显示弹框
    visible: PropTypes.bool.isRequired,
    // 需要编辑的数据信息
    recordData: PropTypes.object.isRequired,
    // 关闭弹框
    onClose: PropTypes.func.isRequired,
    /// 佣金率选项列表
    commissionRateData: PropTypes.array.isRequired,
    // 保存校验
    onValidate: PropTypes.func.isRequired,
    // 编辑
    onEdit: PropTypes.func.isRequired,
    // 获取营业部选项列表
    queryDepartmentList: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      // 新建时校验提示，如果记录已存在，则无法提交提示报错信息
      showAlert: false,
      // 营业信息
      departmentList: [],
    };
  }

  componentDidUpdate(prevProps) {
    const { recordData } = prevProps;
    const { recordData: { departmentCode } } = this.props;
    if (recordData?.departmentCode !== departmentCode) {
      this.handleSearchDepartment(departmentCode);
    }
  }

  @autobind
  handleModalConfirm() {
    const { form } = this.props;
    form.validateFields({ force: true }, (errors, values) => {
      if (!_.isEmpty(errors)) {
        return;
      }
      this.handleSubmit(values);
    });
  }

  @autobind
  handleSubmit(values) {
    const { comRateMinValue, comRateMaxValue, custType } = values;
    const { commissionRateData } = this.props;
    // 选择了全部，转成空
    const newCustType = handleTransSubmitCustType(custType);

    const comRateMaxOrder = getCommIndexOrder(commissionRateData, comRateMaxValue);
    const comRateMinOrder = getCommIndexOrder(commissionRateData, comRateMinValue);
    if (comRateMinOrder > comRateMaxOrder) {
      message.error('佣金下限要小于等于佣金上限');
      return;
    }
    // 编辑时，场景和业务品种只读，取recordData中的值
    const { recordData } = this.props;
    const { businessTypeId, sceneId, businessTypeName } = recordData;
    // 校验和编辑的公共参数(场景、业务品种、客户性质、营业部)
    const commonParams = {
      scene: sceneId,
      businessType: businessTypeId,
      businessTypeName,
      departmentCode: values?.departmentCode,
      custType: newCustType,
      id: recordData?.id,
    };
    // 保存时需校验场景、业务品种、客户性质、营业部是否重复，如重复则无法新建，并且提示：记录已存在
    this.props.onValidate({
      ...commonParams,
    }).then((result) => {
      if (result !== 'Y') {
        this.props.onEdit({
          ...values,
          ...commonParams,
        });
        this.setState({ showAlert: false });
      } else {
        this.setState({ showAlert: true });
      }
    });
  }

  @autobind
  getModalBtnGroup() {
    return [
      {
        key: 'cancel',
        text: '取消',
        onClick: this.handleCloseModal,
      },
      {
        key: 'confirm',
        text: '确定',
        type: 'primary',
        onClick: this.handleModalConfirm,
      }
    ];
  }

  @autobind
  renderSelectList(list) {
    if (_.isEmpty(list)) {
      return [];
    }
    return _.map(list, this.renderOption);
  }

  @autobind
  renderOption({ value, label }) {
    return <Option key={value} value={value}>{label}</Option>;
  }

  @autobind
  handleDepartmentSelect(option) {
    const { form } = this.props;
    // 无值的时候要清空营业部的值
    if (_.isEmpty(option)) {
      form.setFieldsValue({
        departmentCode: '',
      });
      this.setState({ showAlert: false });
      return;
    }
    form.setFieldsValue({
      departmentCode: option.id
    });
  }

  @autobind
  handleSearchDepartment(keyword) {
    if (_.isEmpty(keyword)) {
      return;
    }
    this.props.queryDepartmentList({
      keyword,
    }).then(this.saveDepartment);
  }

  @autobind
  saveDepartment(departmentList) {
    this.setState({ departmentList });
  }

  @autobind
  renderDepartmentOption(option) {
    const { id, name } = option;
    return (
      <AutoComplete.Option key={id} value={name}>{name}</AutoComplete.Option>
    );
  }

  @autobind
  handleCloseModal() {
    this.setState({ showAlert: false });
    this.props.onClose();
  }

  @autobind
  handleChangeComRateMaxData(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-编辑-选择佣金上限',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeComRateMinData(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-编辑-选择佣金下限',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  handleChangeCustType(value, option) {
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-新建-选择客户性质',
        value: option?.props?.children,
      }
    });
  }

  @autobind
  getValue(name, defaultValue) {
    const { form, recordData } = this.props;
    const value = form.getFieldValue(name);
    if (!_.isEmpty(value)) {
      return value;
    }

    if (!_.isNil(recordData[name])) {
      return recordData[name];
    }

    if (!_.isNil(defaultValue)) {
      return defaultValue;
    }

    // 无数据时，返回undefined是因为select组件只有值为undefined时才会展示placeholder
    return undefined;
  }

  render() {
    const {
      visible,
      form: {
        getFieldDecorator,
      },
      commissionRateData,
      recordData,
    } = this.props;
    const { showAlert, departmentList } = this.state;
    // 场景是否是特殊佣金调整，特殊佣金调整展示客户性质，不展示营业部
    const isSpecial = recordData?.sceneId === SPECIAL_KEY;
    // 场景是否是单佣金调整
    const isSingle = recordData?.sceneId === SINGLE_KEY;
    // 场景是否是非标客户特殊佣金调整
    const isStandard = recordData?.sceneId === UN_STANDARD_KEY;
    // 是否展示客户性质模块（单佣金、特殊佣金、非标客户特殊佣金）
    const isShowCustType = isSpecial || isSingle || isStandard;
    // 是否展示营业部(只有单佣金调整、佣金授权时需要展示营业部)
    const isShowDepart = isSingle;
    // 营业部
    const departmentCode = this.getValue('departmentCode');
    // 佣金率上限值
    const comRateMaxValue = this.getValue('comRateMaxValue');
    // 佣金率下限值
    const comRateMinValue = this.getValue('comRateMinValue');
    // 客户性质
    const custType = this.getValue('custType');
    const transType = handleTransCustTypeValue(custType);

    return (
      <Modal
        title="编辑"
        size="normal"
        visible={visible}
        destroyOnClose
        onModalClose={this.handleCloseModal}
        modalFooter={this.getModalBtnGroup()}
        wrapClassName={styles.newRangeConfigModal}
      >
        <div className={styles.content}>
          <IFWrap when={showAlert}>
            <Alert message="记录已存在" type="error" showIcon />
          </IFWrap>
          <Form>
            <FormItem label="场景">
              <div className={styles.label}>{recordData?.sceneName}</div>
            </FormItem>
            <FormItem label="业务品种">
              <div className={styles.label}>{recordData?.businessTypeName}</div>
            </FormItem>
            {
              isShowCustType ? (
                <FormItem label="客户性质">
                  {getFieldDecorator('custType', {
                    rules: [{ required: true, message: '请选择客户性质' }],
                    initialValue: transType,
                  })(
                    <Select placeholder="请选择" onChange={this.handleChangeCustType}>
                      {this.renderSelectList(PARAMS_CUST_TYPE_CONFIG)}
                    </Select>
                  )}
                </FormItem>
              ) : null
            }
            <IFWrap when={isShowDepart}>
              <FormItem label="营业部">
                {getFieldDecorator('departmentCode', {
                  initialValue: departmentCode,
                })(
                  <div>
                    <SimilarAutoComplete
                      placeholder="请选择"
                      optionList={departmentList}
                      style={{ width: 200 }}
                      optionKey="id"
                      defaultValue={recordData?.department}
                      onSelect={this.handleDepartmentSelect}
                      onSearch={this.handleSearchDepartment}
                      renderOptionNode={this.renderDepartmentOption}
                    />
                  </div>
                )}
              </FormItem>
            </IFWrap>
            <FormItem label="佣金率上限">
              {getFieldDecorator('comRateMaxValue', {
                rules: [{ required: true, message: '请选择佣金率上限' }],
                initialValue: comRateMaxValue,
              })(
                <Select placeholder="请选择" onChange={this.handleChangeComRateMaxData}>
                  {this.renderSelectList(commissionRateData)}
                </Select>
              )}
            </FormItem>
            <FormItem label="佣金率下限">
              {getFieldDecorator('comRateMinValue', {
                rules: [{ required: true, message: '请选择佣金率下限' }],
                initialValue: comRateMinValue,
              })(
                <Select placeholder="请选择" onChange={this.handleChangeComRateMinData}>
                  {this.renderSelectList(commissionRateData)}
                </Select>
              )}
            </FormItem>
          </Form>
        </div>
      </Modal>
    );
  }
}
