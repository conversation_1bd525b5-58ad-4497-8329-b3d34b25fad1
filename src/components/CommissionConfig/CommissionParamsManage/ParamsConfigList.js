/*
 * @Author: yanfaping
 * @Date: 2021-07-27 18:38:53
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-08-27 09:31:20
 * @description 佣金率参数条件配置页面
 */
import React, { PureComponent } from 'react';
import { autobind } from 'core-decorators';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { message, Alert } from 'antd';

import Table, { ToolTipCell } from '@/components/common/table';
import Icon from '@/components/common/Icon';
import Popconfirm from '@/components/common/Popconfirm';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import logable, { logCommon } from '@/decorators/logable';
import NewParamsConfigModal from './NewParamsConfigModal';
import EditParamsConfigModal from './EditParamsConfigModal';
import FilterHeader from './FilterHeader';
import {
  PARAMS_CONFIG_COLUMNS,
  PARAMS_PLACEHOLDER_PROPS,
  SPACE_COLUMN_PROPS,
  PARAMS_TIP,
} from './config';
import { handleTransListMoney } from './utils';
import styles from './index.less';

export default class ParamsConfigList extends PureComponent {
  static propTypes = {
    // // 业务品种列表
    comRateList: PropTypes.array.isRequired,
    // 佣金率参数条件配置数据
    paramsConfigData: PropTypes.object.isRequired,
    // 佣金率参数条件配置查询列表
    queryCommissionRateParamsList: PropTypes.func.isRequired,
    // 获取业务品种列表
    queryComRateList: PropTypes.func.isRequired,
    // 佣金率信息
    commissionRateData: PropTypes.array.isRequired,
    // 获取佣金率信息
    queryCommissionRateData: PropTypes.func.isRequired,
    // 佣金率参数条件配置新增
    saveCommissionRateParams: PropTypes.func.isRequired,
    // 佣金率参数条件配置删除
    deleteCommissionRateParams: PropTypes.func.isRequired,
    // 佣金率参数条件配置编辑
    updateCommissionRateParams: PropTypes.func.isRequired,
    // 佣金率参数条件配置新增校验
    validateCommissionRateParams: PropTypes.func.isRequired,
    // 清空数据
    clearReduxData: PropTypes.func.isRequired,
    // 查询营业部列表
    queryDepartmentList: PropTypes.func.isRequired,
    // 场景下拉选项
    paramsSceneList: PropTypes.array.isRequired,
    // 获取场景下拉选项
    queryParamsSceneList: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);

    this.state = {
      // 场景
      scene: '',
      // 业务品种
      businessType: '',
      // 是否展示新建弹框，默认false
      showNewModal: false,
      // 是否展示编辑弹框，默认false
      showEditModal: false,
      // 需要编辑的数据
      recordData: {},
    };
  }

  componentDidMount() {
    this.props.queryParamsSceneList({ type: 'comissionParameter' });
    this.props.queryComRateList();
    this.queryList();
  }

  // 获取列表数据
  @autobind
  queryList(pageNum = 1) {
    const { scene, businessType } = this.state;
    this.props.queryCommissionRateParamsList({
      scene,
      businessType,
      pageNum,
      pageSize: 10,
    });
  }

  // 点击编辑防止数据映射码值，先查完数据再打开弹窗
  @autobind
  handleEditRecord(record) {
    this.props.queryCommissionRateData({
      businessType: record?.businessTypeId,
    });
    this.setState({
      recordData: record,
      showEditModal: true,
    });
    logCommon({
      type: 'Click',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-编辑',
        value: JSON.stringify(record),
      }
    });
  }

  @autobind
  handleDeleteRecord(record) {
    this.props.deleteCommissionRateParams({
      id: record?.id
    }).then((res) => {
      if (res) {
        const { paramsConfigData: { list, page } } = this.props;
        let pageNum = page?.pageNum || 1;
        if (_.size(list) === 1 && page?.pageNum > 1) {
          pageNum = page?.pageNum - 1;
        }
        this.queryList(pageNum);
      } else {
        message.error('删除佣金率参数条件失败！');
      }
    });
    logCommon({
      type: 'Click',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-删除',
        value: JSON.stringify(record),
      }
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率参数条件配置-添加'
    }
  })
  handleCreateRecord() {
    this.setState({ showNewModal: true });
  }

  // 操作列
  @autobind
  updateOperateColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        if (record.flag) {
          return null;
        }

        return (
          <div className={styles.operate}>
            <div className={styles.editBtn}>
              <Icon type="fankui" onClick={() => this.handleEditRecord(record)} />
            </div>
            <div className={styles.deleteBtn}>
              <Popconfirm
                placement="topRight"
                arrowPointAtCenter={false}
                onConfirm={() => this.handleDeleteRecord(record)}
                content="确定要将此条信息删除吗？"
              >
                <Icon type="shanchu" />
              </Popconfirm>
            </div>
          </div>
        );
      }
    };
  }

  @autobind
  updateMoneyColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        const transValue = _.isNumber(text) ? handleTransListMoney(text) : '--';
        if (record.flag) {
          return null;
        }
        return (
          <ToolTipCell
            cellText={transValue}
            tipContent={transValue}
          />
        );
      }
    };
  }

  @autobind
  updateColumn(column) {
    return ({
      ...column,
      render: (text, record) => {
        if (record.flag) {
          return null;
        }

        return (
          <ToolTipCell
            cellText={text}
            tipContent={text}
          />
        );
      }
    });
  }

  @autobind
  renderColumns() {
    return _.map(PARAMS_CONFIG_COLUMNS, (column) => {
      // 总资产，近一年股基交易量 近一年股基交易毛佣金
      const moneyColumnKeys = ['totalAsset', 'yearStockExchange', 'yearStockProfit'];
      const { dataIndex } = column;
      if (dataIndex === 'operate') {
        return this.updateOperateColumn(column);
      }
      // 处理金额
      if (_.includes(moneyColumnKeys, dataIndex)) {
        return this.updateMoneyColumn(column);
      }
      return this.updateColumn(column);
    });
  }

  @autobind
  handlePageChange(pageNum) {
    this.queryList(pageNum);
    logCommon({
      type: 'Click',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-页码切换',
        value: `${pageNum}`,
      }
    });
  }

  @autobind
  handleSelectScene({ value, label }) {
    this.setState({
      scene: value
    }, this.queryList);
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-选择场景',
        value: label,
      }
    });
  }

  @autobind
  handleSelectBusinessType({ value, label }) {
    this.setState({
      businessType: value
    }, this.queryList);
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率参数条件配置-选择业务品种',
        value: label,
      }
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率参数条件配置-新建弹框-取消'
    }
  })
  handleCloseNewParamsConfigModal() {
    this.setState({ showNewModal: false });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率参数条件配置-编辑弹框-取消'
    }
  })
  handleCloseEditParamsConfigModal() {
    this.setState({ showEditModal: false });
    this.handleClearData();
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率参数条件配置-新建弹框-确定'
    }
  })
  handleSave(params) {
    this.props.saveCommissionRateParams(params).then((result) => {
      if (result) {
        // 提交保存完成时，关闭弹窗，清空之前表单查询的下拉选项
        this.setState({
          showNewModal: false,
        }, this.handleClearData);

        this.queryList();
      }
    });
  }

  // 因为佣金率和业务场景联动，新建弹窗保存关闭后，清空佣金率下拉选项
  @autobind
  handleClearData() {
    this.props.clearReduxData({
      commissionRateData: [],
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率参数条件配置-编辑弹框-确定'
    }
  })
  handleEdit(params) {
    this.props.updateCommissionRateParams(params).then((result) => {
      if (result) {
        this.setState({
          showEditModal: false,
        });

        this.queryList();
      }
    });
  }

  render() {
    const {
      paramsConfigData: {
        list = [],
        page = {},
      },
      comRateList,
      commissionRateData,
      validateCommissionRateParams,
      queryCommissionRateData,
      queryDepartmentList,
      paramsSceneList,
    } = this.props;
    const {
      scene,
      businessType,
      showNewModal,
      showEditModal,
      recordData,
    } = this.state;

    return (
      <div className={styles.listArea}>
        <div className={styles.tip}>
          <Alert message={PARAMS_TIP} type="warning" showIcon />
        </div>
        <FilterHeader
          comRateList={comRateList}
          scene={scene}
          businessType={businessType}
          onCreate={this.handleCreateRecord}
          handleSelectScene={this.handleSelectScene}
          handleSelectBusinessType={this.handleSelectBusinessType}
          sceneData={paramsSceneList}
        />
        <div className={styles.tableArea}>
          <Table
            useNewUI
            pagination={false}
            columns={this.renderColumns()}
            dataSource={list}
            rowKey="id"
            isNeedEmptyRow
            rowNumber={10}
            withBorder={_.isEmpty(list)}
            spaceColumnProps={SPACE_COLUMN_PROPS}
            placeHolderImageProps={PARAMS_PLACEHOLDER_PROPS}
          />
          <IFWrap when={!_.isEmpty(list)}>
            <Pagination
              current={page?.pageNum || 1}
              pageSize={10}
              total={page?.totalCount || 0}
              onChange={this.handlePageChange}
            />
          </IFWrap>
        </div>
        <IFWrap when={showNewModal}>
          <NewParamsConfigModal
            visible={showNewModal}
            commissionRateData={commissionRateData}
            comRateList={comRateList}
            onClose={this.handleCloseNewParamsConfigModal}
            onValidate={validateCommissionRateParams}
            onSave={this.handleSave}
            queryCommissionRateData={queryCommissionRateData}
            queryDepartmentList={queryDepartmentList}
            sceneData={paramsSceneList}
          />
        </IFWrap>
        <IFWrap when={showEditModal}>
          <EditParamsConfigModal
            commissionRateData={commissionRateData}
            visible={showEditModal}
            recordData={recordData}
            onClose={this.handleCloseEditParamsConfigModal}
            onValidate={validateCommissionRateParams}
            onEdit={this.handleEdit}
            queryDepartmentList={queryDepartmentList}
          />
        </IFWrap>
      </div>
    );
  }
}
