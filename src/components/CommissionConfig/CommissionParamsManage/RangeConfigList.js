/*
 * @Author: yanfaping
 * @Date: 2021-07-27 18:38:53
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-08-26 09:40:43
 * @description 佣金率范围配置页面
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { message, Alert } from 'antd';

import Table, { ToolTipCell } from '@/components/common/table';
import Icon from '@/components/common/Icon';
import Popconfirm from '@/components/common/Popconfirm';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import logable, { logCommon } from '@/decorators/logable';
import NewRangeConfigModal from './NewRangeConfigModal';
import EditRangeConfigModal from './EditRangeConfigModal';
import FilterHeader from './FilterHeader';
import {
  RANGE_CONFIG_COLUMNS,
  RANGE_PLACEHOLDER_PROPS,
  RANGE_TIP,
  SPACE_COLUMN_PROPS,
} from './config';

import styles from './index.less';

export default class RangeConfigList extends PureComponent {
  static propTypes = {
    // 业务品种列表
    comRateList: PropTypes.array.isRequired,
    // 佣金范围配置数据
    rangeConfigData: PropTypes.object.isRequired,
    // 佣金范围配置查询列表
    queryCommissionRateRangeList: PropTypes.func.isRequired,
    // 获取业务品种列表
    queryComRateList: PropTypes.func.isRequired,
    // 佣金范围配置新增
    saveCommissionRateRange: PropTypes.func.isRequired,
    // 佣金范围配置删除
    deleteCommissionRateRange: PropTypes.func.isRequired,
    // 佣金范围配置编辑
    updateCommissionRateRange: PropTypes.func.isRequired,
    // 佣金范围配置新增校验
    validateCommissionRateRange: PropTypes.func.isRequired,
    // 佣金率信息
    commissionRateData: PropTypes.array.isRequired,
    // 获取佣金率信息
    queryCommissionRateData: PropTypes.func.isRequired,
    // 查询营业部列表
    queryDepartmentList: PropTypes.func.isRequired,
    // 清空数据
    clearReduxData: PropTypes.func.isRequired,
    // 场景下拉选项
    rangeSceneList: PropTypes.array.isRequired,
    // 获取场景下拉选项
    queryRangeSceneList: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);

    this.state = {
      // 场景
      scene: '',
      // 业务品种
      businessType: '',
      // 是否展示新建弹框，默认false
      showNewModal: false,
      // 是否展示编辑弹框，默认false
      showEditModal: false,
      // 需要编辑的数据
      recordData: {},
    };
  }

  componentDidMount() {
    this.props.queryRangeSceneList({ type: 'comissionRange' });
    this.props.queryComRateList();
    this.queryList();
  }

  // 获取列表数据
  @autobind
  queryList(pageNum = 1) {
    const { scene, businessType } = this.state;
    this.props.queryCommissionRateRangeList({
      scene,
      businessType,
      pageNum,
      pageSize: 10,
    });
  }

  @autobind
  handleEditRecord(record) {
    this.props.queryCommissionRateData({
      businessType: record?.businessTypeId,
    });
    this.setState({
      recordData: record,
      showEditModal: true,
    });
    logCommon({
      type: 'Click',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-编辑',
        value: JSON.stringify(record),
      }
    });
  }

  @autobind
  handleDeleteRecord(record) {
    this.props.deleteCommissionRateRange({
      id: record?.id
    }).then((res) => {
      if (res) {
        const { rangeConfigData: { list, page } } = this.props;
        let pageNum = page?.pageNum || 1;
        if (_.size(list) === 1 && page?.pageNum > 1) {
          pageNum = page?.pageNum - 1;
        }
        this.queryList(pageNum);
      } else {
        message.error('删除佣金率范围失败！');
      }
    });
    logCommon({
      type: 'Click',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-删除',
        value: JSON.stringify(record),
      }
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率范围配置-添加'
    }
  })
  handleCreateRecord() {
    this.setState({ showNewModal: true });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率范围配置-新建弹框-取消'
    }
  })
  handleCloseNewRangeConfigModal() {
    this.setState({ showNewModal: false });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率范围配置-编辑弹框-取消'
    }
  })
  handleCloseEditRangeConfigModal() {
    this.setState({ showEditModal: false });
    this.handleClearData();
  }

  // 操作列
  @autobind
  updateOperateColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        if (record.flag) {
          return null;
        }

        return (
          <div className={styles.operate}>
            <div className={styles.editBtn}>
              <Icon type="fankui" onClick={() => this.handleEditRecord(record)} />
            </div>
            <div className={styles.deleteBtn}>
              <Popconfirm
                placement="topRight"
                arrowPointAtCenter={false}
                onConfirm={() => this.handleDeleteRecord(record)}
                content="确定要将此条信息删除吗？"
              >
                <Icon type="shanchu" />
              </Popconfirm>
            </div>
          </div>
        );
      }
    };
  }

  @autobind
  updateColumn(column) {
    return ({
      ...column,
      render: (text, record) => {
        if (record.flag) {
          return null;
        }

        if (_.isEmpty(text)) {
          return <span className={styles.defaultValue}>--</span>;
        }

        return (
          <ToolTipCell
            cellText={text}
            tipContent={text}
          />
        );
      }
    });
  }

  @autobind
  renderColumns() {
    return _.map(RANGE_CONFIG_COLUMNS, (column) => {
      const { dataIndex } = column;
      if (dataIndex === 'operate') {
        return this.updateOperateColumn(column);
      }
      return this.updateColumn(column);
    });
  }

  @autobind
  handlePageChange(pageNum) {
    this.queryList(pageNum);
    logCommon({
      type: 'Click',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-页码切换',
        value: `${pageNum}`,
      }
    });
  }

  @autobind
  handleSelectScene({ value, label }) {
    this.setState({
      scene: value
    }, this.queryList);
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-选择场景',
        value: label,
      }
    });
  }

  @autobind
  handleSelectBusinessType({ value, label }) {
    this.setState({
      businessType: value
    }, this.queryList);
    logCommon({
      type: 'Select',
      payload: {
        name: '全品种佣金参数管理-佣金率范围配置-选择业务品种',
        value: label,
      }
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率范围配置-新建弹框-确定'
    }
  })
  handleSave(params) {
    this.props.saveCommissionRateRange(params).then((result) => {
      if (result) {
        this.setState({
          showNewModal: false,
        }, this.handleClearData);

        this.queryList();
      }
    });
  }

  // 因为佣金率和业务场景联动，新建弹窗保存关闭后，清空佣金率下拉选项
  @autobind
  handleClearData() {
    this.props.clearReduxData({
      commissionRateData: [],
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '全品种佣金参数管理-佣金率范围配置-编辑弹框-确定'
    }
  })
  handleEdit(params) {
    this.props.updateCommissionRateRange(params).then((result) => {
      if (result) {
        this.setState({
          showEditModal: false,
        });

        this.queryList();
      }
    });
  }

  render() {
    const {
      rangeConfigData: {
        list = [],
        page = {},
      },
      comRateList,
      commissionRateData,
      validateCommissionRateRange,
      queryDepartmentList,
      queryCommissionRateData,
      rangeSceneList,
    } = this.props;
    const {
      scene,
      businessType,
      showNewModal,
      showEditModal,
      recordData,
    } = this.state;
    return (
      <div className={styles.listArea}>
        <div className={styles.tip}>
          <Alert message={RANGE_TIP} type="warning" showIcon />
        </div>
        <FilterHeader
          isRangeConfig
          comRateList={comRateList}
          scene={scene}
          businessType={businessType}
          onCreate={this.handleCreateRecord}
          handleSelectScene={this.handleSelectScene}
          handleSelectBusinessType={this.handleSelectBusinessType}
          sceneData={rangeSceneList}
        />
        <div className={styles.tableArea}>
          <Table
            useNewUI
            pagination={false}
            columns={this.renderColumns()}
            dataSource={list}
            rowKey="id"
            spaceColumnProps={SPACE_COLUMN_PROPS}
            placeHolderImageProps={RANGE_PLACEHOLDER_PROPS}
            isNeedEmptyRow
            rowNumber={10}
            withBorder={_.isEmpty(list)}
          />
          <IFWrap when={!_.isEmpty(list)}>
            <Pagination
              current={page?.pageNum || 1}
              pageSize={10}
              total={page?.totalCount || 0}
              onChange={this.handlePageChange}
            />
          </IFWrap>
        </div>
        <IFWrap when={showNewModal}>
          <NewRangeConfigModal
            visible={showNewModal}
            comRateList={comRateList}
            onClose={this.handleCloseNewRangeConfigModal}
            commissionRateData={commissionRateData}
            onValidate={validateCommissionRateRange}
            onSave={this.handleSave}
            queryDepartmentList={queryDepartmentList}
            queryCommissionRateData={queryCommissionRateData}
            sceneData={rangeSceneList}
          />
        </IFWrap>
        <IFWrap when={showEditModal}>
          <EditRangeConfigModal
            visible={showEditModal}
            recordData={recordData}
            commissionRateData={commissionRateData}
            queryDepartmentList={queryDepartmentList}
            onClose={this.handleCloseEditRangeConfigModal}
            onValidate={validateCommissionRateRange}
            onEdit={this.handleEdit}
          />
        </IFWrap>
      </div>
    );
  }
}
