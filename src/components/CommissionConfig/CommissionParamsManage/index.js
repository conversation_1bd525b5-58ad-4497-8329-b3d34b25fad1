/*
 * @Author: yanfaping
 * @Date: 2021-07-27 18:38:53
 * @Last Modified by: l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-08-27 09:19:24
 * @description 全品种佣金参数管理页面
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { connect } from 'dva';
import { Radio } from 'antd';

import { dva } from '@/helper';
import withRouter from '@/decorators/withRouter';
import IFWrap from '@/components/common/IFWrap';
import { logCommon } from '@/decorators/logable';
import RangeConfigList from './RangeConfigList';
import ParamsConfigList from './ParamsConfigList';
import { commissionParamsTab } from './config';

import styles from './index.less';

// 使用helper里面封装的生成effects的方法
const effect = dva.generateEffect;

const { paramsTab, rangeTab } = commissionParamsTab;

const mapStateToProps = (state) => ({
  // 业务品种列表
  comRateList: state.commissionConfig.comRateList,
  // 佣金范围配置数据
  rangeConfigData: state.commissionConfig.rangeConfigData,
  // 佣金率参数条件配置数据
  paramsConfigData: state.commissionConfig.paramsConfigData,
  // 佣金率信息
  commissionRateData: state.commissionConfig.commissionRateData,
  // 佣金范围配置-场景数据
  rangeSceneList: state.commissionConfig.rangeSceneList,
  // 佣金率参数条件配置-场景数据
  paramsSceneList: state.commissionConfig.paramsSceneList,
});

const mapDispatchToProps = {
  // 获取业务品种列表
  queryComRateList: effect('commissionConfig/queryComRateList'),
  // 佣金范围配置查询列表
  queryCommissionRateRangeList: effect('commissionConfig/queryCommissionRateRangeList'),
  // 佣金范围配置新增
  saveCommissionRateRange: effect('commissionConfig/saveCommissionRateRange'),
  // 佣金范围配置删除
  deleteCommissionRateRange: effect('commissionConfig/deleteCommissionRateRange'),
  // 佣金范围配置编辑
  updateCommissionRateRange: effect('commissionConfig/updateCommissionRateRange'),
  // 佣金范围配置新增校验
  validateCommissionRateRange: effect('commissionConfig/validateCommissionRateRange'),
  // 佣金率参数条件配置查询列表
  queryCommissionRateParamsList: effect('commissionConfig/queryCommissionRateParamsList'),
  // 佣金率参数条件配置新增
  saveCommissionRateParams: effect('commissionConfig/saveCommissionRateParams'),
  // 佣金率参数条件配置删除
  deleteCommissionRateParams: effect('commissionConfig/deleteCommissionRateParams'),
  // 佣金率参数条件配置编辑
  updateCommissionRateParams: effect('commissionConfig/updateCommissionRateParams'),
  // 佣金率参数条件配置新增校验
  validateCommissionRateParams: effect('commissionConfig/validateCommissionRateParams'),
  // 获取佣金率信息
  queryCommissionRateData: effect('commissionConfig/queryCommissionRateData'),
  // 查询营业部列表
  queryDepartmentList: effect('commissionConfig/queryDepartmentList'),
  // 清空redux数据
  clearReduxData: effect('commissionConfig/clearReduxDataSuccess'),
  // 获取佣金范围配置场景下拉选项接口
  queryRangeSceneList: effect('commissionConfig/queryRangeSceneList'),
  // 获取佣金参数配置场景下拉选项接口
  queryParamsSceneList: effect('commissionConfig/queryParamsSceneList'),
};

@connect(mapStateToProps, mapDispatchToProps)
@withRouter
export default class CommissionParamsManage extends PureComponent {
  static propTypes = {
    comRateList: PropTypes.array.isRequired,
    rangeConfigData: PropTypes.object.isRequired,
    paramsConfigData: PropTypes.object.isRequired,
    queryComRateList: PropTypes.func.isRequired,
    queryCommissionRateRangeList: PropTypes.func.isRequired,
    saveCommissionRateRange: PropTypes.func.isRequired,
    deleteCommissionRateRange: PropTypes.func.isRequired,
    updateCommissionRateRange: PropTypes.func.isRequired,
    validateCommissionRateRange: PropTypes.func.isRequired,
    queryCommissionRateParamsList: PropTypes.func.isRequired,
    saveCommissionRateParams: PropTypes.func.isRequired,
    deleteCommissionRateParams: PropTypes.func.isRequired,
    updateCommissionRateParams: PropTypes.func.isRequired,
    validateCommissionRateParams: PropTypes.func.isRequired,
    commissionRateData: PropTypes.array.isRequired,
    queryCommissionRateData: PropTypes.func.isRequired,
    queryDepartmentList: PropTypes.func.isRequired,
    clearReduxData: PropTypes.func.isRequired,
    queryRangeSceneList: PropTypes.func.isRequired,
    queryParamsSceneList: PropTypes.func.isRequired,
    rangeSceneList: PropTypes.array.isRequired,
    paramsSceneList: PropTypes.array.isRequired,
  }

  constructor(props) {
    super(props);

    this.state = {
      radioKey: rangeTab,
    };
  }

  @autobind
  handleChangeRadio(e) {
    const radioKey = e.target.value;
    this.setState({
      radioKey,
    });
    logCommon({
      type: 'Click',
      payload: {
        name: '佣金配置管理-全品种佣金参数管理-tab切换',
        value: commissionParamsTab[radioKey],
      },
    });
  }

  render() {
    const { radioKey } = this.state;
    const {
      comRateList,
      rangeConfigData,
      paramsConfigData,
      queryComRateList,
      queryCommissionRateRangeList,
      saveCommissionRateRange,
      deleteCommissionRateRange,
      updateCommissionRateRange,
      validateCommissionRateRange,
      queryCommissionRateParamsList,
      saveCommissionRateParams,
      deleteCommissionRateParams,
      updateCommissionRateParams,
      validateCommissionRateParams,
      commissionRateData,
      queryCommissionRateData,
      queryDepartmentList,
      clearReduxData,
      queryRangeSceneList,
      queryParamsSceneList,
      rangeSceneList,
      paramsSceneList
    } = this.props;
    return (
      <div className={styles.manageWrap}>
        <div className={styles.radioWarp}>
          <Radio.Group
            value={radioKey}
            onChange={this.handleChangeRadio}
          >
            <Radio.Button value={rangeTab}>佣金率范围配置</Radio.Button>
            <Radio.Button value={paramsTab}>佣金率参数条件配置</Radio.Button>
          </Radio.Group>
        </div>
        <IFWrap when={radioKey === rangeTab}>
          <RangeConfigList
            queryComRateList={queryComRateList}
            comRateList={comRateList}
            rangeConfigData={rangeConfigData}
            queryCommissionRateRangeList={queryCommissionRateRangeList}
            saveCommissionRateRange={saveCommissionRateRange}
            deleteCommissionRateRange={deleteCommissionRateRange}
            updateCommissionRateRange={updateCommissionRateRange}
            validateCommissionRateRange={validateCommissionRateRange}
            commissionRateData={commissionRateData}
            queryCommissionRateData={queryCommissionRateData}
            queryDepartmentList={queryDepartmentList}
            clearReduxData={clearReduxData}
            queryRangeSceneList={queryRangeSceneList}
            rangeSceneList={rangeSceneList}
          />
        </IFWrap>
        <IFWrap when={radioKey === paramsTab}>
          <ParamsConfigList
            comRateList={comRateList}
            paramsConfigData={paramsConfigData}
            queryComRateList={queryComRateList}
            queryCommissionRateParamsList={queryCommissionRateParamsList}
            saveCommissionRateParams={saveCommissionRateParams}
            deleteCommissionRateParams={deleteCommissionRateParams}
            updateCommissionRateParams={updateCommissionRateParams}
            validateCommissionRateParams={validateCommissionRateParams}
            commissionRateData={commissionRateData}
            queryCommissionRateData={queryCommissionRateData}
            clearReduxData={clearReduxData}
            queryDepartmentList={queryDepartmentList}
            queryParamsSceneList={queryParamsSceneList}
            paramsSceneList={paramsSceneList}
          />
        </IFWrap>
      </div>
    );
  }
}
