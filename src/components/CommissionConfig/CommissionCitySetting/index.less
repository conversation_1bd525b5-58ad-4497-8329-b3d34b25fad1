.pageContainer {
  height: calc(100vh - 236px);
  overflow: auto;
  background-color: #fff;

  .filterArea {
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .extra {
      flex: 1 1 auto;
      height: 30px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .tableArea {
    .operationCell {
      display: flex;
      align-items: center;

      .editItem {
        margin-top: 3px;
        margin-right: 15px;
        flex: 0 0 auto;
        color: #108ee9;
      }

      .deleteItem {
        flex: 0 0 auto;
        color: #108ee9;
      }
    }
  }
}
