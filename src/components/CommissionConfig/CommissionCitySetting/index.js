/*
 * @Author: sunweibin
 * @Date: 2021-07-26 15:05:38
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-08-25 10:14:30
 * @description 佣金城市地域配置
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { SingleFilter } from '@lego/filters/src';
import { message } from 'antd';
import { connect } from 'dva';

import Button from '@/newUI/button';
import Popconfirm from '@/components/common/Popconfirm';
import Table, { ToolTipCell } from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import Icon from '@/components/common/Icon';
import logable, { logCommon } from '@/decorators/logable';
import withRouter from '@/decorators/withRouter';
import { dva } from '@/helper';
import NewCommissionCitySettingModal from '../NewCommissionCitySettingModal';
import EditCommissionCitySettingModal from '../EditCommissionCitySettingModal';

import {
  DROPDOWN_STYLE,
  TABLE_COLUMNS,
  PLACEHOLDER_PROPS,
  DEFAULT_OPTION,
} from './config';

import styles from './index.less';

const mapStateToProps = (state) => ({
  // 佣金城市地域配置列表数据
  commissionCitySettings: state.commissionConfig.commissionCitySettings,
});

const mapDispatchToProps = {
  // 查询佣金城市地域配置列表数据
  queryCommissionCitySettings: dva.generateEffect('commissionConfig/queryCommissionCitySettings'),
  // 删除佣金城市地域配置条目
  deleteCommissionCitySetting: dva.generateEffect('commissionConfig/deleteCommissionCitySetting'),
  // 查询省直辖市、城市联动
  queryProvinceCity: dva.generateEffect('commissionConfig/queryProvinceCity'),
  // 佣金城市地域配置是否重复校验
  validateCommissionCitySetting: dva.generateEffect('commissionConfig/validateCommissionCitySetting'),
  // 搜索营业部列表
  queryDepartmentList: dva.generateEffect('commissionConfig/queryDepartmentList'),
  // 保存佣金城市地域配置
  saveCommissionCitySetting: dva.generateEffect('commissionConfig/saveCommissionCitySetting'),
  // 更新佣金城市地域配置
  updateCommissionCitySetting: dva.generateEffect('commissionConfig/updateCommissionCitySetting'),
};

@connect(mapStateToProps, mapDispatchToProps)
@withRouter
export default class CommissionCitySetting extends PureComponent {
  static propTypes = {
    // 搜索省直辖市、城市联动
    queryProvinceCity: PropTypes.func.isRequired,
    // 删除佣金城市地域配置条目
    deleteCommissionCitySetting: PropTypes.func.isRequired,
    // 查询佣金城市地域配置列表数据
    queryCommissionCitySettings: PropTypes.func.isRequired,
    // 佣金城市地域配置列表数据
    commissionCitySettings: PropTypes.object.isRequired,
    // 佣金城市地域配置是否重复校验
    validateCommissionCitySetting: PropTypes.func.isRequired,
    // 搜索营业部列表
    queryDepartmentList: PropTypes.func.isRequired,
    // 保存佣金城市地域配置
    saveCommissionCitySetting: PropTypes.func.isRequired,
    // 编辑佣金城市地域配置
    updateCommissionCitySetting: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);

    this.state = {
      provinceList: [],
      // 省（直辖市）
      provinceCode: '',
      // 城市
      cityList: [],
      cityCode: '',
      // 打开添加【佣金城市地域配置弹框】
      newModalVisible: false,
      // 编辑信息弹框
      editModalVisible: false,
      // 需要编辑的信息数据
      editRecord: null,
    };

    this.columns = this.updateColumns(TABLE_COLUMNS);
  }

  componentDidMount() {
    // 初始化先查询一下列表初始数据
    this.getList();
    // 查询下省分列表
    this.props.queryProvinceCity().then(this.saveProvinceList);
  }

  @autobind
  saveProvinceList({ addrDictList }) {
    this.setState({ provinceList: [DEFAULT_OPTION, ...addrDictList] });
  }

  @autobind
  getList(pageNum = 1) {
    const {
      provinceCode,
      cityCode,
    } = this.state;

    this.props.queryCommissionCitySettings({
      provinceCode,
      cityCode,
      pageSize: 10,
      pageNum,
    });
  }

  @autobind
  updateColumns(columns) {
    return _.map(columns, (column) => {
      const { key } = column;
      if (key === 'startCommission') {
        return this.updateCommissionColumn(column);
      }

      if (key === 'operation') {
        return this.updateOperationColumn(column);
      }

      return this.updateWordColumn(column);
    });
  }

  @autobind
  updateCommissionColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        if (record.flag) {
          return null;
        }

        if (_.isNumber(text)) {
          return text;
        }

        return '--';
      }
    };
  }

  @autobind
  updateOperationColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        if (record.flag) {
          return null;
        }
        // NOTE: 如果该条数据的【营业部】列无值，则不给删除
        const hasDepartment = !_.isEmpty(record.departmentName);

        return (
          <div className={styles.operationCell}>
            <div className={styles.editItem}>
              <Icon type="fankui" onClick={() => this.handleEditItem(record)} />
            </div>
            <IFWrap when={hasDepartment}>
              <div className={styles.deleteItem}>
                <Popconfirm
                  placement="topRight"
                  arrowPointAtCenter={false}
                  onConfirm={() => this.handleDeleteItem(record)}
                  content="确定要将此条信息删除吗？"
                >
                  <Icon type="shanchu" />
                </Popconfirm>
              </div>
            </IFWrap>
          </div>
        );
      }
    };
  }

  @autobind
  handleDeleteItem(record) {
    const { id } = record;
    this.props.deleteCommissionCitySetting({ id })
      .then(this.doAferDelete);

    logCommon({
      type: 'Click',
      payload: {
        name: '佣金配置管理-佣金城市地域配置-删除',
        value: JSON.stringify(record),
      }
    });
  }

  @autobind
  doAferDelete(result) {
    if (result) {
      // 删除成功之后，需要判断删除是否当前页唯一一条，
      // 如果是唯一一条，并且不是第一页，则需要返回查询上一页的数据
      const { commissionCitySettings: { list, page } } = this.props;

      let pageNum = page?.pageNum || 1;

      if (_.size(list) === 1 && page?.pageNum > 1) {
        pageNum = page?.pageNum - 1;
      }
      this.getList(pageNum);
    } else {
      message.error('删除佣金城市地域配置失败！');
    }
  }

  @autobind
  handleEditItem(record) {
    this.setState({
      editModalVisible: true,
      editRecord: record,
    });

    logCommon({
      type: 'Click',
      payload: {
        name: '佣金配置管理-佣金城市地域配置-编辑',
        value: JSON.stringify(record),
      }
    });
  }

  @autobind
  updateWordColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        if (record.flag) {
          return null;
        }
        if (text === '' || _.isNull(text)) {
          return '--';
        }
        return (
          <ToolTipCell
            cellText={text}
            tipContent={text}
          />
        );
      }
    };
  }

  @autobind
  handleProvinceChange({ value }) {
    const { value: label, key } = value;
    this.setState({
      provinceCode: key,
      cityCode: '',
      cityList: [],
    }, () => {
      // 选择完省直辖市后搜索联动的城市
      this.props.queryProvinceCity({
        provCd: key,
      }).then(this.saveCityList);
      // 选择【省直辖市】后，更新列表
      this.getList();
    });

    logCommon({
      type: 'Select',
      payload: {
        name: '佣金城市地域配置-省/（直辖）市',
        value: label,
      }
    });
  }

  @autobind
  saveCityList({ addrDictList }) {
    this.setState({ cityList: [DEFAULT_OPTION, ...addrDictList] });
  }

  @autobind
  handleCityChange({ value }) {
    const { value: label, key } = value;
    this.setState({
      cityCode: key,
    }, this.getList);

    logCommon({
      type: 'Select',
      payload: {
        name: '佣金城市地域配置-城市',
        value: label,
      }
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '佣金配置管理-佣金城市地域配置-添加'
    }
  })
  handleAddCommissionCitySetting() {
    this.setState({
      newModalVisible: true,
    });
  }

  @autobind
  handlePageChange(pageNum) {
    logCommon({
      type: 'Click',
      payload: {
        name: '佣金配置管理-佣金城市地域配置-页码切换',
        value: `${pageNum}`,
      }
    });

    this.getList(pageNum);
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '佣金配置管理-佣金城市地域配置-新建弹框-确认'
    }
  })
  handleAddSettingConfirm(params) {
    this.props.saveCommissionCitySetting(params)
      .then((result) => {
        if (result) {
          this.setState({
            newModalVisible: false,
          });

          this.getList();
        }
      });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '佣金配置管理-佣金城市地域配置-新建弹框-确认'
    }
  })
  handleAddSettingCancel() {
    this.setState({
      newModalVisible: false,
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '佣金配置管理-佣金城市地域配置-新建弹框-关闭'
    }
  })
  handleAddSettinClose() {
    this.setState({
      newModalVisible: false,
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '佣金配置管理-佣金城市地域配置-编辑弹框-确认'
    }
  })
  handleEditSettingCancel() {
    this.setState({
      editModalVisible: false,
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '佣金配置管理-佣金城市地域配置-编辑弹框-关闭'
    }
  })
  handleEditSettinClose() {
    this.setState({
      editModalVisible: false,
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '佣金配置管理-佣金城市地域配置-编辑弹框-确认'
    }
  })
  handleEditSettingConfirm(params) {
    this.props.updateCommissionCitySetting(params)
      .then((result) => {
        if (result) {
          this.setState({
            editModalVisible: false,
          });
          // 编辑后，刷新当前页码下的数据
          const {
            commissionCitySettings: { page }
          } = this.props;

          this.getList(page?.pageNum || 1);
        }
      });
  }

  render() {
    const {
      commissionCitySettings: dataSource
    } = this.props;

    const {
      provinceCode,
      provinceList,
      cityCode,
      cityList,
      newModalVisible,
      editModalVisible,
      editRecord,
    } = this.state;

    // 判断是否存在列表数据
    const hasDatasource = !_.isEmpty(dataSource?.list);

    return (
      <div className={styles.pageContainer}>
        <div className={styles.filterArea}>
          <SingleFilter
            needItemObj
            filterName="省/（直辖）市"
            value={provinceCode}
            data={provinceList}
            onChange={this.handleProvinceChange}
            dropdownStyle={DROPDOWN_STYLE}
          />
          <IFWrap when={!_.isEmpty(provinceCode)}>
            <SingleFilter
              needItemObj
              filterName="城市"
              value={cityCode}
              data={cityList}
              onChange={this.handleCityChange}
              dropdownStyle={DROPDOWN_STYLE}
            />
          </IFWrap>
          <div className={styles.extra}>
            <Button
              icon="plus"
              ghost
              type="rpiamry"
              onClick={this.handleAddCommissionCitySetting}
            >
              添加
            </Button>
          </div>
        </div>
        <div className={styles.tableArea}>
          <Table
            dataSource={dataSource?.list || []}
            columns={this.columns}
            rowKey="custId"
            pagination={false}
            isNeedEmptyRow
            rowNumber={10}
            placeHolderImageProps={PLACEHOLDER_PROPS}
            withBorder={!hasDatasource}
            useNewUI
          />
          <IFWrap when={hasDatasource}>
            <Pagination
              current={dataSource?.page?.pageNum || 1}
              pageSize={10}
              total={dataSource?.page?.totalCount || 0}
              onChange={this.handlePageChange}
            />
          </IFWrap>
        </div>
        {
          newModalVisible
            ? (
              <NewCommissionCitySettingModal
                queryProvinceCity={this.props.queryProvinceCity}
                queryDepartmentList={this.props.queryDepartmentList}
                onConfirm={this.handleAddSettingConfirm}
                onCancel={this.handleAddSettingCancel}
                onClose={this.handleAddSettinClose}
                onValidate={this.props.validateCommissionCitySetting}
              />
            )
            : null
        }
        {
          editModalVisible
            ? (
              <EditCommissionCitySettingModal
                record={editRecord}
                onConfirm={this.handleEditSettingConfirm}
                onCancel={this.handleEditSettingCancel}
                onClose={this.handleEditSettinClose}
                onValidate={this.props.validateCommissionCitySetting}
              />
            )
            : null
        }
      </div>
    );
  }
}
