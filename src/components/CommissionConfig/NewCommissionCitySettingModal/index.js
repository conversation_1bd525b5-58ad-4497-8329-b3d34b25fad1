/*
 * @Author: sunwei<PERSON>
 * @Date: 2021-07-27 10:02:27
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-08-25 09:03:32
 * @description 佣金城市地域配置-编辑
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { Select, Input, Alert } from 'antd';

import IFWrap from '@/components/common/IFWrap';
import SimilarAutoComplete from '@/components/common/similarAutoComplete';
import Modal from '@/newUI/modal';
import { logCommon } from '@/decorators/logable';
import { regxp } from '@/helper';
import FormItemWrap from '../FormItemWrap';

import styles from './index.less';

const MODAL_STYLE = {
  height: '406px',
};

const FORM_STYLE = {
  width: '200px',
};

const TEXTAREA_STYLE = {
  width: '446px',
  height: '60px',
};

const Option = Select.Option;

export default class NewCommissionCitySettingModal extends PureComponent {
  static propTypes = {
    // 确认回调
    onConfirm: PropTypes.func.isRequired,
    // 取消回调
    onCancel: PropTypes.func.isRequired,
    // 关闭回调
    onClose: PropTypes.func.isRequired,
    // 查询省市地区
    queryProvinceCity: PropTypes.func.isRequired,
    // 查询营业部
    queryDepartmentList: PropTypes.func.isRequired,
    // 查询是否重复校验接口
    onValidate: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);

    this.state = {
      // 省/（直辖）市, Select 的 value 值为 undefined 时才会展示组件的 placeholder
      provinceCode: undefined,
      provinceList: [],
      provinceError: '',
      // 城市
      cityCode: undefined,
      cityList: [],
      cityError: '',
      // 其实佣金率
      startCommission: null,
      commissionError: '',
      // 营业部
      departmentCode: '',
      departmentName: '',
      departmentList: [],
      // 备注
      remark: '',
      remarkError: '',
      // 已经存在相关的佣金城市地域配置
      existAlert: false,
    };

    this.footer = [
      {
        key: 'cancel',
        text: '取消',
        onClick: this.props.onCancel,
      },
      {
        key: 'confirm',
        text: '确定',
        type: 'primary',
        onClick: this.handleConfirm,
      },
    ];
  }

  componentDidMount() {
    // 查询下省分列表
    this.props.queryProvinceCity().then(this.saveProvinceList);
  }

  @autobind
  saveProvinceList({ addrDictList }) {
    this.setState({ provinceList: addrDictList });
  }

  @autobind
  checkBeforeConfirm() {
    const {
      provinceCode,
      cityCode,
      remark,
      startCommission,
    } = this.state;
    const errors = {};

    if (_.isEmpty(provinceCode)) {
      errors.provinceError = '必填';
    }

    if (_.isEmpty(cityCode)) {
      errors.cityError = '必填';
    }

    if (!regxp.positiveNum1.test(startCommission)) {
      errors.commissionError = '必须为数字';
    }

    if (_.size(remark) > 50) {
      errors.remarkError = '最多50个字符';
    }

    return errors;
  }

  @autobind
  handleConfirm() {
    const errors = this.checkBeforeConfirm();

    if (!_.isEmpty(errors)) {
      this.setState(errors);
      return;
    }

    const {
      provinceCode,
      cityCode,
      remark,
      departmentCode,
      departmentName,
      startCommission,
      provinceList,
      cityList,
    } = this.state;

    // 点击【确定】按钮时需要，校验下是否重复
    this.props.onValidate({
      provinceCode,
      cityCode,
      departmentCode,
    }).then((result) => {
      if (result === 'N') {
        // 省直辖市、城市在保存接口的时候，需要将相关的名称文案也要传给后端
        const provinceName = _.find(provinceList, (item) => item.key === provinceCode)?.value;
        const cityName = _.find(cityList, (item) => item.key === cityCode)?.value;

        this.props.onConfirm({
          provinceCode,
          provinceName,
          cityCode,
          cityName,
          remark,
          departmentName,
          departmentCode,
          startCommission: _.toNumber(startCommission),
        });
      } else {
        this.setState({ existAlert: true });
      }
    });
  }

  @autobind
  handleProvinceChange(provinceCode) {
    // 切换省份后需要清空城市下拉，并且查询一把城市下拉
    this.setState({
      provinceCode,
      provinceError: '',
      cityCode: undefined,
      cityList: [],
      cityError: '',
      existAlert: false,
    });

    if (!_.isEmpty(provinceCode)) {
      this.props.queryProvinceCity({
        provCd: provinceCode,
      }).then(this.saveCityList);
    }

    const { provinceList } = this.state;
    logCommon({
      type: 'Select',
      payload: {
        name: '佣金城市地域配置管理-新建-省/（直辖）市',
        value: JSON.stringify(_.find(provinceList, (item) => item.key === provinceCode)),
      }
    });
  }

  @autobind
  saveCityList({ addrDictList }) {
    this.setState({ cityList: addrDictList });
  }

  @autobind
  saveDepartment(departmentList) {
    this.setState({ departmentList });
  }

  @autobind
  handleCityChange(cityCode) {
    this.setState({
      cityCode,
      cityError: '',
      existAlert: false,
    });

    // 记录日志用
    const { cityList } = this.state;
    logCommon({
      type: 'Select',
      payload: {
        name: '佣金城市地域配置管理-新建-城市',
        value: JSON.stringify(_.find(cityList, (item) => item.key === cityCode)),
      }
    });
  }

  @autobind
  handleStartCommissionChange(e) {
    const value = e.target.value;
    this.setState({
      startCommission: value,
      commissionError: '',
    });
  }

  @autobind
  handleSearchDepartment(keyword) {
    if (_.isEmpty(keyword)) {
      return;
    }

    this.props.queryDepartmentList({
      keyword,
    }).then(this.saveDepartment);
  }

  @autobind
  handleDepartmentSelect(option) {
    if (_.isEmpty(option)) {
      this.setState({
        departmentCode: '',
        departmentName: '',
        existAlert: false,
      });
      return;
    }
    // const { value: label, key } = value;
    this.setState({
      departmentCode: option?.id,
      departmentName: option?.name,
      existAlert: false,
    });

    logCommon({
      type: 'Select',
      payload: {
        name: '佣金城市地域配置-新建-营业部',
        value: JSON.stringify(option),
      }
    });
  }

  @autobind
  handleRemarkChange(e) {
    const remark = e.target.value;
    this.setState({
      remark,
      remarkError: '',
    });
  }

  @autobind
  renderOption(option) {
    return (<Option key={option.key} value={option.key}>{option.value}</Option>);
  }

  @autobind
  renderProvinceOption() {
    const { provinceList } = this.state;
    return _.map(provinceList, this.renderOption);
  }

  // 渲染个人客户的城市
  @autobind
  renderCityOption() {
    const { cityList } = this.state;
    return _.map(cityList, this.renderOption);
  }

  @autobind
  renderDepartmentOption(record) {
    const { id, name } = record;

    return (
      <Option key={id} value={name}>{name}</Option>
    );
  }

  render() {
    const {
      provinceCode,
      provinceError,
      cityCode,
      cityError,
      startCommission,
      commissionError,
      departmentList,
      remark,
      remarkError,
      existAlert,
    } = this.state;

    return (
      <Modal
        title="新建"
        size="normal"
        visible
        destroyOnClose
        style={MODAL_STYLE}
        onModalClose={this.props.onClose}
        modalFooter={this.footer}
      >
        <div className={styles.container}>
          <IFWrap when={existAlert}>
            <Alert message="记录已存在" type="error" showIcon />
          </IFWrap>
          <FormItemWrap
            title="省/(直辖)市"
            isRequired
            errorMsg={provinceError}
          >
            <Select
              placeholder="请选择"
              style={FORM_STYLE}
              value={provinceCode}
              onChange={this.handleProvinceChange}
            >
              {this.renderProvinceOption()}
            </Select>
          </FormItemWrap>
          <FormItemWrap
            title="城市"
            isRequired
            errorMsg={cityError}
          >
            <Select
              placeholder="请选择"
              style={FORM_STYLE}
              value={cityCode}
              onChange={this.handleCityChange}
            >
              {this.renderCityOption()}
            </Select>
          </FormItemWrap>
          <FormItemWrap
            title="起始佣金（‰）"
            isRequired
            errorMsg={commissionError}
          >
            <Input
              placeholder="请输入"
              style={FORM_STYLE}
              value={startCommission}
              onChange={this.handleStartCommissionChange}
            />
          </FormItemWrap>
          <FormItemWrap title="营业部">
            <SimilarAutoComplete
              placeholder="请输入营业部名称"
              optionList={departmentList}
              style={{ width: 200 }}
              optionKey="id"
              onSelect={this.handleDepartmentSelect}
              onSearch={this.handleSearchDepartment}
              renderOptionNode={this.renderDepartmentOption}
            />
          </FormItemWrap>
          <FormItemWrap
            isTextArea
            title="备注"
            errorMsg={remarkError}
          >
            <Input.TextArea
              placeholder="请输入"
              style={TEXTAREA_STYLE}
              value={remark}
              onChange={this.handleRemarkChange}
            />
          </FormItemWrap>
        </div>
      </Modal>
    );
  }
}
