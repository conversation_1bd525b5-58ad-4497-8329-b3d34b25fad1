/*
 * @Author: sunweibin
 * @Date: 2018-12-13 18:07:26
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-08-13 10:19:22
 * @description 表单项布局容器组件
 */
import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import IfWrap from '@/components/common/IFWrap';

import styles from './index.less';

function FormItem(props) {
  const {
    title,
    children,
    isRequired,
    errorMsg,
    isTextArea,
  } = props;

  const formItemCls = cx({
    [styles.formItem]: true,
    [styles.textArea]: isTextArea,
  });

  return (
    <div className={styles.formItemWrap}>
      <div className={styles.itemLable}>
        <IfWrap when={isRequired}>
          <span className={styles.requried}>*</span>
        </IfWrap>
        {`${title}：`}
      </div>
      <div className={styles.valueArea}>
        <div className={formItemCls}>{children}</div>
        <div className={styles.errorMsg}>{errorMsg}</div>
      </div>
    </div>
  );
}

FormItem.propTypes = {
  title: PropTypes.string.isRequired,
  errorMsg: PropTypes.string,
  children: PropTypes.node.isRequired,
  isRequired: PropTypes.bool,
  isTextArea: PropTypes.bool,
};
FormItem.defaultProps = {
  isRequired: false,
  isTextArea: false,
  errorMsg: '',
};

export default FormItem;
