.formItemWrap {
  display: flex;
  margin-bottom: 14px;

  .itemLable {
    flex: 0 0 auto;
    width: 140px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    color: #666;
    text-align: right;

    .requried {
      color: #f00;
    }
  }

  .valueArea {
    flex: 1 1 auto;
    height: 30px;

    .formItem {
      height: 30px;

      &.textArea {
        height: 60px;
      }
    }

    .errorMsg {
      height: 14px;
      font-size: 12px;
      color: red;
      line-height: 14px;
    }

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-form-explain, .ant-form-extra {
        height: 14px;
        line-height: 14px;
      }

      .@{ant-prefix}-select-arrow:before {
        content: "\E606" !important;
      }
      .@{ant-prefix}-select-disabled .@{ant-prefix}-select-selection {
        border: 1px solid #ccc;
        background-color: #eee;

        .@{ant-prefix}-select-selection-selected-value {
          color: #999;
        }
        .@{ant-prefix}-select-arrow {
          color: #ccc;
        }
      }
      .@{ant-prefix}-input-disabled {
        border: 1px solid #ccc;
        background-color: #eee;
        color: #999;
      }
    }
  }
}
