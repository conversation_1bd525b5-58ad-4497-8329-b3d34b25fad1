/*
 * @Description: 潜在客户-抢单潜客 tab 页
 * @Author: Liu<PERSON>ianShu-K0180193
 * @Date: 2020-03-04 13:17:27
 * @Last Modified by: ya<PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-09-07 13:14:12
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { Input, Button, Tooltip } from 'antd';
import classnames from 'classnames';

import ascSvg from 'htsc/static/svg/asc.svg';
import descSvg from 'htsc/static/svg/desc.svg';
import normalSvg from 'htsc/static/svg/normal.svg';
import logable, { logCommon } from '@/decorators/logable';
import Icon from '@/components/common/Icon';
import Table from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import HtFilter from '@/components/common/htFilter';
import { UNBELONG_CUST } from '@/routes/potentialCustomer/config';
import {
  getPage,
  toDateFormat,
  renderCell,
  formatQuery,
  openAccountDefaultQuery,
  getJumpPathname,
  checkManagerPermission,
  checkManagerLevel,
  checkNotManagerPermission,
  renderOpeningStepText,
} from './utils';

import {
  WAIT_DEPOSITORY_TEXT,
  WAIT_DEPOSITORY_FULL_TEXT,
} from '../potentialGrab/config';
import {
  OPEN_ACCOUNT_COLUMNS,
  IS_CHANGE_TYPES,
  DESC,
  ASC,
  OPEN_ACCOUNT_TYPE,
  SPACE_20,
  DEFAULT_PAGE_NUM,
  MARKET_USER_TYPE_MAP,
  KEY_TRANSFER,
  NO_DATA_TEXT,
} from './config';

import styles from './listView.less';

const Search = Input.Search;
// 是否转化展示先不显示，待后续迭代开放，设置个开关，便于下次放开
// 注意 config 里的 column 要同时放开
// utils 里的默认查询条件里的同样需要放开，openAccountDefaultQuery，registerDefaultQuery
const showTran = false;

export default class UnbelongTab extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    queryOpenAccountList: PropTypes.func.isRequired,
    openAccountData: PropTypes.object.isRequired,
    showServiceRecord: PropTypes.func.isRequired,
    addServiceRecord: PropTypes.func.isRequired,
  };

  static contextTypes = {
    replace: PropTypes.func.isRequired,
    push: PropTypes.func.isRequired,
    empInfo: PropTypes.object.isRequired,
  }

  componentDidMount() {
    this.getOpenAccountList();
  }

  // 当查询条件改变的时候重新调接口
  componentDidUpdate(prevProps) {
    const {
      location: {
        query: prevQuery,
      },
    } = prevProps;
    const {
      location: {
        query,
        query: {
          potentialActiveKey = UNBELONG_CUST,
        },
      },
    } = this.props;
    if (prevQuery !== query && potentialActiveKey === UNBELONG_CUST) {
      this.getOpenAccountList();
    }
  }

  @autobind
  getOpenAccountList() {
    const {
      location: {
        query,
      },
      queryOpenAccountList,
    } = this.props;
    const finalQuery = _.omit({ ...openAccountDefaultQuery, ...formatQuery(query) }, 'potentialActiveKey');
    queryOpenAccountList(finalQuery);
  }

  @autobind
  setNewQuery(params) {
    const { location: { query } } = this.props;
    const newQuery = formatQuery(query);
    this.context.replace({
      query: {
        ...newQuery,
        ...params,
      },
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '客户中心-潜在客户-抢单潜客列表-搜索框',
      value: '$args[0]',
    },
  })
  handleInputSearch(value) {
    this.setNewQuery({
      searchText: value,
      pageNum: DEFAULT_PAGE_NUM,
    });
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '客户中心-潜在客户-抢单潜客-是否转化切换',
      value: '$args[0].value',
    },
  })
  handleIsChangeSelect({ value }) {
    this.setNewQuery({
      isChange: value,
      pageNum: DEFAULT_PAGE_NUM,
    });
  }

  // 切换开户潜客页码
  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '客户中心-潜在客户-抢单潜客-翻页',
      value: '$args[0]'
    },
  })
  handlePageChange(pageNum) {
    this.setNewQuery({
      pageNum,
    });
  }

  // 渲染table列之前判断是否有权限展示checkbox框
  @autobind
  renderColumns() {
    const newColumns = _.map(OPEN_ACCOUNT_COLUMNS, (column) => {
      const { key } = column;
      if (key === 'createdTime') {
        return this.renderTag(this.renderSortColumn(column));
      }
      if (key === 'lastUpdatedTime') {
        return this.renderSortColumn(column);
      }
      if (key === 'approvalStatus') {
        return this.renderApprovalStatus(column);
      }
      if (key === 'hasServiceRecord') {
        return this.renderHasServiceRecordColumn(column);
      }
      if (key === 'isChange') {
        return this.renderIsChangeColumn(column);
      }
      if (key === 'currentStep') {
        return {
          ...column,
          render: (text, record) => renderOpeningStepText(text, record),
        };
      }
      return {
        ...column,
        render: (text, record) => renderCell(text, record),
      };
    });
    return newColumns;
  }

  // 渲染排序列表
  @autobind
  renderSortColumn(column) {
    const {
      location: {
        query: {
          sortType = 'lastUpdatedTime',
          sortDirection = DESC,
        },
      },
    } = this.props;
    // 默认排序图标
    let direImg = normalSvg;
    if (sortType === column.dataIndex) {
      direImg = sortDirection === DESC ? descSvg : ascSvg;
    }
    return {
      ...column,
      title: (
        <span className={styles.sortContent}>
          {column.title}
          <img src={direImg} className={styles.sortImg} alt="排序方向" />
        </span>
      ),
      render: (text, record) => renderCell(toDateFormat(text), record),
      onHeaderCell: () => ({
        onClick: () => this.handleSortChange(column),
      })
    };
  }

  // 渲染角标
  @autobind
  renderTag(column) {
    return {
      ...column,
      render: (text, record) => {
        const showText = renderCell(toDateFormat(text), record);
        const {
          servTranTerm,
          servTranDuration,
        } = record;
        // 是否是转化期限
        const isTransfer = servTranTerm === KEY_TRANSFER;
        let tagNode = null;
        const tagClassNames = classnames({
          [styles.tag]: true,
          [styles.serviceTag]: !isTransfer,
          [styles.transferTag]: isTransfer,
        });
        let tooltipText = '剩余添加服务记录时间，到期将自动回收！';
        if (isTransfer) {
          tooltipText = '剩余可转化开户时间，到期将自动回收！';
        }
        // 剩余期限有值，并且 服务期限小于等于三天或者非转化期限，显示 tag
        if (servTranDuration && (servTranDuration <= 3 || !isTransfer)) {
          tagNode = (
            <Tooltip
              placement="topLeft"
              title={tooltipText}
              trigger="hover"
              overlayClassName={styles.tooltip}
            >
              <div className={tagClassNames}>剩{servTranDuration}天</div>
            </Tooltip>
          );
        }
        return (
          <div className={styles.createTimeWrap}>
            {tagNode}
            {showText}
          </div>
        );
      }
    };
  }

  // 渲染是否转化
  @autobind
  renderIsChangeColumn(column) {
    return {
      ...column,
      render: (text) => {
        if (!text) {
          return <span className={styles.noData}>{NO_DATA_TEXT}</span>;
        }
        return text === 'Y' ? '是' : '否';
      }
    };
  }

  // 审核状态
  @autobind
  renderApprovalStatus(column) {
    return {
      ...column,
      render: (text, record) => {
        const showText = text === WAIT_DEPOSITORY_FULL_TEXT ? WAIT_DEPOSITORY_TEXT : text;
        return renderCell(showText, record);
      },
    };
  }

  // 是否有服务记录
  @autobind
  renderHasServiceRecordColumn(column) {
    const { showServiceRecord, addServiceRecord } = this.props;
    return {
      ...column,
      render: (text, record) => {
        const {
          hasServiceRecord,
          isChange,
          id,
        } = record;
        return (
          <div className={styles.hasServiceRecord}>
            <IFWrap when={hasServiceRecord}>
              <Icon
                type="chakanlishibanben"
                className={styles.hasServiceRecordIcon}
                onClick={() => {
                  showServiceRecord({
                    custId: id,
                    type: OPEN_ACCOUNT_TYPE,
                  });
                }}
              />
            </IFWrap>
            <IFWrap when={isChange === '否'}>
              <Icon
                type="bianji3"
                className={styles.hasServiceRecordIcon}
                onClick={() => {
                  addServiceRecord({
                    custId: id,
                    custType: OPEN_ACCOUNT_TYPE,
                  });
                }}
              />
            </IFWrap>
          </div>
        );
      },
    };
  }

  // 点击排序
  @autobind
  handleSortChange(column) {
    const { location: { query: { sortType, sortDirection } } } = this.props;
    const { dataIndex } = column;
    let nextSortDirection = DESC;
    if (sortType === dataIndex) {
      nextSortDirection = sortDirection === DESC ? ASC : DESC;
    }
    this.setNewQuery({
      sortType: dataIndex,
      sortDirection: nextSortDirection,
      pageNum: DEFAULT_PAGE_NUM,
    });
    logCommon({
      type: 'Click',
      payload: {
        name: `潜客商机-开户潜客-${column.title}排序`,
        value: nextSortDirection === ASC ? '升序' : '降序',
      },
    });
  }

  // 判断是否是 A 类员工
  @autobind
  checkClassAEmp() {
    const {
      empInfo,
    } = this.context;
    const { empInfo: { srvRoleCd } } = empInfo;
    // 判断员工类型
    return !_.includes(MARKET_USER_TYPE_MAP, srvRoleCd);
  }

  // 抢单按钮
  @autobind
  @logable({
    type: 'ButtonClick',
    payload: {
      name: '客户中心-潜在客户-抢单潜客-抢单按钮',
      value: '',
    },
  })
  handleGrabOrder() {
    const { push } = this.context;
    push({
      pathname: '/customerPool/potentialGrab',
      query: {
        potentialGrabactiveKey: OPEN_ACCOUNT_TYPE,
      },
    });
  }

  // 转化追踪按钮
  @autobind
  @logable({
    type: 'ButtonClick',
    payload: {
      name: '客户中心-潜在客户-抢单潜客-转化追踪按钮',
      value: '',
    },
  })
  handleJumpPage() {
    this.context.push({
      pathname: getJumpPathname(),
    });
  }

  // 显示追踪按钮
  @autobind
  showTrackBtn() {
    // 当是管理层 时候，需要满足管理层权限要求
    // 或者 营业部层，满足营业部层级权限要求，并且是A类员工
    return (checkManagerLevel() && checkManagerPermission())
      || (checkNotManagerPermission() && this.checkClassAEmp());
  }

  render() {
    const {
      openAccountData: { list = [], page = {} },
      location: {
        query: {
          isChange = 'N',
        },
      },
    } = this.props;
    // 获取分页器属性
    const pageProps = getPage(page);
    // 渲染表格
    const columns = this.renderColumns();
    const allWidth = _.sumBy(columns, 'width');
    // 开户潜客无数据展位图属性
    const OPEN_ACCOUNT_PLACE_HOLDER = {
      title: '暂无抢单潜客数据',
      style: {
        height: '435px',
      },
      // extraNode: (
      //   <IFWrap when={this.checkClassAEmp()}>
      //     <div className={styles.tableGrabButton} onClick={this.handleGrabOrder}>
      //       去抢单
      //       <Icon type="xiangyou1" className={styles.tableGrabButtonIcon} />
      //     </div>
      //   </IFWrap>
      // ),
    };
    return (
      <div className={styles.listView}>
        <div className={styles.headerFilter}>
          <div className={styles.filterItem}>
            <Search
              placeholder="手机号/姓名/身份证号"
              onSearch={this.handleInputSearch}
            />
          </div>
          <IFWrap when={showTran}>
            <div className={styles.filterItem}>
              <HtFilter
                menuContainer="body"
                value={isChange}
                data={IS_CHANGE_TYPES}
                onChange={this.handleIsChangeSelect}
                filterName="是否转化"
                filterId="isChange"
                type="single"
                dataMap={['code', 'value']}
                filterOption={['isChange']}
              />
            </div>
          </IFWrap>
          <div className={styles.btnGroup}>
            <IFWrap when={this.showTrackBtn()}>
              <Button type="primary" ghost onClick={this.handleJumpPage} className={styles.trackBtn}>转化追踪</Button>
            </IFWrap>
            {/*
            <IFWrap when={this.checkClassAEmp()}>
              <Button type="primary" className={styles.grabBtn} onClick={this.handleGrabOrder}>
              抢单</Button>
            </IFWrap>
            */}
          </div>
        </div>
        <div className={styles.tableArea}>
          <Table
            useNewUI
            pagination={false}
            columns={columns}
            dataSource={list}
            scroll={{ x: allWidth }}
            rowKey="id"
            spaceColumnProps={SPACE_20}
            placeHolderImageProps={OPEN_ACCOUNT_PLACE_HOLDER}
          />
          <IFWrap when={!_.isEmpty(list)}>
            <div className={styles.pageArea}>
              <Pagination
                {...pageProps}
                onChange={this.handlePageChange}
              />
            </div>
          </IFWrap>
        </div>
      </div>
    );
  }
}
