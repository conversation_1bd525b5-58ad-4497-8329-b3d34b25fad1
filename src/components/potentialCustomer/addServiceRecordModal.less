.addServiceRecordWrap {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-form-item {
      margin-bottom: 0;
    }
    .ant-form-item-control {
      line-height: 30px;
    }
  }
  .infoGroup {
    margin-top: 20px;
    .inline {
      display: flex;
    }
    .infoItem {
      display: flex;
      margin: 0 39px 14px 21px;
      .infoLabel {
        width: 100px;
        font-size: 14px;
        color: #9b9b9b;
        text-align: right;
        line-height: 30px;
        i {
          margin-right: 4px;
          color: red;
          font-style: normal;
        }
      }
      .infoContent {
        color: #333;
        font-size: 14px;
        span {
          line-height: 30px;
        }
        .textArea {
          width: 560px;
        }
          /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
          .ant-select {
            width: 200px;
            height: 30px;
            border-radius: 3px;
          }
          .ant-uploader .ant-uploader-attachList .ant-uploader-button .ant-uploader-content .ant-uploader-text {
            line-height: 30px;
          }
        }
      }
    }
  }
}
