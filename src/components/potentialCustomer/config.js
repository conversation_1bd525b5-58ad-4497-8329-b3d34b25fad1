/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-09-26 15:05:51
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-04-26 10:57:46
 * @Description: 潜客商机列表配置文件
 */

// 列间距20
export const SPACE_20 = {
  width: 20,
};
// 列间距25
export const SPACE_31 = {
  width: 31,
};
// 抢单潜客的Table列
export const OPEN_ACCOUNT_COLUMNS = [
  {
    key: 'createdTime',
    dataIndex: 'createdTime',
    title: '创建时间',
    width: 138,
    fixed: 'left',
  },
  {
    key: 'lastUpdatedTime',
    dataIndex: 'lastUpdatedTime',
    title: '最近一次操作时间',
    width: 138,
    fixed: 'left',
  },
  {
    key: 'phone',
    dataIndex: 'phone',
    title: '手机号码',
    width: 91,
    fixed: 'left',
  },
  {
    key: 'phoneLocation',
    dataIndex: 'phoneLocation',
    title: '手机号归属地',
    width: 84,
  },
  {
    key: 'name',
    dataIndex: 'name',
    title: '姓名',
    width: 56,
  },
  {
    key: 'custId',
    dataIndex: 'custId',
    title: '客户号',
    width: 99,
  },
  // {
  //   key: 'isChange',
  //   dataIndex: 'isChange',
  //   title: '是否转化',
  //   width: 56,
  // },
  {
    key: 'idnumber',
    dataIndex: 'idnumber',
    title: '身份证号',
    width: 56,
  },
  {
    key: 'channel',
    dataIndex: 'channel',
    title: '渠道名称',
    width: 174,
  },
  {
    key: 'currentStep',
    dataIndex: 'currentStep',
    title: '当前步骤',
    width: 84,
  },
  {
    key: 'isSubmitApproval',
    dataIndex: 'isSubmitApproval',
    title: '是否提交审核',
    width: 84,
  },
  {
    key: 'approvalStatus',
    dataIndex: 'approvalStatus',
    title: '审核状态',
    width: 84,
  },
  {
    key: 'productName',
    dataIndex: 'productName',
    title: '套餐',
    width: 150,
  },
  {
    key: 'applicationAccount',
    dataIndex: 'applicationAccount',
    title: '申请账户',
    width: 140,
  },
  {
    key: 'depostoryBank',
    dataIndex: 'depostoryBank',
    title: '存管银行',
    width: 60,
  },
  {
    key: 'hasServiceRecord',
    dataIndex: 'hasServiceRecord',
    title: '服务记录',
    width: 56,
    fixed: 'right',
  },
];

// 注册潜客的Table列
export const REGISTER_COLUMNS = [
  {
    key: 'registerTime',
    dataIndex: 'registerTime',
    title: '注册时间',
    width: 138,
  },
  {
    key: 'recentLoginTime',
    dataIndex: 'recentLoginTime',
    title: '最近登录时间',
    width: 138,
  },
  {
    key: 'phone',
    dataIndex: 'phone',
    title: '手机号码',
    width: 91,
  },
  {
    key: 'phoneLocation',
    dataIndex: 'phoneLocation',
    title: '手机号归属地',
    width: 84,
  },
  {
    key: 'userName',
    dataIndex: 'userName',
    title: '用户名',
    width: 110,
  },
  // {
  //   key: 'isChange',
  //   dataIndex: 'isChange',
  //   title: '是否转化',
  //   width: 56,
  // },
  {
    key: 'custBehaviorLead',
    dataIndex: 'custBehaviorLead',
    title: '客户行为线索',
  },
  {
    key: 'registerSource',
    dataIndex: 'registerSource',
    title: '注册来源',
    width: 166,
  },
  {
    key: 'hasServiceRecord',
    dataIndex: 'hasServiceRecord',
    title: '服务记录',
    width: 56,
  },
];
// 开户潜客【是否已被申领过】筛选下拉值
export const IS_CHANGE_TYPES = [
  {
    code: '',
    value: '不限',
  },
  {
    code: 'Y',
    value: '是',
  },
  {
    code: 'N',
    value: '否',
  },
];
// 降序
export const DESC = 'desc';
// 升序
export const ASC = 'asc';
// 开户潜客类型
export const OPEN_ACCOUNT_TYPE = '1';
// 注册潜客类型
export const REGISTER_TYPE = '2';
// 默认第一页
export const DEFAULT_PAGE_NUM = 1;
// 每页大小默认20
export const PAGE_SIZE = 10;

// 时间格式，时分秒
export const DATE_FORMAT_ALL = 'YYYY-MM-DD HH:mm:ss';
// 选择框列的宽度
export const SELECTION_COLUMN_WIDTH = 33;

// 服务方式-涨乐财富通
export const ZL_KEY = 'ZLFins';
// 服务方式-企微
export const WORKWX_WAY_KEY = 'HTSC WorkWx';
// 添加服务记录界面上显示的日期格式
export const DATE_FORMAT_SHOW = 'YYYY年MM月DD日';
// 年月日的格式化
export const DATE_FORMAT_YYYYMMDD = 'YYYY-MM-DD';
// 潜客的默认服务类型
export const DEFAULT_SERVICE_TYPE = '199';
// A 类员工
export const MARKET_USER_TYPE_MAP = ['207090', '207060', '207100'];
// 转化的 key
export const KEY_TRANSFER = 'tran';
// 没有数据的text
export const NO_DATA_TEXT = '--';
// 总部
export const ZONGBU_CODE = '2';
// 分公司
export const FENGONGSI_CODE = '3';
// 营业部
export const YINGYEBU_CODE = '4';
// 营业部管理用户
export const YINGYEBU_ORG_CODE = 'org';

// 【视频见证】审核状态配置
export const VideoApproveStatus = [
  { code: '-1', text: '审核退回' },
  { code: '0', text: '待审核' },
  { code: '1', text: '已通过' },
  { code: '2', text: '待审核' },
  { code: '3', text: '未提交' },
];
