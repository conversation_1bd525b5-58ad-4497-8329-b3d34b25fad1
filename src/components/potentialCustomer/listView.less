.listView {
  padding-top: 14px;
  .headerFilter {
    display: flex;
    flex-wrap: wrap;
    .filterItem {
      margin-right: 10px;
    }
    .btnGroup {
      position: absolute;
      right: 0;
      .trackBtn {
        padding-left: 32px;
        background: url(./images/track.svg) 10px center no-repeat !important;
        background-size: 16px 16px !important;
        &:active {
          background: #108ee9 url(./images/track_white.svg) 10px center no-repeat !important;
          background-size: 16px 16px !important;
          color: #fff !important;
        }
      }
      button {
        min-width: 80px;
        margin-left: 10px !important;
        img {
          width: 16px;
          height: 16px;
          vertical-align: sub;
          margin-right: 4px;
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  .tableArea {
    margin-top: 10px;
    position: relative;
    height: 100%;
    width: 100%;
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-table-scroll {
        .ant-table-body {
          overflow-y: hidden !important;
        }
      }
      .ant-table-tbody > tr > td.ant-table-selection-column,
      .ant-table-thead > tr > th.ant-table-selection-column {
        text-align: left;
        padding-left: 20px;
      }
      .ant-table-fixed-right .ant-table-thead > tr > th:first-child {
        padding-left: 20px;
      }
      .ant-table-fixed-right .ant-table-tbody > tr > td:first-child {
        padding-left: 20px;
      }
    }
    .sortContent {
      cursor: pointer;
      .sortImg {
        width: 12px;
        height: 14px;
        margin-left: 6px;
      }
    }
    .hasServiceRecord {
      text-align: right;
      color: #ccc;
      padding-right: 10px;
      i {
        color: #108ee9;
        &:nth-child(2) {
          margin-left: 16px;
        }
      }
    }
    .hasServiceRecord:hover {
      cursor: pointer;
    }
    .createTimeWrap {
      position: relative;
    }
    .tag {
      position: absolute;
      width: 34px;
      height: 34px;
      top: -8px;
      left: -20px;
      font-size: 10px;
      transform: rotate(-45deg) scale(0.85);
      color: #fff;
      cursor: pointer;
      &:before {
        content: "";
        position: absolute;
        width: 0;
        height: 0;
        border-right: 34px solid transparent;
        transform: rotate(45deg) scale(1.15);
        z-index: -1;
      }
    }
    .serviceTag {
      &:before {
        border-top: 34px solid #d33d06;
      }
    }
    .transferTag {
      &:before {
        border-top: 34px solid #f18200;
      }
    }
    .tableGrabButton {
      color: #108ee9;
      display: flex;
      align-items: center;
      cursor: pointer;
      .tableGrabButtonIcon {
        font-size: 12px;
        margin-left: 5px;
      }
    }
  }
  .pageArea {
    margin: 20px 0 40px 20px;
  }
  .noData {
    color: #ccc;
  }
}
.tooltip {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-tooltip-inner {
      width: 268px;
    }
  }
}
