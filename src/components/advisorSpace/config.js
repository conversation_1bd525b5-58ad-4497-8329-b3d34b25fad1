/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-09-14 14:04:07
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-04-19 15:32:43
 */
import _ from 'lodash';

const advisorSpace = {
  pageName: '投顾空间申请',
  pageType: '14', // 查询列表接口中的type值
  statusOptions: [
    {
      show: true,
      label: '预约成功',
      value: '06',
    },
    {
      show: true,
      label: '已撤销',
      value: '07',
    }
  ],
};
// 列表项中tag的样式配置项
const STATUS_TAGS = [
  {
    code: '06',
    text: '预约成功',
    type: 'processing',
  },
  {
    code: '07',
    text: '已撤销',
    type: 'stop',
  },
];

// 时间段选择样式
const SCHEDULE_STYLES = {
  rowContentStyle: { width: '140px', height: '37px', lineHeight: '37px' },
  cellStyle: { height: '37px' },
  disabledCellStyle: { background: '#108ee9', opacity: '0.3' },
  selectedCellStyle: { background: '#108ee9' },
};

// 根据申请单的状态Code值获取到申请单状态标签组件的属性，用于展示各色标签
const getStatusTagProps = code => _.find(STATUS_TAGS, item => item.code === code);
export {
  advisorSpace,
  getStatusTagProps,
  SCHEDULE_STYLES,
};
