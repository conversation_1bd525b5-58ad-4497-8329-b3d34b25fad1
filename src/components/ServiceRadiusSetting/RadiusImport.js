/*
 * @Author: qianwen.li
 * @Date: 2022-06-13 12:12:59
 * @LastEditTime: 2024-02-23 13:54:59
 * @LastEditors: fanxingmeng
 * @Description: 投顾服务半径设置-导入
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { Upload, message } from 'antd';
import cx from 'classnames';
import * as XLSX from 'xlsx';
import { emp } from '@aorta-pc/utils';
import _ from 'lodash';

import styles from './radiusImport.less';

export default class RadiusImport extends PureComponent {
  static propTypes = {
    // 是否禁用导入按钮
    disabled: PropTypes.bool.isRequired,
    // 数据格式化
    format: PropTypes.arrayOf(PropTypes.array),
    // 需要几张表
    sheetCount: PropTypes.number,
    // 从第几条数据开始
    range: PropTypes.arrayOf(PropTypes.number),
    // 上传事件
    onUploadData: PropTypes.func.isRequired,
    // 改变加载中状态
    onUploading: PropTypes.func.isRequired,
  }

  static defaultProps = {
    format: [['empId']],
    sheetCount: 1,
    range: [4],
  }

  @autobind
  beforeUpload(file) {
    if (!_.includes(file.name, '.xls') && !_.includes(file.name, '.xlsx')) {
      message.error('导入格式不是Excel');
      return false;
    }
    return true;
  }

  @autobind
  handleChangeUpload({ file }) {
    if (file?.status === 'uploading') {
      this.props.onUploading(true);
    }
    if (file?.status === 'error') {
      this.props.onUploading(false);
    }
    if (file?.status === 'done') {
      this.analyzingCustList(file);
    }
  }

  @autobind
  analyzingCustList(file) {
    const { format, sheetCount, range } = this.props;
    // 解析Excel文件数据
    const reader = new FileReader();
    reader.onload = (event) => {
      const result = event.target.result;
      const workBook = XLSX.read(result, { type: 'array' });
      // 需要几张表，则截取几张表
      const needSheetNames = _.slice(workBook.SheetNames, 0, sheetCount);
      const sheetKeys = Object.keys(workBook.Sheets);
      const data = [];
      const originData = [];
      _.forEach(sheetKeys, (key, index) => {
        if (_.includes(needSheetNames, key)) {
          const tempData = workBook.Sheets[key];
          originData[index] = XLSX.utils.sheet_to_json(tempData);
          data[index] = XLSX.utils.sheet_to_json(tempData, {
            // 从第几条开始
            range: range[index],
            // 格式化 key
            header: format[index] || format[0],
            // 是否跳过标题
            skipHeader: false,
          });
        }
      });
      // 将列表保存时需要的数据传到父组件并关闭loading
      const attachmentId = file?.response?.resultData?.attaches?.[0]?.attachId;
      const fileName = file?.name;
      this.props.onUploadData(data, originData, attachmentId, fileName);
      this.props.onUploading(false);
    };
    reader.readAsArrayBuffer(file?.originFileObj);
  }

  render() {
    const { disabled } = this.props;
    const action = '/fspa/aorta/dmz/api/storage/s3/uploadForSpecial';
    return (
      <Upload
        action={action}
        data={{ empId: emp.getId() }}
        accept=".xls,.xlsx"
        beforeUpload={this.beforeUpload}
        onChange={this.handleChangeUpload}
        disabled={disabled}
        showUploadList={false}
      >
        <span
          className={cx({
            [styles.radiusImportWrap]: true,
            [styles.disabled]: disabled,
          })}
        >
          导入
        </span>
      </Upload>
    );
  }
}
