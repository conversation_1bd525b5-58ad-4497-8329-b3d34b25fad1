/**
 * @Author: sunweibin
 * @Date: 2018-02-26 13:49:27
 * @Last Modified by: LiuJianShu-K0180193
 * @Last Modified time: 2020-09-21 00:57:27
 * @description 套利软件展示页详情组件
 */

import React, { PureComponent } from 'react';
import { autobind } from 'core-decorators';
import PropTypes from 'prop-types';
import _ from 'lodash';

import Uploader from '@/newUI/upload';
import InfoTitle from '../common/InfoTitle';
import InfoItem from '../common/infoItem';
import ApproveList from '../common/approveList';
import styles from './detail.less';
import { seibelConfig } from '../../config';
import { time } from '../../helper';
import {
  EMPTY_PARAM,
} from './config';

// 合约条款的表头、状态对应值
const { contract: { status } } = seibelConfig;
export default class Detail extends PureComponent {
  static propTypes = {
    protocolDetail: PropTypes.object.isRequired,
    flowHistory: PropTypes.array.isRequired,
    attachmentList: PropTypes.array,
  }

  static defaultProps = {
    attachmentList: [],
  }

  // 拼接开通权限的显示文本
  @autobind
  joinPermissionText(textArray) {
    if (!Array.isArray(textArray)) return '';
    return textArray.map((item) => item.value).join('，');
  }

  // 将拟稿人信息中的状态切换成中文
  @autobind
  convertDraftStatus(draftStatus) {
    let statusLabel = '';
    if (draftStatus) {
      statusLabel = status[Number(draftStatus)].label;
    }
    return statusLabel;
  }

  render() {
    const {
      protocolDetail,
      flowHistory,
      attachmentList,
    } = this.props;
    // 客户姓名，
    // 由于后台接口之前contactName和accountName同时存在，
    // 有时候有有时候没有，
    // 所以采取两个字段同时判断，来显示客户名称以及经纪客户号
    // 都没有数据则显示暂无
    const custName = `${(protocolDetail.contactName || protocolDetail.accountName) || EMPTY_PARAM} ${protocolDetail.econNum || EMPTY_PARAM}`;
    // 资金账户
    const fundAccount = protocolDetail?.fundAccount || EMPTY_PARAM;
    // 信用账户
    const creditAccount = protocolDetail?.creditAccount || EMPTY_PARAM;
    const approverName = protocolDetail.approver ? `${protocolDetail.approverName} (${protocolDetail.approver})` : EMPTY_PARAM;
    const nowStep = {
      // 当前步骤
      stepName: protocolDetail.workflowNode || EMPTY_PARAM,
      // 当前审批人
      handleName: approverName,
    };
    // 拟稿信息状态文字
    const statusLabel = this.convertDraftStatus(protocolDetail.status);
    return (
      <div className={styles.detailBox}>
        <h1 className={styles.detailTitle}>编号:{protocolDetail.appId}</h1>
        <div className={styles.detailModule}>
          <InfoTitle head="基本信息" />
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="操作类型"
              value={protocolDetail.operationTypeText || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="子类型"
              value={protocolDetail.subType || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="客户"
              value={custName}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="资金账户"
              value={fundAccount}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="信用账户"
              value={creditAccount}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议编号"
              value={protocolDetail.agreementNum}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议模板"
              value={protocolDetail.templateId}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议开始日期"
              value={time.format(protocolDetail.startDt) || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议有效期"
              value={time.format(protocolDetail.vailDt) || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="软件账号"
              value={protocolDetail.softAccount || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="软件密码"
              value={protocolDetail.softPassword || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="备注"
              value={protocolDetail.content || EMPTY_PARAM}
            />
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="拟稿信息" />
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="拟稿人"
              value={`${protocolDetail.divisionName || EMPTY_PARAM} ${protocolDetail.createdName || EMPTY_PARAM}`}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="提请时间"
              value={protocolDetail.createdDt}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="状态"
              value={statusLabel || EMPTY_PARAM}
            />
          </div>
        </div>
        <div className={styles.mutiUploadModule}>
          <InfoTitle head="附件信息" />
          <div className={styles.line}>
            {
              _.map(attachmentList, (item) => (
                <Uploader
                  disabled
                  showTitle
                  removeable={false}
                  title={item.title === '承诺书' ? '合规交易承诺书' : item.title}
                  defaultFileList={item.attachmentList}
                  key={`${protocolDetail.id}${item.title}`}
                />
              ))
            }
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="审批记录" />
          <div className={styles.line}>
            <ApproveList data={flowHistory} nowStep={nowStep} />
          </div>
        </div>
      </div>
    );
  }
}
