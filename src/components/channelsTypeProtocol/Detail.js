/**
 * @Description: 通道类型协议详情页面
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2017-09-19 09:37:42
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>hu-K0180193
 * @Last Modified time: 2020-05-26 20:16:38
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';

import Uploader from '@/newUI/upload';
import InfoTitle from '../common/InfoTitle';
import InfoItem from '../common/infoItem';
import ApproveList from '../common/approveList';
import styles from './detail.less';
import CommonTable from '../common/table';
import IfWrap from '../common/IFWrap';
import { seibelConfig } from '../../config';
import { time } from '../../helper';
import channelType from '../../helper/page/channelType';
import config from '../../routes/channelsTypeProtocol/config';
import {
  EMPTY_PARAM,
} from './config';

const {
  underCustTitleList, // 下挂客户表头集合
  protocolClauseTitleList, // 协议条款表头集合
  protocolProductTitleList, // 协议产品表头集合
} = seibelConfig.channelsTypeProtocol;

const SUBTYPE = {
  // 高速通道
  heightSpeed: '0501',
  // 紫金通道
  violetGold: '0502',
};

// const EMPTY_OBJECT = {};
const EMPTY_ARRAY = [];
// bool MAP数据
const mapBoolData = {
  Y: '是',
  N: '否',
};
// 合约条款的表头、状态对应值
const { contract: { status } } = seibelConfig;
export default class Detail extends PureComponent {
  static propTypes = {
    protocolDetail: PropTypes.object.isRequired,
    flowHistory: PropTypes.array.isRequired,
    attachmentList: PropTypes.array,
    // showEditModal: PropTypes.func,
    // hasEditPermission: PropTypes.bool,
    currentView: PropTypes.string,
  }

  static defaultProps = {
    attachmentList: EMPTY_ARRAY,
    // showEditModal: () => {},
    // hasEditPermission: false,
    currentView: '',
  }

  // @autobind
  // changeEdit() {
  //   this.setState({
  //     edit: true,
  //   });
  // }

  render() {
    const {
      protocolDetail,
      flowHistory,
      attachmentList,
      // showEditModal,
      // hasEditPermission,
      // 传入视图不同，判断是否显示申请单编号
      currentView,
    } = this.props;
    // 客户名称
    const custName = `${(protocolDetail.contactName || protocolDetail.accountName) || EMPTY_PARAM} ${protocolDetail.econNum || EMPTY_PARAM}`;
    // 资金账户
    const fundAccount = protocolDetail?.fundAccount || EMPTY_PARAM;
    // 信用账户
    const creditAccount = protocolDetail?.creditAccount || EMPTY_PARAM;
    const approverName = protocolDetail.approver ? `${protocolDetail.approverName} (${protocolDetail.approver})` : EMPTY_PARAM;
    const nowStep = {
      // 当前步骤
      stepName: protocolDetail.workflowNode || EMPTY_PARAM,
      // 当前审批人
      handleName: approverName,
    };
    const scroll = {
      x: true,
    };
    /**
     * 【十档行情】、【是否多账户使用】只有在
     * 子类型为【紫金快车道】、操作类型为【协议订购】并且选择的协议模板为非【十档行情】类模板
     * 才展示
     */
    let isShowTenLevelAndMultiAccount = false;
    // 判断是否是紫金快车道 并且是 协议订购
    if (currentView === SUBTYPE.violetGold
        && _.includes(config.subscribeArray, protocolDetail.operationType)) {
      // 判断是否是十档行情
      isShowTenLevelAndMultiAccount = !_.includes(protocolDetail.templateId, '十档');
    }
    // 是否是套利软件
    const isArbirageSoftWare = channelType.isArbirageSoftware(protocolDetail.subType);
    // 是否多账户使用
    const multiUsedFlag = protocolDetail.multiUsedFlag === 'Y' || false;
    // 判断是否显示下挂客户-操作类型是新增或者删除 || （非套利软件 && 多账户使用）
    const showUnderCust = (protocolDetail.operationType === 'AddDel')
      || (!isArbirageSoftWare && multiUsedFlag);
    // 判断是否显示协议编号
    // const isShowProtocolNum = !(protocolDetail.operationType === '协议订购');
    let statusLabel = '';
    if (protocolDetail.status) {
      statusLabel = status[Number(protocolDetail.status)].label;
    } else {
      statusLabel = '';
    }
    return (
      <div className={styles.detailBox}>
        <h1 className={styles.detailTitle}>编号:{protocolDetail.appId}</h1>
        <div className={styles.detailModule}>
          <InfoTitle head="基本信息" />
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="操作类型"
              value={protocolDetail.operationTypeText || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="子类型"
              value={protocolDetail.subType || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="客户"
              value={custName}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="资金账户"
              value={fundAccount}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="信用账户"
              value={creditAccount}
            />
          </div>
          <IfWrap when={currentView === SUBTYPE.heightSpeed}>
            <div className={styles.line}>
              <InfoItem
                className={styles.detailInfoItem}
                label="申请单编号"
                value={protocolDetail.appId || EMPTY_PARAM}
              />
            </div>
          </IfWrap>
          <IfWrap when={!!protocolDetail.agreementNum}>
            <div className={styles.line}>
              <InfoItem
                className={styles.detailInfoItem}
                label="协议编号"
                value={protocolDetail.agreementNum || EMPTY_PARAM}
              />
            </div>
          </IfWrap>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议模板"
              value={protocolDetail.templateId || EMPTY_PARAM}
            />
          </div>
          <IfWrap when={isShowTenLevelAndMultiAccount}>
            <div className={styles.line}>
              <InfoItem
                className={styles.detailInfoItem}
                label="是否多账户使用"
                value={mapBoolData[protocolDetail.multiUsedFlag]}
              />
            </div>
            <div className={styles.line}>
              <InfoItem
                className={styles.detailInfoItem}
                label="是否订购十档行情"
                value={mapBoolData[protocolDetail.levelTenFlag]}
              />
            </div>
          </IfWrap>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议开始日期"
              value={time.format(protocolDetail.startDt) || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议有效期"
              value={time.format(protocolDetail.vailDt) || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="备注"
              value={protocolDetail.content || EMPTY_PARAM}
            />
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="拟稿信息" />
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="拟稿人"
              value={`${protocolDetail.divisionName || EMPTY_PARAM} ${protocolDetail.createdName || EMPTY_PARAM}`}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="提请时间"
              value={protocolDetail.createdDt}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="状态"
              value={statusLabel || EMPTY_PARAM}
            />
          </div>
        </div>
        <div className={styles.tableModule}>
          <InfoTitle head="协议产品" />
          <div className={styles.line}>
            <CommonTable
              dataSource={protocolDetail.item || []}
              columns={protocolProductTitleList}
              scroll={scroll}
              pagination={false}
              useNewUI
            />
          </div>
        </div>
        <div className={styles.tableModule}>
          <InfoTitle head="协议条款" />
          <div className={styles.line}>
            <CommonTable
              dataSource={protocolDetail.term || EMPTY_ARRAY}
              columns={protocolClauseTitleList}
              pagination={false}
              useNewUI
            />
          </div>
        </div>
        <IfWrap when={showUnderCust}>
          <div className={styles.tableModule}>
            <InfoTitle head="下挂客户" />
            <div className={styles.line}>
              <CommonTable
                dataSource={protocolDetail.cust}
                columns={underCustTitleList}
                pagination={false}
                useNewUI
              />
            </div>
          </div>
        </IfWrap>
        <div className={styles.mutiUploadModule}>
          <InfoTitle head="附件信息" />
          <div className={styles.line}>
            {
              _.map(attachmentList, (item) => (
                <Uploader
                  disabled
                  showTitle
                  removeable={false}
                  title={item.title}
                  defaultFileList={item.attachmentList}
                  key={`${protocolDetail.id}${item.title}`}
                />
              ))
            }
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="审批记录" />
          <div className={styles.line}>
            <ApproveList data={flowHistory} nowStep={nowStep} />
          </div>
        </div>
      </div>
    );
  }
}
