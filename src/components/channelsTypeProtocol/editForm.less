@import '../permission/publicconfig.less';
@import '../common/dropdownSelect/style';

.editComponent {
  width: 100%;
  height: 100%;
  margin: 0;
  overflow: auto;
}

.editWrapper {
  position: relative;
  margin: 20px 0;
  .customerTable {
    margin: 10px 0 0;
  }
  .mt10 {
    margin-top: 10px;
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-select-selection {
      height: 30px;
    }
  }
}

.dcHeader {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  border: none;
  border-bottom: 1px solid #e9e9e9;
}

.dcHaderNumb {
  float: left;
  font-size: 14px;
  color: #333;
}

.cutSpace {
  height: 20px;
}

.addClauseButton {
  position: absolute;
  right: 0;
  top: -10px;
}

.checkApprover {
  display: inline-block;
  width: 200px;
  height: 32px;
  border: 1px solid #e9e9e9;
  cursor: pointer;
  line-height: 32px;
  padding-left: 8px;
  border-radius: 4px;
  &:hover {
    border-color: #49a9ee;
  }
  .searchIcon {
    font-size: 14px;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    float: right;
  }
}

.transferWrapper {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-table-wrapper .ant-table .ant-table-thead > tr > th {
	  max-width: none;
	}
  }
}
