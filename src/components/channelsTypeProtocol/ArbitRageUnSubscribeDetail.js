/*
 * @Description: 套利软件-协议退订详情
 * @Author: <PERSON><PERSON><PERSON><PERSON>hu-K0180193
 * @Date: 2020-09-17 16:05:20
 * @Last Modified by: <PERSON><PERSON>ianShu-K0180193
 * @Last Modified time: 2020-09-17 16:37:36
 */

import React, { PureComponent } from 'react';
import { autobind } from 'core-decorators';
import PropTypes from 'prop-types';
import InfoTitle from '@/components/common/InfoTitle';
import InfoItem from '@/components/common/infoItem';
import FlowHistory from '@/components/common/flowHistory';
import { time } from '@/helper';
import { seibelConfig } from '../../config';
import styles from './detail.less';
import {
  EMPTY_PARAM,
} from './config';

// 合约条款的表头、状态对应值
const { contract: { status } } = seibelConfig;
export default class ArbitRageUnSubscribeDetail extends PureComponent {
  static propTypes = {
    protocolDetail: PropTypes.object.isRequired,
    agreementHistoryFlow: PropTypes.object.isRequired,
  }

  // 拼接开通权限的显示文本
  @autobind
  joinPermissionText(textArray) {
    if (!Array.isArray(textArray)) return '';
    return textArray.map((item) => item.value).join('，');
  }

  // 将拟稿人信息中的状态切换成中文
  @autobind
  convertDraftStatus(draftStatus) {
    let statusLabel = '';
    if (draftStatus) {
      statusLabel = status[Number(draftStatus)].label;
    }
    return statusLabel;
  }

  render() {
    const {
      protocolDetail,
      agreementHistoryFlow,
    } = this.props;
    // 客户姓名，
    // 由于后台接口之前contactName和accountName同时存在，
    // 有时候有有时候没有，
    // 所以采取两个字段同时判断，来显示客户名称以及经纪客户号
    // 都没有数据则显示暂无
    const custName = `${(protocolDetail.contactName || protocolDetail.accountName) || EMPTY_PARAM} ${protocolDetail.econNum || EMPTY_PARAM}`;
    let statusLabel = '';
    if (protocolDetail.status) {
      statusLabel = status[Number(protocolDetail.status)].label;
    } else {
      statusLabel = '';
    }
    return (
      <div className={styles.detailBox}>
        <h1 className={styles.detailTitle}>编号:{protocolDetail.appId}</h1>
        <div className={styles.detailModule}>
          <InfoTitle head="基本信息" />
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="操作类型"
              value={protocolDetail.operationTypeText || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="子类型"
              value={protocolDetail.subType || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="客户"
              value={custName}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="资金账户"
              value={protocolDetail?.fundAccount}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="信用账户"
              value={protocolDetail?.creditAccount}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议编号"
              value={protocolDetail.agreementNum || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议模板"
              value={protocolDetail.templateId || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议开始日期"
              value={time.format(protocolDetail.startDt) || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="协议有效期"
              value={time.format(protocolDetail.vailDt) || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="软件账号"
              value={protocolDetail.softAccount || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="备注"
              value={protocolDetail.content || EMPTY_PARAM}
            />
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="拟稿信息" />
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="拟稿人"
              value={`${protocolDetail.divisionName || EMPTY_PARAM} ${protocolDetail.createdName || EMPTY_PARAM}`}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="提请时间"
              value={protocolDetail.createdDt || EMPTY_PARAM}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="状态"
              value={statusLabel || EMPTY_PARAM}
            />
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="审批记录" />
          <div className={styles.line}>
            <FlowHistory data={agreementHistoryFlow} />
          </div>
        </div>
      </div>
    );
  }
}
