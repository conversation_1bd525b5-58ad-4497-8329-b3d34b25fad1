/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-02-27 19:17:32
 * @description 资产配置列表页面-搜索区域
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { Input, AutoComplete } from 'antd';
import duty, { DutyCode } from '@aorta/duty';
import {
  debounce, isEmpty, map, trim
} from 'lodash';

import HtFilter from '@/components/common/htFilter';
import DateRangePick from '@lego/date/src';
import TreeFilter from '@lego/treeSelect/src';
import SingleFilter from '@lego/filters/src';
import logable from '@/decorators/logable';
import withRouter from '@/decorators/withRouter';
import { MY_CREATED } from '@/routes/AssetConfigList/config';
import {
  DATE_FORMAT,
} from './config';

import styles from './searchArea.less';

const { Option } = AutoComplete;

@withRouter
export default class SearchArea extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    changeQuery: PropTypes.func.isRequired,
    // 客户列表
    queryAssetConfigCustList: PropTypes.func.isRequired,
    assetConfigCustList: PropTypes.array.isRequired,
    // 创建者
    queryConfigAssertCreatorList: PropTypes.func.isRequired,
    creatorList: PropTypes.array.isRequired,
    // 机构树
    listDataviewOrgTreeByEmpId: PropTypes.func.isRequired,
    orgInfo: PropTypes.array.isRequired,
    // 字典
    getAssetAllocationBizStateDict: PropTypes.func.isRequired,
    stateDictList: PropTypes.array.isRequired,
  };

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  };

  componentDidMount() {
    const {
      listDataviewOrgTreeByEmpId,
      getAssetAllocationBizStateDict,
    } = this.props;
    listDataviewOrgTreeByEmpId({
      type: '05', // 表示资产配置
    });
    getAssetAllocationBizStateDict();
  }

  @autobind
  handleSearchCust(keyword) {
    const {
      location: {
        query,
      },
      queryAssetConfigCustList,
    } = this.props;
    let orgIdList;
    try {
      orgIdList = !isEmpty(query?.orgIdList) ? JSON.parse(query?.orgIdList) : '';
    } catch (e) {
      console.log(e);
    }
    queryAssetConfigCustList({
      keyword: trim(keyword),
      ...query,
      orgIdList,
    });
  }

  @autobind
  @logable({
    type: 'Search',
    payload: {
      name: '资产配置-客户搜索',
      value: '$args[0]',
    }
  })
  handleSearchSelect(custId) {
    this.props.changeQuery({
      custId,
      pageNum: 1
    });
  }

  @autobind
  getAutoCompleteOptions() {
    const { assetConfigCustList } = this.props;
    if (isEmpty(assetConfigCustList)) {
      return ([
        <Option key="NONE_INFO" disabled>请搜索更多结果</Option>
      ]);
    }
    return map(assetConfigCustList, ({ custName, custId }) => (
      <Option key={custId}>
        {`${custName}(${custId})`}
      </Option>
    ));
  }

  @autobind
  handleSearchBlur(keyword) {
    // 当搜索框为空时，需要重置搜索
    if (isEmpty(keyword)) {
      this.handleSearchSelect(keyword);
    }
  }

  @autobind
  handleStatusChange({ value }) {
    this.props.changeQuery({
      status: value,
      pageNum: 1
    });
  }

  @autobind
  handleCreatorSearch(keyword) {
    const {
      location: {
        query,
      },
      queryConfigAssertCreatorList,
    } = this.props;
    let orgIdList;
    try {
      orgIdList = !isEmpty(query?.orgIdList) ? JSON.parse(query?.orgIdList) : '';
    } catch (e) {
      console.log(e);
    }
    queryConfigAssertCreatorList({
      keyword,
      ...query,
      orgIdList,
    });
  }

  @autobind
  @logable({
    type: 'Search',
    payload: {
      name: '资产配置-创建者',
      value: '$args[0].creatorId',
    }
  })
  handleCreatorSelect({ value }) {
    this.props.changeQuery({
      ...value,
      pageNum: 1
    });
  }

  // 获取部门
  @autobind
  transformCustRangeData(list) {
    return map(list, (item) => {
      const obj = {
        label: item.orgName,
        value: item.orgCode,
        key: item.orgCode,
      };
      if (item.children && item.children.length) {
        obj.children = this.transformCustRangeData(item.children);
      }
      return obj;
    });
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '资产配置-部门',
      value: '$args[0]',
    },
  })
  handleSelectDepartment(value) {
    this.props.changeQuery({
      orgIdList: !isEmpty(value) ? JSON.stringify(value) : '',
      pageNum: 1
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '资产配置-创建时间',
      value: '$args[0].value',
    },
  })
  handleSelectCreateTime({ value }) {
    const [beginTime, endTime] = value;
    this.props.changeQuery({
      beginTime,
      endTime,
      pageNum: 1
    });
  }

  @autobind
  getProcessStatus() {
    const {
      stateDictList,
    } = this.props;
    return [{ key: '', value: '全部' }, ...stateDictList];
  }

  render() {
    const {
      location: {
        query: {
          creatorId = '',
          creatorName = '不限',
          beginTime,
          endTime,
          status = '',
          assetListActiveTab,
        }
      },
      creatorList,
      orgInfo
    } = this.props;
    const creator = {
      creatorId,
      creatorName,
    };

    const hasDuty = duty.has(DutyCode.HTSC_ZCPZCX);

    const processStatus = this.getProcessStatus();

    return (
      <div className={styles.searchArea}>
        <div className={`${styles.custFilter} ${styles.filterItem}`}>
          <AutoComplete
            className={styles.custFilter}
            dataSource={this.getAutoCompleteOptions()}
            onSearch={debounce(this.handleSearchCust, 500)}
            onSelect={this.handleSearchSelect}
            defaultActiveFirstOption={false}
            onChange={this.handleSearchBlur}
            allowClear
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
          >
            <Input.Search placeholder="仅支持搜索已发起配置流程客户" />
          </AutoComplete>
        </div>
        <div className={`${styles.filterItem}`}>
          <HtFilter
            filterName="流程状态"
            filterId="status"
            type="single"
            data={processStatus}
            value={status}
            onChange={this.handleStatusChange}
          />
        </div>
        <div className={`${styles.filterItem}`}>
          <SingleFilter
            menuContainer="body"
            dropdownStyle={{ width: 270 }}
            type="singleSearch"
            value={creator}
            defaultLabel="不限"
            filterName="创建者"
            placeholder="仅支持搜索已发起流程的创建者"
            data={creatorList}
            dataMap={['creatorId', 'creatorName']}
            showSearch
            needItemObj
            useLabelInValue
            disabled={!hasDuty && assetListActiveTab === MY_CREATED}
            onChange={this.handleCreatorSelect}
            onInputChange={debounce(this.handleCreatorSearch, 300)}
          />
        </div>
        <div className={`${styles.filterItem}`}>
          <TreeFilter
            multiple
            treeCheckable
            key="id"
            filterId="id"
            filterName="部门"
            searchPlaceholder="搜索"
            defaultLabel="全部"
            treeData={this.transformCustRangeData(orgInfo)}
            onChange={this.handleSelectDepartment}
            showSearch
            treeDefaultExpandAll
          />
        </div>
        <div className={`${styles.filterItem}`}>
          <DateRangePick
            allowClear
            type="date"
            filterName="创建时间"
            filterId="createTime"
            menuContainer="body"
            onChange={this.handleSelectCreateTime}
            filterValue={[beginTime, endTime]}
            stateDateWrapper={(date) => date.format(DATE_FORMAT)}
          />
        </div>
      </div>
    );
  }
}
