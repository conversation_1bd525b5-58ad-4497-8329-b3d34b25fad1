export const TABLE_COLUMN = [
  {
    key: 'custId',
    dataIndex: 'custId',
    title: '经纪客户号',
    width: 100,
  },
  {
    key: 'id',
    dataIndex: 'id',
    title: '资产配置id',
    width: 90,
  },
  {
    key: 'custName',
    dataIndex: 'custName',
    title: '客户姓名',
    width: 100,
  },
  {
    key: 'riskLevel',
    dataIndex: 'riskLevel',
    title: '风险等级',
    width: 90,
  },
  {
    key: 'configScene',
    dataIndex: 'configScene',
    title: '配置场景',
    width: 80,
  },
  {
    key: 'statusText',
    dataIndex: 'statusText',
    title: '流程状态',
    width: 100,
  },
  {
    key: 'createTime',
    dataIndex: 'createTime',
    title: '创建时间',
    width: 80,
  },
  {
    key: 'creatorId',
    dataIndex: 'creatorId',
    title: '创建人',
    width: 90,
  },
  {
    key: 'orgName',
    dataIndex: 'orgName',
    title: '部门',
    width: 130,
  },
  {
    key: 'operate',
    dataIndex: 'operate',
    title: '操作',
    width: 90,
  },
];

// 无数据
export const NODATA = '--';

// 列间距20
export const SPACE_20 = {
  width: 20,
};

export const CONFING = '1';
export const DONE = '2';

// 新建
export const XIN_JIAN = '1';

// 流程异常
export const LIU_CHENG_YI_CHANG = '3';

// 办结
export const BAN_JIE = '8';

// 终止
export const ZHONG_ZHI = '9';

// 报告生成中
export const BAO_GAO_SHENG_CHENG_ZHONG = '10';

// 报告生成失败
export const BAO_GAO_SHENG_CHENG_SHI_BAI = '11';

export const CANNOT_EDIT = [ZHONG_ZHI, BAO_GAO_SHENG_CHENG_ZHONG, BAO_GAO_SHENG_CHENG_SHI_BAI];

export const PROGRESS_STATUS = [
  {
    key: '',
    value: '全部',
  },
  {
    key: CONFING,
    value: '配置中',
  },
  {
    key: DONE,
    value: '方案生成',
  },
];

export const DATE_FORMAT = 'YYYY-MM-DD';

export const TABLE_PLACE_HOLDER = {
  style: { height: '500px' },
};

// 方案生成的状态节点
export const STATUS_DONE = '方案生成';
