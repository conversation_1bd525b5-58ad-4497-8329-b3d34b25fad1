.searchArea {
  display: flex;
  padding: 10px 0 11px;

  & > .filterItem {
    margin-right: 10px;
    height: 30px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .lego-date-rangePicker-container {
        position: static;
        display: inline-block;
      }

      .lego-disabled .lego-selection--single,
      .lego-disabled .lego-selection__choice__remove {
        background-color: #f5f5f5;
        border: 1px solid #d9d9d9;
      }

      .lego-selection__rendered {
        height: 30px;
        line-height: 30px;

        .lego-selection-selected-filterName {
          font-size: 14px;
        }

        .lego-selection-selected-value {
          font-size: 14px;
        }
      }

      .lego-filter-filterWrapper .ant-btn[disabled],
      .lego-filter-filterWrapper .ant-btn[disabled]:hover {
        background-color: #f5f5f5;
        color: #ccc !important;
      }
    }
  }

  & > .custFilter {
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-input {
        width: 250px;
        height: 30px;
      }

      .ant-select-dropdown-menu-item-disabled {
        color: #ccc;
      }
    }
  }

  /** 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
}

