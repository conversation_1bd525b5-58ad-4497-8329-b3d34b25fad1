.tableArea {
  margin-top: 14px;

  .activetyName {
    width: 210px;
  }

  .operateBtn {
    cursor: pointer;
    color: #108ee9;

    span {
      padding-right: 10px;
      cursor: pointer;
    }

    .disbaled {
      color: #ccc;
      cursor: default;
    }
  }

  .custId {
    color: #108ee9;

    a {
      text-decoration: none;
    }
  }

  .pageArea {
    display: flex;
    justify-content: flex-end;
  }

  .noData {
    color: #ccc;
  }
}

.title {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  line-height: 20px;
}

.content {
  font-size: 14px;
  color: #666;
  line-height: 20px;
  max-width: 230px;
  word-break: break-all;
}

.signTip {
  .title {
    font-weight: bold;
  }
}

.icon {
  float: left;
  width: 18px;
  height: 18px;
}
