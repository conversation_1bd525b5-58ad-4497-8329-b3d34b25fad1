/* eslint-disable no-console */
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-18 15:13:20
 * @Last Modified by: yeyixin
 * @Last Modified time: 2023-05-12 09:38:26
 * @description 资产配置列表页面-表格区域
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { connect } from 'dva';
import { Popconfirm, message } from 'antd';
import { sensors } from '@lego/bigbox-utils';
import { emp } from '@aorta-pc/utils';
import _ from 'lodash';
import { routerRedux } from 'dva/router';
import Table, { ToolTipCell } from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import {
  STEP_SHOWREPORT,
  STEP_TEMPORARY,
} from '@/routes/assetConfigAdd/config';
import { isCanJumpToCust360Detial } from '@/components/productOperation/utils';
import { url as urlHelper } from '@/helper';
import { getHost } from '@/utils/host';
import warnSrc from '@/routes/assetConfigAdd/images/warn.svg';
import {
  TABLE_COLUMN,
  NODATA,
  SPACE_20,
  TABLE_PLACE_HOLDER,
  XIN_JIAN,
  LIU_CHENG_YI_CHANG,
  BAN_JIE,
  BAO_GAO_SHENG_CHENG_SHI_BAI,
  STATUS_DONE,
} from './config';
import { SHI_CHANGE_GUAN_DIAN, ZHANG_HU_FEN_XI } from '../assetConfigAdd/config';
import signSrc from './images/sign.svg';

import styles from './tableArea.less';

const { logCommon } = sensors;

const mapStateToProps = (state) => ({
});
const mapDispatchToProps = {
  push: routerRedux.push,
};
@connect(mapStateToProps, mapDispatchToProps)
export default class TableArea extends PureComponent {
  static propTypes = {
    // 列表数据
    data: PropTypes.object.isRequired,
    // 删除
    deleteAssetConfigData: PropTypes.func.isRequired,
    // 获取数据
    getList: PropTypes.func.isRequired,
    // 修改location中的query
    changeQuery: PropTypes.func.isRequired,
    //
    getCustMess: PropTypes.func.isRequired,
    // 下载
    checkAssetAllocationDownloadLimit: PropTypes.func.isRequired,
    updateReduxData: PropTypes.func.isRequired,
    // 重新生成pdf
    retryGenerateFinalPdfReport: PropTypes.func.isRequired,
    // 立即触发
    doSign: PropTypes.func.isRequired,
  };

  static contextTypes = {
    push: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      // pdf是否有灰度
      isPdfGrey: false,
    };
  }

  async componentDidMount() {
    // 调用获取灰度字段的接口
    const res = await getHost().dispatch({
      type: 'global/queryIsCanary',
      payload: {
        loading: false,
        key: 'assetconfigpdf',
      },
    });
    if (res) {
      this.setInitCanary(res);
    }
  }

  @autobind
  setInitCanary(res) {
    this.setState({
      isPdfGrey: res
    });
  }

  @autobind
  renderColumns() {
    const newColumns = _.map(TABLE_COLUMN, (column) => {
      const { key } = column;
      if (key === 'custId') {
        return this.renderCustIdColumn(column);
      }
      if (key === 'creatorId') {
        return {
          ...column,
          render: (text, record) => (text
            ? (
              <ToolTipCell tipContent={`${record?.creatorName}(${text})`} cellText={`${record?.creatorName}(${text})`} />
            )
            : (
              this.renderNoData()
            )),
        };
      }
      if (key === 'operate') {
        return this.renderOperate(column);
      }
      return {
        ...column,
        render: (text, record) => this.renderCell(text, record),
      };
    });
    return newColumns;
  }

  @autobind
  renderCell(text, record, clickable = false) {
    if (record.flag) {
      return null;
    }
    if (text === '' || _.isNull(text)) {
      return this.renderNoData();
    }
    return <ToolTipCell tipContent={text} cellText={text} />;
  }

  @autobind
  renderNoData() {
    return <span className={styles.noData}>{NODATA}</span>;
  }

  @autobind
  renderCustIdColumn(column) {
    return {
      ...column,
      render: (text) => {
        if (text === '' || _.isNull(text)) {
          return this.renderNoData();
        }
        const isCanJumpTo360 = isCanJumpToCust360Detial(emp.getId());
        const linkHash = `/customerCenter/detail?custId=${text}`;
        const linkHref = urlHelper.getNewBrowserTabUrl(linkHash);
        const node = isCanJumpTo360 ? <a href={linkHref} target="_blank" rel="noopener noreferrer">{text}</a> : text;
        return (
          <div className={styles.custId}>
            {node}
          </div>
        );
      },
    };
  }

  @autobind
  getTipTitle(ifSignAgreement) {
    if (!ifSignAgreement) {
      return (
        <div className={styles.signTip}>
          <div className={styles.title}>协议签署</div>
          <div className={styles.content}>客户尚未签署投顾服务协议，通知客户前往涨乐端签署协议后，您可在聊TA下载配置报告。</div>
        </div>
      );
    }
    return (
      <div>
        <div className={styles.title}>提示！</div>
        <div className={styles.content}>报告有效期(30日)内支持下载5次，超有效期仅支持查看</div>
      </div>
    );
  }

  @autobind
  renderIcon(ifSignAgreement) {
    return (
      <img src={ifSignAgreement ? warnSrc : signSrc} className={styles.icon} alt="图标" />
    );
  }

  @autobind
  renderOperate(column) {
    return {
      ...column,
      render: (text, record) => {
        // 当前用户为创建者
        const isCreator = emp.getId() === record?.creatorId;
        // 创建人在新建或者新建时的流程异常（没有flowId）才可以编辑
        const canEdit = isCreator
          && (record?.statusCode === XIN_JIAN
            || (record?.statusCode === LIU_CHENG_YI_CHANG
              && _.isEmpty(record?.flowId)));
        const canUpload = isCreator
          && record?.statusCode === BAN_JIE
          && record?.finalReportLeftDownloadCount > 0;
        const regenerate = isCreator && record?.statusCode === BAO_GAO_SHENG_CHENG_SHI_BAI;
        return (
          <div className={styles.operateBtn}>
            <IFWrap when={!canEdit}>
              <span
                onClick={() => {
                  this.handleView(record);
                }}
              >
                查看
              </span>
            </IFWrap>
            <IFWrap when={canEdit}>
              <span
                onClick={() => {
                  this.handleEdit(record);
                }}
              >
                编辑
              </span>
              <Popconfirm
                placement="topRight"
                title="确认删除么？"
                onConfirm={() => {
                  this.handleDelete(record);
                }}
                okText="是"
                cancelText="否"
              >
                <span>删除</span>
              </Popconfirm>
            </IFWrap>
            <IFWrap when={canUpload && this.state.isPdfGrey}>
              <Popconfirm
                placement="topRight"
                title={this.getTipTitle(record?.ifSignAgreement)}
                icon={this.renderIcon(record?.ifSignAgreement)}
                onConfirm={_.throttle(
                  () => {
                    this.handleDownload(record);
                  },
                  500
                )}
                okText={record?.ifSignAgreement ? '确认' : '立即触发'}
                cancelText="取消"
              >
                <span>
                  {`下载(${record?.finalReportLeftDownloadCount})`}
                </span>
              </Popconfirm>
            </IFWrap>
            <IFWrap when={regenerate}>
              <span
                onClick={() => { this.handleRegenerate(record); }}
              >
                重新生成
              </span>
            </IFWrap>
          </div>
        );
      },
    };
  }

  @autobind
  handleEdit(record) {
    // 编辑 路由跳转
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-编辑',
        value: record?.code,
      },
    });
    this.context.push({
      pathname: '/assetConfig/add',
      query: {
        id: record?.id,
        current: 0,
        stepCode: STEP_TEMPORARY,
      },
    });
  }

  @autobind
  handleView(record) {
    // 查看 路由跳转
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-预览',
        value: record?.code,
      },
    });
    const { getCustMess } = this.props;
    const custId = record?.custId ?? '';
    getCustMess({
      custId,
    }).then((res) => {
      try {
        const analysisPeriodRangData = JSON.parse(record?.analysisPeriod);
        // 因新老资产配置有不同的流程，所以有不同的状态节点
        // 老的资产配置流程，没有 pdf 可以预览，所以不需要显示在线预览 pdf
        // 所以老的资产配置流程，需要进入到老的查看页面
        // 所以加了判断条件：状态为 方案生成 的资产配置，进入到老的资产配置查看页面
        // 其他条件则进入到新的页面
        if (record?.statusText === STATUS_DONE) {
          this.context.push({
            pathname: '/assetConfig/add',
            query: {
              id: record?.id,
              current: 4,
              custId,
              analysisPeriodRang: analysisPeriodRangData?.rang,
              analysisPeriodDesc: analysisPeriodRangData?.desc,
              stepCode: STEP_SHOWREPORT,
              showFenxi: !_.isEmpty(record?.reportViewData?.[ZHANG_HU_FEN_XI]),
              showGuandian: !_.isEmpty(record?.reportViewData?.[SHI_CHANGE_GUAN_DIAN]),
            },
          });
          return;
        }
        this.context.push({
          pathname: '/assetConfig/approval',
          query: {
            custId,
            id: record?.id,
            current: 4,
            processInstanceId: record?.flowId,
            analysisPeriodRang: analysisPeriodRangData?.rang,
            analysisPeriodDesc: analysisPeriodRangData?.desc,
            stepCode: STEP_SHOWREPORT,
            showFenxi: !_.isEmpty(record?.reportViewData?.[ZHANG_HU_FEN_XI]),
            showGuandian: !_.isEmpty(record?.reportViewData?.[SHI_CHANGE_GUAN_DIAN]),
            isView: true,
          },
        });
      } catch (error) {
        console.log(error);
      }
    });
  }

  @autobind
  handleDelete(record) {
    // 删除操作
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-删除',
        value: record?.id,
      },
    });
    this.props.deleteAssetConfigData({ id: record?.id }).then((res) => {
      if (res) {
        this.props.getList();
      }
    });
  }

  @autobind
  handleDownload(record) {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-下载',
        value: record?.id,
      },
    });
    const {
      data,
      data: {
        list,
      },
      checkAssetAllocationDownloadLimit,
      updateReduxData,
      doSign,
    } = this.props;
    // 未签约
    if (!record?.ifSignAgreement) {
      // 5天内再次点击【立即触发】则不推送涨乐
      if (!record?.overFiveDay) {
        message.warning('5日内仅能触发一次签约，可引导客户涨乐签署');
        return;
      }
      doSign({
        assetAllocationId: record?.id,
        custId: record?.custId,
      }).then((res) => {
        if (res) {
          this.props.getList();
          message.success('签约已触发，5日内仅能触发一次签约');
        }
      });
      return;
    }
    checkAssetAllocationDownloadLimit({ assetAllocationId: record?.id })
      .then(({ canDownloadNow, leftDownloadCount }) => {
      // 下载成功后手动更新列表数据
        if (canDownloadNow && record?.finalReportUrl) {
          const url = `/fspa/mcrm/api/storage/download?fileId=${record?.finalReportUrl}`;
          window.open(url, '_self');
          updateReduxData({
            assetConfigData: {
              ...data,
              list: _.map(list, (item) => {
                if (item.id === record.id) {
                  return {
                    ...item,
                    finalReportLeftDownloadCount: leftDownloadCount
                  };
                }
                return item;
              })
            }
          });
        }
      });
  }

  @autobind
  handleRegenerate(record) {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-重新生成',
        value: record?.id,
      },
    });
    this.props.retryGenerateFinalPdfReport({ assetAllocationId: record?.id }).then((res) => {
      if (res) {
        this.props.getList();
      }
    });
  }

  @autobind
  handlePageChange(pageNum) {
    this.props.changeQuery({
      pageNum: String(pageNum),
    });
  }

  render() {
    const {
      data: { list = [], page = {} },
    } = this.props;
    const { pageNum, totalCount } = page;
    // 渲染表格
    const columns = this.renderColumns();
    return (
      <div className={styles.tableArea}>
        <Table
          useNewUI
          pagination={false}
          columns={columns}
          dataSource={list}
          rowKey={(record) => record?.code}
          spaceColumnProps={SPACE_20}
          placeHolderImageProps={TABLE_PLACE_HOLDER}
        />
        <IFWrap when={!_.isEmpty(list)}>
          <div className={styles.pageArea}>
            <Pagination
              pageSize={20}
              current={pageNum || 1}
              total={totalCount || 0}
              showTotal={(total) => `共${total}条`}
              onChange={this.handlePageChange}
            />
          </div>
        </IFWrap>
      </div>
    );
  }
}
