.rankChart {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;

     /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    & .echartTooltipTable {
      color: #777;
      .tooltipItem {
        & > div {
          display: inline-block;
          width: 100%;
          height: 100%;
          text-align: justify;
          text-justify: distribute-all-lines;
          text-align-last: justify;
          -moz-text-align-last: justify;
          -webkit-text-align-last: justify;
        }
      }
      .itemValue {
        & > span {
          margin-left: 8px;
          margin-right: 8px;
          color: #000;
          font-size: 16px;
        }
      }
    }
    & .echartTooltip {
      display: inline-block;
      width: 10px;
      height: 10px;
      margin-right: 4px;
      border-radius: 100%;
    }
    & .echartTooltipHD { color: #4f596a; }
  }
}
.ranking { flex: 0 0 auto; }
.rankingchart {
  width: 100%;
  flex-grow: 1;
  flex-shrink: 1;
}
.rankNumberAndChange {
  height: 35px;
  line-height: 35px;
}
.rankNumber {
  display: inline-block;
  min-width: 32px;
  margin-right: 8px;
  font-size: 16px;
  color: #333;
}
.rankChangeIcon {
  font-size: 12px;
  margin-right: 8px;
}
.rankUp {
  color: #ff4403;
  composes: rankChangeIcon;
}
.rankDown {
  color: #24d2af;
  composes: rankChangeIcon;
}
.rankHold {
  color: #357bf0;
  composes: rankChangeIcon;
}
.rankChange {
  font-size: 12px;
  color: #a1a1a1;
}
