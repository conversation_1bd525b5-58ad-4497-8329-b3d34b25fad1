import React, { Component } from 'react';
import echarts from 'echarts';
import PropTypes from 'prop-types';
import { createCanvas, registerFont } from 'canvas'; // eslint-disable-line
// import myshFont from '../../../static/font/msyh/MSYH.ttf';

const defaultConfig = {
  enableAutoDispose: true,
  animation: false,
};

const SSR_PAGE_WIDTH = 1222;

export default class EChartsSSR extends Component {
  constructor(props) {
    super(props);
    echarts.setCanvasCreator(createCanvas);
  }

  static propTypes = {
    option: PropTypes.object.isRequired,
    width: PropTypes.number,
    height: PropTypes.number,
  }

  static defaultProps = {
    width: SSR_PAGE_WIDTH,
    height: 500,
  }

  render() {
    echarts.setCanvasCreator(createCanvas);
    // registerFont(`./${myshFont}`, { family: 'Microsoft YaHei' });
    const { option, width, height } = this.props;
    const config = {
      ...defaultConfig,
      option,
    };
    const canvas = createCanvas(parseInt(width, 10), parseInt(height, 10));
    const ctx = canvas.getContext('2d');
    ctx.font = '12px "Microsoft YaHei"';
    const chart = echarts.init(canvas, null);

    chart.setOption(config.option);

    return (
      <img src={`${canvas.toDataURL()}`} alt="charts" />
    );
  }
}
