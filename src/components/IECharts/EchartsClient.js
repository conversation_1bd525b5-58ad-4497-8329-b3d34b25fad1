/**
 * @fileOverview IECharts/wrapper.js
 * <AUTHOR>
 * @description copy别人的然后进行修改
 */

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import ECharts from 'echarts';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import Resize from 'element-resize-detector';

const DEFAULT_OPTION = {};

const DEFAULT_SERIES_OPTION = {
  line: {
    symbol: 'none',
  },
};

// 处理一下option，部分选项需要项目统一的默认值
const formatOption = (option) => {
  const finalOption = _.merge({}, DEFAULT_OPTION, option);
  let series = option.series;
  if (!_.isArray(series)) {
    series = [series];
  }
  series = _.map(
    series,
    (item) => {
      const defaultOptions = DEFAULT_SERIES_OPTION[item.type];
      return {
        ...defaultOptions,
        ...item,
      };
    },
  );
  return {
    ...finalOption,
    series,
  };
};

export default class IECharts extends Component {
  static propTypes = {
    className: PropTypes.string,
    style: PropTypes.object,
    theme: PropTypes.string,
    group: PropTypes.string,
    option: PropTypes.object.isRequired,
    initOpts: PropTypes.object,
    notMerge: PropTypes.bool,
    lazyUpdate: PropTypes.bool,
    loading: PropTypes.bool,
    optsLoading: PropTypes.object,
    onReady: PropTypes.func,
    resizable: PropTypes.bool,
    onEvents: PropTypes.object,
    onDispatch: PropTypes.object,
  }

  static defaultProps = {
    className: 'react-echarts',
    style: {
      width: '100%',
      height: '100%',
    },
    notMerge: false,
    lazyUpdate: false,
    onReady: () => { },
    loading: false,
    resizable: false,
    onEvents: {},
    onDispatch: {},
    initOpts: {},
    optsLoading: {},
    group: '',
    theme: '',
  }

  instance = null;

  fnResize = null;

  resize = null;

  componentDidMount() {
    this.init();
    this.update();
  }

  componentWillReceiveProps(nextProps) {
    if (this.instance && (this.props.loading !== nextProps.loading)) {
      if (nextProps.loading) {
        this.instance.showLoading('default', this.props.optsLoading);
      } else {
        this.instance.hideLoading();
      }
    }
  }

  shouldComponentUpdate(nextProps) {
    return (!this.instance
      || !_.isEqual(nextProps.option, this.props.option)
      || (nextProps.group !== this.props.group)
    );
  }

  componentDidUpdate() {
    if (this.props.option) {
      this.update();
      this.myresize();
    }
  }

  componentWillUnmount() {
    if (this.resize && this.resize.uninstall) {
      // const dom = ReactDOM.findDOMNode(this);
      const dom = this.chartBox;
      this.resize.uninstall(dom);
    }
    if (this.fnResize && this.fnResize.cancel) {
      this.fnResize.cancel();
    }
    if (this.instance) {
      this.instance.dispose();
    }
  }

  @autobind
  init() {
    const {
      theme,
      initOpts,
      loading,
      group,
      resizable,
      onReady,
      optsLoading
    } = this.props;
    if (!this.instance) {
      const dom = this.chartBox;
      // const dom = ReactDOM.findDOMNode(that);
      // fix getInstanceByDom is not a function error
      // let instance = ECharts.getInstanceByDom(dom);
      let instance = null;
      if (typeof ECharts.getInstanceByDom === 'function') {
        instance = ECharts.getInstanceByDom(dom);
      }
      if (!instance) {
        instance = ECharts.init(dom, theme, initOpts);
      }
      if (loading) {
        instance.showLoading('default', optsLoading);
      } else {
        instance.hideLoading();
      }
      instance.group = group;
      this.mybind(instance);
      if (!this.fnResize) {
        this.fnResize = _.debounce(this.myresize, 250, {
          leading: true,
          trailing: true,
        });
      }
      if (resizable && !this.resize) {
        this.resize = Resize({
          strategy: 'scroll',
        });
        this.resize.listenTo(dom, this.fnResize);
      }
      onReady(instance);
      this.instance = instance;
    }
  }

  @autobind
  update() {
    const { option, notMerge, lazyUpdate } = this.props;
    this.instance.setOption(
      formatOption(option),
      notMerge,
      lazyUpdate
    );
  }

  @autobind
  myresize() {
    if (this.instance) {
      this.instance.resize();
    }
  }

  /**
   * 提供当前echarts实例给外部
   */
  @autobind
  getChartsInstance() {
    return this.instance;
  }

  @autobind
  mygetInstance() {
    return ECharts.getInstanceByDom(this.chartBox);
    // return ECharts.getInstanceByDom(ReactDOM.findDOMNode(that));
  }

  @autobind
  mybind(instance) {
    const { onEvents, onDispatch } = this.props;
    const on = (name, func) => {
      if (typeof func === 'function') {
        const newfunc = func.bind(instance);
        instance.off(name, newfunc);
        instance.on(name, newfunc);
      }
    };
    const dispatchAction = (payload) => {
      instance.dispatchAction(payload);
    };
    const events = Object.keys(onEvents);
    for (let i = 0; i < events.length; i++) {
      if (Array.hasOwnProperty.call(onEvents, events[i])) {
        on(events[i].toLowerCase(), onEvents[events[i]]);
      }
    }

    dispatchAction(onDispatch);
  }

  render() {
    const { className, style } = this.props;

    return (
      <div
        className={className}
        style={style}
        ref={(input) => { this.chartBox = input; }}
      />
    );
  }
}
