/*
 * @Author: yanfaping
 * @Date: 2023-07-18 15:50:52
 * @LastEditors: yanfaping
 * @LastEditTime: 2023-08-07 16:33:48
 * @Description: 单佣金-账户最低收费设置佣金Select列表
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { Radio } from 'antd';
import { InfoCell } from '@crm/biz-ui';
import IFWrap from '@/components/common/IFWrap';
import SingleSelect from './SingleCommissionSelect';
import {
  NORMAL_KEY,
  CREDIT_KEY,
  ONLINE_MINIMUMCHAREG_KEYS,
  SELECT,
  NO_SELECT,
  MULTI_MINIMUM_CHARGE_RADIO,
  MULTI_CREDIT_KEYS,
  SHOW_RADIO_CUSTS
} from './config';

import { getMinimumChargeList } from './utils';

import styles from './singleMiniChargeSelectGroup.less';

const RadioGroup = Radio.Group;

export default class SingleMiniChargeSelectGroup extends PureComponent {
  static propTypes = {
    // 是否可见
    visible: PropTypes.bool.isRequired,
    // 当前多档最低收费佣金费率选项
    currentMiniRadios: PropTypes.array,
    // 切换下拉框选项值
    onChange: PropTypes.func,
    // 初始值
    baseCommission: PropTypes.object,
    // 客户信息
    custInfo: PropTypes.object,
    // 是否是线上调佣
    isOnline: PropTypes.bool,
    // 切换是否设置多档最低收费
    onRadioChange: PropTypes.func,
    // 是否设置多档最低收费
    radioKey: PropTypes.string,
    // 是否有灰度权限
    hasGrey: PropTypes.bool.isRequired,
    // 是否置灰不可点
    allDisabled: PropTypes.bool,
  };

  constructor(props) {
    super(props);
    this.wrapRef = React.createRef();
  }

  static defaultProps = {
    currentMiniRadios: [],
    onChange: _.noop,
    baseCommission: {},
    custInfo: {},
    isOnline: false,
    onRadioChange: _.noop,
    radioKey: '',
    allDisabled: false,
  };

  @autobind
  getWrapRef() {
    return this.wrapRef?.current;
  }

  @autobind
  isDisabledSelect(item) {
    const {
      custInfo: {
        openRzrq,
        minimumCharge,
        hasCreditAccount,
      },
      isOnline,
    } = this.props;
    // 获取佣金率的默认值disabled值
    let disabled = false;

    if (item.code === CREDIT_KEY) {
      // 单佣金调整-信用账户收取最低费用
      // openRzrq与后端确定：是否开通两融账户 和 是否开通信用账户
      // 多档二期新增hasCreditAccount表示是否开通信用账户，openRzrq使用规则保持之前不变
      // 1. 【线上】调佣，需要置灰
      // 2. 满足收取最低费用规则且客户是否开通两融账户 和 是否开通信用账户，否则置灰
      disabled = isOnline || !(minimumCharge === 'Y' && openRzrq === 'Y' && hasCreditAccount);
    } else if (item.code === NORMAL_KEY) {
      // 单佣金调整-普通账户收取最低费用
      // 1. 【线上】调佣，需要置灰
      // 2. 满足收取最低费用规则，否则置灰
      disabled = isOnline || minimumCharge === 'N';
      // 多档信用佣金，信用账户股票最低收费、信用账户上海债券最低收费
      // 1、客户是否开通两融账户 和 是否开通信用账户，未开通置灰
      // 多档二期新增hasCreditAccount表示是否开通信用账户，openRzrq使用规则保持之前不变
    } else if (_.includes(MULTI_CREDIT_KEYS, item.code)) {
      disabled = openRzrq === 'N' || !hasCreditAccount;
    }

    return disabled;
  }

  // 是否展示右侧的提示以及提示内容
  @autobind
  judgeTipInfo(item) {
    const {
      isOnline,
      custInfo: {
        hasCreditAccount,
      },
      hasGrey,
    } = this.props;
    let isShowTip = false;
    let tipContent = '';

    if (isOnline && _.includes(ONLINE_MINIMUMCHAREG_KEYS, item.code)) {
      isShowTip = true;
      tipContent = '暂不支持线上确认，若有调整需求调佣方式请选线下';
    }

    if (hasGrey && !hasCreditAccount && _.includes(MULTI_CREDIT_KEYS, item.code)) {
      isShowTip = true;
      tipContent = '信用资金账户开户成功后可设置佣金';
    }

    return {
      isShowTip,
      tipContent,
    };
  }

  @autobind
  makeSelect(item) {
    const {
      options,
      code,
      codeType = '',
      paramName
    } = item;
    const { custInfo, baseCommission, allDisabled } = this.props;

    const disabled = allDisabled || this.isDisabledSelect(item);
    // 右侧提示
    const { isShowTip, tipContent } = this.judgeTipInfo(item);

    return (
      <SingleSelect
        custInfo={custInfo}
        value={baseCommission[paramName] || ''}
        disabled={disabled}
        key={code}
        label={codeType}
        name={paramName}
        options={options}
        onChange={this.props.onChange}
        getPopupContainer={this.getWrapRef}
        isShowTip={isShowTip}
        tipContent={tipContent}
        className={styles.selectItem}
        showSearch
        optionFilterProp="title"
      />
    );
  }

  @autobind
  showRadioGroup() {
    const { hasGrey, custInfo } = this.props;
    // 有灰度权限-判断客户类型是否为机构户、产品户，是则展示“是否定制化设置最低收费”，否则不展示
    if (hasGrey) {
      return _.includes(SHOW_RADIO_CUSTS, custInfo?.custType);
    }

    // 无灰度权限-不展示“是否设置多档最低收费”
    return false;
  }

  @autobind
  setRadioDisabled() {
    const { isOnline, allDisabled } = this.props;
    // 1. 【线上】调佣，需要置灰
    // 2. 是否满足收取最低费用规则，不满足置灰
    return isOnline || allDisabled;
  }

  render() {
    const {
      visible,
      radioKey,
      onRadioChange,
      currentMiniRadios,
    } = this.props;

    if (!visible) {
      return null;
    }

    const selectList = getMinimumChargeList(currentMiniRadios, MULTI_MINIMUM_CHARGE_RADIO);
    // 是否展示设置多档最低收费radio
    const showRadio = this.showRadioGroup();

    return (
      <div className={styles.miniChargeSelectBox} ref={this.wrapRef}>
        <IFWrap when={showRadio}>
          <div className={styles.radioBox}>
            <InfoCell
              span={100}
              label="是否定制化设置最低收费"
              labelWidth="170px"
              content={(
                <RadioGroup
                  value={radioKey}
                  size="normal"
                  className={styles.radioGroup}
                  onChange={onRadioChange}
                  disabled={this.setRadioDisabled()}
                >
                  <Radio key={SELECT} value={SELECT}>是</Radio>
                  <Radio key={NO_SELECT} value={NO_SELECT}>否</Radio>
                </RadioGroup>
            )}
            />
          </div>
        </IFWrap>
        <div className={styles.selectBox}>
          {
            _.map(selectList, (item) => this.makeSelect(item))
          }
        </div>
      </div>
    );
  }
}
