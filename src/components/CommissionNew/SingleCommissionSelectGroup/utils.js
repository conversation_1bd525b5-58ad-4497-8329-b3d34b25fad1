import _ from 'lodash';

// 根据佣金参数条件配置中设置的营业部与客户类型，找到选项需要校验的指标阈值
// 获取到指标比较的基准值，该值在佣金配置管理页面设置的指标的值
// 优先级判断: 1 > 2 > 3 > 4
// 1. 配置的客户类型和营业部都与所选客户完全一致的
// 2. 配置条件中 custType 值为空（表示全部类型）&& 配置的营业部与所选客户一致
// 3. 未配置营业部，配置的客户类型与所选客户的客户类型一致的
// 4. 未配置营业部，客户类型是全部的
function getThresholdData(conditionList, customer) {
  // 当前所选客户的客户类型和营业部
  const { custTypeForValid, openOrgId } = customer;
  // 首先从所有配置中过滤出客户类型一致或者客户类型为全部的所有数据
  const typeMatchData = _.filter(conditionList, (item) => (item.custType === custTypeForValid)
    || (item.custType === ''));
  // 优先级1：客户类型和营业部都匹配的数据
  const orgAndTypeAllSameData = _.find(typeMatchData, (item) => (item.openOrgId === openOrgId)
    && (item.custType === custTypeForValid));
  // 优先级2：客户类型为全部，营业部一致的数据
  const departSameData = _.find(typeMatchData, (item) => (item.custType === '')
    && (item.openOrgId === openOrgId));
  // 优先级3：营业部未配置，客户类型一致的数据
  const custTypeSameData = _.find(typeMatchData, (item) => (item.custType === custTypeForValid)
    && _.isEmpty(item.openOrgId));
  // 优先级4：营业部未配置，客户类型为全部的数据
  const allCustTypeData = _.find(typeMatchData, (item) => (item.custType === '')
    && _.isEmpty(item.openOrgId));
  // 按优先级判断返回数据
  if (!_.isEmpty(orgAndTypeAllSameData)) {
    return orgAndTypeAllSameData;
  }
  if (!_.isEmpty(departSameData)) {
    return departSameData;
  }
  if (!_.isEmpty(custTypeSameData)) {
    return custTypeSameData;
  }
  if (!_.isEmpty(allCustTypeData)) {
    return allCustTypeData;
  }
  return {};
}

export function isPassThreshold(option, customer) {
  if (_.isString(option.value) && _.isEmpty(option.value)) {
    return true;
  }

  // 针对“佣金配置管理”可对客户性质下的佣金率进行资产信息校验配置，所以这里取对应客户性质下的资产信息进行校验
  // 在取之前需判断该字段是否为空，如果为空则不进行资产校验
  if (_.isEmpty(option?.conditionList)) {
    return true;
  }
  // 根据佣金参数条件配置中设置的营业部与客户类型，找到选项需要校验的指标阈值
  const thresholdData = getThresholdData(option?.conditionList, customer);
  // 首先判断需要需要对哪些阈值进行判断
  const thresholdKeys = ['totalAsset', 'yearStockExchange', 'yearStockProfit'];
  const thresholdObject = _.reduce(thresholdData, (result, optionValue, key) => {
    if (_.includes(thresholdKeys, key) && !_.isNil(thresholdData[key])) {
      const item = result;
      item[key] = optionValue;
      return item;
    }
    return result;
  }, {});
  // 判断如果没有需要判断的阈值对象（即三个阈值指标都是null），则该选项可选
  if (_.isEmpty(thresholdObject)) {
    return true;
  }
  // 判断阈值与客户指标的关系,只要有一个指标满足就可选该选项
  const canSelect = _.some(thresholdObject, (optionValue, key) => {
    const custValue = customer[key];
    return custValue >= optionValue;
  });
  return canSelect;
}

export function checkPassThreshold(option, customer) {
  const res = { isPass: false, msg: '' };
  if (_.isString(option.value) && _.isEmpty(option.value)) {
    res.isPass = true;
    return res;
  }

  // 针对“佣金配置管理”可对客户性质下的佣金率进行资产信息校验配置，所以这里取对应客户性质下的资产信息进行校验
  // 在取之前需判断该字段是否为空，如果为空则不进行资产校验
  if (_.isEmpty(option?.conditionList)) {
    res.isPass = true;
    return res;
  }
  // 根据佣金参数条件配置中设置的营业部与客户类型，找到选项需要校验的指标阈值
  const thresholdData = getThresholdData(option?.conditionList, customer);
  // 首先判断需要需要对哪些阈值进行判断
  const thresholdKeys = ['totalAsset', 'yearStockExchange', 'yearStockProfit'];
  const thresholdObject = _.reduce(thresholdData, (result, optionValue, key) => {
    if (_.includes(thresholdKeys, key) && !_.isNil(thresholdData[key])) {
      const item = result;
      item[key] = optionValue;
      return item;
    }
    return result;
  }, {});
  // 判断如果没有需要判断的阈值对象（即三个阈值指标都是null），则该选项可选
  if (_.isEmpty(thresholdObject)) {
    res.isPass = true;
    return res;
  }
  // 判断阈值与客户指标的关系,只要有一个指标满足就可选该选项
  const canSelect = _.some(thresholdObject, (optionValue, key) => {
    const custValue = customer[key];
    return custValue >= optionValue;
  });

  res.isPass = canSelect;

  // 拼接错误消息
  // 总资产：xx万元，近一年股基交易量：xx万元，近一年股基交易毛佣金：xx万元
  if (!canSelect) {
    _.forEach(thresholdObject, (value, key) => {
      let valueStr;
      if (value) {
        // valueStr = number.formatToUnit({
        //   num: value,
        //   floatLength: 2,
        //   unit: '元',
        //   isThousandFormat: false,
        //   isRound: false,
        // });

        valueStr = `${Number(value) / 10000}万元`;
      }
      if (value && key === 'totalAsset') {
        res.msg += `总资产：${valueStr}`;
      }
      if (value && key === 'yearStockExchange') {
        res.msg += ` , 近一年股基交易量：${valueStr}`;
      }
      if (value && key === 'yearStockProfit') {
        res.msg += ` , 近一年股基交易毛佣金：${valueStr}`;
      }
    });
  }

  return res;
}

// 因为后端返回了所有类型的其他佣金费率，但是不同类型佣金调整对应的其他佣金费率不太一样，前端过滤出配置中的类型
export function getOtherRatiosList(list, configList) {
  const configRatiosList = _.filter(list, (item) => !_.isEmpty(configList[item?.code])) || [];
  return configRatiosList;
}

// 给options的每一项添加label和value
function formatOptions(options) {
  if (_.isEmpty(options)) {
    return [];
  }
  return _.map(options, (item) => ({
    ...item,
    label: item?.codeDesc,
    value: item?.codeValue,
    show: true,
  }));
}

// 账户最低收费设置佣金项的集合-数据转换
// 后端接口拿到的佣金项，添加对应的佣金入参字段名，在驳回修改时做回填使用
// 由于后端返回的佣金类型的下拉选项格式为{ codeDesc："", codeValue："""}，再对佣金类型的options数组转换为Select中label和value的渲染格式
export function getMinimumChargeList(list, configList) {
  const configRatiosList = _.map(list, (item) => {
    const options = formatOptions(item?.options);
    const value = configList[item?.code];
    return {
      ...item,
      paramName: !_.isEmpty(value) ? value?.paramName : '',
      options
    };
  }) || [];
  return configRatiosList;
}

// 获取场内基金必填字段
export function getOnExchangeFundRequiredFields(data, customer) {
  const CODE = 5;
  const commissions = {
    oCommission: customer?.hasNormalAccount && data?.oCommission !== CODE,
    doCommission: customer?.hasCreditAccount && data?.doCommission !== CODE,
    coCommission: customer?.hasCreditAccount && data?.coCommission !== CODE,
  };
  return commissions;
}
