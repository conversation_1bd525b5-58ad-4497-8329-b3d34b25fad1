.lineInputWrap {
  width: 100%;
  margin: 10px 0;

  &:after {
    content: " ";
    display: table;
    clear: both;
  }

  .label {
    float: left;
    width: 190px;
    font-size: 14px;
    color: #9b9b9b;
    text-align: right;
    margin-right: -190px;
    display: flex;
    max-height: 40px;
    justify-content: right;
    height: auto;

    .text {
      max-height: 40px;
      width: calc(100% - 22px);
      text-align: right;
    }
    .required {
      margin-right: 4px;
      color: red;
      font-style: normal;
    }
    .colon {
      padding: 0 4px;
    }
  }
  .componentBox {
    margin-left: 190px;
    float: left;
    font-size: 14px;
    color: #333;
    display: flex;
    &.inputBox {
      input {
        width: 228px;
        height: 32px;
      }
    }
    &.selectBox {
      :global(.ant-select) {
        width: 228px;
        height: 32px;
        border-radius: 2px;

        .ant-select-selection {
          border-radius: 2px;
        }
      }
    }
    &.textAreaBox {
      textarea {
        width: 417px;
        height: 74px;
        resize: none;
      }
    }
  }

  .tipIcon {
    margin: 4px 0 0 10px;
    cursor: pointer;

    .icon {
      color: #108ee9;
    }
  }
}
