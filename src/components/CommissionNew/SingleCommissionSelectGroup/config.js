// 单佣金调整-其它佣金率-普通账户收取最低费用
export const NORMAL_KEY = 'HTSC_NORMAL_RATIO';
// 单佣金调整-其它佣金率-信用账户收取最低费用
export const CREDIT_KEY = 'HTSC_CREDIT_RATIO';
// 单佣金调整-其它佣金率-担保股基
export const STK_COMMISSION_KEY = 'HTSC_DBFARE_RATIO';
// 单佣金调整-其它佣金率-信用股基
export const CREDIT_COMMISSION_KEY = 'HTSC_CBFARE_RATIO';
// 单佣金调整-其它佣金率-B股
export const B_GU_COMMISSION_KEY = 'HTSC_BGFARE_RATIO';
// 单佣金调整-其它佣金率-担保权证
export const HTSC_DQFARE_RATIO_KEY = 'HTSC_DQFARE_RATIO';
// 单佣金调整-其它佣金率-权证
export const HTSC_QFARE_RATIO_KEY = 'HTSC_QFARE_RATIO';

// 所有的其他佣金费率名称以及接口字段的配置项
export const ALL_OTHER_RATES_CONFIG = {
  HTSC_ZFARE_RATIO: {
    brief: '债券',
    paramName: 'zqCommission',
    order: 1,
  },

  HTSC_DBFARE_RATIO: {
    brief: '担保股基',
    paramName: 'stkCommission',
    order: 6,
  },

  HTSC_CBFARE_RATIO: {
    brief: '信用股基',
    paramName: 'creditCommission',
    order: 15,
  },

  HTSC_DDFARE_RATIO: {
    brief: '担保品大宗交易',
    paramName: 'ddCommission',
    order: 8,
  },

  HTSC_HFARE_RATIO: {
    brief: '回购',
    paramName: 'hCommission',
    order: 11,
  },

  HTSC_DZFARE_RATIO: {
    brief: '担保债券',
    paramName: 'dzCommission',
    order: 2,
  },

  HTSC_COFARE_RATIO: {
    brief: '信用场内基金',
    paramName: 'coCommission',
    order: 12,
  },

  HTSC_STBFARE_RATIO: {
    brief: '股转',
    paramName: 'stbCommission',
    order: 13,
  },

  HTSC_OFARE_RATIO: {
    brief: '场内基金',
    paramName: 'oCommission',
    order: 9,
  },

  HTSC_DOFARE_RATIO: {
    brief: '担保场内基金',
    paramName: 'doCommission',
    order: 10,
  },

  HTSC_HKFARE_RATIO: {
    brief: '港股通（净佣金）',
    paramName: 'hkCommission',
    order: 14,
  },

  HTSC_BGFARE_RATIO: {
    brief: 'B股',
    paramName: 'bgCommission',
    order: 5,
  },

  HTSC_QFARE_RATIO: {
    brief: '权证',
    paramName: 'qCommission',
    order: 3,
  },

  HTSC_DQFARE_RATIO: {
    brief: '担保权证',
    paramName: 'dqCommission',
    order: 4,
  },

  // 单佣金调整不需要个股期权配置，先注释
  // HTSC_OPTFARE_RATIO: {
  //   brief: '个股期权',
  //   paramName: 'opCommission',
  //   order: 16,
  // },

  HTSC_DFARE_RATIO: {
    brief: '大宗交易',
    paramName: 'dCommission',
    order: 7,
  },

  HTSC_CZFARE_RATIO: {
    brief: '信用债券',
    paramName: 'creditBondsCommission',
    order: 16,
  },
};

// 线上调佣--需要禁用并且右侧需要提示的其他佣金费率类型(B股、担保权证、权证)
export const ONLINE_DISABLE_TYPES = [
  HTSC_QFARE_RATIO_KEY,
  HTSC_DQFARE_RATIO_KEY,
  B_GU_COMMISSION_KEY,
];

// 线上调佣-【信用账户收取最低费用】/【普通账户收取最低费用】禁用并右侧提示内容
export const ONLINE_MINIMUMCHAREG_KEYS = [
  NORMAL_KEY,
  CREDIT_KEY,
];

// 未开通信用账户 or 未开通客户融资融券-需要禁用的其他佣金费率类型集合
export const UN_OPEN_CREDIT_OR_RZRQ_KEYS = [
  // 担保债券
  'HTSC_DZFARE_RATIO',
  // 担保权证
  'HTSC_DQFARE_RATIO',
  // 担保股基
  'HTSC_DBFARE_RATIO',
  // 担保品大宗交易
  'HTSC_DDFARE_RATIO',
  // 担保场内基金
  'HTSC_DOFARE_RATIO',
  // 信用场内基金
  'HTSC_COFARE_RATIO',
  // 信用股基
  'HTSC_CBFARE_RATIO',
  // 信用债券
  'HTSC_CZFARE_RATIO',
];

// 多档最低收费选择【是】佣金项集合
export const MULTI_MINIMUM_CHARGE_RADIO_SELECT = {
  gpMinimumCommission: {
    brief: '普通账户股票',
    paramName: 'gpMinimumCommission',
  },

  creditgpMinimumCommission: {
    brief: '信用账户股票',
    paramName: 'creditgpMinimumCommission',
  },

  onExchangeFundCommission: {
    brief: '普通账户场内基金',
    paramName: 'onExchangeFundCommission',
  },

  creditOnExchangeFundCommission: {
    brief: '信用账户场内基金',
    paramName: 'creditOnExchangeFundCommission',
  },

  shNonConvertibleBondsCommission: {
    brief: '普通账户上海债券（不含可转债）',
    paramName: 'shNonConvertibleBondsCommission',
  },

  shCreditNonConvertibleBondsCommission: {
    brief: '信用账户上海债券（不含可转债）',
    paramName: 'shCreditNonConvertibleBondsCommission',
  },

  shConvertibleBondsCommission: {
    brief: '普通账户上海可转债',
    paramName: 'shConvertibleBondsCommission',
  },

  shCreditConvertibleBondsCommission: {
    brief: '信用账户上海可转债',
    paramName: 'shCreditConvertibleBondsCommission',
  },

  szNonConvertibleBondsCommission: {
    brief: '普通账户深圳债券（不含可转债）',
    paramName: 'szNonConvertibleBondsCommission',
  },

  szCreditNonConvertibleBondsCommission: {
    brief: '信用账户深圳债券（不含可转债）',
    paramName: 'szCreditNonConvertibleBondsCommission',
  },

  szConvertibleBondsCommission: {
    brief: '普通账户深圳可转债',
    paramName: 'szConvertibleBondsCommission',
  },

  szCreditConvertibleBondsCommission: {
    brief: '信用账户深圳可转债',
    paramName: 'szCreditConvertibleBondsCommission',
  },
};

// 多档最低收费佣金项集合
export const MULTI_MINIMUM_CHARGE_RADIO = {
  ...MULTI_MINIMUM_CHARGE_RADIO_SELECT,

  HTSC_NORMAL_RATIO: {
    brief: '普通账户收取最低费用',
    paramName: 'normalMinimumCharge',
  },

  HTSC_CREDIT_RATIO: {
    brief: '信用账户收取最低费用',
    paramName: 'creditMinimumCharge',
  },
};

// 信用账户股票最低收费
export const CREDIT_GP_KEY = 'creditgpMinimumCommission';
// 信用账户上海债券最低收费
export const CREDIT_ZQ_KEY = 'creditzqMinimumCommission';

// 多档最低收费-信用账户佣金项key值配置
export const MULTI_CREDIT_KEYS = [
  CREDIT_GP_KEY,
  CREDIT_ZQ_KEY,
  CREDIT_KEY,
  // 信用账户股票最低收费
  'creditgpMinimumCommission',
  // 信用账户收取最低费用
  'creditMinimumCharge',
  // 信用账户场内基金
  'creditOnExchangeFundCommission',
  // 信用账户上海债券（不含可转债）
  'shCreditNonConvertibleBondsCommission',
  // 信用账户上海可转债
  'shCreditConvertibleBondsCommission',
  // 信用账户深圳债券（不含可转债）
  'szCreditNonConvertibleBondsCommission',
  // 信用账户深圳可转债（不含可转债）
  'szCreditConvertibleBondsCommission',
];

// 是否设置多档最低收费-是
export const SELECT = 'Y';
// 是否设置多档最低收费-否
export const NO_SELECT = 'N';

// 需要展示是否设置多档最低收费佣金的客户类型集合
export const SHOW_RADIO_CUSTS = ['org', 'prod', 'per'];

// 账户收取最低费用-佣金入参字段名称集合
export const ALL_MINIMUMCHAREG_PARAMS = [
  // 普通账户股票最低收费
  'gpMinimumCommission',
  // 信用账户股票最低收费
  'creditgpMinimumCommission',
  // 普通账户收取最低费用
  'normalMinimumCharge',
  // 信用账户收取最低费用
  'creditMinimumCharge',
  // 普通账户场内基金
  'onExchangeFundCommission',
  // 信用账户场内基金
  'creditOnExchangeFundCommission',
  // 普通账户上海债券（不含可转债）
  'shNonConvertibleBondsCommission',
  // 信用账户上海债券（不含可转债）
  'shCreditNonConvertibleBondsCommission',
  // 普通账户上海可转债
  'shConvertibleBondsCommission',
  // 信用账户上海可转债
  'shCreditConvertibleBondsCommission',
  // 普通账户深圳债券（不含可转债）
  'szNonConvertibleBondsCommission',
  // 信用账户深圳债券（不含可转债）
  'szCreditNonConvertibleBondsCommission',
  // 普通账户深圳可转债
  'szConvertibleBondsCommission',
  // 信用账户深圳可转债（不含可转债）
  'szCreditConvertibleBondsCommission',
];

// 佣金调整增加新投顾签约的校验-需要校验的佣金key值
export const NEED_INVEST_CONTRACT_CHECK_KEYS = [
  // 信用股基佣金
  'creditCommission',
  // 担保股基佣金
  'stkCommission'
];

// 信用账户佣金类型key值集合
export const CREDIT_ACCOUNT_COMMISON_KEYS = [
  // 担保债券
  'dzCommission',
  // 担保权证
  'dqCommission',
  // 担保股基
  'stkCommission',
  // 担保品大宗交易
  'ddCommission',
  // 担保场内基金
  'doCommission',
  // 信用场内基金
  'coCommission',
  // 信用股基
  'creditCommission',
  // 信用债券
  'creditBondsCommission',
];

// 全账户提佣校验佣金品种code值集合
export const VALIDATE_UNIFIED_COMMISION_CODES = [
  // 信用股基
  'HTSC_CBFARE_RATIO',
  // 担保股基
  'HTSC_DBFARE_RATIO',
  // 场内基金
  'HTSC_OFARE_RATIO',
  // 信用场内基金
  'HTSC_COFARE_RATIO',
  // 担保场内基金
  'HTSC_DOFARE_RATIO',
];
