.miniChargeSelectBox {
  position: relative;

  /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
    .ant-alert {
      padding: 10px 10px 11px 34px;
      color: #333;
      font-size: 14px;
      line-height: 19px;
      margin: 6px 0 10px 14px;
    }

    .ant-alert-icon {
      top: 13px;
    }

    .ant-bizInfoCell-label-text {
      width: calc(100% - 14px);
      text-align: right;
    }
  }

  .selectItem {
    width: 50%;
    margin: 0 0 10px;
  }
}

.selectBox {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.otherComsBox {
  width: 50%;
  float: left;
}

.blockTip {
  clear: both;
  font-size: 14px;
  padding: 5px 0;
  display: flex;

  .icon {
    color: #108ee9;
    margin-right: 8px;
  }

  .text {
    color: #999;

    p {
      margin: 0;
    }
  }

  &.hide {
    display: none;
  }
}
