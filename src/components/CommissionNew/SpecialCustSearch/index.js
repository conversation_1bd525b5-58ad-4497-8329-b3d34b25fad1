/**
 * @Author: yanfaping
 * @Date: 2021-02-04 14:49:33
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-06-27 15:59:57
 * @description 特殊佣金申请-搜索客户
 */
import React from 'react';
import PropTypes from 'prop-types';
import {
  InfoGroup,
  InfoCell,
} from '@crm/biz-ui';
import _ from 'lodash';
import { number } from '@/helper';
import SpecialCustSelect from './SpecialCustSelect';

const infoCellStyle = { paddingLeft: 0 };
const custNameStyle = {
  paddingLeft: 0,
  marginTop: '12px',
};
const valueStyle = {
  lineHeight: '30px',
};
const DEFAULT_VALUE = '--';

export default function SpecialCommissionSearchCust(props) {
  const {
    dataSource,
    onSearchValue,
    onSelectValue,
    onValidateCust,
    unfinishRoute,
    terminalOrderFlow,
    changeOrderStatus,
    customer,
  } = props;

  const getValue = (value) => (!_.isEmpty(value) ? value : DEFAULT_VALUE);

  const formatMoney = (value) => (_.isNumber(value) ? `${number.thousandFormat(value)}元` : DEFAULT_VALUE);

  const searchNode = (
    <SpecialCustSelect
      dataSource={dataSource}
      onSearchValue={onSearchValue}
      onSelectValue={onSelectValue}
      onValidateCust={onValidateCust}
      unfinishRoute={unfinishRoute}
      terminalOrderFlow={terminalOrderFlow}
      changeOrderStatus={changeOrderStatus}
    />
  );

  return (
    <div>
      {
         _.isEmpty(customer) ? (
           <InfoGroup labelWidth="154px">
             <InfoCell
               span={50}
               style={infoCellStyle}
               label="客户"
               content={searchNode}
               contentStyle={valueStyle}
             />
           </InfoGroup>
         ) : (
           <InfoGroup labelWidth="154px">
             <InfoCell
               span={50}
               style={infoCellStyle}
               label="客户"
               content={searchNode}
               contentStyle={valueStyle}
             />
             <InfoCell span={50} style={custNameStyle} label="客户名称" content={getValue(customer?.custName)} />
             <InfoCell span={50} style={infoCellStyle} label="风险等级" content={getValue(customer?.riskLevelText)} />
             <InfoCell span={50} style={infoCellStyle} label="总资产" content={formatMoney(customer?.totalAsset)} />
             <InfoCell span={50} style={infoCellStyle} label="近一年股基交易量" content={formatMoney(customer?.yearStockExchange)} />
             <InfoCell span={50} style={infoCellStyle} label="近一年股基交易毛佣金" content={formatMoney(customer?.yearStockProfit)} />
           </InfoGroup>
         )
       }
    </div>
  );
}
SpecialCommissionSearchCust.propTypes = {
  dataSource: PropTypes.array.isRequired,
  onSearchValue: PropTypes.func.isRequired,
  onSelectValue: PropTypes.func.isRequired,
  onValidateCust: PropTypes.func.isRequired,
  unfinishRoute: PropTypes.func.isRequired,
  terminalOrderFlow: PropTypes.func.isRequired,
  changeOrderStatus: PropTypes.func.isRequired,
  customer: PropTypes.object,
};

SpecialCommissionSearchCust.defaultProps = {
  customer: {},
};
