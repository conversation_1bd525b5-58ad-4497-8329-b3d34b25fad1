/*
 * @Author: yanfaping
 * @Date: 2025-02-06 14:36:20
 * @LastEditors: yanfaping
 * @LastEditTime: 2025-02-24 13:27:52
 * @Description: 产品经服引入-佣金设置入口进来-操作按钮
 */
import React from 'react';
import PropTypes from 'prop-types';
import { Select } from 'antd';
import styles from './index.less';

const Option = Select.Option;
function CommissionSetButton({ onCancel, onTerminate }) {
  return (
    <div className={styles.commissionSetButton}>
      <Select
        defaultValue="cancel"
        dropdownClassName={styles.commissionSetButtonSelect}
      >
        <Option key="cancel" value="cancel" onClick={onCancel}>取消</Option>
        <Option key="terminate" value="terminate" onClick={onTerminate}>终止</Option>
      </Select>
    </div>
  );
}

CommissionSetButton.propTypes = {
  // 点击取消触发事件
  onCancel: PropTypes.func.isRequired,
  // 点击终止触发事件
  onTerminate: PropTypes.func.isRequired,
};

export default CommissionSetButton;
