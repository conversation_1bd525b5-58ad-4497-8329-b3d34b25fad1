/*
 * @Author: yanfaping
 * @Date: 2022-07-26 15:07:28
 * @Last Modified by: yanfaping
 * @Last Modified time: 2022-10-18 16:38:45
 * @description 非标客户特殊佣金调整-客户搜索
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { AutoComplete } from 'antd';

import logable, { logCommon } from '@/decorators/logable';
import SimilarAutoComplete from '@/components/common/similarAutoComplete';
import confirm from '@/components/common/confirm';

import styles from './index.less';

const Option = AutoComplete.Option;

// 自动外呼调佣申请码值
const smartCode = '0205';

export default class UnStandardCustSelect extends PureComponent {
  static propTypes = {
    // 客户列表
    dataSource: PropTypes.array.isRequired,
    // 搜素事件
    onSearchValue: PropTypes.func.isRequired,
    // 下拉选择事件
    onSelectValue: PropTypes.func.isRequired,
    // 校验客户是否有在途订单
    onValidateCust: PropTypes.func,
    // 校验客户
    onValidateCustOtherInfo: PropTypes.func,
    width: PropTypes.number,
    // 校验客户是否在跟踪期内
    onCheckCustIsTrackingPeriod: PropTypes.func,
    // 校验客户如果有被驳回订单，终止订单
    terminalOrderFlow: PropTypes.func,
    // 校验客户如果有新建订单，修改订单状态
    changeOrderStatus: PropTypes.func,
    // 查询【佣金模式】的新投顾签约有哪些需要禁用的佣金率
    queryDisableContractRatioType: PropTypes.func.isRequired,
  }

  static defaultProps = {
    width: 300,
    onValidateCust: _.noop,
    onValidateCustOtherInfo: _.noop,
    terminalOrderFlow: _.noop,
    changeOrderStatus: _.noop,
    onCheckCustIsTrackingPeriod: _.noop,
  }

  constructor(props) {
    super(props);
    this.state = {
      // 标识 选中的客户，是否有被驳回订单
      isRejected: false,
      // 标识 选中的客户，是否有新建订单
      isNew: false,
      canSelected: false,
    };
  }

  @autobind
  clearCust() {
    this.setState({
      canSelected: false,
    });
    this.custSearch.clearValue();
  }

  @autobind
  custSearchRef(input) {
    this.custSearch = input;
  }

  @autobind
  handleOrder(selectItem, otherParams) {
    const { isRejected, isNew } = this.state;
    const params = {
      custEcom: selectItem.custId,
      ...otherParams
    };
    // 是否是fsp流程
    const isFspFlow = otherParams?.fspFlow;
    // 有被驳回订单，点击确定,如果是fsp流程则掉接口终止订单，否则就更改状态
    if (isRejected) {
      if (isFspFlow) {
        this.props.terminalOrderFlow(params);
      } else {
        this.props.changeOrderStatus(params);
      }
    }
    // 有新建订单，点击确定则掉接口修改“新建”状态为“已失败”，继续发起调佣流程
    if (isNew) {
      this.props.changeOrderStatus(params);
    }
    // 把选择的客户传到外层，提交时二次校验
    this.props.onSelectValue(selectItem);
  }

  @autobind
  handleOKAfterValidate(selectItem, otherParams, validResult) {
    if (this.state.canSelected) {
      // 可以选中
      const { openRzrq, minimumCharge, hasCreditAccount } = validResult;
      this.props.onSelectValue({
        ...selectItem,
        openRzrq,
        minimumCharge,
        disableContractRatioType: validResult?.disableContractRatioType ?? {},
        // hasTerminateCommissionContract: validResult?.hasTerminateCommissionContract
        hasCreditAccount,
      });
    } else {
      // 是否需要清空所选客户,如果是有在途订单（待审批，新建，驳回）,确认之后不清空所选客户
      if (otherParams) {
        this.handleOrder(selectItem, otherParams);
        return;
      }
      // 干掉客户
      this.clearCust();
    }
  }

  @autobind
  handleCancelAfterValidate() {
    this.clearCust();
  }

  // 校验不通过，弹框
  @autobind
  fail2Validate(obj) {
    const {
      shortCut,
      content,
      selectItem,
      params,
      validResult,
      cancelVisible = true,
    } = obj;
    confirm({
      shortCut,
      content,
      title: '提示',
      onOk: () => { this.handleOKAfterValidate(selectItem, params, validResult); },
      onCancel: this.handleCancelAfterValidate,
      cancelVisible,
    });
    this.setState({
      canSelected: false,
    });
  }

  @autobind
  isInTrackingPeriod(selectItem) {
    const { custId, custType } = selectItem || {};
    // 判断客户是否在跟踪期内
    return this.props.onCheckCustIsTrackingPeriod({
      custId,
      custType,
    });
  }

  // 客户校验
  @autobind
  async afterValidateSingleCust(selectItem, validResult) {
    if (_.isEmpty(validResult)) {
      confirm({ content: '客户校验失败' });
      return;
    }
    const {
      hasOrder, // 是否有在途订单
      orderStatus, // 订单状态
      fspFlow, // 是否是fsp流程
      orderId, // 订单id
      flowId, // 流程id
      flowStarter, // 流程发起者工号
      validmsg, // 校验信息
      subType, // 子类型
      hasTerminateCommissionContract, // 是否有在途投顾解约
      hasNormalAccount, // 是否开通普通账户
    } = validResult;
    const params = {
      fspFlow,
      orderId,
      flowId,
      flowStarter
    };
    this.setState({
      isRejected: false,
      isNew: false,
    });
    // 客户需完成普通资金账户开户后，方可设置佣金
    if (!hasNormalAccount) {
      this.fail2Validate({ content: '客户需完成普通资金账户开户后，方可设置佣金' });
      return;
    }
    // 投顾签约客户不允许调整佣金
    if (selectItem?.despiteSigning) {
      this.fail2Validate({ shortCut: 'isDespiteSigning', validResult });
      return;
    }
    // 判断是否有自动外呼调佣
    if (hasOrder === 'Y' && subType === smartCode) {
      this.fail2Validate({ shortCut: 'hasAutoDial' });
      return;
    }
    // 有待审批订单时
    if (hasOrder === 'Y' && orderStatus === 'hasCheck') {
      this.fail2Validate({
        content: validmsg, selectItem, validResult, cancelVisible: false
      });
      return;
    }
    // 有被驳回订单时
    if (hasOrder === 'Y' && orderStatus === 'hasRejected') {
      this.setState({ isRejected: true });
      this.fail2Validate({
        shortCut: 'rejected', selectItem, params, validResult
      });
      return;
    }
    // 有新建订单时
    if (hasOrder === 'Y' && orderStatus === 'hasNew') {
      this.setState({ isNew: true });
      this.fail2Validate({
        shortCut: 'new', selectItem, params, validResult
      });
      return;
    }
    // 当前客户是否有在途的投顾签约和线上签约时
    if (hasOrder === 'Y' && !_.isEmpty(validmsg)) {
      this.fail2Validate({ content: validmsg });
      return;
    }
    // 判断客户是否在跟踪期内
    const hasTrackingPeriod = await this.isInTrackingPeriod(selectItem);
    if (hasTrackingPeriod) {
      this.fail2Validate({ content: '当前客户在承诺期内，不允许再次发起申请' });
      return;
    }

    // 2023-12-26 同事吧问题优化——添加客户判断服务经理和开户营业部是否为空，为空则弹框提示，不可选
    if (_.isEmpty(selectItem?.empId) && _.isEmpty(selectItem?.openOrgId)) {
      this.fail2Validate({ content: '请于客户开户后T+1日发起客户调佣流程' });
      return;
    }
    // 2024-05-09有新的在途投顾解约则报错
    if (hasTerminateCommissionContract === 'Y') {
      this.fail2Validate({ content: '该客户有在途的投顾解约流程，请在解约流程办结后再发起申请。' });
      return;
    }
    this.setState({
      canSelected: true,
    });
    this.handleOKAfterValidate(selectItem, null, validResult);
  }

  // 选择某个客户
  @autobind
  handleSelectCust(cust) {
    if (_.isEmpty(cust)) {
      // 此时有可能客户搜索组件会传递一个空对象
      // 将空值传递出去以便可以让父组件更新其他组件数据
      // 删除客户
      // 向父组件传递一个空对象
      this.props.onSelectValue(null);
      return;
    }
    // 选中值了
    const { custId, custType } = cust;
    Promise.all([
      this.props.onValidateCust({
        custId,
        custType,
      }),
      this.props.onValidateCustOtherInfo({
        custId,
        custType,
      }),
      this.props.queryDisableContractRatioType({
        custId, // 客户的 custId
      }),
    ]).then((response) => {
      // 把返回值放在一个对象里
      const validResult = {
        ...response[0],
        ...response[1],
        ...response[2],
      };
      this.afterValidateSingleCust(cust, validResult);
    });
    // 记录选择的客户信息日志
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '选择客户',
        value: JSON.stringify(cust),
      },
    });
  }

  // 搜索客户列表
  @autobind
  @logable({ type: 'Click', payload: { name: '搜索客户', value: '$args[0]' } })
  handleSearchCustList(value) {
    this.props.onSearchValue(value);
  }

  @autobind
  renderOption(cust) {
    const { custName, custId, riskLevelText = '' } = cust;
    const text = `${custName}（${custId}） - ${riskLevelText}`;
    return (
      <Option key={custId} value={custId}>
        <span className={styles.prodValue} title={text}>{text}</span>
      </Option>
    );
  }

  render() {
    const { width, dataSource } = this.props;
    return (
      <div className={styles.selectWrap}>
        <SimilarAutoComplete
          ref={this.custSearchRef}
          placeholder="经纪客户号/客户名称"
          optionList={dataSource}
          style={{ width }}
          optionKey="custId"
          onSelect={this.handleSelectCust}
          onSearch={this.handleSearchCustList}
          renderOptionNode={this.renderOption}
        />
      </div>
    );
  }
}
