/*
 * @Author: yanfaping
 * @Date: 2025-02-06 14:36:20
 * @LastEditors: yanfaping
 * @LastEditTime: 2025-02-06 15:42:25
 * @Description: 佣金只读客户下拉框
 */
import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { Icon, Input } from 'antd';
import styles from './index.less';

function ReadOnlySelect({ selectedCust, optionsKey }) {
  const defaultValue = selectedCust?.custEcom
    ? `${selectedCust?.custName}（${selectedCust?.custEcom}） - ${selectedCust?.riskLevelLabel}` : null;
  const value = _.isEmpty(optionsKey) ? defaultValue : selectedCust[optionsKey];

  const isEmptyValue = _.isEmpty(_.trim(value));
  const iconType = isEmptyValue ? 'search' : 'clear';

  return (
    <Input
      value={value}
      className={styles.selectCustInput}
      placeholder="经纪客户号/客户名称"
      suffix={(
        <Icon
          type={iconType}
          className={styles.searchIcon}
        />
      )}
      disabled
    />
  );
}

ReadOnlySelect.propTypes = {
  // 所选客户信息
  selectedCust: PropTypes.object,
  // 客户信息中需要展示的字段key
  optionsKey: PropTypes.string,
};

ReadOnlySelect.defaultProps = {
  selectedCust: {},
  optionsKey: '',
};

export default ReadOnlySelect;
