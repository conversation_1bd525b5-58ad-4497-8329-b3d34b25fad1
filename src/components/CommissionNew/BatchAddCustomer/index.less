.tableDiv {
  padding-top: 10px;
  padding-left: 20px;
  position: relative;
  margin-top: 14px;
  .divPopover {
    background-color: #fff;
    background-clip: padding-box;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.35);
    font-size: 12px;
    font-variant: tabular-nums;
    line-height: 1.5;
    color: rgba(0, 0, 0, 0.65);
    box-sizing: border-box;
    margin: 0;
    padding: 4px 12px;
    list-style: none;
    position: absolute;
    top: -25px;
    left: 20px;
    z-index: 1030;
    cursor: auto;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    white-space: normal;
    font-weight: normal;
    text-align: left;
    animation: 0.2s;
    .arrow {
      background: #fff;
      width: 8.48528137px;
      height: 8.48528137px;
      position: absolute;
      left: 20px;
      bottom: -3px;
      border-color: transparent;
      border-style: solid;
      box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.35);
      transform: translateX(-50%) rotate(45deg);
    }
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .commonPage {
      margin-top: 20px;
    }
  }
}
.operationOfCustList {
  overflow: hidden;
  width: 100%;
}
.searchSelectArea {
  display: inline-block;
  width: 400px;
  font-size: 14px;
  padding-left: 16px;
  color: #999;
  & > button {
    margin-left: 20px;
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-select-dropdown-menu-item {
      padding-top: 0 !important;
    }
  }
}
.delectCustomerButton {
  float: right;
  height: 32px;
  & > button {
    margin-left: 20px;
  }
}

.confirmContent {
  h3 {
    font-size: 14px;
  }
  h4 {
    margin-top: 20px;
    font-size: 12px;
  }
  a {
    font-size: 12px;
  }
  i {
    color: #666;
    font-size: 14px !important;
    vertical-align: baseline !important;
    margin-right: 8px;
  }
}

.custInfoTip {
  display: inline-block;
  color: #367fdf;
  padding-left: 10px;
  font-size: 14px;
}

.blockTip {
  clear: both;
  font-size: 14px;
  padding: 5px 0;
  display: flex;
  margin-top: 6px;

  .icon {
    color: #108ee9;
    margin: -2px 8px 0 0;
  }

  .text {
    color: #999;
  }
}

.mutexError {
  .mutexErrorMsg {
    font-size: 14px;
  }

  .mutexErrorFile {
    margin-top: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
  }

  a {
    font-size: 12px;
  }

  i {
    color: #666;
    font-size: 14px !important;
    vertical-align: baseline !important;
    margin-right: 8px;
  }
}
