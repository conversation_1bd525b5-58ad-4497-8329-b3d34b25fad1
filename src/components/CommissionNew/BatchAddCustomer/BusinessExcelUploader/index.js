/*
 * @Description: 业务组件-前端解析 excel 并循环调用进度
 * @Author: LiuJianShu-K0180193
 * @Date: 2019-06-12 10:11:41
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-10-21 13:39:52
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import {
  Button,
  Modal,
  message,
} from 'antd';
import classnames from 'classnames';

import ExcelUploader from '@/components/common/excelUploader';
import IfWrap from '@/components/common/IFWrap';
import DownloadPopover from '@/components/common/DownloadPopover';
import logable, { logPV } from '@/decorators/logable';
import {
  emp,
  event,
} from '@/helper';
import styles from './index.less';

// 登陆人的组织 ID
const empOrgId = emp.getOrgId();
export default class BusinessExcelUploader extends PureComponent {
  static propTypes = {
    // 上传按钮的文字
    text: PropTypes.string,
    hasData: PropTypes.bool,
    hasDataTips: PropTypes.string,
    // 文件
    file: PropTypes.string,
    // 上传的条数限制
    limit: PropTypes.number,
    // 数据格式化
    format: PropTypes.arrayOf(PropTypes.array),
    // 发送的数据 key
    payloadKey: PropTypes.string,
    // 从第几条数据开始
    range: PropTypes.arrayOf(PropTypes.number),
    // 需要几张表
    sheetCount: PropTypes.number,
    // 校验接口
    validator: PropTypes.func.isRequired,
    validateData: PropTypes.string.isRequired,
    // 查询进度
    queryProgress: PropTypes.func.isRequired,
    progress: PropTypes.object.isRequired,
    // 回调
    callback: PropTypes.func.isRequired,
    // 清空数据
    clearData: PropTypes.func,
    // 接口需要的其他参数
    otherPayload: PropTypes.object,
    // 查询校验进度接口的其他参数
    otherProgressPayload: PropTypes.object,
    // 下载弹窗
    popoverVisible: PropTypes.bool,
    beforeUpload: PropTypes.func,
    getPopupContainer: PropTypes.func,
    // 错误文件下载提示是否需要关闭按钮，需要的话，可传入方法更新popoverVisible
    onPopClose: PropTypes.func,
    // 错误文件下载提示框位置
    placement: PropTypes.string,
    // 批量添加按钮是否禁用，有些入口需要符合条件才能添加，这里单独加个属性控制
    btnDisable: PropTypes.bool,
    // 是否展示校验弹框
    showValidateModal: PropTypes.bool,
    renderValidateModal: PropTypes.func,
  }

  static defaultProps = {
    text: '批量导入数据',
    hasData: false,
    hasDataTips: '导入后将清空客户列表已有数据，请确认！',
    file: '',
    limit: 500,
    format: [['customerId', 'customerName']],
    range: [4, 4],
    sheetCount: 1,
    clearData: _.noop,
    payloadKey: 'custIds',
    otherPayload: {},
    otherProgressPayload: {},
    popoverVisible: false,
    beforeUpload: _.noop,
    getPopupContainer: () => document.body,
    onPopClose: _.noop,
    placement: 'top',
    btnDisable: false,
    showValidateModal: false,
    renderValidateModal: _.noop,
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    const { popoverVisible: prevVisible } = prevState;
    const { popoverVisible: nextVisible } = nextProps;
    if (prevVisible !== nextVisible) {
      return {
        popoverVisible: nextVisible,
      };
    }
    return null;
  }

  constructor(props) {
    super(props);
    this.state = {
      text: props.text,
      // 上传按钮的禁用状态
      disabled: false,
      // 导入的弹窗
      importVisible: false,
      popoverVisible: false,
      btnType: '',
    };
  }

  // 导入数据
  @autobind
  @logPV({ pathname: '/modal/businessExcelUploader', title: '批量导入数据确认弹窗' })
  handleImportData() {
    const {
      beforeUpload,
    } = this.props;
    if (beforeUpload !== _.noop) {
      beforeUpload();
    }
    this.setState({
      importVisible: true,
    });
  }

  // 取消上传
  @autobind
  @logable({ type: 'ButtonClick', payload: { name: '取消上传' } })
  handleCancelImport() {
    this.setState({
      importVisible: false,
    });
  }

  @logable({ type: 'Click', payload: { name: '下载模板' } })
  handleDownloadClick() {}

  @autobind
  handleUpload(data, originData) {
    const {
      hasData,
      validator,
      clearData,
      limit,
      payloadKey,
      otherPayload,
    } = this.props;
    this.setState({
      importVisible: false,
    });
    if (_.isEmpty(data[0])) {
      message.error('导入的数据不能为空');
      return;
    }
    if (data[0].length > limit) {
      message.error(`导入的客户数量不能大于${limit}`);
      return;
    }
    validator({
      [payloadKey]: data[0],
      orgId: empOrgId,
      batchId: '',
      type: 'multi',
      ...otherPayload,
    }).then((res) => {
      if (hasData && clearData !== _.noop) {
        clearData();
      }
      const { showValidateModal, renderValidateModal } = this.props;
      if (showValidateModal && res?.hasMutexError) {
        renderValidateModal(res);
      } else {
        this.intervalQueryProgress();
      }
    });
  }

  @autobind
  intervalQueryProgress() {
    const {
      validateData,
      queryProgress,
      otherProgressPayload,
    } = this.props;
    return queryProgress({
      batchId: validateData,
      ...otherProgressPayload,
    }).then(() => {
      const {
        progress,
        text,
        callback,
      } = this.props;
      const {
        total,
        validate,
      } = progress;
      const percent = Number.parseInt((validate / total) * 100, 10);
      let showText = `上传${percent}%`;
      let btnType = 'ghost';
      let disabled = true;
      if (percent === 100) {
        showText = text;
        btnType = '';
        disabled = false;
      }
      this.setState({
        btnType,
        text: showText,
        disabled,
      }, () => {
        // 校验条数与总条数不相等则进行调用查询进度接口
        if (total !== validate) {
          return event.sleep(500).then(this.intervalQueryProgress);
        }
        // 全部校验后，调用回调函数
        callback(progress);
        return true;
      });
    });
  }

  render() {
    const {
      hasData,
      hasDataTips,
      file,
      range,
      sheetCount,
      format,
      progress,
      getPopupContainer,
      onPopClose,
      placement,
      btnDisable,
    } = this.props;
    const {
      text,
      disabled,
      importVisible,
      popoverVisible,
      btnType,
    } = this.state;
    const downloadUrl = _.get(progress, 'failExcelPath', '');
    // 是否展示关闭按钮
    const showCloseIcon = onPopClose !== _.noop;

    const btnClassName = classnames({
      [styles.downloadLink]: true,
      [styles[btnType]]: !_.isEmpty(btnType),
      [styles.disabledLink]: btnDisable,
    });

    const uploadElement = hasData
      ? (
        <a
          className={btnClassName}
          onClick={this.handleImportData}
          disabled={disabled || btnDisable}
        >
          {text}
        </a>
      )
      : (
        <ExcelUploader
          key="firstExcelUploader"
          range={range}
          sheetCount={sheetCount}
          onUpload={this.handleUpload}
          text={text}
          type={btnType}
          format={format}
          btnDisable={btnDisable}
          disabled={disabled || btnDisable}
        />
      );

    return (
      <div className={styles.wrap}>
        <DownloadPopover
          visible={popoverVisible}
          url={downloadUrl}
          getPopupContainer={getPopupContainer}
          autoAdjustOverflow={false}
          onPopClose={onPopClose}
          showCloseIcon={showCloseIcon}
          placement={placement}
        >
          {uploadElement}
        </DownloadPopover>
        <IfWrap when={!_.isEmpty(file)}>
          <a
            onClick={this.handleDownloadClick}
            href={file}
            className={styles.downloadLink}
          >
            下载导入模板
          </a>
        </IfWrap>

        <Modal
          visible={importVisible}
          title="提示"
          centered
          onCancel={this.handleCancelImport}
          footer={[
            <Button className={styles.cancelBtn} key="back" onClick={this.handleCancelImport}>
              取消
            </Button>,
            <ExcelUploader
              key="secondExcelUploader"
              type="primary"
              range={range}
              sheetCount={sheetCount}
              onUpload={this.handleUpload}
              format={format}
              text="确定"
            />,
          ]}
        >
          <p>{hasDataTips}</p>
        </Modal>
      </div>
    );
  }
}
