@import '../../../../css/variable.less';
.wrap {
  display: inline-block;
  .downloadLink {
    display: inline-block;
    box-sizing: border-box;
    width: 110px;
    height: 30px;
    line-height: 28px;
    background: #fff;
    color: #333;
    border: 1px solid #ddd;
    text-align: center;
    border-radius: 4px;
    margin-left: 20px;
    &:hover, &:active {
      border-color: #108ee9;
    }
  }

  .disabledLink {
    color: #fff;
    background: #c2c2c2;
    cursor: not-allowed;
    pointer-events: auto;

    &:hover {
      border-color: transparent;
    }
  }

  .primary {
    color: @white;
    border-color: @primary-color;
    background: @primary-color;
    &:hover,
    &:focus {
      color: @white;
      background: @primary-color-rgba;
    }
    &:active {
      color: @white;
      background: @primary-active-color;
      opacity: 1;
    }
  }
  .danger {
    background: @danger-color;
    color: @white;
    border-color: @danger-color;
    &:hover,
    &:focus {
      color: @white;
      background: rgba(208, 2, 27, .8);
      border-color: @danger-color;
    }
    &:active {
      color: @white;
      background: @danger-active-color;
      border-color: @danger-color;
      opacity: 1;
    }
  }
  .ghost {
    background: transparent;
    color: @primary-color;
    border-color: @primary-color;
    &:hover,
    &:focus {
      color: @primary-color;
      opacity: .8;
    }
    &:active {
      background: @primary-active-color !important;
      color: @white;
      opacity: 1;
    }
  }
}
.cancelBtn {
  width: 110px;
  margin-right: 10px;
}
