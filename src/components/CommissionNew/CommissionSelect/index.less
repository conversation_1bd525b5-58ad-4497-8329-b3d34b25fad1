.lineInputWrap {
    width: 100%;
    margin: 10px 0;
    &:after {
        content: " ";
        display: table;
        clear: both;
    }

    .label {
        float: left;
        min-width: 154px;
        font-size: 14px;
        color: #9b9b9b;
        text-align: right;
        line-height: 32px;
        margin-right: -154px;

        .required {
            margin-right: 4px;
            color: red;
            font-style: normal;
        }

        .colon {
            padding: 0 4px;
        }
    }
    .componentBox {
        margin-left: 154px;
        float: left;
        font-size: 14px;
        color: #333;
        display: flex;
        &.inputBox {
            input {
                width: 220px;
                height: 32px;
            }
        }
        &.selectBox {
            :global(.ant-select) {
                width: 220px;
                height: 32px;
                border-radius: 4px;
            }
        }
        &.textAreaBox {
            textarea {
                width: 417px;
                height: 74px;
                resize: none;
            }
        }
    }

    .tipIcon {
      margin: 4px 0 0 10px;
      cursor: pointer;

      .icon {
        color: #108ee9;
      }
    }
}
