/**
 * @file components/commissionAdjustment/OtherCommissionSelect.js
 * @description 新建批量佣金调整中其他佣金费率下拉框
 * <AUTHOR>
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import Select from '@/components/common/Select';
import Icon from '@/components/common/Icon';
import Tooltip from '@/components/common/Tooltip';
import IfWrap from '@/components/common/IFWrap';
import styles from './index.less';

export default class OtherCommissionSelect extends PureComponent {
  static propTypes = {
    reset: PropTypes.number.isRequired,
    label: PropTypes.string,
    options: PropTypes.array,
    name: PropTypes.string,
    onChange: PropTypes.func.isRequired,
    getPopupContainer: PropTypes.func.isRequired,
    disabled: PropTypes.bool,
    // 是否展示提示图标
    isShowTip: PropTypes.bool,
    // 提示内容
    tipContent: PropTypes.string,
    // 值
    value: PropTypes.string,
    // 是否必选
    required: PropTypes.bool,
    // 佣金品种code
    code: PropTypes.string,
  }

  static defaultProps = {
    name: '',
    label: '',
    options: [],
    disabled: false,
    isShowTip: false,
    tipContent: '',
    value: '',
    required: false,
    code: '',
  }

  @autobind
  handleChange(name, value, option) {
    const { code } = this.props;
    this.props.onChange(name, value, option, { code });
  }

  render() {
    const {
      name,
      label,
      options,
      getPopupContainer,
      disabled,
      isShowTip,
      tipContent,
      value,
      required,
      ...resetProps
    } = this.props;
    const newOptions = _.cloneDeep(options);
    newOptions.unshift({
      label: '请选择',
      value: '',
      show: true,
    });
    return (
      <div className={styles.lineInputWrap}>
        <div className={styles.label}>
          <IfWrap when={required}>
            <span className={styles.required}>*</span>
          </IfWrap>
          {label}
          <span className={styles.colon}>:</span>
        </div>
        <div className={`${styles.componentBox} ${styles.selectBox}`}>
          <Select
            disabled={disabled}
            name={name}
            data={newOptions}
            value={value}
            {...resetProps}
            onChange={this.handleChange}
            getPopupContainer={getPopupContainer}
          />
          <IfWrap when={isShowTip}>
            <Tooltip
              title={tipContent}
              placement="top"
              trigger="hover"
              getPopupContainer={() => document.querySelector(`.${styles.tipIcon}`)}
            >
              <div className={styles.tipIcon}>
                <Icon type="tishi2" className={styles.icon} />
              </div>
            </Tooltip>
          </IfWrap>
        </div>
      </div>
    );
  }
}
