/*
 * @Author: sunwei<PERSON>
 * @Date: 2021-06-26 13:44:31
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-06-26 13:55:36
 * @description 服务订购新页面的面包屑
 */

import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import styles from './index.less';

function CommissionBreadcrum(props, context) {
  const { name } = props;

  const newBreadcrumCls = cx({
    [styles.breadcrumb]: true,
    [styles.newBreadCrum]: true,
  });

  return (
    <div className={styles.menuSection}>
      <div className={styles.breadcrumb} onClick={() => context.goBack()}>
        <div className={styles.breadcrumbItem}>
          <span className={styles.breadcrumbName}>佣金调整</span>
        </div>
      </div>
      <div className={newBreadcrumCls}>
        <div className={styles.breadcrumbItem}>
          <span className={styles.breadcrumbDivide}>/</span>
          <span className={styles.breadcrumbName}>{name}</span>
        </div>
      </div>
    </div>
  );
}

CommissionBreadcrum.propTypes = {
  // 展示的二级面包屑名称
  name: PropTypes.string.isRequired,
};

CommissionBreadcrum.contextTypes = {
  goBack: PropTypes.func.isRequired,
};

export default CommissionBreadcrum;
