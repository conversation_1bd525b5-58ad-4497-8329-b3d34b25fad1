.menuSection {
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex: 1;
  box-shadow: 0 2px 2px 0 rgba(70, 70, 70, 0.11);
  margin-bottom: 2px;
  background-color: #fff;

  .breadcrumDropdownunActive {
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .lego-dropdown-content {
        color: #999 !important;
      }
    }
  }

  .breadcrumb {
    padding-left: 20px;
    flex: 0 0 auto;

    .breadcrumbItem {
      font-size: 14px;
      display: inline-block;
      line-height: 40px;
      height: 40px;
      color: #999;

      .breadcrumbName {
        color: #999;
      }
      .breadcrumbDivide {
        color: #999;
        margin: 0 8px;
      }
    }

    &.newBreadCrum {
      padding-left: 0;

      .breadcrumbItem {
        .breadcrumbName,
        .breadcrumbDivide {
          color: #333;
        }
      }
    }
  }
}
