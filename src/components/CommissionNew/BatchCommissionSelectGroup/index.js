/**
 * <AUTHOR>
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-15 15:36:20
 * @description 批量佣金的其他佣金费率选择组合组件
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import Icon from '@/components/common/Icon';

import CommissionSelect from '../CommissionSelect';
import {
  ALL_OTHER_RATIOS,
  ONLINE_DISABLE_TYPES,
  HTSC_QFARE_RATIO_KEY,
  HTSC_DQFARE_RATIO_KEY,
  B_GU_COMMISSION_KEY,
  REQUIRED_PARAMS_CODES,
} from './config';

import styles from './index.less';

export default class BatchCommissionSelectGroup extends PureComponent {
  static propTypes = {
    reset: PropTypes.number.isRequired,
    onChange: PropTypes.func,
    baseCommission: PropTypes.object,
    otherRatio: PropTypes.array,
    // 是否是线上调佣
    isOnline: PropTypes.bool,
  };

  static defaultProps = {
    onChange: () => { },
    baseCommission: {},
    otherRatio: [],
    isOnline: false,
  };

  static contextTypes = {
    dict: PropTypes.object,
  };

  @autobind
  getWrapRef() {
    return this.wrap;
  }

  @autobind
  wrapRef(input) {
    this.wrap = input;
  }

  @autobind
  isDisabledSelect(item) {
    const { isOnline } = this.props;
    let disabled = false;
    // 线上调佣，B股,担保权证，权证都不给选
    if (_.includes(ONLINE_DISABLE_TYPES, item.code) && isOnline) {
      disabled = true;
    }

    return disabled;
  }

  @autobind
  getTypeName(code) {
    let name = '';
    switch (code) {
      case B_GU_COMMISSION_KEY:
        name = 'B股';
        break;
      case HTSC_QFARE_RATIO_KEY:
        name = '权证';
        break;
      case HTSC_DQFARE_RATIO_KEY:
        name = '担保权证';
        break;
      default:
        break;
    }
    return name;
  }

  // 是否展示右侧的提示以及提示内容
  @autobind
  judgeTipInfo(item) {
    const { isOnline } = this.props;
    let isShowTip = false;
    let tipContent = '';
    // 当前调佣方式是线上时，B股,担保权证，权证 提示
    if (_.includes(ONLINE_DISABLE_TYPES, item.code) && isOnline) {
      isShowTip = true;
      const name = this.getTypeName(item.code);
      tipContent = `${name}佣金调整暂不支持线上确认，若有调整需求调佣方式请选线下`;
    }

    return {
      isShowTip,
      tipContent,
    };
  }

  @autobind
  makeSelect(item) {
    const {
      onChange,
      reset,
      baseCommission,
    } = this.props;
    const { code, options } = item;
    const { brief, paramName } = ALL_OTHER_RATIOS[code];

    const disabled = this.isDisabledSelect(item);
    // 右侧提示
    const { isShowTip, tipContent } = this.judgeTipInfo(item);

    const newOptions = options.map((option) => ({
      label: option.codeDesc,
      value: option.codeValue,
      show: true,
    }));

    return (
      <CommissionSelect
        disabled={disabled}
        value={baseCommission[paramName] || ''}
        reset={reset}
        key={code}
        label={brief}
        name={paramName}
        options={newOptions}
        onChange={onChange}
        getPopupContainer={this.getWrapRef}
        isShowTip={isShowTip}
        tipContent={tipContent}
        required={_.includes(REQUIRED_PARAMS_CODES, paramName)}
        code={code}
      />
    );
  }

  // 因为后端返回了所有类型的其他佣金费率，但是不同类型佣金调整对应的其他佣金费率不太一样，前端过滤出配置中的类型
  @autobind
  getOtherRatiosList(list, configList) {
    const configRatiosList = _.filter(list, (item) => !_.isEmpty(configList[item?.code])) || [];
    return configRatiosList;
  }

  render() {
    const { otherRatio } = this.props;
    const compactRatios = _.compact(otherRatio);
    // 从后端返回的所有其他佣金费率中过滤出配置的其他佣金费率
    const configRatiosList = this.getOtherRatiosList(compactRatios, ALL_OTHER_RATIOS);
    // 根据排序order将所有配置分为左右两列
    const orderRatios = _.sortBy(configRatiosList, (o) => ALL_OTHER_RATIOS[o.code]?.order);
    const oddCommissionArray = _.filter(orderRatios, (v, index) => index % 2 === 1);
    const evenCommissionArray = _.filter(orderRatios, (v, index) => index % 2 === 0);
    return (
      <div className={styles.otherComsBoxWrap} ref={this.wrapRef}>
        <div className={styles.otherComsBox}>
          { evenCommissionArray.map(this.makeSelect) }
        </div>
        <div className={styles.otherComsBox}>
          { oddCommissionArray.map(this.makeSelect) }
        </div>
        <div className={styles.blockTip}>
          <div className={styles.icon}><Icon type="tishi2" /></div>
          <div className={styles.text}>
            <p>本功能不提供特殊资产校验的费率设置，如需调整请通过单客户佣金调整功能</p>
            <p>特别提醒：上海和深圳可转债成本佣金为万0.4，低于成本一律按成本收取佣金；</p>
            <p>
              港股通初始佣金同步规则：客户首次开通港股通时点A股非现场交易佣金-万0.541得出的数值作为港股通初始净佣金，
              之后调整A股佣金不影响港股通佣金。如需调整港股通佣金建议先检查恒生系统客户港股通初始佣金数值，避免误操作。
            </p>
          </div>
        </div>
      </div>
    );
  }
}
