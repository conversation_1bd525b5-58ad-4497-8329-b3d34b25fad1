/*
 * @Author: sunwei<PERSON>
 * @Date: 2021-06-26 18:22:31
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-06-26 18:27:33
 * @description 下一步审批人触发组件
 */
import React from 'react';
import PropTypes from 'prop-types';
import {
  Icon,
} from 'antd';

import styles from './index.less';

function ApproverTrigger(props) {
  const { name, id } = props;

  return (
    <div className={styles.checkApprover} onClick={props.onClick}>
      {name === '' ? '' : `${name}(${id})`}
      <div className={styles.searchIcon}>
        <Icon type="search" />
      </div>
    </div>
  );
}

ApproverTrigger.propTypes = {
  onClick: PropTypes.func.isRequired,
  name: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
};

export default ApproverTrigger;
