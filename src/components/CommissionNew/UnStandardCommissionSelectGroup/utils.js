/*
 * @Author: yanfaping
 * @Date: 2022-07-26 15:07:28
 * @Last Modified by: yanfaping
 * @Last Modified time: 2022-07-26 15:07:28
 * @description 非标客户特殊佣金调整-其他佣金费率-工具项
 */
import _ from 'lodash';
import {
  MULTI_MINIMUM_CHARGE_RADIO_SELECT,
  MULTI_MINIMUM_CHARGE_RADIO,
  SELECT,
  ONLINE_MINIMUMCHAREG_KEYS
} from './config';
// 因为后端返回了所有类型的其他佣金费率，但是不同类型佣金调整对应的其他佣金费率不太一样，前端过滤出配置中的类型
export function getOtherRatiosList(list, configList) {
  const configRatiosList = _.filter(list, (item) => !_.isEmpty(configList[item?.code])) || [];
  return configRatiosList;
}

// 给options的每一项添加label和value
function formatOptions(options) {
  if (_.isEmpty(options)) {
    return [];
  }
  return _.map(options, (item) => ({
    ...item,
    label: item?.codeDesc,
    value: item?.codeValue,
    show: true,
  }));
}

// 账户最低收费设置佣金项的集合-数据转换
// 后端接口拿到的佣金项，添加对应的佣金入参字段名，在驳回修改时做回填使用
// 由于后端返回的佣金类型的下拉选项格式为{ codeDesc："", codeValue："""}，再对佣金类型的options数组转换为Select中label和value的渲染格式
// export function getMinimumChargeList(list, configList) {
//   const configRatiosList = _.map(list, (item) => {
//     const options = formatOptions(item?.options);
//     const value = configList[item?.code];
//     return {
//       ...item,
//       paramName: !_.isEmpty(value) ? value?.paramName : '',
//       options
//     };
//   }) || [];
//   return configRatiosList;
// }

export function getConfigItem(list, config, key) {
  const listItem = _.find(list, (item) => item?.code === key);
  const options = formatOptions(listItem?.options);
  return {
    ...listItem,
    paramName: config?.paramName,
    options
  };
}

export function getMinimumChargeList(list, radioKey) {
  const configList = radioKey === SELECT
    ? MULTI_MINIMUM_CHARGE_RADIO_SELECT
    : _.pick(MULTI_MINIMUM_CHARGE_RADIO, ONLINE_MINIMUMCHAREG_KEYS);
  const configRatiosList = _.map(configList,
    (config, key) => getConfigItem(list, config, key));
  return configRatiosList;
}

// 获取场内基金必填字段
export function getOnExchangeFundRequiredFields(data, customer) {
  const CODE = 5;
  const commissions = {
    oCommission: data?.oCommission !== CODE,
    doCommission: customer?.hasCreditAccount && data?.doCommission !== CODE,
    coCommission: customer?.hasCreditAccount && data?.coCommission !== CODE,
  };
  return commissions;
}
