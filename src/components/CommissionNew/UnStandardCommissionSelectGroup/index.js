/*
 * @Author: yanfaping
 * @Date: 2022-07-26 15:07:28
 * @Last Modified by: yanfaping
 * @Last Modified time: 2022-07-26 15:07:28
 * @description 非标客户特殊佣金调整-其他佣金费率下拉选择组
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import Icon from '@/components/common/Icon';
import IFWrap from '@/components/common/IFWrap';

import UnStandardCommissionSelect from './UnStandardCommissionSelect';
import {
  UN_STANDAED_OTHER_COMMISSION,
  STK_COMMISSION_KEY,
  CREDIT_COMMISSION_KEY,
  UN_OPEN_CREDIT_OR_RZRQ_KEYS,
} from './config';
import { getOtherRatiosList } from './utils';

import styles from './index.less';

export default class UnStandardCommissionSelectGroup extends PureComponent {
  static propTypes = {
    // 其他佣金费率列表
    otherRatios: PropTypes.array,
    // 切换下拉框选项值
    onChange: PropTypes.func,
    // 其他佣金费率对应的值
    baseCommission: PropTypes.object,
    // 客户信息
    custInfo: PropTypes.object,
    // 是否有灰度权限
    hasGrey: PropTypes.bool.isRequired,
    // 是否置灰不可点
    allDisabled: PropTypes.bool,
    // 场内基金最低收费信息
    minimumOnExchangeFund: PropTypes.object,
    // 全账户提佣校验
    validateUnifiedCommissionRaise: PropTypes.func.isRequired,
  };

  static defaultProps = {
    otherRatios: [],
    onChange: () => { },
    baseCommission: {},
    custInfo: {},
    allDisabled: false,
    minimumOnExchangeFund: {},
  };

  @autobind
  getWrapRef() {
    return this.wrap;
  }

  @autobind
  wrapRef(input) {
    this.wrap = input;
  }

  @autobind
  checkOpenRzrq(item) {
    const {
      custInfo: {
        openRzrq,
        hasCreditAccount,
      },
      hasGrey
    } = this.props;

    // 判断是否有灰度权限
    // 有灰度：判断客户是否开通两融账户 且 是否开通信用账户，未开通以下佣金置灰不可选
    // (【担保债券】【担保权证】【担保股基】【担保品大宗交易】【担保场内基金】【信用场内基金】【信用股基】【信用债券】
    if (hasGrey) {
      // 多档二期新增hasCreditAccount表示是否开通信用账户，openRzrq使用规则保持之前不变
      return (openRzrq === 'N' || !hasCreditAccount) && _.includes(UN_OPEN_CREDIT_OR_RZRQ_KEYS, item.code);
    }

    // 无灰度：未开通两融，【担保股基】、【信用股基】置灰不可选
    return openRzrq === 'N' && _.includes([CREDIT_COMMISSION_KEY, STK_COMMISSION_KEY], item.code);
  }

  @autobind
  isDisabledSelect(item) {
    const { custInfo } = this.props;

    // 获取佣金率的默认值disabled值
    let disabled = false;

    // 2023-07-20：多档最低收费-添加是否开通信用账户限制，未开通信用账户，置灰不可选
    // openRzrq与后端确定：表示客户是否开通两融 或 是否开通信用账户
    if (this.checkOpenRzrq(item)) {
      disabled = true;
      // eslint-disable-next-line max-len
    } else if (_.includes(custInfo?.disableContractRatioType?.signedType || [], item.code)) {
      // 判断客户佣金模式的新投顾签约哪些费率只读
      disabled = true;
    }

    return disabled;
  }

  // 是否展示右侧的提示以及提示内容
  @autobind
  judgeTipInfo(item) {
    const {
      custInfo: {
        hasCreditAccount,
      },
      hasGrey,
    } = this.props;
    let isShowTip = false;
    let tipContent = '';

    if (hasGrey && !hasCreditAccount && _.includes(UN_OPEN_CREDIT_OR_RZRQ_KEYS, item.code)) {
      isShowTip = true;
      tipContent = '信用资金账户开户成功后可设置佣金';
    }

    return {
      isShowTip,
      tipContent,
    };
  }

  @autobind
  makeSelect(item) {
    const {
      baseCommission,
      custInfo,
      allDisabled,
      minimumOnExchangeFund,
    } = this.props;
    const { options, code } = item;
    const { paramName, brief } = UN_STANDAED_OTHER_COMMISSION[code];

    const disabled = allDisabled || this.isDisabledSelect(item);

    const newOptions = options.map((option) => ({
      ...option,
      label: option.codeDesc,
      value: option.codeValue,
      show: true,
    }));

    // 右侧提示
    const { isShowTip, tipContent } = this.judgeTipInfo(item);

    return (
      <UnStandardCommissionSelect
        custInfo={custInfo}
        value={baseCommission[paramName] || ''}
        disabled={disabled}
        key={code}
        code={code}
        label={brief}
        name={paramName}
        options={newOptions}
        getPopupContainer={this.getWrapRef}
        onChange={this.props.onChange}
        isShowTip={isShowTip}
        tipContent={tipContent}
        required={minimumOnExchangeFund?.[paramName] || false}
        validateUnifiedCommissionRaise={this.props.validateUnifiedCommissionRaise}
      />
    );
  }

  render() {
    const { otherRatios, baseCommission } = this.props;
    const compactRatios = _.compact(otherRatios);
    // 从后端返回的所有其他佣金费率中过滤出配置的其他佣金费率
    const configRatiosList = getOtherRatiosList(compactRatios, UN_STANDAED_OTHER_COMMISSION);
    // 根据排序order将所有配置分为左右两列
    const orderRatios = _.sortBy(configRatiosList,
      (o) => UN_STANDAED_OTHER_COMMISSION[o.code]?.order);
    const oddCommissionArray = _.filter(orderRatios, (v, index) => index % 2 === 1);
    const evenCommissionArray = _.filter(orderRatios, (v, index) => index % 2 === 0);
    // 是否设置了B股佣金，是则给出提示语（b2023-07-20: 多档最低收费-添加B股提示）
    const isSetBgCommission = !_.isEmpty(baseCommission?.bgCommission);

    return (
      <div className={styles.otherComsBoxWrap} ref={this.wrapRef}>
        <div className={styles.otherComsBox}>
          {
             evenCommissionArray.map(this.makeSelect)
           }
        </div>
        <div className={styles.otherComsBox}>
          {
             oddCommissionArray.map(this.makeSelect)
           }
        </div>
        <div className={styles.blockTip}>
          <div className={styles.icon}><Icon type="tishi2" /></div>
          <div className={styles.text}>
            <p>特别提醒：上海和深圳可转债成本佣金为万0.4，低于成本一律按成本收取佣金；</p>
            <p>
              港股通初始佣金同步规则：客户首次开通港股通时点A股非现场交易佣金-万0.541得出的数值作为港股通初始净佣金，
              之后调整A股佣金不影响港股通佣金。如需调整港股通佣金建议先检查恒生系统客户港股通初始佣金数值，避免误操作。
            </p>
            <IFWrap when={isSetBgCommission}>
              <span className={styles.bgTipText}>
                B股佣金折扣设置会于流程办结后经晚间清算同步。实际交易佣金以清算时系统折扣为准。请在恒生系统确认佣金生效后及时告知客户！
              </span>
            </IFWrap>
          </div>
        </div>
      </div>
    );
  }
}
