/*
 * @Author: yanfaping
 * @Date: 2022-07-26 15:07:28
 * @Last Modified by: yanfaping
 * @Last Modified time: 2022-07-26 15:07:28
 * @description 非标客户特殊佣金调整-其他佣金费率-品种下拉框
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import cx from 'classnames';

import newConfirm from '@/components/common/newUI/confirm';
import Select from '@/components/common/Select';
import logable from '@/decorators/logable';
import Tooltip from '@/components/common/Tooltip';
import Icon from '@/components/common/Icon';
import IfWrap from '@/components/common/IFWrap';
import {
  // UN_STANDAED_OTHER_COMMISSION,
  ALL_MINIMUMCHAREG_PARAMS,
  NEED_INVEST_CONTRACT_CHECK_KEYS,
  SELECT,
  VALIDATE_UNIFIED_COMMISION_CODES
} from './config';

import styles from './unStandardCommissionSelect.less';

export default class UnStandardCommissionSelect extends PureComponent {
  static propTypes = {
    // 佣金名称
    label: PropTypes.string,
    // 佣金可选列表
    options: PropTypes.array,
    // 切换下拉框选项值
    onChange: PropTypes.func.isRequired,
    // 客户信息
    custInfo: PropTypes.object,
    // 自定义样式
    className: PropTypes.string,
    isShowTip: PropTypes.bool,
    tipContent: PropTypes.string,
    // 是否必选
    required: PropTypes.bool,
    // 佣金品种code
    code: PropTypes.string,
    // 全账户提佣校验
    validateUnifiedCommissionRaise: PropTypes.func,
  }

  static defaultProps = {
    label: '',
    options: [],
    custInfo: {},
    className: '',
    isShowTip: false,
    tipContent: '',
    required: false,
    code: '',
    validateUnifiedCommissionRaise: _.noop,
  }

  // 判断客户费率是否在可选列表中
  @autobind
  isInOptions(custValue) {
    const { options } = this.props;
    const option = _.find(options, (item) => item.value === custValue);

    return !_.isEmpty(option);
  }

  // 判断所选的option是否大于客户当前的佣金费率,
  // 返回 true, 表示可以通过校验
  @autobind
  isPassTooHighCompare(name, value, option) {
    const { custInfo } = this.props;
    // 所选佣金对应的客户当前佣金率code值
    const currentValue = custInfo[name]?.code;
    if (_.isEmpty(currentValue)) {
      // 客户信息为空，不做校验
      return true;
    }

    // 如果客户对应的佣金费 code 值, 不在可选的费率选项中就不做过高的校验
    if (!this.isInOptions(currentValue)) {
      return true;
    }

    const order = custInfo[name]?.indexOrder;
    return option?.indexOrder <= order;
  }

  @autobind
  judgeThreshold(name, value, option) {
    const canChange = true;
    const content = '';
    // 场内基金二期去掉比大小校验， 代码待删除
    // if (!this.isPassTooHighCompare(name, value, option)) { // option与客户当前佣金费率比较
    //   const commissionName =
    // _.find(UN_STANDAED_OTHER_COMMISSION, ['paramName', name])?.brief || '';
    //   canChange = false;
    //   content = `${commissionName}本次新设费率值高于客户当前费率，请确认。`;
    // }
    if (canChange) {
      this.props.onChange(name, value, option);
    } else {
      newConfirm({
        title: '提示',
        content,
        okText: '确定',
        cancelVisible: false,
        onOk: () => false,
      });
    }
  }

  @autobind
  async handleValidateUnifiedCommissionRaise(name, value, option) {
    const { custInfo, code } = this.props;
    const validateRes = await this.props.validateUnifiedCommissionRaise({
      ratioType: code || '',
      custIdList: custInfo?.custId ? [custInfo?.custId] : [],
      ratioCode: value || '',
    });
    if (!validateRes?.pass) {
      newConfirm({
        title: '提示',
        content: validateRes?.errorMsg,
        okText: '确定',
        cancelVisible: false,
        onOk: () => false,
      });
    } else {
      this.judgeThreshold(name, value, option);
    }
  }

  @autobind
  checkCustInfo(name, value, option) {
    const { code } = this.props;
    // 针对【股基】【信用股基】【担保股基】【场内基金】【信用场内基金】【担保场内基金】佣金品种，添加全账户提佣校验
    if (_.includes(VALIDATE_UNIFIED_COMMISION_CODES, code)) {
      this.handleValidateUnifiedCommissionRaise(name, value, option);
    } else {
      // NOTE: 此处需要针对具体的客户数据进行阈值、与之前的值比较大小判断
      this.judgeThreshold(name, value, option);
    }
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '$props.label',
      value: '$args[1]',
    },
  })
  onChange(name, value, option) {
    const { custInfo } = this.props;
    // 【普通账户收取最低费用】【信用账户收取最低费用】【普通账户股票最低收费】【普通账户上海债券最低收费】【信用账户股票最低收费】【信用账户上海债券最低收费】
    // 不做比较
    const isMinimumCharge = _.includes(ALL_MINIMUMCHAREG_PARAMS, name);
    if (isMinimumCharge) {
      this.props.onChange(name, value, option);
      return;
    }

    // 2024-01-30 佣金调整增加新投顾签约的校验
    // 判断客户是否有收费模式为佣金模式的新的投顾签约协议
    // 如果有则在调整【信用股基佣金】【担保股基佣金】提示 该客户已签订投顾签约协议，请前往投顾签约功能进行调整
    const isGJCommission = _.includes(NEED_INVEST_CONTRACT_CHECK_KEYS, name);
    if (isGJCommission && custInfo?.hasCommModeContract === SELECT) {
      newConfirm({
        title: '提示',
        content: '该客户已签订投顾签约协议，请前往投顾签约功能进行调整',
        okText: '确定',
        cancelVisible: false,
        onOk: () => false,
      });
      return;
    }

    if (!_.isEmpty(custInfo)) {
      this.checkCustInfo(name, value, option);
    } else {
      this.props.onChange(name, value, option);
    }
  }

  render() {
    const {
      label,
      options,
      className,
      isShowTip,
      tipContent,
      required,
      ...resetProps
    } = this.props;
    const newOptions = _.cloneDeep(options);
    newOptions.unshift({
      label: '请选择',
      value: '',
      show: true,
      indexOrder: null,
    });

    const lineInputWrap = cx({
      [styles.lineInputWrap]: true,
      [className]: true
    });

    return (
      <div className={lineInputWrap}>
        <div className={styles.label}>
          <span className={styles.text}>
            <IfWrap when={required}>
              <span className={styles.required}>*</span>
            </IfWrap>
            {label}
          </span>
          <span className={styles.colon}>:</span>
        </div>
        <div className={`${styles.componentBox} ${styles.selectBox}`}>
          <Select
            {...resetProps}
            data={newOptions}
            onChange={this.onChange}
          />
          <IfWrap when={isShowTip}>
            <Tooltip
              title={tipContent}
              placement="top"
              trigger="hover"
              getPopupContainer={() => document.querySelector(`.${styles.tipIcon}`)}
            >
              <div className={styles.tipIcon}>
                <Icon type="tishi2" className={styles.icon} />
              </div>
            </Tooltip>
          </IfWrap>
        </div>
      </div>
    );
  }
}
