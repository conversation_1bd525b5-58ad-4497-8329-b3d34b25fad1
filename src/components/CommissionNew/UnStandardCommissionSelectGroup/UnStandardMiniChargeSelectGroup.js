/*
 * @Author: yanfaping
 * @Date: 2023-07-18 15:50:52
 * @LastEditors: yanfaping
 * @LastEditTime: 2023-08-07 16:38:15
 * @Description: 非标客户特殊佣金调整-账户最低收费设置佣金Select列表
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { Radio } from 'antd';
import { InfoCell } from '@crm/biz-ui';
import IFWrap from '@/components/common/IFWrap';
import UnStandardCommissionSelect from './UnStandardCommissionSelect';
import {
  SELECT,
  NO_SELECT,
  // MULTI_MINIMUM_CHARGE_RADIO,
  SHOW_RADIO_CUSTS,
  MULTI_CREDIT_KEYS
} from './config';

import { getMinimumChargeList } from './utils';

import styles from './unStandardMiniChargeSelectGroup.less';

const RadioGroup = Radio.Group;

export default class SpecialMiniChargeSelectGroup extends PureComponent {
  static propTypes = {
    // 当前多档最低收费佣金费率选项
    currentMiniRadios: PropTypes.array,
    // 切换下拉框选项值
    onChange: PropTypes.func,
    // 初始值
    baseCommission: PropTypes.object,
    // 客户信息
    custInfo: PropTypes.object,
    // 切换是否设置多档最低收费
    onRadioChange: PropTypes.func,
    // 是否设置多档最低收费
    radioKey: PropTypes.string,
    // 是否有灰度权限
    hasGrey: PropTypes.bool.isRequired,
    // 是否置灰不可点
    allDisabled: PropTypes.bool,
  };

  static defaultProps = {
    currentMiniRadios: [],
    onChange: _.noop,
    baseCommission: {},
    custInfo: {},
    onRadioChange: _.noop,
    radioKey: '',
    allDisabled: false,
  };

  constructor(props) {
    super(props);
    this.wrapRef = React.createRef();
  }

  @autobind
  getWrapRef() {
    return this.wrapRef?.current;
  }

  @autobind
  isDisabledSelect(item) {
    const {
      custInfo: {
        openRzrq,
        hasCreditAccount,
      },
    } = this.props;
    // 获取佣金率的默认值disabled值
    let disabled = false;

    if (_.includes(MULTI_CREDIT_KEYS, item.code)) {
      // 如果是信用账户收取最低费用 需要判断客户是否开通两融账户 和 是否开通信用账户，未开通则置灰
      // 多档二期新增hasCreditAccount表示是否开通信用账户，openRzrq使用规则保持之前不变
      disabled = openRzrq === 'N' || !hasCreditAccount;
    }

    return disabled;
  }

  // 是否展示右侧的提示以及提示内容
  @autobind
  judgeTipInfo(item) {
    const {
      custInfo: {
        hasCreditAccount,
      },
      hasGrey,
    } = this.props;
    let isShowTip = false;
    let tipContent = '';

    if (hasGrey && !hasCreditAccount && _.includes(MULTI_CREDIT_KEYS, item.code)) {
      isShowTip = true;
      tipContent = '信用资金账户开户成功后可设置佣金';
    }

    return {
      isShowTip,
      tipContent,
    };
  }

  @autobind
  makeSelect(item) {
    const {
      options,
      code,
      codeType = '',
      paramName
    } = item;
    const { custInfo, baseCommission, allDisabled } = this.props;

    const disabled = allDisabled || this.isDisabledSelect(item);

    // 右侧提示
    const { isShowTip, tipContent } = this.judgeTipInfo(item);

    return (
      <UnStandardCommissionSelect
        custInfo={custInfo}
        value={baseCommission[paramName] || ''}
        disabled={disabled}
        isShowTip={isShowTip}
        tipContent={tipContent}
        key={code}
        label={codeType}
        name={paramName}
        options={options}
        getPopupContainer={this.getWrapRef}
        onChange={this.props.onChange}
        className={styles.selectItem}
        showSearch
        optionFilterProp="title"
      />
    );
  }

  @autobind
  showRadioGroup() {
    const { hasGrey, custInfo } = this.props;
    // 有灰度权限-判断客户类型是否为机构户、产品户，是则展示“是否设置多档最低收费”，否则不展示
    if (hasGrey) {
      return _.includes(SHOW_RADIO_CUSTS, custInfo?.custType);
    }

    // 无灰度权限-不展示“是否设置多档最低收费”
    return false;
  }

  render() {
    const {
      radioKey,
      onRadioChange,
      currentMiniRadios,
      allDisabled
    } = this.props;

    const selectList = getMinimumChargeList(currentMiniRadios, radioKey);
    // 是否展示设置多档最低收费radio
    const showRadio = this.showRadioGroup();

    return (
      <div className={styles.miniChargeSelectBox} ref={this.wrapRef}>
        <IFWrap when={showRadio}>
          <div className={styles.radioBox}>
            <InfoCell
              span={100}
              label="是否定制化设置最低收费"
              labelWidth="170px"
              content={(
                <RadioGroup
                  value={radioKey}
                  size="normal"
                  className={styles.radioGroup}
                  onChange={onRadioChange}
                  disabled={allDisabled}
                >
                  <Radio key={SELECT} value={SELECT}>是</Radio>
                  <Radio key={NO_SELECT} value={NO_SELECT}>否</Radio>
                </RadioGroup>
            )}
            />
          </div>
        </IFWrap>
        <div className={styles.selectBox}>
          {
            _.map(selectList, (item) => this.makeSelect(item))
          }
        </div>
      </div>
    );
  }
}
