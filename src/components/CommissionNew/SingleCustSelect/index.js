/**
 *  带搜索icon的select和添加按钮
 *  当输入或者选中值后icon变化成关闭点击后清除input的value值
 * <AUTHOR>
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-02-27 16:50:18
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { AutoComplete } from 'antd';

import SimilarAutoComplete from '@/components/common/similarAutoComplete';
import confirm from '@/components/common/confirm';
import logable, { logCommon } from '@/decorators/logable';
import { emp } from '@/helper';

import styles from './index.less';

const Option = AutoComplete.Option;

export default class SelectAssembly extends PureComponent {
  static propTypes = {
    dataSource: PropTypes.array.isRequired,
    onSearchValue: PropTypes.func.isRequired,
    onSelectValue: PropTypes.func.isRequired,
    // 校验客户
    onValidateCust: PropTypes.func.isRequired,
    onValidateCustOtherInfo: PropTypes.func.isRequired,
    width: PropTypes.number,
    // 佣金调整校验客户如果有被驳回订单，终止订单
    terminalOrderFlow: PropTypes.func,
    // 佣金调整校验客户如果有新建订单，修改订单状态
    changeOrderStatus: PropTypes.func,
    // 单佣金调整校验客户-如果有被驳回订单，终止订单
    // （针对异地限价与股基佣金和资讯产品解绑，使用新流程的doApprove接口）
    doApprove: PropTypes.func,
    // 调佣方式
    adjustCommissionWayCode: PropTypes.string.isRequired,
    // 查询【佣金模式】的新投顾签约有哪些需要禁用的佣金率
    queryDisableContractRatioType: PropTypes.func.isRequired,
  }

  static defaultProps = {
    width: 300,
    terminalOrderFlow: _.noop,
    changeOrderStatus: _.noop,
    doApprove: _.noop,
  }

  constructor(props) {
    super(props);
    this.state = {
      isRejected: false, // 标识 选中的客户，是否有被驳回订单
      isNew: false, // 标识 选中的客户，是否有新建订单
      canSelected: false,
      hasNewFlow: false, // 是否是新功能体验岗创建的订单
      taskId: '', // doApprove接口需要的流程业务id
      bizId: '', // 新的订单id（针对股基佣金和资讯产品解绑，使用新的bizId）
    };
  }

  @autobind
  clearCust() {
    this.setState({
      canSelected: false,
    });
    this.custSearch.clearValue();
  }

  @autobind
  custSearchRef(input) {
    this.custSearch = input;
  }

  @autobind
  handleOrder(selectItem, otherParams) {
    const {
      isRejected,
      isNew,
      hasNewFlow,
      taskId,
      bizId,
    } = this.state;
    const params = {
      custId: selectItem.custEcom,
      ...otherParams
    };
    // 是否是fsp流程
    const isFspFlow = otherParams?.fspFlow;
    // 有被驳回订单，点击确定,如果是fsp流程则掉接口终止订单，否则就更改状态
    if (isRejected) {
      // 针对异地限价与股基佣金和资讯产品解绑，如果有驳回订单，点击确定则使用新流程的doApprove接口终止订单
      if (hasNewFlow) {
        this.props.doApprove({
          taskId,
          flowId: otherParams?.flowId,
          actionName: '终止',
          bpmnName: '单佣金调整流程',
          submitter: emp.getId(),
          businessId: bizId,
        });
      } else if (isFspFlow) {
        this.props.terminalOrderFlow(params);
      } else {
        this.props.changeOrderStatus(params);
      }
    }
    // 有新建订单，点击确定则掉接口修改“新建”状态为“已失败”，继续发起调佣流程
    if (isNew) {
      this.props.changeOrderStatus(params);
    }
    // 把选择的客户传到外层，提交时二次校验
    this.props.onSelectValue(selectItem);
  }

  @autobind
  handleOKAfterValidate(selectItem, otherParams, validResult) {
    if (this.state.canSelected) {
      // 可以选中
      const {
        openRzrq,
        minimumCharge,
        hasCreditAccount = false,
        hasNormalAccount = false,
      } = validResult;
      this.props.onSelectValue({
        ...selectItem,
        openRzrq,
        minimumCharge,
        disableContractRatioType: validResult?.disableContractRatioType ?? {},
        // hasTerminateCommissionContract: validResult?.hasTerminateCommissionContract,
        hasCreditAccount,
        hasNormalAccount,
      });
    } else {
      // 是否需要清空所选客户,如果是有在途订单（待审批，新建，驳回）,确认之后不清空所选客户
      if (otherParams) {
        this.handleOrder(selectItem, otherParams);
        return;
      }
      // 干掉客户
      this.clearCust();
    }
  }

  @autobind
  handleCancelAfterValidate() {
    this.clearCust();
  }

  // 校验不通过，弹框
  @autobind
  fail2Validate(obj) {
    const {
      shortCut,
      content,
      selectItem,
      params,
      validResult,
      cancelVisible = true,
    } = obj;
    confirm({
      shortCut,
      content,
      title: '提示',
      onOk: () => { this.handleOKAfterValidate(selectItem, params, validResult); },
      onCancel: this.handleCancelAfterValidate,
      cancelVisible
    });
    this.setState({
      canSelected: false,
    });
  }

  // 客户校验
  @autobind
  afterValidateSingleCust(selectItem, validResult) {
    if (_.isEmpty(validResult)) {
      confirm({ content: '客户校验失败' });
      return;
    }
    const {
      riskRt,
      investRt,
      investTerm,
      hasorder, // 是否有在途订单
      orderStatus, // 订单状态
      fspFlow, // 是否是fsp流程
      orderId, // 订单id
      flowId, // 流程id
      flowStarter, // 流程发起者工号
      hasNewFlow, // 是否是新功能体验岗创建的订单
      taskId = '', // 业务流程id
      bizId = '', // 新流程订单id
      validmsg,
      hasTerminateCommissionContract, // 是否有在途投顾解约
      hasNormalAccount, // 是否开通普通账户
    } = validResult;
    const params = {
      fspFlow,
      orderId,
      flowId,
      flowStarter
    };
    this.setState({
      isRejected: false,
      isNew: false,
    });
    // 客户需完成普通资金账户开户后，方可设置佣金
    if (!hasNormalAccount) {
      this.fail2Validate({ content: '客户需完成普通资金账户开户后，方可设置佣金' });
      return;
    }
    // 单佣金调整有待审批订单时
    if (hasorder === 'Y' && orderStatus === 'hasCheck') {
      this.fail2Validate({
        content: validmsg, selectItem, validResult, cancelVisible: false
      });
      return;
    }
    // 单佣金调整有被驳回订单时
    if (hasorder === 'Y' && orderStatus === 'hasRejected') {
      this.setState({
        isRejected: true, hasNewFlow, taskId, bizId,
      });
      this.fail2Validate({
        shortCut: 'rejected', selectItem, params, validResult
      });
      return;
    }
    // 单佣金调整有新建订单时
    if (hasorder === 'Y' && orderStatus === 'hasNew') {
      this.setState({ isNew: true });
      this.fail2Validate({
        shortCut: 'new', selectItem, params, validResult
      });
      return;
    }
    // 风险测评校验
    if (riskRt === 'N') {
      this.fail2Validate({ shortCut: 'custRisk', validResult });
      return;
    }
    // 偏好品种校验
    if (investRt === 'Y') {
      this.fail2Validate({ shortCut: 'custInvestRt', validResult });
      return;
    }
    // 投资期限校验
    if (investTerm === 'Y') {
      this.fail2Validate({ shortCut: 'custInvestTerm', validResult });
      return;
    }
    // 2023-12-26 同事吧问题优化——添加客户判断服务经理和开户营业部是否为空，为空则弹框提示，不可选
    if (_.isEmpty(selectItem?.prMngId) && _.isEmpty(selectItem?.openOrgId)) {
      this.fail2Validate({ content: '客户开户营业部信息缺失，暂无法发起佣金设置流程' });
      return;
    }
    // 针对股基佣金和资讯产品解绑以及线上调佣，添加判断validMsg是否有值，有值则弹框提示，不可调佣
    if (!_.isEmpty(validmsg)) {
      this.fail2Validate({ content: validmsg });
      return;
    }
    // 2024-05-09有新的在途投顾解约则报错
    if (hasTerminateCommissionContract === 'Y') {
      this.fail2Validate({ content: '该客户有在途的投顾解约流程，请在解约流程办结后再发起申请。' });
      return;
    }
    this.setState({
      canSelected: true,
    });
    this.handleOKAfterValidate(selectItem, null, validResult);
  }

  // 选择某个客户
  @autobind
  handleSelectCust(cust) {
    if (!_.isEmpty(cust)) {
      // 选中值了
      const { id, custType, custEcom } = cust;
      const { adjustCommissionWayCode } = this.props;
      Promise.all([
        this.props.onValidateCust({
          custRowId: id,
          custType,
          adjustCommissionWayCode,
          custId: custEcom, // 客户的 custId
        }),
        this.props.onValidateCustOtherInfo({
          custId: custEcom, // 客户的 custId
          custType,
        }),
        this.props.queryDisableContractRatioType({
          custId: custEcom, // 客户的 custId
        }),
      ]).then((response) => {
        // 把返回值放在一个对象里
        const validResult = {
          ...response[0],
          ...response[1],
          ...response[2],
        };
        this.afterValidateSingleCust(cust, validResult);
      });
      logCommon({
        type: 'DropdownSelect',
        payload: {
          name: '选择客户',
          value: JSON.stringify(cust),
        },
      });
    } else {
      // 此时有可能客户搜索组件会传递一个空对象
      // 将空值传递出去以便可以让父组件更新其他组件数据
      // 删除客户
      // 向父组件传递一个空对象
      this.props.onSelectValue(null);
    }
  }

  // 搜索客户列表
  @autobind
  @logable({ type: 'Click', payload: { name: '搜索客户', value: '$args[0]' } })
  handleSearchCustList(value) {
    this.props.onSearchValue(value);
  }

  @autobind
  renderOption(cust) {
    const { custName, custEcom, riskLevelLabel = '' } = cust;
    const text = `${custName}（${custEcom}） - ${riskLevelLabel}`;
    return (
      <Option key={custEcom} value={text}>
        <span className={styles.prodValue} title={text}>{text}</span>
      </Option>
    );
  }

  render() {
    const { width, dataSource } = this.props;
    return (
      <SimilarAutoComplete
        ref={this.custSearchRef}
        placeholder="经纪客户号/客户名称"
        optionList={dataSource}
        style={{ width }}
        optionKey="custEcom"
        onSelect={this.handleSelectCust}
        onSearch={this.handleSearchCustList}
        renderOptionNode={this.renderOption}
      />
    );
  }
}
