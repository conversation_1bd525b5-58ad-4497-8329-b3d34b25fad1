import React from 'react';
import _ from 'lodash';
import { data as dataHelper, regxp } from '@/helper';
import {
  CREDIT_ACCOUNT_COMMISON_KEYS, MULTI_CREDIT_KEYS
} from '@/components/CommissionNew/SingleCommissionSelectGroup/config';
import { HTSC_COMMISSION_LEVEL } from './config';

const ALL_CREDIT_COMMISSION = [
  ...CREDIT_ACCOUNT_COMMISON_KEYS,
  ...MULTI_CREDIT_KEYS,
];

/**
 * AllComissionObj：所有佣金配置类型
 * comissionValues: 产品佣金费率信息
 * 返回值：其他佣金配置下拉项，用于单选框回填
 */
export function getProBroCommissionOptions(AllComissionObj, comissionValues = {}) {
  if (_.isEmpty(comissionValues)) {
    return [];
  }
  const otherCommissionArr = [];
  _.mapValues(AllComissionObj, (value, key) => {
    const commisionValue = comissionValues?.[value?.paramName] || {};
    const comission = {
      code: key,
      codeType: value?.brief,
      options: [{
        codeDesc: commisionValue?.label,
        codeValue: commisionValue?.code,
        conditionList: null,
        id: dataHelper.uuid()
      }],
    };
    otherCommissionArr.push(comission);
  });
  return otherCommissionArr;
}

/**
 * data: 经服产品佣金配置信息
 * 返回值：GJ佣金下拉项格式
 */
export function getProductBrokerageCommissionGJOptions(data) {
  const targetGJComData = data?.standardCommission?.standardCommissionNormalAccount?.basicCommission
    || {};
  const defaultOptions = [{
    label: '请选择', value: ''
  }];
  if (_.isEmpty(targetGJComData)) {
    return defaultOptions;
  }
  return _.concat(defaultOptions, [{
    label: targetGJComData?.label,
    value: targetGJComData?.code,
    id: dataHelper.uuid()
  }]);
}

/**
 * AllComissionObj：所有佣金配置类型
 * data：经服产品佣金配置信息
 * 返回值：平铺后的佣金费率集合
 */
export function getComissionValues(AllComissionObj, data, hasCreditAccount = false) {
  const standardCommission = data?.standardCommission || {};
  const {
    standardCommissionNormalAccount = {},
    standardCommissionCreditAccount = {},
    minimumCommissions = {},
  } = standardCommission || {};

  // 合并佣金费率信息
  const proBrokerageCommissions = {
    ...standardCommissionNormalAccount,
    ...standardCommissionCreditAccount,
    ...minimumCommissions,
  };

  // 根据传入的佣金类型集合，获取每个调佣类型可调佣金类型费率信息
  const paramNames = _.map(AllComissionObj, (value) => value?.paramName);
  const comissionValues = _.pick(proBrokerageCommissions, paramNames) || {};

  // 其他佣金费率下拉项
  const otherCommissionsDict = getProBroCommissionOptions(AllComissionObj, comissionValues);
  // 其他佣金费率设置的当前值
  let otherCommsCurrentValue = {};
  // 是否设置了信用佣金费率
  const hasSetCreditCommission = _.some(comissionValues,
    (item, key) => _.includes(ALL_CREDIT_COMMISSION, key) && !_.isEmpty(item?.code));
  // 当前客户是否开通信用账户，开通则正常回填设置的所有佣金费率
  // 未开通，则将信用费率置为null
  if (hasCreditAccount) {
    otherCommsCurrentValue = _.mapValues(comissionValues, (value) => value?.code);
  } else {
    otherCommsCurrentValue = _.mapValues(comissionValues, (value, key) => {
      if (_.includes(ALL_CREDIT_COMMISSION, key)) {
        return null;
      }
      return value?.code;
    });
  }
  return {
    comissionValues,
    otherCommissionsDict,
    otherCommsCurrentValue,
    // 是否展示未开通信用账户提示，（未开通信用账户 且 设置了信用佣金费率）
    showCreditWarnning: !hasCreditAccount && hasSetCreditCommission
  };
}

// 判断客户是否有在途佣金模式的新投顾签约调整了股基
export function checkIfCommisionSigned(disableContractRatioType = {}) {
  return _.includes(disableContractRatioType?.signedType || [], HTSC_COMMISSION_LEVEL);
}

// 错误信息换行转换
export function transformErrorMsg(errorMsg) {
  if (_.isEmpty(errorMsg)) {
    return '';
  }
  const textList = errorMsg.split(regxp.returnLine);
  return _.map(textList, (item, index) => (
    <div key={item + index}>
      {item}
    </div>
  ));
}
