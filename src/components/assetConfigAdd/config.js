// 当前资产
export const DANG_QIAN_ZI_CHAN = 'dangQianZiChan';
// 投资收益
export const TOU_ZI_SHOU_YI = 'touZiShouYi';
// 收益率
export const SHOU_YI_LV = 'shouYiLv';
// 年化收益率
export const NIAN_HUA_SHOU_YI_LV = 'nianHuaShouYiLv';
// 最大回撤
export const ZUI_DA_HUI_CHE = 'zuiDaHuiChe';
// 波动率
export const BO_DONG_LV = 'boDongLv';
// 资产类别
export const ZI_CHAN_LEI_BIE = 'ziChanLeiBie';
// 当前市值
export const DANG_QIAN_SHI_ZHI = 'dangQianShiZhi';
// 配置占比
export const PEI_ZHI_ZHAN_BI = 'peiZhiZhanBi';
// 收益
export const SHOU_YI = 'shouYi';
// 基准指数收益率
export const JI_ZHUN_ZHI_SHU_SHOU_YI_LV = 'jiZhunZhiShuShouYiLv';
// 年化波动率
export const NIAN_HUA_BO_DONG_LV = 'nianHuaBoDongLv';
// 标的名称
export const BIAO_DI_MING_CHENG = 'biaoDiMingCheng';
// 资产分类
export const ZI_CHAN_FEN_LEI = 'ziChanFenLei';
// 收益额
export const SHOU_YI_E = 'shouYiE';
// 当前持仓市值
export const DANG_QIAN_CHI_CANG_SHI_ZHI = 'dangQianChiCangShiZhi';
// 当前持仓占比
export const DANG_QIAN_CHI_CANG_ZHAN_BI = 'dangQianChiCangZhanBi';
// 国内权益资产类别
export const GUO_NEI_QUAN_YI_ZI_CHAN_LEI_BIE = 'guoNeiQuanYiZiChanLeiBie';
// 平均持有时长
export const PING_JUN_CHI_YOU_SHI_CHANG = 'pingJunChiYouShiChang';
// 双边换手率
export const SHUANG_BIAN_HUAN_SHOU_LV = 'shuangBianHuanShouLv';
// 持仓时间最短
export const CHI_CANG_SHI_JIAN_ZUI_DUAN = 'chiCangShiJianZuiDuan';
// 持仓收益率最高
export const CHI_CANG_SHOU_YI_LV_ZUI_GAO = 'chiCangShouYiLvZuiGao';
// 持仓时间最短收益率
export const CHI_CANG_SHI_JIAN_ZUI_DUAN_SHOU_YI_LV = 'chiCangShiJianZuiDuanShouYiLv';
export const ZUI_DUAN_SHOU_YI_LV = 'zuiDuanShouYiLv';
export const ZUI_GAO_SHOU_YI_LV = 'zuiGaoShouYiLv';
export const ZUI_DI_SHOU_YI_LV = 'zuiDiShouYiLv';
// 持仓时间最长
export const CHI_CANG_SHI_JIAN_ZUI_CHANG = 'chiCangShiJianZuiChang';
// 持仓收益率最低
export const CHI_CANG_SHOU_YI_LV_ZUI_DI = 'chiCangShouYiLvZuiDi';
// 持仓时间最长收益率
export const CHI_CANG_SHI_JIAN_ZUI_CHANG_SHOU_YI_LV = 'chiCangShiJianZuiChangShouYiLv';
export const ZUI_CHANG_SHOU_YI_LV = 'zuiChangShouYiLv';
// Calmar比率
export const CALMAR_BI_LV = 'calmarBiLv';
// 夏普比率
export const SHA_PU_BI_LV = 'xiaPuBiLv';
// 盈利股票TOP5
export const YING_LI_GU_PIAO_TOP_5 = 'guPiaoTOP5';
// 行业
export const HANG_YE = 'hangYe';
// 方案对比
export const FANG_AN_DUI_BI = 'fangAnDuiBi';
// 区间收益率
export const QU_JIAN_SHOU_YI_LV = 'quJianShouYiLv';
// 区间波动率
export const QU_JIAN_BO_DONG_LV = 'quJianBoDongLv';

// 需要格式化金额数字的 columns
export const FORMAT_AMOUNT_COLUMNS = [
  DANG_QIAN_ZI_CHAN,
  TOU_ZI_SHOU_YI,
  DANG_QIAN_SHI_ZHI,
  SHOU_YI_E,
  DANG_QIAN_CHI_CANG_SHI_ZHI,
  SHOU_YI,
];
// 需要格式化百分比的 columns
export const FORMAT_PERCENT_COLUMNS = [
  SHOU_YI_LV,
  NIAN_HUA_SHOU_YI_LV,
  ZUI_DA_HUI_CHE,
  BO_DONG_LV,
  PEI_ZHI_ZHAN_BI,
  DANG_QIAN_CHI_CANG_ZHAN_BI,
  CHI_CANG_SHI_JIAN_ZUI_DUAN_SHOU_YI_LV,
  CHI_CANG_SHI_JIAN_ZUI_CHANG_SHOU_YI_LV,
  ZUI_DUAN_SHOU_YI_LV,
  ZUI_CHANG_SHOU_YI_LV,
  NIAN_HUA_BO_DONG_LV,
  QU_JIAN_SHOU_YI_LV,
  QU_JIAN_BO_DONG_LV,
  JI_ZHUN_ZHI_SHU_SHOU_YI_LV,
];
// 需要格式化天数的 columns
export const FORMAT_DAY_COLUMNS = [
  PING_JUN_CHI_YOU_SHI_CHANG,
];
// 当前持仓配置分布
export const CURRENT_POSITION_CONFIG_COLUMNS = [
  {
    title: '资产类别',
    dataIndex: ZI_CHAN_LEI_BIE,
    key: ZI_CHAN_LEI_BIE,
  },
  {
    title: '当前市值',
    dataIndex: DANG_QIAN_SHI_ZHI,
    key: DANG_QIAN_SHI_ZHI,
  },
  {
    title: '配置占比',
    dataIndex: PEI_ZHI_ZHAN_BI,
    key: PEI_ZHI_ZHAN_BI,
  },
];
// 账户收益表现
export const ACCOUNT_INCOME_SITUATION_COLUMNS = [
  {
    title: '对比维度',
    dataIndex: 'duiBiWeiDu',
    key: 'duiBiWeiDu',
  },
  {
    title: '收益率',
    dataIndex: SHOU_YI_LV,
    key: SHOU_YI_LV,
  },
  {
    title: '年化收益率',
    dataIndex: NIAN_HUA_SHOU_YI_LV,
    key: NIAN_HUA_SHOU_YI_LV,
  },
  {
    title: '最大回撤',
    dataIndex: ZUI_DA_HUI_CHE,
    key: ZUI_DA_HUI_CHE,
  },
  {
    title: '年化波动率',
    dataIndex: BO_DONG_LV,
    key: BO_DONG_LV,
  },
  {
    title: '当前资产',
    dataIndex: DANG_QIAN_ZI_CHAN,
    key: DANG_QIAN_ZI_CHAN,
  },
  {
    title: '账户收益',
    dataIndex: TOU_ZI_SHOU_YI,
    key: TOU_ZI_SHOU_YI,
  },
];

// 大类资产收益贡献
export const LARGE_ASSETS_INCOME_CONTRIBUTION_COLUMNS = [
  {
    title: '资产类别',
    dataIndex: 'ziChanLeiBie',
    key: 'ziChanLeiBie',
  },
  {
    title: '收益',
    dataIndex: SHOU_YI,
    key: SHOU_YI,
  },
  {
    title: '收益率',
    dataIndex: SHOU_YI_LV,
    key: SHOU_YI_LV,
  },
  {
    title: '基准指数收益率',
    dataIndex: JI_ZHUN_ZHI_SHU_SHOU_YI_LV,
    key: JI_ZHUN_ZHI_SHU_SHOU_YI_LV,
  },
  {
    title: '年化收益率',
    dataIndex: NIAN_HUA_SHOU_YI_LV,
    key: NIAN_HUA_SHOU_YI_LV,
  },
  {
    title: '年化波动率',
    dataIndex: NIAN_HUA_BO_DONG_LV,
    key: NIAN_HUA_BO_DONG_LV,
  },
  {
    title: '最大回撤',
    dataIndex: ZUI_DA_HUI_CHE,
    key: ZUI_DA_HUI_CHE,
  },
];
// 产品及股票收益详情
export const PRODUCT_STOCK_INCOME_DETAILS_COLUMNS = [
  {
    title: '标的名称',
    dataIndex: BIAO_DI_MING_CHENG,
    key: BIAO_DI_MING_CHENG,
  },
  {
    title: '资产分类',
    dataIndex: ZI_CHAN_FEN_LEI,
    key: ZI_CHAN_FEN_LEI,
  },
  {
    title: '收益率',
    dataIndex: SHOU_YI_LV,
    key: SHOU_YI_LV,
    defaultSortOrder: 'descend',
    sortDirections: ['ascend', 'descend', 'ascend', 'descend'],
    sorter: (a, b) => a?.[SHOU_YI_LV] - b?.[SHOU_YI_LV],
  },
  {
    title: '收益额',
    dataIndex: SHOU_YI_E,
    key: SHOU_YI_E,
    sortDirections: ['ascend', 'descend', 'ascend', 'descend'],
    sorter: (a, b) => a?.[SHOU_YI_E] - b?.[SHOU_YI_E],
  },
  {
    title: '当前持仓市值',
    dataIndex: DANG_QIAN_CHI_CANG_SHI_ZHI,
    key: DANG_QIAN_CHI_CANG_SHI_ZHI,
  },
  {
    title: '当前持仓占比',
    dataIndex: DANG_QIAN_CHI_CANG_ZHAN_BI,
    key: DANG_QIAN_CHI_CANG_ZHAN_BI,
  },
];
// 股票持仓换手分析
export const STOCK_EXCHANGE_ANALYSIS_COLUMNS = [
  {
    title: '国内权益资产类别',
    dataIndex: GUO_NEI_QUAN_YI_ZI_CHAN_LEI_BIE,
    key: GUO_NEI_QUAN_YI_ZI_CHAN_LEI_BIE,
  },
  {
    title: '平均持有时长',
    dataIndex: PING_JUN_CHI_YOU_SHI_CHANG,
    key: PING_JUN_CHI_YOU_SHI_CHANG,
  },
  {
    title: '双边换手率',
    dataIndex: SHUANG_BIAN_HUAN_SHOU_LV,
    key: SHUANG_BIAN_HUAN_SHOU_LV,
  },
];
// 股票持仓换手分析-最长最短收益率不同
export const STOCK_EXCHANGE_ANALYSIS_COLUMNS_ZERO = [
  {
    title: '持仓时间最短',
    dataIndex: CHI_CANG_SHI_JIAN_ZUI_DUAN,
    key: CHI_CANG_SHI_JIAN_ZUI_DUAN,
  },
  {
    title: '收益率',
    dataIndex: CHI_CANG_SHI_JIAN_ZUI_DUAN_SHOU_YI_LV,
    key: CHI_CANG_SHI_JIAN_ZUI_DUAN_SHOU_YI_LV,
  },
  {
    title: '持仓时间最长',
    dataIndex: CHI_CANG_SHI_JIAN_ZUI_CHANG,
    key: CHI_CANG_SHI_JIAN_ZUI_CHANG,
  },
  {
    title: '收益率',
    dataIndex: CHI_CANG_SHI_JIAN_ZUI_CHANG_SHOU_YI_LV,
    key: CHI_CANG_SHI_JIAN_ZUI_CHANG_SHOU_YI_LV,
  },
];
// 股票持仓换手分析-最长、最短时间相同且股票数量大于1，区分最高、最低收益率
export const STOCK_EXCHANGE_ANALYSIS_COLUMNS_ONE = [
  {
    title: '持仓收益最高',
    dataIndex: CHI_CANG_SHOU_YI_LV_ZUI_GAO,
    key: CHI_CANG_SHOU_YI_LV_ZUI_GAO,
  },
  {
    title: '区间收益率',
    dataIndex: ZUI_GAO_SHOU_YI_LV,
    key: ZUI_GAO_SHOU_YI_LV,
  },
  {
    title: '持仓收益最低',
    dataIndex: CHI_CANG_SHOU_YI_LV_ZUI_DI,
    key: CHI_CANG_SHOU_YI_LV_ZUI_DI,
  },
  {
    title: '区间收益率',
    dataIndex: ZUI_DI_SHOU_YI_LV,
    key: ZUI_DI_SHOU_YI_LV,
  },
];
// 最长最短收益率不同
export const HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO = 0;
// 最长、最短时间相同且股票数量大于1，区分最高、最低收益率
export const HUAN_SHOU_ZHAN_SHI_CHANG_JING_ONE = 1;
// 最长、最短时间相同且股票数量为1（不区分最高、最低收益率）
export const HUAN_SHOU_ZHAN_SHI_CHANG_JING_TWO = 2;
// 股票持仓换手分析-最长、最短时间相同且股票数量为1（不区分最高、最低收益率）
export const STOCK_EXCHANGE_ANALYSIS_COLUMNS_TWO = [
  {
    title: '持仓标的',
    dataIndex: CHI_CANG_SHOU_YI_LV_ZUI_GAO,
    key: CHI_CANG_SHOU_YI_LV_ZUI_GAO,
  },
  {
    title: '区间收益率',
    dataIndex: ZUI_GAO_SHOU_YI_LV,
    key: ZUI_GAO_SHOU_YI_LV,
  },
];

// 权益公募换手分析
export const PUBLIC_EXCHANGE_ANALYSIS_COLUMNS = (flag) => {
  let chiCangBiaoDiTitlt = '持仓时间最短';
  if (flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ONE) {
    chiCangBiaoDiTitlt = '持仓收益最高';
  }
  if (flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_TWO) {
    chiCangBiaoDiTitlt = '持仓标的';
  }
  const column1 = [
    {
      title: '国内权益资产类别',
      dataIndex: GUO_NEI_QUAN_YI_ZI_CHAN_LEI_BIE,
      key: GUO_NEI_QUAN_YI_ZI_CHAN_LEI_BIE,
      width: 100,
    },
    {
      title: '平均持有时长',
      dataIndex: PING_JUN_CHI_YOU_SHI_CHANG,
      key: PING_JUN_CHI_YOU_SHI_CHANG,
      width: 60,
    },
    {
      title: '双边换手率',
      dataIndex: SHUANG_BIAN_HUAN_SHOU_LV,
      key: SHUANG_BIAN_HUAN_SHOU_LV,
      width: 60,
    },
    {
      title: chiCangBiaoDiTitlt,
      dataIndex: flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO
        ? CHI_CANG_SHI_JIAN_ZUI_DUAN : CHI_CANG_SHOU_YI_LV_ZUI_GAO,
      key: flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO
        ? CHI_CANG_SHI_JIAN_ZUI_DUAN : CHI_CANG_SHOU_YI_LV_ZUI_GAO,
      width: 180,
    },
    {
      title: '区间收益率',
      dataIndex: flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO
        ? ZUI_DUAN_SHOU_YI_LV : ZUI_GAO_SHOU_YI_LV,
      key: flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO
        ? ZUI_DUAN_SHOU_YI_LV : ZUI_GAO_SHOU_YI_LV,
      width: 60,
    },
  ];
  const column2 = [
    {
      title: flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO ? '持仓时间最长' : '持仓收益最低',
      dataIndex: flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO
        ? CHI_CANG_SHI_JIAN_ZUI_CHANG : CHI_CANG_SHOU_YI_LV_ZUI_DI,
      key: flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO
        ? CHI_CANG_SHI_JIAN_ZUI_CHANG : CHI_CANG_SHOU_YI_LV_ZUI_DI,
      width: 180,
    },
    {
      title: '区间收益率',
      dataIndex: flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO
        ? ZUI_CHANG_SHOU_YI_LV : ZUI_DI_SHOU_YI_LV,
      key: flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO
        ? ZUI_CHANG_SHOU_YI_LV : ZUI_DI_SHOU_YI_LV,
      width: 60,
    },
  ];
  return flag === HUAN_SHOU_ZHAN_SHI_CHANG_JING_TWO ? column1 : [...column1, ...column2];
};

// 股票收益表现
export const STOCK_INCOME_SITUATION_COLUMNS = [
  {
    title: '对比维度',
    dataIndex: 'duiBiWeiDu',
    key: 'duiBiWeiDu',
  },
  {
    title: '收益率',
    dataIndex: SHOU_YI_LV,
    key: SHOU_YI_LV,
  },
  {
    title: '年化收益率',
    dataIndex: NIAN_HUA_SHOU_YI_LV,
    key: NIAN_HUA_SHOU_YI_LV,
  },
  {
    title: '最大回撤',
    dataIndex: ZUI_DA_HUI_CHE,
    key: ZUI_DA_HUI_CHE,
  },
  {
    title: '年化波动率',
    dataIndex: NIAN_HUA_BO_DONG_LV,
    key: NIAN_HUA_BO_DONG_LV,
  },
  {
    title: '夏普比率',
    dataIndex: SHA_PU_BI_LV,
    key: SHA_PU_BI_LV,
  },
  {
    title: 'Calmar比率',
    dataIndex: CALMAR_BI_LV,
    key: CALMAR_BI_LV,
  },
];
// 股票Top5
export const STOCK_PROFIT_AND_LOSS_RANK_COLUMNS = (flag) => [
  {
    title: flag ? '盈利股票TOP5' : '亏损股票TOP5',
    dataIndex: YING_LI_GU_PIAO_TOP_5,
    width: 150,
    key: YING_LI_GU_PIAO_TOP_5,
  },
  {
    title: '行业',
    dataIndex: HANG_YE,
    key: HANG_YE,
    width: 60,
  },
  {
    title: '收益额',
    dataIndex: SHOU_YI_E,
    key: SHOU_YI_E,
    width: 60,
  },
  {
    title: '收益率',
    dataIndex: SHOU_YI_LV,
    key: SHOU_YI_LV,
    width: 60,
  },
];
// 组合业绩
export const COMBINATION_PERFORMANCE_COLUMNS = [
  {
    title: '方案对比',
    dataIndex: FANG_AN_DUI_BI,
    key: FANG_AN_DUI_BI,
  },
  {
    title: '区间收益率',
    dataIndex: QU_JIAN_SHOU_YI_LV,
    key: QU_JIAN_SHOU_YI_LV,
  },
  {
    title: '区间波动率',
    dataIndex: QU_JIAN_BO_DONG_LV,
    key: QU_JIAN_BO_DONG_LV,
  },
  {
    title: '最大回撤',
    dataIndex: ZUI_DA_HUI_CHE,
    key: ZUI_DA_HUI_CHE,
  },
  {
    title: '夏普比率',
    dataIndex: SHA_PU_BI_LV,
    key: SHA_PU_BI_LV,
  },
  {
    title: 'Calmar比率',
    dataIndex: CALMAR_BI_LV,
    key: CALMAR_BI_LV,
  },
];
// 锚点滚动的id位置名称
export const ZHANG_HU_FEN_XI = 'ZhangHuFenXi';
export const SHI_CHANGE_GUAN_DIAN = 'ShiChangGuanDian';
export const PEI_ZHI_JIAN_YI = 'PeiZhiJianYi';
// 滚动导航配置
export const INTRO_CONFIG = [
  {
    key: '1',
    id: ZHANG_HU_FEN_XI,
    name: '账户分析',
    enName: 'ACCOUNT ANALYSIS'
  },
  {
    key: '2',
    id: SHI_CHANGE_GUAN_DIAN,
    name: '市场观点',
    enName: 'MARKET VIEW'
  },
  {
    key: '3',
    id: PEI_ZHI_JIAN_YI,
    name: '配置建议',
    enName: 'CONFIGURATION SUGGESTIONS'
  },
];

// 模块对应关系
export const MODULES_LIST = [
  {
    id: 'ZhangHuZongTiQingKuang01',
    name: '账户总体情况',
    hasReportFlag: true,
    children: [
      {
        id: 'ZhangHuShouYiBiaoXian',
        name: '账户收益表现',
        hasReportFlag: true,
      },
      {
        id: 'YeJiQuShiJiHuiCheBiaoXian',
        name: '业绩趋势及回撤表现',
        hasReportFlag: true,
      }
    ]
  },
  {
    id: 'DaLeiZiChanPeiZhiFenXi02',
    name: '大类资产配置分析',
    hasReportFlag: true,
    children: [
      {
        id: 'DangQianChiCangPeiZhiFenBu',
        name: '当前持仓配置分布',
        hasReportFlag: true,
      },
      {
        id: 'DaLeiZiChanShouYiGongXian',
        name: '大类资产收益贡献',
        hasReportFlag: true,
      }
    ]
  },
  {
    id: 'GuoNeiQuanYiPeiZhiFenXi03',
    name: '国内权益配置分析',
    children: [
      {
        id: 'GuPiaoShouYiBiaoXian',
        name: '股票收益表现',
        hasReportFlag: true,
      },
      {
        id: 'GuPiaoChiCangHuanShouFenXi',
        name: '股票持仓换手分析',
        hasReportFlag: null,
      },
      {
        id: 'GuPiaoChiCangHangYeFenXi',
        name: '股票持仓行业分析',
        hasReportFlag: null,
      },
      {
        id: 'GuPiaoChiCangFengGeFenXi',
        name: '股票持仓风格分析',
        hasReportFlag: null,
      },
      {
        id: 'GuPiaoYingKuiTOP5',
        name: '股票盈亏TOP5',
        hasReportFlag: null,
      },
      {
        id: 'QuanYiGongMuJiJinShouYiBiaoXian',
        name: '权益公募基金收益表现',
        hasReportFlag: null,
      },
      {
        id: 'QuanYiGongMuHuangShouFenXi',
        name: '权益公募换手分析',
        hasReportFlag: null,
      },
      {
        id: 'SaiDaoPeiZhiFenXi',
        name: '赛道配置分析',
        hasReportFlag: null,
      }
    ]
  },
  {
    id: 'ShouYiMingXi04',
    name: '收益明细',
    hasReportFlag: true,
    children: [
      {
        id: 'GuPiaoShouYiXiangQing',
        name: '产品及股票收益详情',
      }
    ]
  }
];

export const STATUS_MAP = {
  ok: 'OK',
  allZero: 'ALL_ZERO',
  failed: 'FAILED',
};

export const LABEL_MY_PLAN = '我的配置方案';
export const LABEL_DEFAULT_PLAN = '推荐配置方案';

export const CHART_SYMBOLS = [
  'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAfCAMAAAAhm0ZxAAABPlBMVEUAAAAAAAD///+AgICAgP9VqqqqqqqAgL+Av79mmZmZmcyAgKqAqtVtkraSkraAn79xjsaAmcyLotGAlb92ncSJncSAkraApMh3mbuImcyAn7+HpcOAnMaGobyGocmAmb+ApsyAosWFm7yFm8iAn8qFmcKAncSEocaApMiEnsGAmcSAn8eDosmAnsOHnsODn8GDn8iAn8aGn8aGnsiCoMSCn8GFociCnMaCoMiEncSEosSEn8TK1+WCoMfH1OWEosjFzuLGzuO1xt25yuGxxN3e5/Hf5/LP2ujR2ur09/r09/rz9vrz9vnw8/jw8/js8fbs8fb9/f78/f79/f78/f79/f77/P3+/v/+///9/f7+/v7+/v/9/f7////+/v7+/v/////+v2j+wWz+yoH+yoL+yoP/6cv/6cz/6s3///9JdwqkAAAAYXRSTlMAAQECAgMDBAQFBQYGBwcICQoLDA0NDg4PDxAREhMTFBQWFxcYGRobHB0eICEiIiUlKCgqKy0uMTM0NDg6Ozs8Pj9MTVJeX29vpaanqautsLHl5ubn5+j09PX19fb5+vr6lU0slwAAAAFiS0dEabxrxLQAAAF3SURBVCjPnZLNbhRBDISryp4Jyy6ROCCFC+//XhyRokB2kyzTdnHoCUII5UD/qCV/7W7ZVcD/Df51AgD8R4xzc497x9wXyVdme97gnKRIgobtntQJkKQkCQSM7u62YeQkESJFwC13VTeABKiIUKRE2t2jyELPPEVqyUVBwMPb2AibSFARuskl706H0/np8Zuka9h2UgotuS5fPt0C73F/+Eq0o+0IRea6rp/vbmuMxpF4hnvmUczMPN7WBrj58TG7qkQKpKTMAwoAUFgzJZEQSSqo497hxkmMGQZISjzvUgQulEgCAkCC/InY2UaCxGQ27Mv3WIN5w4eLDXv2zHa3z4s+CMDD/cXdtoG07S53/xjn9d3L9fLU7Zr1we7uIfLyLHV3jTGmSmm3R4QETx1GjTGG2w7O+rn/W2Ns27aNru4gSFre3x6TdVU74S7y2t3M3/p1VRtpuAvhKm2vundV207ADTtcu188/eLdmf/yme23/PmWr38Bdg408EbhcLEAAAAASUVORK5CYII=',
  'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAfCAMAAAAhm0ZxAAAAWlBMVEUAAACNocCEocODocSEncKFoMSEocWFosaGocj7+/2FociDoMT+/v/8/f7y9fn9/v7+/v/4+fvs8Pb19/rO2+nh5/ClutapvNi4yd64yuFrrvT////T5vuIvfbfL7xSAAAAGnRSTlMABgwWERwjKC/UOzT17aLr6tmrnGlVSkEvK/IQ+MUAAADBSURBVCjP1dLLDgIhDAVQlUcZGZz3s/D/vylUiOCYzMKVd3vS2yZw+btcU77KLeSgCXjiD+KchXBeIhHbhqquho0RljS1GNJOBRKtDVq3O4vNSpjGPIkH2j3E4kN49IPJhKzQkTmspCCLlUzIGvdXsJaCpVKqhGwOfGlh2b6DLSbeaRayfJ8aDYaYUdG+/E7Qc9/VXT9roDvfFgb1PUSHMbJYSghKaaWAiCrJIkoAkJGSJaREOnm/k3c/+y/lP/s1T+XhDc5Aof4vAAAAAElFTkSuQmCC',
];

export const RGBA_COLORS = [
  'rgba(254, 191, 104, .1)',
  'rgba(101, 174, 252, .1)',
];

export const LABEL_COLORS = [
  '#febf68', '#65aefc',
];
