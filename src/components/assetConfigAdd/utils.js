import React from 'react';
import includes from 'lodash/includes';
import isNil from 'lodash/isNil';
import isNull from 'lodash/isNull';

import {
  formatToUnit,
  formatRatio,
} from '@/helper/number';
import { ToolTipCell } from '@/components/common/table';
import {
  FORMAT_AMOUNT_COLUMNS,
  FORMAT_PERCENT_COLUMNS,
  FORMAT_DAY_COLUMNS,
  STATUS_MAP,
} from './config';

function renderCell(text, record) {
  if (record.flag) {
    return null;
  }
  if (text === '' || isNull(text)) {
    return '--';
  }
  return <ToolTipCell tipContent={text} cellText={text} />;
}

// 将表格里的数字转换成金额或者百分比
export function transferNumberColumn(column) {
  if (includes(FORMAT_AMOUNT_COLUMNS, column.key)) {
    return {
      ...column,
      render: (text, record) => {
        if (record.flag) {
          return null;
        }
        if (isNil(text)) {
          return '--';
        }
        const value = formatToUnit({
          num: text,
          floatLength: 2,
          unit: '元',
          isThousandFormat: false,
        });
        return value;
      },
    };
  }
  if (includes(FORMAT_DAY_COLUMNS, column.key)) {
    return {
      ...column,
      render: (text) => {
        if (isNil(text)) {
          return '--';
        }
        const value = formatToUnit({
          num: text,
          floatLength: 0,
          unit: '天',
          isThousandFormat: false,
        });
        return value;
      },
    };
  }
  if (includes(FORMAT_PERCENT_COLUMNS, column.key)) {
    return {
      ...column,
      render: (text) => {
        if (isNil(text)) {
          return '--';
        }
        const value = formatRatio(text);
        return value;
      },
    };
  }
  return {
    ...column,
    render: (text, record) => renderCell(text, record),
  };
}

export function checkStatusCode(status) {
  return {
    // 是否请求成功
    isOk: status === STATUS_MAP.ok,
    // 是否成功失败，数据为 null 的情况
    isFailed: status === STATUS_MAP.failed,
    // 是否全为 0
    isAllZero: status === STATUS_MAP.allZero,
  };
}
