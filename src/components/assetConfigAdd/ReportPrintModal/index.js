import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { Modal, Checkbox } from 'antd';
import { logCommon } from '@/decorators/logable';
import InfoTitle from '@/components/common/InfoTitle';
import {
  ZHANG_HU_FEN_XI,
  SHI_CHANGE_GUAN_DIAN,
} from '@/components/assetConfigAdd/config';
import {
  forEach, isEmpty, size, map,
} from 'lodash';
import IFWrap from '@/components/common/IFWrap';
import { isNull } from '@/helper/check';
import styles from './index.less';

const MODULES_TIP_TEXT = '（报告中仅展示绝对盈亏前五数据）';
const MODULES_TIP_KEY = 'ShouYiMingXi04';

export default function ReportPrintModal(props) {
  const {
    visible,
    onSave,
    onClose,
    moduleConfig,
    marketOpinion,
    modulesConfig,
    opinionList,
    checkOpinionList,
    checkModuleConfig,
    updateReduxData,
  } = props;
  const [checkOpinionListLocal, setCheckOpinionList] = useState([]);
  const [checkModuleConfigLocal, setCheckModuleConfig] = useState([]);
  const [checkModuleConfigTextLocal, setCheckModuleTextConfig] = useState([]);

  // 市场观点数据
  const getOptionsList = () => {
    const newList = [];
    const newCheckList = [];
    forEach(marketOpinion, (item) => {
      const newOpinionList = [];
      forEach(item?.opinionList, (item1) => {
        if (!item1?.unChecked) {
          newCheckList.push(item1?.assetTypeName ?? '',);
        }
        newOpinionList.push({
          label: item1?.assetTypeName ?? '',
          checked: !(item1?.unChecked ?? true),
          disabled: false,
          value: item1?.assetTypeName ?? '',
        });
      });
      newList.push({
        name: item?.assetName ?? '',
        id: item?.assetName ?? '',
        children: newOpinionList,
        disabled: false,
      });
    });
    if (isEmpty(checkOpinionList)) {
      setCheckOpinionList(newCheckList);
    } else {
      setCheckOpinionList(checkOpinionList);
    }
    updateReduxData({
      opinionList: newList,
    });
  };

  // 模块数据处理
  const getmoduleDetailList = () => {
    const newList = [];
    const newCheckList = [];
    const newCheckTextList = [];
    forEach(moduleConfig, (item) => {
      const newChildrenList = [];
      forEach(item?.children, (item1) => {
        const checked = item1?.checked ?? true;
        if (checked) {
          newCheckList.push(item1?.value ?? '');
          newCheckTextList.push(item1?.label ?? '');
        }
        newChildrenList.push({
          label: item1?.label ?? '',
          checked,
          disabled: item1?.disabled,
          value: item1?.value ?? '',
        });
      });
      newList.push({
        name: item?.label ?? '',
        id: item?.value ?? '',
        children: newChildrenList,
        disabled: item?.disabled,
        tips: item?.value === MODULES_TIP_KEY ? MODULES_TIP_TEXT : ''
      });
    });
    if (isEmpty(checkModuleConfig)) {
      setCheckModuleConfig(newCheckList);
    } else {
      setCheckModuleConfig(checkModuleConfig);
    }
    setCheckModuleTextConfig(newCheckTextList);
    updateReduxData({
      modulesConfig: newList,
    });
  };
  useEffect(() => {
    getOptionsList();
    getmoduleDetailList();
  }, []);
  const handleChangeModules = (checkValue) => {
    setCheckModuleConfig(checkValue);
  };
  const moduleDetailRender = () => map(modulesConfig, (item) => {
    const hasTips = !isNull(item?.tips);
    return (
      <div className={styles.checkBox} key={item?.id ?? ''}>
        <div className={styles.checkBoxTit}>{item?.name ?? ''}：</div>
        <div className={cx({
          [styles.checkGroupBox]: !hasTips,
          [styles.checkGroupTipsBox]: hasTips,
        })}
        >
          <Checkbox.Group
            options={item?.children ?? []}
            disabled={item?.disabled}
            value={checkModuleConfigLocal}
            onChange={handleChangeModules}
          />
          <IFWrap when={hasTips}>
            <div className={styles.tips}>{item?.tips}</div>
          </IFWrap>
        </div>
      </div>
    );
  });
  const handleChangeOpinion = (checkValue) => {
    setCheckOpinionList(checkValue);
  };
  const opinionRender = () => map(opinionList, (item) => (
    <div className={styles.checkBox} key={item?.id ?? ''}>
      <div className={styles.checkBoxTit}>{item?.name ?? ''}：</div>
      <div className={styles.checkOpinionGroupBox}>
        <Checkbox.Group
          onChange={handleChangeOpinion}
          options={item?.children ?? []}
          disabled={item?.disabled}
          value={checkOpinionListLocal}
        />
      </div>
    </div>
  ));
  const titleRender = () => (
    <div className={styles.modalTit}>
      <div className={styles.modalMainTit}>请确定报告输出内容</div>
      <div className={styles.modalSecondTit}>
        （以下勾选为前置步骤已选内容）
      </div>
    </div>
  );
  const handleToSave = () => {
    if (isEmpty(checkModuleConfigLocal) && isEmpty(checkOpinionListLocal)) {
      Modal.error({
        title: '账户分析和市场观点栏目必选一个',
      });
    } else {
      logCommon({
        type: 'Submit',
        payload: {
          name: '资产配置-选择报告输出内容-确定',
          value: `${(checkModuleConfigTextLocal).toString()},${(checkOpinionListLocal).toString()}`
        },
      });
      updateReduxData({
        checkModuleConfig: checkModuleConfigLocal,
        checkOpinionList: checkOpinionListLocal,
      });
      onSave({
        data: {
          [ZHANG_HU_FEN_XI]: checkModuleConfigLocal,
          [SHI_CHANGE_GUAN_DIAN]: checkOpinionListLocal,
        },
      });
      onClose();
    }
  };
  // 点击取消按钮事件
  const handleOnCancel = () => {
    logCommon({
      type: 'ButtonClick',
      payload: {
        name: '资产配置-选择报告输出内容-取消',
      },
    });
    onClose();
  };
  return (
    <Modal
      className={styles.ReportPrintModal}
      width={780}
      visible={visible}
      title={titleRender()}
      onOk={handleToSave}
      onCancel={handleOnCancel}
      maskClosable={false}
      onClose={onClose}
    >
      <IFWrap when={size(moduleConfig) !== 0}>
        <div className={styles.moduleBox}>
          <InfoTitle head="账户分析" />
          {moduleDetailRender()}
        </div>
      </IFWrap>
      <IFWrap when={size(marketOpinion) !== 0}>
        <>
          <InfoTitle head="市场观点" />
          {opinionRender()}
        </>
      </IFWrap>

    </Modal>
  );
}

ReportPrintModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onSave: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  moduleConfig: PropTypes.array.isRequired,
  marketOpinion: PropTypes.array.isRequired,
  modulesConfig: PropTypes.array.isRequired,
  opinionList: PropTypes.array.isRequired,
  updateReduxData: PropTypes.func.isRequired,
  checkOpinionList: PropTypes.array.isRequired,
  checkModuleConfig: PropTypes.array.isRequired,
};
