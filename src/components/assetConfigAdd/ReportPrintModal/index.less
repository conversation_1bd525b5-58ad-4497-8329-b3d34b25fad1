.ReportPrintModal {
  .moduleBox {
    margin-bottom: 29px;
  }

  .modalTit {
    display: flex;
    align-items: center;

    .modalMainTit {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }

    .modalSecondTit {
      font-size: 14px;
      color: #999;
    }
  }

  .checkBox {
    display: flex;

    .checkBoxTit {
      min-width: 126px;
      height: 20px;
      font-size: 14px;
      text-align: right;
      color: #666;
      line-height: 20px;
    }

    .checkGroupBox {
      flex: 1;
      display: flex;
      align-items: center;

        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .@{ant-prefix}-checkbox-group {
          width: 100%;
        }

        .@{ant-prefix}-checkbox-wrapper {
          margin: 0 0 8px;
          min-width: 78px;
          width: 33.33%;
        }
      }
    }

    .checkGroupTipsBox {
      display: flex;
      align-items: center;

      .tips {
        font-size: 12px;
        color: #999;
        margin-left: 10px;
      }
    }

    .checkOpinionGroupBox {
      flex: 1;
      display: flex;
      align-items: center;

      .tips {
        font-size: 12px;
        color: #999;
        margin-left: 10px;
      }

        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .@{ant-prefix}-checkbox-group {
          width: 100%;
        }

        .@{ant-prefix}-checkbox-wrapper {
          margin: 0 0 8px;
          min-width: 78px;
          width: 20%;
        }
      }
    }
  }
}