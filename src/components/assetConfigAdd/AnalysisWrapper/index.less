.wrapper {
  min-width: 1260px;
  background-color: #fff;
  border: 1px solid #e4ecf1;
  padding-bottom: 20px;
  border-radius: 12px;
  margin-top: 30px;
  min-height: 168px;
  position: relative;
  overflow: hidden;

  &::after, &::before {
    content: '';
    height: 240px;
    position: absolute;
  }

  &::after {
    width: calc(100% + 2px);
    z-index: 0;
    top: -1px;
    left: -1px;
    border-radius: 12px 12px 0 0;
    box-shadow: -4px -4px 8px #f0f9ff, 4px 0 8px #f0f9ff;
  }

  &::before {
    width: 100%;
    background: #fff;
    z-index: 1;
    top: 0;
    left: 0;
    border-radius: 12px 12px 0 0;
  }

  .header {
    height: 48px;
    background-color: #fff;
    background-image: linear-gradient(270deg, rgba(239, 249, 255, 0) 0%, #eff9ff 100%);
    border-radius: 12px 12px 0 0;
    position: relative;
    font-size: 16px;
    font-weight: bold;
    color: #108ee9;
    line-height: 48px;
    text-indent: 57px;
    z-index: 1;

    .index {
      position: absolute;
      width: 28px;
      height: 38px;
      background: #108ee9;
      border-radius: 0 0 4px 4px;
      top: 0;
      left: 19px;
      font-size: 20px;
      font-weight: normal;
      color: #fff;
      line-height: 46px;
      text-indent: 0;
      text-align: center;
    }
  }

  .content {
    padding: 0 20px;
    background-color: #fff;
    z-index: 1;
    position: relative;
  }
}
