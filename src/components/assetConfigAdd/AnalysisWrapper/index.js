import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

import styles from './index.less';

export default function AnalysisWrapper(props) {
  const {
    className,
    title,
    index,
    children,
  } = props;

  const fillZero = (number) => {
    if (number > 9) {
      return number;
    }
    return `0${number}`;
  };

  let wrapperClass = classNames(styles.wrapper);
  if (className) {
    wrapperClass = classNames(styles.wrapper, className);
  }
  return (
    <div className={wrapperClass}>
      <div className={styles.header}>
        <div className={styles.index}>
          {fillZero(index)}
        </div>
        {title}
      </div>
      <div className={styles.content}>
        {children}
      </div>
    </div>
  );
}

AnalysisWrapper.propTypes = {
  className: PropTypes.string,
  title: PropTypes.element.isRequired,
  index: PropTypes.number.isRequired,
  children: PropTypes.element.isRequired,
};

AnalysisWrapper.defaultProps = {
  className: '',
};
