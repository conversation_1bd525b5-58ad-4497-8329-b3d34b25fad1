.introWrap {
  position: fixed;
  right: 0;
  top: 200px;
  font-size: 14px;
  color: #666;
  z-index: 1000;

  .introItemActive {
    background-color: #f3f7fd;
    font-weight: bold;
    color: #108ee9;
  }

  .intro {
    width: 104px;
    border-radius: 2px;
    background: #fff;
    box-shadow: 0 1px 5px 2px rgba(0, 0, 0, 0.12);
  }

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .@{ant-prefix}-timeline-item {
      height: 40px;
      padding: 10px;

      &:last-child {
        padding-bottom: 0;
      }
    }

    .@{ant-prefix}-timeline-item-tail {
      border-left: 1px dashed #dedede;
      top: 18px;
      left: 13px;
      z-index: 2;
    }

    .@{ant-prefix}-timeline-item-head {
      width: 8px;
      height: 8px;
      top: 18px;
      border: 1px solid transparent;
      z-index: 3;
    }

    .@{ant-prefix}-timeline-item-content {
      top: 0;
    }
  }

  img {
    width: 40px;
    height: 40px;
    margin-bottom: 15px;
  }

  .intro {
    .introItem {
      cursor: pointer;
      height: 30px;
    }
  }
}