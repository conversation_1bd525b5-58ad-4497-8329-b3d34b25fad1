/*
 * @Description: 页面引导组件
 * @Author: <PERSON><PERSON>xia<PERSON>
 * @Date: 2022-11-17 11:27:42
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Timeline } from 'antd';
import cx from 'classnames';
import { logCommon } from '@/decorators/logable';
import map from 'lodash/map';
import IFWrap from '@/components/common/IFWrap';
import foldImg from './images/fold.png';
import unfoldImg from './images/unfold.png';
import styles from './index.less';

const Intro = (props) => {
  const {
    introStep,
    handleClickIntroItem,
    config,
  } = props;

  // 控制是否展开引导
  const [showIntro, setShowIntro] = useState(true);

  const handleToShowIcon = () => {
    logCommon({
      type: 'Click',
      payload: {
        name: `资产配置-生产报告-目录-${showIntro ? '收起' : '展开'}`,
      },
    });
    setShowIntro(!showIntro);
  };
  return (
    <div className={styles.introWrap}>
      <div className={styles.icon}>
        <img
          src={showIntro ? unfoldImg : foldImg}
          onClick={handleToShowIcon}
        />
      </div>
      <IFWrap when={showIntro}>
        <div className={styles.intro}>
          <Timeline>
            {map(config, (item, index) => {
              const introItemActiveCls = cx({
                [styles.introItemActive]: introStep === index,
              });
              return (
                <Timeline.Item
                  color={introStep === index ? '#108ee9' : '#ccc'}
                  className={introItemActiveCls}
                >
                  <div
                    onClick={() => { handleClickIntroItem(item.id, index); }}
                    className={styles.introItem}
                  >
                    {item.name}
                  </div>
                </Timeline.Item>
              );
            })}
          </Timeline>
        </div>
      </IFWrap>
    </div>
  );
};

export default Intro;

Intro.propTypes = {
  handleClickIntroItem: PropTypes.func.isRequired,
  introStep: PropTypes.number,
  config: PropTypes.array.isRequired,
};

Intro.defaultProps = {
  introStep: -1,
};
