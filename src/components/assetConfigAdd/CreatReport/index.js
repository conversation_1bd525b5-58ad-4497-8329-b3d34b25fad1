import React, { PureComponent } from 'react';
import { withRouter } from 'dva/router';
import PropTypes from 'prop-types';
import MarketOpinion from '@/components/assetConfigAdd/MarketOpinion';
import ConfigurationSuggestions from '@/components/assetConfigAdd/ConfigurationSuggestions';
import AccountAnalysis from '@/components/assetConfigAdd/AccountAnalysis';
import { filter, map } from 'lodash';
import { logCommon } from '@/decorators/logable';
import { autobind } from 'core-decorators';
import {
  INTRO_CONFIG,
  ZHANG_HU_FEN_XI,
  SHI_CHANGE_GUAN_DIAN,
  PEI_ZHI_JIAN_YI,
} from '@/components/assetConfigAdd/config';
import IFWrap from '@/components/common/IFWrap';
import Intro from './Intro';
import TopTit from './TopTit';
import styles from './index.less';

@withRouter
export default class CreatReport extends PureComponent {
  static contextTypes = {
    push: PropTypes.func.isRequired,
    replace: PropTypes.func.isRequired,
  };

  static propTypes = {
    location: PropTypes.object.isRequired,
    // 更新错误状态，重试按钮
    updateErrorFlag: PropTypes.func.isRequired,
    // 审批按钮
    flowButtonsAndApprovers: PropTypes.object.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      // 引导组件定位到哪一步
      introStep: -1,
    };
  }

  @autobind
  handleClickIntroItem(id, index) {
    document.getElementById(id).scrollIntoView();
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-生产报告-切换目录',
        value: this.getConfigList()?.[index]?.name ?? ''
      },
    });
    this.setState({ introStep: index });
  }

  // 获取最终的右边导航数据
  getConfigList = () => {
    const {
      location: {
        query: { showFenxi = true, showGuandian = true } = {},
      },
    } = this.props;
    let newConfigList = INTRO_CONFIG;
    if (!showFenxi) {
      newConfigList = filter(
        newConfigList,
        (item) => item?.id !== ZHANG_HU_FEN_XI
      );
    }
    if (!showGuandian) {
      newConfigList = filter(
        newConfigList,
        (item) => item?.id !== SHI_CHANGE_GUAN_DIAN
      );
    }
    return map(newConfigList, (item, index) => ({
      no: index + 1,
      ...item,
    }));
  };

  renderModule = () => {
    const { updateErrorFlag, flowButtonsAndApprovers } = this.props;
    const newConfigList = this.getConfigList();
    return map(newConfigList, (item, index) => {
      let contentNode = '';
      if (item?.id === ZHANG_HU_FEN_XI) {
        contentNode = (
          <AccountAnalysis
            updateErrorFlag={updateErrorFlag}
          />
        );
      }
      if (item?.id === SHI_CHANGE_GUAN_DIAN) {
        contentNode = (
          <div className={styles.marketOpinion}>
            <MarketOpinion updateErrorFlag={updateErrorFlag} />
          </div>
        );
      }
      if (item?.id === PEI_ZHI_JIAN_YI) {
        contentNode = (
          <ConfigurationSuggestions
            updateErrorFlag={updateErrorFlag}
            flowButtonsAndApprovers={flowButtonsAndApprovers}
          />
        );
      }
      return (
        <div id={item?.id}>
          <IFWrap when={index !== 0}>
            <div className={styles.splitBox} />
          </IFWrap>
          <TopTit
            data={{
              no: item?.no,
              tit: item?.name,
              titEn: item?.enName,
            }}
          />
          {contentNode}
        </div>
      );
    });
  };

  render() {
    const { introStep } = this.state;
    return (
      <div className={styles.CreatReport}>
        <Intro
          introStep={introStep}
          handleClickIntroItem={this.handleClickIntroItem}
          config={this.getConfigList()}
        />
        {this.renderModule()}
      </div>
    );
  }
}
