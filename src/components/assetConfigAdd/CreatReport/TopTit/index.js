import React from 'react';
import PropTypes from 'prop-types';
import ImgStep1 from '@/components/assetConfigAdd/static/step1.png';
import ImgStep2 from '@/components/assetConfigAdd/static/step2.png';
import ImgStep3 from '@/components/assetConfigAdd/static/step3.png';
import styles from './index.less';

export default function TopTit(props) {
  const {
    data: { no = 1, tit = '标题', titEn = 'biaoti' },
  } = props;
  // 获取图片地址
  const getImgSrc = () => {
    if (no === 1) {
      return ImgStep1;
    }
    if (no === 2) {
      return ImgStep2;
    }
    return ImgStep3;
  };
  return (
    <div className={styles.topTit}>
      <div className={styles.box}>
        <div className={styles.titLeft} />
        <img src={getImgSrc()} className={styles.noImgBox} />
        <div className={styles.titRight}>
          <div className={styles.textTit}>{tit}</div>
          <div className={styles.textEnTit}>{titEn}</div>
        </div>
      </div>
    </div>
  );
}

TopTit.propTypes = {
  data: PropTypes.object.isRequired,
};
