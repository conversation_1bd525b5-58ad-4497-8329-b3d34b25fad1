.searchBox {
  width: 300px;
  height: 36px;
  border-radius: 18px;
  overflow: hidden;
  background: #fff;
  display: flex;
  align-items: center;
  position: relative;

  .searchInput {
    flex: 1;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .@{ant-prefix}-select-selection {
        border: none;
        box-shadow: none;
      }
    }
  }

  .iconBox {
    position: absolute;
    right: 0;
    top: 0;
    width: 44px;
    min-width: 44px;
    background: #cbe4ff;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .searchIcon {
      width: 14px;
      height: 14px
    }
  }
}