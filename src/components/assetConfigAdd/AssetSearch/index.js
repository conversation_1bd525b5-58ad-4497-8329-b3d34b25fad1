/*
 * @Description: 新建资产配置-搜索客户组件
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-20 13:51:18
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { logCommon } from '@/decorators/logable';
import { Select } from 'antd';
import { debounce, map, isEqual } from 'lodash';
import styles from './index.less';
import SearchIcon from '../images/search.svg';

const Option = Select.Option;
export default class AssetSearch extends PureComponent {
  static propTypes = {
    onSelect: PropTypes.func.isRequired,
    onSearch: PropTypes.func.isRequired,
    custList: PropTypes.array.isRequired,
    isReject: PropTypes.bool.isRequired,
    defaultValue: PropTypes.string,
    custId: PropTypes.string,
  };

  static defaultProps = {
    defaultValue: null,
    custId: null,
  }

  componentDidMount() {
    const {
      custId
    } = this.props;
    if (custId) {
      this.handleSearchCustomer(custId);
    }
  }

  componentDidUpdate(prevProps) {
    const {
      custId,
      defaultValue,
    } = this.props;
    if (!isEqual(prevProps.defaultValue, defaultValue) && custId) {
      this.handleSelectCustomer(defaultValue);
    }
  }

  // 选中某个客户事件
  handleSelectCustomer = (data) => {
    const { onSelect } = this.props;
    logCommon({
      type: 'Select',
      payload: {
        name: '资产配置-客户KYC-选择客户',
        value: data.key,
      },
    });
    onSelect({
      custId: data.key,
      custName: data.label,
    });
  };

  handleSearchCustomer=(keyword) => {
    const { onSearch } = this.props;
    onSearch({ keyword });
  }

  // 下拉框渲染事件
  optionRender = () => {
    const { custList } = this.props;
    return map(custList, (data) => (
      <Option key={data.custId} value={data.custId}>
        {`${data.custName}（${data.custId}）`}
      </Option>
    ));
  };

  render() {
    return (
      <div className={styles.searchBox}>
        <Select
          labelInValue
          showSearch
          disabled={this.props.isReject}
          defaultValue={{ key: this.props.custId }}
          defaultActiveFirstOption={false}
          filterOption={false}
          onSelect={this.handleSelectCustomer}
          onSearch={debounce(this.handleSearchCustomer, 250, { maxWait: 1000 })}
          showArrow={false}
          className={styles.searchInput}
          dropdownClassName={styles.searchMenu}
          placeholder="请输入客户姓名或经纪号"
        >
          {this.optionRender()}
        </Select>
        <div className={styles.iconBox}>
          <img className={styles.searchIcon} src={SearchIcon} />
        </div>
      </div>
    );
  }
}
