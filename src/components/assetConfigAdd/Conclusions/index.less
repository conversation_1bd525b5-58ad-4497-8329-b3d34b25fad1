.conclusions {
  background: linear-gradient(270deg, #f4f9ff 0%, #e9f3ff 100%);
  border-radius: 2px;
  font-size: 14px;
  color: #333;
  line-height: 22px;
  padding: 20px;
  margin-top: 24px;
  position: relative;

  ::before, ::after {
    content: "";
    position: absolute;
    width: 26px;
    height: 24px;
  }

  ::before {
    top: 0;
    left: 0;
    background: url('../images/left_quote.svg') no-repeat;
    background-size: 100% 100%;
  }

  ::after {
    bottom: 0;
    right: 0;
    background: url('../images/right_quote.svg') no-repeat;
    background-size: 100% 100%;
  }

  .conclusionItem {
    background: url('../images/arrow.svg') no-repeat 0 4px;
    background-size: 13px 13px;
    text-indent: 20px;
    margin-top: 6px;
  }
}
