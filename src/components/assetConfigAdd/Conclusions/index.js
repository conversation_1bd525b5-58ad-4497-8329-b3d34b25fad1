import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';

import styles from './index.less';

export default function Conclusions(props) {
  const {
    conclusions,
  } = props;

  return (
    <div className={styles.conclusions}>
      {
        map(conclusions, (conclusion) => (
          <div
            className={styles.conclusionItem}
            key={conclusion}
            // eslint-disable-next-line
            dangerouslySetInnerHTML={{
              __html: conclusion,
            }}
          />
        ))
      }
    </div>
  );
}

Conclusions.propTypes = {
  conclusions: PropTypes.array,
};

Conclusions.defaultProps = {
  conclusions: [],
};
