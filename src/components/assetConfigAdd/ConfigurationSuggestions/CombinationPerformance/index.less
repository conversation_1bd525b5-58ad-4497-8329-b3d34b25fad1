.moduleDetail {
  .table {
    margin-top: 20px;

    .fangAnDuiBi {
      font-weight: bold;
    }

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-table {
        border-bottom: 1px solid #d0d7e2 !important;
      }

      .ant-table-tbody > tr:last-child > td {
        border-bottom: 0;
      }

      .ant-table-thead {
        tr {
          th {
            background: #fff !important;
          }
        }
      }
    }

    tbody > tr:nth-child(2) {
      background: #eef8ff;

      td {
        color: #3364b5 !important;
      }
    }

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-select {
        vertical-align: text-top;
      }

      .ant-select-selection {
        border: 0;
        transition: none;

        &:hover {
          transition: none;
          background-color: #e6f9ff;
        }
      }

      .ant-select-selection__rendered {
        margin-left: 0;
      }

      .ant-select-arrow {
        border-left: 0;
        border-right: 0;
        border-top: 0;
        width: auto;
        height: 12px;
        margin-top: -6px;
      }
    }
  }
}

.dropdownSelect {
  width: 220px !important;
}

.dropdown {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 1px solid #108ee9;
  border-radius: 50%;
  position: relative;
  vertical-align: middle;
  margin-left: 5px;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    display: inline-block;
    top: 2px;
    left: 3px;
    width: 4px;
    height: 4px;
    border-bottom: 1px solid #108ee9;
    border-right: 1px solid #108ee9;
    transform: rotate(45deg);
  }
}
