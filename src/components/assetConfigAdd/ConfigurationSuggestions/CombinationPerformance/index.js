import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import isNil from 'lodash/isNil';

import Table from '@/components/common/table';
import Notes from '@/components/assetConfigAdd/Notes';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import {
  formatRatio,
} from '@/helper/number';

import {
  transferNumberColumn,
} from '../../utils';
import {
  COMBINATION_PERFORMANCE_COLUMNS,
  SHA_PU_BI_LV,
  CALMAR_BI_LV,
  FANG_AN_DUI_BI,
} from '../../config';

import styles from './index.less';

export default function CombinationPerformance(props) {
  const {
    viewFlag,
    data,
  } = props;
  const handleTransferColumns = () => {
    const columns = map(COMBINATION_PERFORMANCE_COLUMNS, (column) => {
      if (column.key === FANG_AN_DUI_BI) {
        return {
          ...column,
          render: (text) => (
            <div className={styles.fangAnDuiBi}>
              {text}
            </div>
          ),
        };
      }
      if (column.key === SHA_PU_BI_LV || column.key === CALMAR_BI_LV) {
        return {
          ...column,
          render: (text) => {
            if (isNil(text)) {
              return '--';
            }
            const value = formatRatio(text, { floatLength: 3, sign: '', radix: 1 });
            return value;
          },
        };
      }
      return transferNumberColumn(column);
    });
    return columns;
  };

  const columns = handleTransferColumns();

  const notes = [
    '* 推荐配置方案：根据客户投资偏好等信息，基于投研模型匹配出的最优配置方案',
    '* 风险提示：以上回测结果仅供参考，不作为任何收益保证或承诺。',
  ];

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="组合业绩"
        rightContent=""
        viewFlag={viewFlag}
      />
      <Table
        className={styles.table}
        dataSource={data}
        columns={columns}
        pagination={false}
        rowKey="custId"
        useNewUI
        withBorder
      />
      <Notes
        notes={notes}
      />
    </div>
  );
}

CombinationPerformance.propTypes = {
  viewFlag: PropTypes.bool.isRequired,
  data: PropTypes.array.isRequired,
};

CombinationPerformance.defaultProps = {
};
