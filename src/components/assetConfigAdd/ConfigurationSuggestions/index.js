import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import { autobind } from 'core-decorators';
import {
  message,
} from 'antd';
import moment from 'moment';
import to from 'await-to-js';
import isEmpty from 'lodash/isEmpty';
import map from 'lodash/map';
import isEqual from 'lodash/isEqual';
import flatten from 'lodash/flatten';
import forEach from 'lodash/forEach';
import find from 'lodash/find';
import noop from 'lodash/noop';
import max from 'lodash/max';

import withRouter from '@/decorators/withRouter';
import IFWrap from '@/components/common/IFWrap';
import CommonModal from '@/components/assetConfigAdd/CommonModal';
import {
  STEP_TEMPORARY,
  HAS_NO_TABLE_DATA_TEXT,
  STEP_SHOWREPORT,
} from '@/routes/assetConfigAdd/config';
import { dva } from '@/helper';
import { NORMAL_REJECT_CHANGED } from '@/routes/AssetConfigApproval/config';
import BacktestingHeader from './BacktestingHeader';
import CombinationPerformance from './CombinationPerformance';
import IncomeTrend from './IncomeTrend';
import DynamicWithdrawal from './DynamicWithdrawal';
import AssetScheme from './AssetScheme';
import ProductConfig from './ProductConfig';
import {
  checkStatusCode,
} from '../utils';
import styles from './index.less';

const ZI_CHAN_PEI_ZHI_FANG_AN = 'ZiChanPeiZhiFangAn';

const effect = dva.generateEffect;
const mapStateToProps = (state) => ({
  // 模块列表
  moduleList: state.assetConfig.moduleList,
  marketOpinion: state.assetConfig.marketOpinion,
  backTestingData: state.assetConfig.backTestingData,
  // 客户KYC
  custKyc: state.assetConfig.custKyc,
  // 选择的客户信息
  custMess: state.assetConfig.custMess,
  // 分析周期数据
  analysisPeriodData: state.assetConfig.analysisPeriodData,
  // 是否显示方案回测
  showBackTesting: state.assetConfig.showBackTesting,
  needBackTesting: state.assetConfig.needBackTesting,
  // 资产比例信息
  assetProductInfo: state.assetConfig.assetProductInfo,
  // 原始的资产比例信息
  assetProductInfoOrigin: state.assetConfig.assetProductInfoOrigin,
  ifTotal100Flag: state.assetConfig.ifTotal100Flag,
  ifOver10Flag: state.assetConfig.ifOver10Flag,
  // 资产配置方案数据
  assetPlanData: state.assetConfig.assetPlanData,
});
const mapDispatchToProps = {
  replace: routerRedux.replace,
  getAssetPlan: effect('assetConfig/getAssetPlan'),
  updateReduxData: effect('assetConfig/updateReduxData'),
  // 查询方案回测
  queryPlanBacktesting: effect('assetConfig/queryPlanBacktesting'),
};

@withRouter
@connect(mapStateToProps, mapDispatchToProps)
export default class ConfigurationSuggestions extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    getAssetPlan: PropTypes.func.isRequired,
    backTestingData: PropTypes.object.isRequired,
    custMess: PropTypes.object.isRequired,
    analysisPeriodData: PropTypes.object.isRequired,
    assetPlanData: PropTypes.object.isRequired,
    showBackTesting: PropTypes.bool.isRequired,
    needBackTesting: PropTypes.bool.isRequired,
    assetProductInfo: PropTypes.object.isRequired,
    assetProductInfoOrigin: PropTypes.object.isRequired,
    queryPlanBacktesting: PropTypes.func.isRequired,
    // 更新错误状态，重试按钮
    updateErrorFlag: PropTypes.func,
    updateReduxData: PropTypes.func.isRequired,
    isReject: PropTypes.bool,
    // 审批按钮和审批人
    flowButtonsAndApprovers: PropTypes.object,
  };

  static defaultProps = {
    // 更新错误状态，重试按钮
    updateErrorFlag: noop,
    isReject: false,
    flowButtonsAndApprovers: {},
  };

  static contextTypes = {
    push: PropTypes.func.isRequired,
    replace: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      huiCeStartDate: '',
      huiCeEndDate: moment().subtract(1, 'day').format('YYYY-MM-DD'),
      commonVisible: false,
      commonVisibleText: '',
      commonVisibleFun: noop,
      // 弹窗是否有标题
      titleFlag: false,
      // 是否有取消按钮
      cancelFlag: false,
    };
  }

  componentDidMount() {
    this.getAssetSchemeFun();
  }

  getAssetSchemeFun = () => {
    const {
      getAssetPlan,
      location: {
        query: { stepCode = '', analysisPeriodRang = '', id = '' } = {},
      } = {},
      custMess,
      updateErrorFlag,
      analysisPeriodData,
      isReject,
      flowButtonsAndApprovers,
    } = this.props;
    const ranges = analysisPeriodRang.split('-');
    const selectedPeriod = find(analysisPeriodData?.analysisPeriod, { rang: analysisPeriodRang });
    getAssetPlan({
      // 驳回时如果流程状态是5就表示风险等级或者合投变更，此时stepCode传temporary
      stepCode: isReject && flowButtonsAndApprovers?.approvalStatecode !== NORMAL_REJECT_CHANGED
        ? STEP_SHOWREPORT
        : stepCode,
      reportCode: ZI_CHAN_PEI_ZHI_FANG_AN,
      custId: custMess?.custId,
      assetAllocationId: id,
      timeRangeLevel: selectedPeriod?.code,
      timeRangeStartTimeMs: moment(ranges?.[0])?.startOf('date')?.valueOf(),
      timeRangeEndTimeMs: moment(ranges?.[1])?.endOf('date')?.valueOf(),
    }).then().catch(() => {
      updateErrorFlag();
    });
  }

  componentDidUpdate(prevProps) {
    const {
      showBackTesting,
      needBackTesting,
    } = this.props;
    if ((showBackTesting && !prevProps?.showBackTesting)
      || (needBackTesting && !prevProps?.needBackTesting)) {
      const {
        myAssetList,
      } = this.generateProductList();
      const allFoundDate = map(myAssetList, (item) => item?.foundDate);
      // 最晚成立的时间,即为回测开始时间
      const lastFoundDate = max(allFoundDate);
      const startDate = moment(lastFoundDate).format('YYYY-MM-DD');
      // eslint-disable-next-line
      this.setState({
        huiCeStartDate: startDate,
      });
      const defaultStartDate = startDate;
      const defaultEndDate = moment().subtract(1, 'day').format('YYYY-MM-DD');
      this.getPlanBacktestingData(defaultStartDate, defaultEndDate);
    }
  }

  componentWillUnmount() {
    this.props.updateReduxData({
      showBackTesting: false,
      needBackTesting: false,
    });
  }

  @autobind
  async getPlanBacktestingData(startDate, endDate) {
    const {
      queryPlanBacktesting,
    } = this.props;
    const payload = this.generateBackTestingParams(startDate, endDate);
    const [err, resultData] = await to(queryPlanBacktesting(payload));
    const statusCode = resultData?.statusCode;
    const {
      isFailed,
    } = checkStatusCode(statusCode);
    if (isFailed) {
      this.setState({
        commonVisible: true,
        commonVisibleText: HAS_NO_TABLE_DATA_TEXT,
        commonVisibleFun: this.handleCloseCommonModal,
      });
    }
    if (err) {
      message.error('方案回测接口错误，请重试', 2);
    }
  }

  @autobind
  compareProductList(assetList, myAssetList) {
    const sortedAssetList = assetList.sort((a, b) => a?.prdtId - b?.prdtId);
    const sortedMyAssetList = myAssetList.sort((a, b) => a?.prdtId - b?.prdtId);
    return isEqual(sortedAssetList, sortedMyAssetList);
  }

  @autobind
  generateProductList() {
    const {
      assetProductInfoOrigin,
      assetProductInfo,
    } = this.props;

    const assetProductList = [];
    forEach(assetProductInfoOrigin, (item) => {
      assetProductList.push(item.productList);
    });
    const myAssetProductList = [];
    forEach(assetProductInfo, (item) => {
      myAssetProductList.push(item.productList);
    });
    const flattenAssetProductList = flatten(assetProductList);
    const flattenMyAssetProductList = flatten(myAssetProductList);
    const assetList = map(flattenAssetProductList, (product) => ({
      prdtId: product?.huiCePrdtId,
      weight: product?.weight,
      fundType: Number(product?.huiCeFundTypeCode),
      foundDate: product?.foundDate,
    }));
    const myAssetList = map(flattenMyAssetProductList, (product) => ({
      prdtId: product?.huiCePrdtId,
      weight: product?.weight,
      fundType: Number(product?.huiCeFundTypeCode),
      foundDate: product?.foundDate,
    }));
    return {
      assetList,
      myAssetList,
    };
  }

  @autobind
  generateBackTestingParams(start, end) {
    const {
      custMess,
      location: {
        query: {
          analysisPeriodRang,
          id,
        },
      },
      analysisPeriodData,
    } = this.props;
    const selectedPeriod = find(analysisPeriodData?.analysisPeriod, { rang: analysisPeriodRang });
    const {
      assetList,
      myAssetList,
    } = this.generateProductList();
    const isSame = this.compareProductList(assetList, myAssetList);
    const payload = {
      assetAllocationId: id,
      custId: custMess?.custId,
      reportCode: 'FanAnHuiCe',
      timeRangeLevel: selectedPeriod?.code,
      timeRangeStartTimeMs: moment(start).startOf('date').valueOf(),
      timeRangeEndTimeMs: moment(end).endOf('date').valueOf(),
      paramJson: {},
    };
    if (isSame) {
      payload.paramJson = {
        assetList,
      };
    } else {
      payload.paramJson = {
        assetList,
        myAssetList,
      };
    }
    return payload;
  }

  @autobind
  handleDateChange(start, end) {
    if (!isEmpty(start) && !isEmpty(end)) {
      this.getPlanBacktestingData(start, end);
    }
  }

  // 关闭共用弹窗
  @autobind
  handleCloseCommonModal() {
    this.setState({
      commonVisible: false,
      titleFlag: false,
      cancelFlag: false,
    });
  }

  render() {
    const {
      backTestingData,
      location: {
        query: {
          stepCode = '',
        } = {},
      } = {},
      assetPlanData,
      showBackTesting,
      needBackTesting,
      updateErrorFlag,
      isReject,
      flowButtonsAndApprovers,
    } = this.props;
    const {
      huiCeStartDate,
      huiCeEndDate,
      commonVisible,
      titleFlag,
      commonVisibleText,
      commonVisibleFun,
      cancelFlag,
    } = this.state;

    const reportData = backTestingData?.reportData || {};
    const viewFlag = stepCode !== STEP_TEMPORARY && !isReject;

    const statusCode = backTestingData?.statusCode;
    const {
      isOk,
    } = checkStatusCode(statusCode);
    return (
      <div className={styles.configurationSuggestions}>
        {commonVisible ? (
          <CommonModal
            visible
            titleFlag={titleFlag}
            recieveText={commonVisibleText}
            onClose={this.handleCloseCommonModal}
            onOk={commonVisibleFun}
            cancelFlag={cancelFlag}
          />
        ) : null}
        {/*  资产配置方案 */}
        <AssetScheme data={assetPlanData} />
        {/* 资产下的产品表的配置 */}
        <ProductConfig
          viewFlag={viewFlag}
          updateErrorFlag={updateErrorFlag}
          isReject={isReject}
          flowButtonsAndApprovers={flowButtonsAndApprovers}
        />
        {/* 方案回测 */}
        <IFWrap when={showBackTesting || needBackTesting}>
          <div className={styles.backtesting}>
            <BacktestingHeader
              onDateChange={this.handleDateChange}
              huiCeStartDate={huiCeStartDate}
              huiCeEndDate={huiCeEndDate}
            />
            <div className={styles.tips}>
              *
              组合业绩、收益趋势以及动态回撤模块涉及的回测结果仅供参考，不做为任何收益保证或承诺。
            </div>
            <IFWrap when={isOk}>
              <div className={styles.chartArea}>
                <CombinationPerformance
                  data={reportData?.tableData}
                  viewFlag={viewFlag}
                />
                <IncomeTrend
                  data={reportData?.shouYiQuShiGraphData?.dataSet}
                  indexData={reportData?.shouYiQuShiGraphData?.index}
                  combinationData={reportData?.tableData}
                  viewFlag={viewFlag}
                />
                <DynamicWithdrawal
                  data={reportData?.dongTaiHuiCeGraphData?.dataSet}
                  viewFlag={viewFlag}
                />
              </div>
            </IFWrap>
          </div>
        </IFWrap>
      </div>
    );
  }
}
