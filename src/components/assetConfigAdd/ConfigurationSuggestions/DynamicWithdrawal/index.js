import React from 'react';
import PropTypes from 'prop-types';
import includes from 'lodash/includes';
import slice from 'lodash/slice';
import map from 'lodash/map';
import isEmpty from 'lodash/isEmpty';
import ceil from 'lodash/ceil';
import forEach from 'lodash/forEach';
import indexOf from 'lodash/indexOf';
import moment from 'moment';
import echarts from 'echarts';

import IECharts from '@/components/IECharts';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import {
  formatRatio,
} from '@/helper/number';

import {
  LABEL_MY_PLAN,
  LABEL_DEFAULT_PLAN,
  LABEL_COLORS,
} from '../../config';

import styles from './index.less';

export default function DynamicWithdrawal(props) {
  const {
    data,
    viewFlag,
  } = props;

  const handleFormatTooltip = (param) => {
    const length = param?.length ?? 1;
    let classes = [];
    if (length > 1) {
      classes = [styles.averageCircle, styles.custCircle];
    } else {
      classes = param?.[0]?.seriesName === LABEL_MY_PLAN
        ? [styles.custCircle]
        : [styles.averageCircle];
    }
    const secondLine = length > 1
      ? `<div class=${styles.valueLine}>
        <div class=${styles.left}>
          <div class=${classes[1]}></div>
          <div>${param[1].seriesName}</div>
        </div>
        <div>${formatRatio(param[1].data[2]) || '--'}</div>
      </div>`
      : '';
    return (`
      <div class=${styles.tooltip}>
        <div class=${styles.title}>${param[0].axisValue}</div>
        <div class=${styles.valueLine}>
          <div class=${styles.left}>
            <div class=${classes[0]}></div>
            <div>${param[0].seriesName}</div>
          </div>
          <div>${formatRatio(param[0].data[1]) || '--'}</div>
        </div>
        ${secondLine}
      </div>`
    );
  };

  const handleTransferValues = () => {
    const [firstItem, ...rest] = data?.source || [];
    if (!isEmpty(data?.source)) {
      const dateValues = map(firstItem, (child) => {
        if (isEmpty(child)) {
          return child;
        }
        return moment(child).format('YYYY-MM-DD');
      });
      return [
        dateValues,
        ...rest,
      ];
    }
    return [];
  };

  const transferValues = handleTransferValues();

  const handleGenerateIntervalDates = () => {
    const graphDates = transferValues?.[0] ?? [];
    if (!isEmpty(graphDates)) {
      const dates = slice(graphDates, 1);
      const intervalDates = [dates[0]];
      const interval = ceil(dates.length / 12);
      for (let i = 1; i < 12; i++) {
        intervalDates.push(dates?.[i * interval]);
      }
      intervalDates.push(dates[dates.length - 1]);
      return intervalDates;
    }
    return [];
  };

  const intervalDates = handleGenerateIntervalDates();

  const handlegenerateLableList = () => {
    const labelList = [];
    forEach(data?.source || [], (item) => {
      if (item?.[0] !== '') {
        labelList.push(item?.[0]);
      }
    });
    return labelList;
  };

  const labelList = handlegenerateLableList();

  const options = {
    grid: {
      left: 0,
      right: 34,
      top: 60,
      bottom: 0,
      containLabel: true
    },
    legend: {
      top: 10,
      right: 0,
      itemWidth: 15,
      itemHeight: 2,
      icon: 'rect',
      selectedMode: false,
      textStyle: {
        color: '#8c8c8c'
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'transparent',
      formatter: (param) => handleFormatTooltip(param),
    },
    color: [],
    dataset: {
      source: handleTransferValues(),
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: true,
        alignWithLabel: true,
        interval: (index, value) => {
          if (includes(intervalDates, value)) {
            return true;
          }
          return false;
        }
      },
      axisLabel: {
        interval: 0,
        formatter: (value, index) => {
          if (includes(intervalDates, value)) {
            return value;
          }
          return '';
        },
        textStyle: {
          color: '#666',
          fontSize: 12,
        }
      },
      axisLine: {
        onZero: false,
        lineStyle: {
          color: '#8c8c8c',
        }
      },
    },
    yAxis: {
      gridIndex: 0,
      axisLabel: {
        color: '#666',
        formatter: (value) => {
          if (value === 0) {
            return `{zero|${value}%}`;
          }
          return `${(value * 100).toFixed(0)}%`;
        },
        rich: {
          zero: {
            fontWeight: 'bold',
            color: '#333',
          }
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: [],
  };

  const handleGenerateSeries = (title, index, defaultIndex) => ({
    type: 'line',
    seriesLayoutBy: 'row',
    connectNulls: true,
    emphasis: { focus: 'series' },
  });

  const getRenderOptions = () => {
    // 我的配置、推荐配置
    const series = [];
    const colors = [];
    const myPlanIndex = indexOf(labelList, LABEL_MY_PLAN);
    const defaultPlanIndex = indexOf(labelList, LABEL_DEFAULT_PLAN);
    // 如果存在方案，则按照方案的 index 存入 series
    if (myPlanIndex !== -1) {
      series[myPlanIndex] = {
        ...handleGenerateSeries(LABEL_MY_PLAN, myPlanIndex, 0),
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0.17,
              color: 'rgba(254, 191, 104, 0.16)'
            }, {
              offset: 0.85,
              color: 'rgba(254, 191, 104, 0)'
            }], false),
          }
        },
      };
      colors[myPlanIndex] = LABEL_COLORS[0];
    }
    if (defaultPlanIndex !== -1) {
      series[defaultPlanIndex] = {
        ...handleGenerateSeries(LABEL_DEFAULT_PLAN, defaultPlanIndex, 1),
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0.17,
              color: 'rgba(113, 178, 255, 0.16)'
            }, {
              offset: 0.85,
              color: 'rgba(113, 181, 255, 0)'
            }], false),
          }
        },
      };
      colors[defaultPlanIndex] = LABEL_COLORS[1];
    }
    options.series = series;
    options.color = colors;
    return options;
  };

  const renderOptions = getRenderOptions();

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="动态回撤"
        rightContent=""
        viewFlag={viewFlag}
      />
      <div className={styles.chartArea}>
        <IECharts
          option={renderOptions}
          resizable
          style={{
            height: '335px',
          }}
          notMerge
        />
        <div className={styles.chartTitle}>
          回撤率
          <span>（%）</span>
        </div>
      </div>
    </div>
  );
}

DynamicWithdrawal.propTypes = {
  data: PropTypes.object.isRequired,
  viewFlag: PropTypes.bool.isRequired,
};

DynamicWithdrawal.defaultProps = {
};
