import React, { useState, useEffect } from 'react';
import {
  DatePicker,
} from 'antd';
import moment from 'moment';
import PropTypes from 'prop-types';

import Tooltip from '@/components/common/Tooltip';
import InfoForm from '@/components/common/infoForm';
import InformationSvg from '../../images/information.svg';

import styles from './index.less';

export default function BacktestingHeader(props) {
  const {
    onDateChange,
    huiCeStartDate,
    huiCeEndDate,
  } = props;

  const start = moment(huiCeStartDate);
  const end = moment(huiCeEndDate);

  const [startTime, setStartTime] = useState(start);
  const [endTime, setEndTime] = useState(end);
  const disabledStartDate = (current) => current >= end.startOf('day') || current < start.startOf('day');

  useEffect(() => {
    if (huiCeStartDate) {
      setStartTime(moment(huiCeStartDate));
    }
  }, [huiCeStartDate]);

  const handleStartDatePickerChange = (date, dateString) => {
    setStartTime(date);
    setEndTime('');
    onDateChange(dateString, '');
  };

  const disabledEndDate = (current) => (
    current <= moment(startTime).endOf('day') || current > end.endOf('day')
  );

  const handleEndDatePickerChange = (date, dateString) => {
    setEndTime(date);
    onDateChange(startTime.format('YYYY-MM-DD'), dateString);
  };

  return (
    <div className={styles.backtestingHeader}>
      <div className={styles.left}>
        <h2>方案回测</h2>
      </div>
      <div className={styles.right}>
        <InfoForm
          label="回测开始时间"
          className={styles.item}
          style={{ width: '100px' }}
        >
          <DatePicker
            showToday={false}
            value={startTime}
            disabledDate={disabledStartDate}
            onChange={handleStartDatePickerChange}
          />
          <Tooltip title="回测开始时间以所选产品成立最晚时间为起始时间" placement="top">
            <img src={InformationSvg} />
          </Tooltip>
        </InfoForm>
        <InfoForm
          label="回测结束时间"
          className={styles.item}
          style={{ width: '100px' }}
        >
          <DatePicker
            showToday={false}
            value={endTime}
            disabledDate={disabledEndDate}
            onChange={handleEndDatePickerChange}
          />
        </InfoForm>
      </div>
    </div>
  );
}

BacktestingHeader.propTypes = {
  onDateChange: PropTypes.func.isRequired,
  huiCeStartDate: PropTypes.string,
  huiCeEndDate: PropTypes.string,
};

BacktestingHeader.defaultProps = {
  huiCeStartDate: '',
  huiCeEndDate: '',
};
