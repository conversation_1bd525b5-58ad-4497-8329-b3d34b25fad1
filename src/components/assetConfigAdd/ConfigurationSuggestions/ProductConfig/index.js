/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-08 09:38:14
 * @description 资产配置步骤四-产品配置模块
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import classnames from 'classnames';
import { connect } from 'dva';
import { sensors } from '@lego/bigbox-utils';
import {
  map,
  find,
  forEach,
  flatten,
  isEmpty,
  filter,
  flattenDeep,
  differenceBy,
  concat,
  indexOf,
  cloneDeep,
} from 'lodash';

import IFWrap from '@/components/common/IFWrap';
import { dva } from '@/helper';
import withRouter from '@/decorators/withRouter';
import PlaceHolderImage from '@/components/common/placeholderImage';
import dayjs from 'dayjs';
import { STEP_SHOWREPORT } from '@/routes/assetConfigAdd/config';
import { NORMAL_REJECT_CHANGED } from '@/routes/AssetConfigApproval/config';
import ProductItem from './ProductItem';
import AddProductModal from './AddProductModal';
import { DEFAULT_FILTER_STRING } from './config';

import styles from './index.less';

const effect = dva.generateEffect;
const REPORT_CODE = 'AssetAllocationSuggestedPrdtList';
const { logable } = sensors;
const TEMPORARY = 'temporary';

const mapStateToProps = (state) => ({
  // 资产比例信息
  assetProductInfo: state.assetConfig.assetProductInfo,
  // 产品筛选信息
  productFilters: state.assetConfig.productFilters,
  // 模糊搜索产品信息
  productSearchList: state.assetConfig.productSearchList,
  // 产品信息
  productData: state.assetConfig.productData,
  assetProductInfoOrigin: state.assetConfig.assetProductInfoOrigin,
  // 客户KYC
  custKyc: state.assetConfig.custKyc,
  // 选择的客户信息
  custMess: state.assetConfig.custMess,
  saveKycData: state.assetConfig.saveKycData,
  // 分析周期数据
  analysisPeriodData: state.assetConfig.analysisPeriodData,
});

const mapDispatchToProps = {
  // 获取资产比例信息
  getAssetProductInfo: effect('assetConfig/getAssetProductInfo'),
  // 获取产品筛选信息
  getProductFilters: effect('assetConfig/getProductFilters'),
  // 模糊搜索产品信息
  getProductListByKeyword: effect('assetConfig/getProductListByKeyword'),
  // 获取产品信息
  getProductData: effect('assetConfig/getProductData'),
  updateReduxData: effect('assetConfig/updateReduxData'),
};

@withRouter
@connect(mapStateToProps, mapDispatchToProps)
export default class ProductConfig extends PureComponent {
  static propTypes = {
    // 获取资产比例信息
    getAssetProductInfo: PropTypes.func.isRequired,
    assetProductInfo: PropTypes.array.isRequired,
    // 获取产品筛选信息
    getProductFilters: PropTypes.func.isRequired,
    productFilters: PropTypes.object.isRequired,
    // 模糊搜索产品信息
    getProductListByKeyword: PropTypes.func.isRequired,
    productSearchList: PropTypes.array.isRequired,
    // 获取产品信息
    getProductData: PropTypes.func.isRequired,
    productData: PropTypes.object.isRequired,
    location: PropTypes.object.isRequired,
    updateReduxData: PropTypes.func.isRequired,
    custMess: PropTypes.object.isRequired,
    saveKycData: PropTypes.object.isRequired,
    viewFlag: PropTypes.bool,
    analysisPeriodData: PropTypes.object.isRequired,
    updateErrorFlag: PropTypes.object.isRequired,
    isReject: PropTypes.bool.isRequired,
    // 审批按钮和审批人
    flowButtonsAndApprovers: PropTypes.object.isRequired,
  };

  static defaultProps= {
    viewFlag: false,
  };

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      modalVisible: false,
      // 新增标识列表
      addTipList: [],
    };
  }

  componentDidMount() {
    const {
      assetProductInfo,
    } = this.props;
    if (isEmpty(assetProductInfo)) {
      this.initData();
    }
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    const { flowButtonsAndApprovers: nextFlowButtonsAndApprovers } = this.props;
    const { flowButtonsAndApprovers: prevFlowButtonsAndApprovers } = prevProps;
    // 这里为了解决驳回修改刷新时数据不对的问题，虽然步骤不支持中间步骤刷新，但是要是真的刷了，数据错了也不好
    if (nextFlowButtonsAndApprovers !== prevFlowButtonsAndApprovers
      && isEmpty(prevFlowButtonsAndApprovers)) {
      this.initData();
    }
  }

  @autobind
  initData() {
    const {
      getAssetProductInfo,
      updateErrorFlag,
      custMess,
      analysisPeriodData,
      location: {
        query: {
          stepCode = TEMPORARY,
          id = '',
          analysisPeriodRang = ''
        }
      },
      flowButtonsAndApprovers,
      isReject,
    } = this.props;
    const selectedPeriod = find(analysisPeriodData?.analysisPeriod, { rang: analysisPeriodRang });
    const ranges = analysisPeriodRang.split('-');
    getAssetProductInfo({
      // 驳回时如果流程状态是5就表示风险等级或者合投变更，此时stepCode传temporary
      stepCode: isReject && flowButtonsAndApprovers?.approvalStatecode !== NORMAL_REJECT_CHANGED
        ? STEP_SHOWREPORT
        : stepCode,
      reportCode: REPORT_CODE,
      custId: custMess?.custId,
      assetAllocationId: id,
      timeRangeLevel: selectedPeriod?.code,
      timeRangeStartTimeMs: dayjs(ranges?.[0])?.startOf('date')?.valueOf(),
      timeRangeEndTimeMs: dayjs(ranges?.[1])?.endOf('date')?.valueOf(),
    }).then((res) => {
      this.handleTabClick(res[0]?.assetName ?? '');
      this.transformFinalData(res);
    }).catch(() => {
      updateErrorFlag();
    });
  }

  @autobind
  getTotalWeight(assetProductInfo) {
    if (isEmpty(assetProductInfo)) {
      return 0;
    }
    const totalWeight = assetProductInfo.reduce((prev, cur) => (prev + cur.changedWeight), 0);
    return parseFloat(totalWeight.toFixed(2));
  }

  // 转换最终的比例
  @autobind
  async transformFinalData(data) {
    await this.props.updateReduxData({
      ifOver10Flag: false
    });
    const minusList = [];
    const finalData = map(data, (item) => {
      // 遍历求每一个资产下产品的比例和
      const assetWeight = item.productList.reduce(
        (prev, cur) => (prev + cur.weight), 0
      );
      // 如果减少了配置比例，把减少的差值存储起来
      if (assetWeight < item.weight) {
        minusList.push(item.weight - assetWeight);
      }
      return {
        ...item,
        changedWeight: assetWeight,
      };
    });
    // 调减比例之和
    const totalDifference = minusList.reduce(
      (prev, cur) => (prev + cur), 0
    );
    // 如果求和的结果减去原始比例超过10
    if (totalDifference >= 10) {
      this.props.updateReduxData({
        ifOver10Flag: true
      });
    }

    // 存储数据，并且计算资产比例总和
    this.props.updateReduxData({
      assetProductInfo: finalData,
      ifTotal100Flag: this.getTotalWeight(finalData) === 100
    });
    return Promise.resolve();
  }

  // 获取已选产品总数
  @autobind
  getTotalProduct() {
    const { assetProductInfo } = this.props;
    if (isEmpty(assetProductInfo)) {
      return 0;
    }
    let productListFLatten = [];
    forEach(assetProductInfo, (item) => {
      productListFLatten.push(item.productList);
    });
    productListFLatten = flatten(productListFLatten);
    return productListFLatten.length;
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '资产配置-配置建议-大类资产TAB',
      value: '$args[0]',
    }
  })
  handleTabClick(tab) {
    const {
      location: { query, pathname },
    } = this.props;
    // 每次点击的时候需要去除当前tab的新增标识
    const { addTipList } = this.state;
    this.setState({
      addTipList: filter(addTipList, (item) => item !== tab)
    });
    this.context.replace({
      pathname,
      query: {
        ...query,
        assetTypeTab: tab
      },
    });
  }

  // 修改产品配置比例
  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '资产配置-配置建议-编辑配置比例',
    }
  })
  handleConfigOkClick(changedPrdtItem) {
    const {
      assetProductInfo,
      location: {
        query: {
          assetTypeTab
        },
      },
    } = this.props;
    const finalData = map(assetProductInfo, (item) => {
      if (item.assetName === assetTypeTab) {
        return {
          ...item,
          productList: map(item.productList, (productItem) => {
            if (productItem.tecPrdtId === changedPrdtItem.tecPrdtId) {
              return changedPrdtItem;
            }
            return productItem;
          })
        };
      }
      return item;
    });
    this.transformFinalData(finalData);
  }

  // 删除产品
  @autobind
  @logable({
    type: 'Submit',
    payload: {
      name: '资产配置-配置建议-删除产品',
    }
  })
  handleProductDeleteClick(deletedPrdtItem) {
    const {
      assetProductInfo,
      location: {
        query: {
          assetTypeTab
        }
      }
    } = this.props;
    const finalData = map(assetProductInfo, (item) => {
      if (item.assetName === assetTypeTab) {
        return {
          ...item,
          productList: filter(item.productList,
            (productItem) => productItem.tecPrdtId !== deletedPrdtItem.tecPrdtId)
        };
      }
      return item;
    });
    this.transformFinalData(finalData);
  }

  // 保存最终选择的产品
  @autobind
  setFInalProducts(selectedRows) {
    const {
      assetProductInfo,
    } = this.props;
    const { addTipList } = this.state;
    const oldArr = cloneDeep(addTipList);
    const finalData = map(assetProductInfo, (item) => {
      // 每个子类需要先去选择数据中将当前类型的数据筛选出来，组成一个新的数组
      const checkedList = filter(selectedRows, { assetType: item.assetName });
      // 先筛选出不同的数据，相当于增加的数据
      const addList = differenceBy(checkedList, item?.productList, 'tecPrdtId');
      // 再将选中数据中的新增数据去除,获取剩下的数据就是新增之前有的数据
      const oldList = differenceBy(checkedList, addList, 'tecPrdtId');
      // 最后将新增数据拼接到数据的前面
      const newList = concat(addList, oldList);
      // 当新增数据为空，且新增标识数组中没有这个tab的时候需要新增
      if (!isEmpty(addList) && indexOf(addTipList, item.assetName) === -1) {
        oldArr.push(item.assetName);
      }
      return {
        ...item,
        productList: newList,
      };
    });
    this.setState({
      addTipList: oldArr,
    });
    this.transformFinalData(finalData);
  }

  // 获取当前资产tab下的产品列表
  @autobind
  getAssetProductList() {
    const {
      assetProductInfo,
    } = this.props;
    // 给子类的产品增加assetType属性，方便操作产品去筛选
    // 将所有选中的数据去除层级，顺便加上assetType属性
    const checkedArr = map(assetProductInfo, (item) => map(item?.productList, (itemChild) => ({
      assetType: item?.assetName,
      ...itemChild
    })));
    return flattenDeep(checkedArr);
  }

  @autobind
  @logable({
    type: 'ButtonClick',
    payload: {
      name: '资产配置-配置建议-添加产品',
    }
  })
  showAddModal() {
    this.setState({
      modalVisible: true
    });
    const {
      location: { query, pathname },
    } = this.props;
    this.context.replace({
      pathname,
      query: {
        ...query,
        assetType: DEFAULT_FILTER_STRING,
      },
    });
  }

  @autobind
  closeAddModal() {
    this.setState({
      modalVisible: false,
    });
  }

  render() {
    const {
      getProductFilters,
      productFilters,
      getProductListByKeyword,
      productSearchList,
      getProductData,
      productData,
      assetProductInfo,
      saveKycData,
      custMess,
      updateReduxData,
      location: {
        query: {
          assetTypeTab = assetProductInfo[0]?.assetName ?? '',
        }
      },
      viewFlag,
    } = this.props;
    const { modalVisible, addTipList } = this.state;
    const productList = find(assetProductInfo,
      (item) => item.assetName === assetTypeTab)?.productList;
    const palceholder = viewFlag
      ? '暂无推荐配置产品适配客户'
      : '暂无推荐配置产品适配客户，可通过右上角【添加】选择产品';
    const wrapClassNames = classnames({
      [styles.wrap]: true,
      [styles.viewFlag]: viewFlag,
    });
    return (
      <div className={wrapClassNames}>
        <div className={styles.tabBar}>
          <div className={styles.tabs}>
            {
              map(assetProductInfo, (item) => {
                const assetClassNames = classnames({
                  [styles.tabItem]: true,
                  [styles.activeTabItem]: assetTypeTab === item.assetName
                });
                return (
                  <div
                    key={item.assetName}
                    className={assetClassNames}
                    onClick={() => this.handleTabClick(item.assetName)}
                  >
                    <div className={styles.text}>
                      {item.assetName} {parseFloat((item.changedWeight || 0).toFixed(2))}%
                    </div>
                    {
                      indexOf(addTipList, item.assetName) !== -1 ? <div className={styles.newCircle} /> : ''
                    }
                  </div>
                );
              })
            }
          </div>
          <div className={styles.totalArea}>
            <div className={styles.item}>
              <span>共计比例：</span>
              <span>{this.getTotalWeight(assetProductInfo)}%</span>
            </div>
            <div className={styles.item}>
              <span>已选产品：</span>
              <span>{this.getTotalProduct()}只</span>
            </div>
            <IFWrap when={!viewFlag}>
              <div className={styles.addBtn} onClick={this.showAddModal}>+添加</div>
            </IFWrap>
          </div>
        </div>
        <div className={styles.contentArea}>
          {
            isEmpty(productList)
              ? <PlaceHolderImage title={palceholder} style={{ height: 182 }} />
              : map(productList, (item) => (
                <ProductItem
                  key={item.tecPrdtId}
                  data={item}
                  onDeleteClick={this.handleProductDeleteClick}
                  onOkClick={this.handleConfigOkClick}
                  viewFlag={viewFlag}
                />
              ))
          }
        </div>
        <IFWrap when={modalVisible}>
          <AddProductModal
            visible={modalVisible}
            onCloseModal={this.closeAddModal}
            getProductFilters={getProductFilters}
            productFilters={productFilters}
            getProductListByKeyword={getProductListByKeyword}
            productSearchList={productSearchList}
            getProductData={getProductData}
            productData={productData}
            setFinalProducts={this.setFInalProducts}
            assetProductList={this.getAssetProductList()}
            saveKycData={saveKycData}
            custMess={custMess}
            updateReduxData={updateReduxData}
          />
        </IFWrap>
      </div>
    );
  }
}
