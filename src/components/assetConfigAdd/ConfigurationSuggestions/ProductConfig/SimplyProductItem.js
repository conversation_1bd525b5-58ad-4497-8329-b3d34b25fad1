/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-08 09:39:29
 * @description 资产配置-配置建议-产品item
 */
import React from 'react';
import PropTypes from 'prop-types';

import deleteIcon from './images/delete.svg';

import styles from './simplyProductItem.less';

export default function SimplyProductItem(props) {
  const { data, onDeleteClick } = props;

  return (
    <div className={styles.product}>
      {
        `${data.productName}(${data.productCode})`
      }
      <span
        className={styles.delete}
        onClick={() => onDeleteClick(data.tecPrdtId)}
      >
        <img src={deleteIcon} alt="delete" />
      </span>
    </div>
  );
}

SimplyProductItem.propTypes = {
  data: PropTypes.object.isRequired,
  onDeleteClick: PropTypes.func.isRequired,
};
