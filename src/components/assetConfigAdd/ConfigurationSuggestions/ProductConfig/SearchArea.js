/* eslint-disable no-console */
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-18 15:13:20
 * @Last Modified by: yeyixin
 * @Last Modified time: 2023-09-16 19:21:21
 * @description 资产配置-配置建议-添加产品弹窗-搜索区域
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { Input, AutoComplete } from 'antd';
import classnames from 'classnames';
import { sensors } from '@lego/bigbox-utils';
import {
  debounce,
  isEmpty,
  map,
  includes,
  filter,
} from 'lodash';

import HtFilter from '@/components/common/htFilter';
import withRouter from '@/decorators/withRouter';
import IFWrap from '@/components/common/IFWrap';
import upIcon from './images/up.svg';
import downIcon from './images/down.svg';
import {
  PRIVATE,
  PUBLIC,
  DEFAULT_FILTER_STRING,
} from './config';

import styles from './searchArea.less';

const { Option } = AutoComplete;
const { logable } = sensors;

@withRouter
export default class SearchArea extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    // 获取产品筛选信息
    getProductFilters: PropTypes.func.isRequired,
    productFilters: PropTypes.object.isRequired,
    // 模糊搜索产品信息
    getProductListByKeyword: PropTypes.func.isRequired,
    productSearchList: PropTypes.array.isRequired,
    changeQuery: PropTypes.func.isRequired,
    custMess: PropTypes.object.isRequired,
  };

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      // 是否展开
      unfold: false,
    };
  }

  componentDidMount() {
    const {
      custMess,
      getProductFilters,
      location: {
        query: {
          id = '',
        }
      }
    } = this.props;
    getProductFilters({
      custId: custMess?.custId,
      assetAllocationId: id,
    }).then((res) => {
      const {
        productPoolPublic,
        productPoolPrivate,
        productType,
        activityTag,
        tradeStatus,
      } = res;
      const isPublic = productType[0].key === PUBLIC;
      this.props.changeQuery({
        productPool: isPublic ? productPoolPublic[0].key : productPoolPrivate[0].key,
        productType: productType[0].key,
        activityTag: activityTag[0].key,
        tradeStatus: tradeStatus[0].key,
        riskLevel: DEFAULT_FILTER_STRING,
        trackClassification: DEFAULT_FILTER_STRING,
        productClassification: DEFAULT_FILTER_STRING,
        investStrategy: DEFAULT_FILTER_STRING,
        assetType: DEFAULT_FILTER_STRING,
        productCode: '',
      });
    });
  }

  @autobind
  isActive(objStr, key) {
    try {
      return includes(JSON.parse(objStr), key);
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  @autobind
  handleSearchProduct(keyword) {
    const {
      custMess,
      location: {
        query: {
          id,
          productType
        }
      },
      getProductListByKeyword
    } = this.props;
    getProductListByKeyword({
      keyword,
      custId: custMess?.custId,
      assetAllocationId: id,
      productType
    });
  }

  @autobind
  @logable({
    type: 'Search',
    payload: {
      name: '资产配置-添加产品-搜索',
      value: '$args[0]',
    }
  })
  handleSearchSelect(productCode) {
    this.props.changeQuery({
      productCode,
      pageNum: 1,
    });
  }

  @autobind
  getAutoCompleteOptions() {
    const { productSearchList } = this.props;
    if (isEmpty(productSearchList)) {
      return ([
        <Option key="NONE_INFO" disabled>请搜索更多结果</Option>
      ]);
    }
    return map(productSearchList, ({ name, code, ifMatch }) => (
      <Option
        key={code}
        className={styles.productOption}
        disabled={!ifMatch}
        title={`${name}(${code})`}
      >
        <div className={styles.product}>{`${name}(${code})`}</div>
        <div className={classnames({
          [styles.match]: true,
          [styles.disbaled]: !ifMatch,
        })}
        >{ifMatch ? '适当性匹配' : '适当性不匹配'}
        </div>
      </Option>
    ));
  }

  @autobind
  handleSearchBlur(keyword) {
    // 当搜索框为空时，需要重置搜索
    if (isEmpty(keyword)) {
      this.handleSearchSelect(keyword);
    }
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-产品池范围',
      value: '$args[0]',
    }
  })
  handleProductPoolChange({ value }) {
    this.props.changeQuery({
      productPool: value,
      pageNum: 1,
    });
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-产品类别',
      value: '$args[0]',
    }
  })
  handleProductTypeChange({ value }) {
    const {
      productFilters: {
        productPoolPublic,
        productPoolPrivate,
      }
    } = this.props;
    this.props.changeQuery({
      productType: value,
      productPool: value === PUBLIC ? productPoolPublic?.[0]?.key : productPoolPrivate?.[0]?.key,
      pageNum: 1,
      trackClassification: DEFAULT_FILTER_STRING,
      productClassification: DEFAULT_FILTER_STRING,
      investStrategy: DEFAULT_FILTER_STRING,
    });
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-资产类别',
      value: '$args[0]',
    }
  })
  handleAssetTypeChange(data) {
    this.props.changeQuery({
      assetType: JSON.stringify(data?.value),
      pageNum: 1,
    });
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-活动标签',
      value: '$args[0]',
    }
  })
  handleActivityTagChange({ value }) {
    this.props.changeQuery({
      activityTag: value,
      pageNum: 1,
    });
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-交易状态',
      value: '$args[0]',
    }
  })
  handleTradeTypeChange({ value }) {
    this.props.changeQuery({
      tradeStatus: value,
      pageNum: 1,
    });
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-投资策略',
      value: '$args[0]',
    }
  })
  handleInvestStrategyClick(key) {
    const {
      location: {
        query: {
          investStrategy,
        }
      }
    } = this.props;
    try {
      const list = this.getSelectedList(JSON.parse(investStrategy), key);
      this.props.changeQuery({
        investStrategy: JSON.stringify(list),
        pageNum: 1
      });
    } catch (error) {
      console.error(error);
    }
  }

  @autobind
  handlMoreClick() {
    this.setState((prev) => ({
      unfold: !prev.unfold,
    }));
  }

  // 若选择不限，则将其他选项清除，反之将不限清除
  @autobind
  getSelectedList(originList, key) {
    let list = originList;
    if (isEmpty(key)) {
      list = [''];
    } else {
      list = filter(list, (item) => item !== '');
      if (includes(list, key)) {
        list = filter(list, (item) => item !== key);
      } else {
        list.push(key);
      }
    }
    return isEmpty(list) ? [''] : list;
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-风险等级',
      value: '$args[0]',
    }
  })
  handleRiskLevelClick(key) {
    const {
      location: {
        query: {
          riskLevel,
        }
      }
    } = this.props;
    try {
      const list = this.getSelectedList(JSON.parse(riskLevel), key);
      this.props.changeQuery({
        riskLevel: JSON.stringify(list),
        pageNum: 1,
      });
    } catch (error) {
      console.error(error);
    }
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-产品分类',
      value: '$args[0]',
    }
  })
  handleProductClick(key) {
    const {
      location: {
        query: {
          productClassification,
        }
      }
    } = this.props;
    try {
      const list = this.getSelectedList(JSON.parse(productClassification), key);
      this.props.changeQuery({
        productClassification: JSON.stringify(list),
        pageNum: 1,
      });
    } catch (error) {
      console.error(error);
    }
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-赛道分类',
      value: '$args[0]',
    }
  })
  handleTrackClick(key) {
    const {
      location: {
        query: {
          trackClassification,
        }
      }
    } = this.props;
    try {
      const list = this.getSelectedList(JSON.parse(trackClassification), key);
      this.props.changeQuery({
        trackClassification: JSON.stringify(list),
        pageNum: 1
      });
    } catch (error) {
      console.error(error);
    }
  }

  render() {
    const {
      location: {
        query: {
          riskLevel: activeRiskLevel = DEFAULT_FILTER_STRING,
          productClassification: activeProductClassification = DEFAULT_FILTER_STRING,
          trackClassification: activeTrackClassification = DEFAULT_FILTER_STRING,
          investStrategy: activeInvestStrategy = DEFAULT_FILTER_STRING,
          assetType: assetTypeKey = DEFAULT_FILTER_STRING,
          productPool: productPoolKey,
          productType: productTypeKey,
          activityTag: activityTagKey,
          tradeStatus: tradeStatusKey,
        }
      },
      productFilters: {
        productPoolPublic,
        productPoolPrivate,
        productType,
        activityTag,
        assetType,
        tradeStatus,
        riskLevel,
        trackClassification,
        productClassification,
        investStrategy,
      },
    } = this.props;

    const { unfold } = this.state;
    return (
      <div>
        <div className={styles.searchArea}>
          <div className={`${styles.custFilter} ${styles.filterItem}`}>
            <AutoComplete
              className={styles.custFilter}
              dataSource={this.getAutoCompleteOptions()}
              onSearch={debounce(this.handleSearchProduct, 500)}
              onSelect={this.handleSearchSelect}
              defaultActiveFirstOption={false}
              onChange={this.handleSearchBlur}
              allowClear
              optionLabelProp="title"
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
            >
              <Input.Search placeholder="产品代码/产品简称" />
            </AutoComplete>
          </div>
          <div className={`${styles.filterItem}`}>
            <HtFilter
              filterName="产品池范围"
              filterId="productPool"
              type="single"
              value={productPoolKey}
              data={productTypeKey === PUBLIC ? productPoolPublic : productPoolPrivate}
              onChange={this.handleProductPoolChange}
            />
          </div>
          <div className={`${styles.filterItem}`}>
            <HtFilter
              filterName="产品类型"
              filterId="productType"
              type="single"
              value={productTypeKey}
              data={productType}
              onChange={this.handleProductTypeChange}
            />
          </div>
          <div className={`${styles.filterItem}`}>
            <HtFilter
              data={assetType}
              onChange={this.handleAssetTypeChange}
              filterName="资产类别"
              filterId="assetType"
              type="multi"
              value={JSON.parse(assetTypeKey)}
              dataMap={['key', 'value']}
            />
          </div>

          <div className={`${styles.filterItem}`}>
            <HtFilter
              filterName="活动标签"
              filterId="activityTag"
              type="single"
              value={activityTagKey}
              data={activityTag}
              onChange={this.handleActivityTagChange}
            />
          </div>
          <div className={`${styles.filterItem}`}>
            <HtFilter
              filterName="交易状态"
              filterId="tradeStatus"
              type="single"
              value={tradeStatusKey}
              data={tradeStatus}
              onChange={this.handleTradeTypeChange}
            />
          </div>
        </div>
        <div className={styles.splitLine} />
        <div className={styles.extraFilters}>
          <div className={styles.label}>风险等级</div>
          <div className={styles.valueLine}>
            {
              map(riskLevel, (valueItem) => (
                <span
                  className={classnames({
                    [styles.value]: true,
                    [styles.activeValue]: this.isActive(activeRiskLevel, valueItem.key)
                  })}
                  key={valueItem.key}
                  onClick={() => this.handleRiskLevelClick(valueItem.key)}
                >
                  {valueItem.value}
                </span>
              ))
            }
          </div>
        </div>
        <IFWrap when={productTypeKey === PUBLIC}>
          <div className={styles.extraFilters}>
            <div className={styles.label}>赛道分类</div>
            <div className={styles.valueLine}>
              {
                map(trackClassification, (valueItem) => (
                  <span
                    className={classnames({
                      [styles.value]: true,
                      [styles.activeValue]: this.isActive(activeTrackClassification, valueItem.key)
                    })}
                    key={valueItem.key}
                    onClick={() => this.handleTrackClick(valueItem.key)}
                  >
                    {valueItem.value}
                  </span>
                ))
              }
            </div>
          </div>
        </IFWrap>
        <IFWrap when={productTypeKey === PUBLIC}>
          <div className={styles.extraFilters}>
            <div className={styles.label}>产品分类</div>
            <div className={styles.valueLine}>
              {
              map(productClassification, (valueItem) => (
                <span
                  className={classnames({
                    [styles.value]: true,
                    [styles.activeValue]: this.isActive(activeProductClassification, valueItem.key)
                  })}
                  key={valueItem.key}
                  onClick={() => this.handleProductClick(valueItem.key)}
                >
                  {valueItem.value}
                </span>
              ))
            }
            </div>
          </div>
        </IFWrap>
        <IFWrap when={productTypeKey === PRIVATE}>
          <div className={styles.extraFilters}>
            <div className={styles.label}>投资策略</div>
            <div className={classnames({
              [styles.valueLine]: true,
              [styles.investLine]: true,
              [styles.investLineUnfold]: unfold,
            })}
            >
              {
              map(investStrategy, (valueItem) => (
                <span
                  className={classnames({
                    [styles.value]: true,
                    [styles.activeValue]: this.isActive(activeInvestStrategy, valueItem.key)
                  })}
                  key={valueItem.key}
                  onClick={() => this.handleInvestStrategyClick(valueItem.key)}
                >
                  {valueItem.value}
                </span>
              ))
            }
            </div>
            <div className={styles.more} onClick={this.handlMoreClick}>
              {
                !unfold ? '更多' : '收起'
              }
              <img src={!unfold ? downIcon : upIcon} alt="more" />
            </div>
          </div>
        </IFWrap>
      </div>

    );
  }
}
