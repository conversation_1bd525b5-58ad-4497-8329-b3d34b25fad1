/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-02-13 14:46:49
 * @description 资产配置-配置建议-产品item
 */
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Input, message } from 'antd';
import { map, isEmpty, isNaN } from 'lodash';

import IFWrap from '@/components/common/IFWrap';
import { getProductCenterUrl } from '@/utils/productCenter';
import { logCommon } from '@/decorators/logable';
import closeIcon from './images/close.svg';
import { NODATA } from './config';

import styles from './productItem.less';

export default function ProductItem(props) {
  const {
    data,
    onDeleteClick,
    onOkClick,
    viewFlag,
  } = props;

  const [weight, setWeight] = useState(data?.weight);

  const url = getProductCenterUrl({
    code: data?.productCode,
  });

  const handleInputChange = (e) => {
    // 替换非数字字符为 ''
    let value = e.target.value.replace(/[^\d.]/g, '');
    // 只保留小数点后两位
    const index = value.indexOf('.');
    if (index !== -1) {
      const valueList = value.split('.');
      value = `${valueList[0]}.${valueList[1].slice(0, 2)}`;
    }

    setWeight(value);

    if (value <= 0 || isNaN(Number(value))) {
      message.error('配置比例需大于0');
      onOkClick({
        ...data,
        weight: 0,
      });
      return;
    }
    onOkClick({
      ...data,
      weight: Number(value)
    });
  };

  const renderRatioArea = () => {
    if (!viewFlag) {
      return (
        <Input
          suffix="%"
          autoComplete="off"
          value={weight}
          onChange={handleInputChange}
          defaultValue={data.weight}
        />
      );
    }
    return `${data?.weight}%`;
  };

  const handleDeleteProduct = () => {
    onDeleteClick({
      ...data,
    });
  };

  const handleJumpProductCenter = () => {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-配置建议-跳转到产品详情',
        value: JSON.stringify(data),
      },
    });
  };

  return (
    <div className={styles.contentItem}>
      <div className={styles.leftWrap}>
        <div className={styles.titleBar}>
          <div>
            <div className={styles.productName}>
              <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => handleJumpProductCenter(data)}
              >
                {data?.productName}
              </a>
            </div>
            <div className={styles.productCode}>{data?.productCode}</div>
            <div className={styles.statusText}>{data?.statusText}</div>
            {
              map(data?.tags, (item) => (
                <div
                  key={item.name}
                  className={styles[item.type]}
                >
                  {item.name}
                </div>
              ))
            }
          </div>
          <div className={styles.weightArea}>
            <div className={styles.ratioLabel}>配置比例：</div>
            <div className={styles.ratioValue}>{renderRatioArea()}</div>
          </div>
        </div>
        <div className={styles.contentArea}>
          {
            map(data?.otherInfos, (item) => (
              <div className={styles.info} key={item.label}>
                <div>{item.label}：</div>
                <div>{item.value || NODATA}</div>
                <IFWrap when={item.hasShouFaTag}>
                  <div className={styles.shoufa}>首发</div>
                </IFWrap>
              </div>
            ))
          }
          <IFWrap when={!isEmpty(data.recommendReason)}>
            <div className={styles.reason}>
              <div>推荐理由：</div>
              <div>{data.recommendReason}</div>
            </div>
          </IFWrap>
        </div>
      </div>
      <IFWrap when={!viewFlag}>
        <div className={styles.button} onClick={handleDeleteProduct}>
          <img src={closeIcon} alt="close" />
        </div>
      </IFWrap>
    </div>
  );
}

ProductItem.propTypes = {
  data: PropTypes.object.isRequired,
  onDeleteClick: PropTypes.func.isRequired,
  onOkClick: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
};
