/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-08 09:32:53
 * @description 资产配置-配置建议-添加产品弹窗
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { sensors } from '@lego/bigbox-utils';
import { isNull, isEmpty } from 'lodash';

import Modal from '@/components/common/newUI/modal';
import withRouter from '@/decorators/withRouter';
import SearchArea from './SearchArea';
import TableArea from './TableArea';
import { DEFAULT_FILTER_STRING } from './config';

import styles from './addProductModal.less';

const { logable } = sensors;

@withRouter
export default class AddProductModal extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    visible: PropTypes.bool.isRequired,
    onCloseModal: PropTypes.func.isRequired,
    // 获取产品筛选信息
    getProductFilters: PropTypes.func.isRequired,
    productFilters: PropTypes.object.isRequired,
    // 模糊搜索产品信息
    getProductListByKeyword: PropTypes.func.isRequired,
    productSearchList: PropTypes.array.isRequired,
    // 获取产品信息
    getProductData: PropTypes.func.isRequired,
    productData: PropTypes.object.isRequired,
    setFinalProducts: PropTypes.func.isRequired,
    // 第一个页面配置的产品信息
    assetProductList: PropTypes.array.isRequired,
    saveKycData: PropTypes.object.isRequired,
    custMess: PropTypes.object.isRequired,
    updateReduxData: PropTypes.func.isRequired,
  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      selectedRows: null
    };
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    const { assetProductList } = nextProps;
    const { selectedRows } = prevState;
    if (assetProductList !== selectedRows && isNull(selectedRows)) {
      return {
        selectedRows: assetProductList,
      };
    }
    return null;
  }

  componentDidMount() {
    const {
      location: {
        query: {
          productType,
        }
      },
    } = this.props;
    if (!isEmpty(productType)) {
      this.getProductList();
    }
  }

  componentDidUpdate(prevProps) {
    const { location: { query: nextQuery } } = this.props;
    const { location: { query: prevQuery } } = prevProps;
    if (nextQuery !== prevQuery) {
      this.getProductList();
    }
  }

  componentWillUnmount() {
    this.props.updateReduxData({
      productSearchList: [],
    });
  }

  @autobind
  getProductList() {
    const {
      location: {
        query,
        query: {
          id = '',
          pageNum = 1,
          riskLevel,
          trackClassification,
          productClassification,
          investStrategy,
          assetType,
        }
      },
      custMess,
    } = this.props;
    const assetTypeValue = JSON.parse(assetType);
    try {
      this.props.getProductData({
        ...query,
        pageNum: Number(pageNum),
        pageSize: 10,
        custId: custMess?.custId,
        assetAllocationId: id,
        riskLevel: JSON.parse(riskLevel),
        trackClassification: JSON.parse(trackClassification),
        productClassification: JSON.parse(productClassification),
        investStrategy: JSON.parse(investStrategy),
        assetType: isEmpty(assetTypeValue) ? [''] : assetTypeValue,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error);
    }
  }

  @autobind
  handleQueryChange(params) {
    const {
      location: { query, pathname },
    } = this.props;
    this.context.replace({
      pathname,
      query: {
        ...query,
        ...params,
      },
    });
    return Promise.resolve();
  }

  // 点击确认
  @autobind
  @logable({
    type: 'Submit',
    payload: {
      name: '资产配置-添加产品-确定',
    },
  })
  onOk() {
    const { selectedRows } = this.state;
    this.props.setFinalProducts(selectedRows);
    this.onCancel();
  }

  @autobind
  @logable({
    type: 'ButtonClick',
    payload: {
      name: '资产配置-添加产品-取消',
    },
  })
  onCancel() {
    this.handleQueryChange({
      productPool: '',
      productType: '',
      activityTag: '',
      productCode: '',
      tradeStatus: '',
      assetType: DEFAULT_FILTER_STRING,
      riskLevel: DEFAULT_FILTER_STRING,
      trackClassification: DEFAULT_FILTER_STRING,
      productClassification: DEFAULT_FILTER_STRING,
      investStrategy: DEFAULT_FILTER_STRING,
      pageNum: 1,
    });
    this.props.onCloseModal();
  }

  @autobind
  changeProduct(selectedRows) {
    this.setState({
      selectedRows,
    });
  }

  render() {
    const {
      visible,
      getProductFilters,
      productFilters,
      getProductListByKeyword,
      productSearchList,
      getProductData,
      productData,
      saveKycData,
      custMess
    } = this.props;

    const { selectedRows } = this.state;

    return (
      <Modal
        title="添加产品"
        modalKey="addProduct"
        maskClosable={false}
        size="large"
        visible={visible}
        onModalClose={this.props.onCloseModal}
        wrapClassName={styles.productModal}
        modalFooter={[
          {
            key: 'choiceApproverCancelBtn',
            text: '取消',
            onClick: this.onCancel,
          },
          {
            key: 'choiceApproverConfirmBtn',
            text: '确定',
            type: 'primary',
            onClick: this.onOk,
          }
        ]}
      >
        <div>
          <SearchArea
            getProductFilters={getProductFilters}
            productFilters={productFilters}
            getProductListByKeyword={getProductListByKeyword}
            productSearchList={productSearchList}
            getProductData={getProductData}
            changeQuery={this.handleQueryChange}
            productData={productData}
            saveKycData={saveKycData}
            custMess={custMess}
          />
          <TableArea
            data={productData}
            selectedRows={selectedRows}
            getList={this.getProductList}
            changeQuery={this.handleQueryChange}
            changeProduct={this.changeProduct}
            getProductData={getProductData}
            saveKycData={saveKycData}
            custMess={custMess}
          />
        </div>
      </Modal>
    );
  }
}
