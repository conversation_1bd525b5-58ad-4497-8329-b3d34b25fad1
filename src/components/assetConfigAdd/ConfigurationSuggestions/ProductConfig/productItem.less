.contentItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .leftWrap {
    font-size: 14px;
    color: #333;
    border-radius: 12px;
    flex: 1;
    margin-bottom: 15px;
    width: calc(100% - 30px);
    border: 1px solid #e4ecf1;

    .titleBar {
      height: 62px;
      padding: 0 19px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      background: linear-gradient(270deg, rgba(245, 245, 245, 0) 0%, #f5f5f5 100%);
      border-radius: 12px 0;

      & > div {
        display: flex;
      }

      .productName {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        line-height: 22px;
        margin-right: 10px;

        a {
          color: #333;
        }
      }

      .productCode,
      .statusText {
        font-size: 13px;
        color: #333;
        line-height: 20px;
        margin-right: 10px;
      }

      .track {
        height: 18px;
        font-size: 12px;
        padding: 0 6px;
        color: #e89f4a;
        text-align: center;
        line-height: 18px;
        background: #fff4e7;
        border-radius: 2px;
        margin-right: 5px;
      }

      .style {
        height: 18px;
        font-size: 12px;
        padding: 0 6px;
        color: #749cf4;
        text-align: center;
        line-height: 18px;
        background: #eef3ff;
        border-radius: 2px;
        margin-right: 5px;
      }

      .activity {
        height: 18px;
        font-size: 12px;
        padding: 0 6px;
        color: #6cb6d6;
        text-align: center;
        line-height: 18px;
        background: #e7f8ff;
        border-radius: 2px;
        margin-right: 5px;
      }

      .weightArea {
        display: flex;
        align-items: baseline;
      }

      .ratioLabel {
        font-size: 14px;
        color: #666;
        line-height: 20px;
        margin-top: 4px;
      }

      .ratioValue {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        line-height: 24px;
        width: 80px;
      }

      .editBtn {
        margin-left: 10px;
        cursor: pointer;

        & > img {
          width: 17px;
          height: 18px;
        }
      }

      .okBtn {
        margin-left: 10px;
        cursor: pointer;
        font-size: 14px;
        color: #108ee9;
        line-height: 20px;
      }

      .title {
        line-height: 20px;
        margin-right: 5px;
        max-width: 70%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .icon {
        width: 14px;
        height: 14px;
        margin-top: -4px;
      }
    }

    .contentArea {
      padding: 15px 20px;
      display: flex;
      flex-wrap: wrap;

      .info {
        display: flex;
        align-items: center;
        margin-right: 60px;
        font-size: 14px;
        color: #666;
        line-height: 20px;

        .shoufa {
          width: 40px;
          height: 18px;
          background: #fdb02b;
          border-radius: 1px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          color: #fff;
          line-height: 14px;
          margin-left: 5px;
        }
      }

      .reason {
        display: flex;
        margin-top: 8px;
        width: 100%;
        font-size: 14px;
        color: #666;
        line-height: 20px;

        & div:last-child {
          width: calc(100% - 70px);
        }
      }
    }
  }

  .button {
    cursor: pointer;
    margin: 0 0 15px 20px;

    & > img {
      width: 20px;
      height: 20px;
    }
  }
}
