.wrap {
  width: 100%;
  height: 100%;
  margin-top: 20px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 2px;
  border: 1px solid #e4ecf1;

  .tabBar {
    width: 100%;
    height: 56px;
    background: #5097e4;
    border-radius: 2px 2px 0 0;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .tabs {
      display: flex;
      padding-top: 8px;

      .tabItem {
        color: #fff;
        font-size: 14px;
        line-height: 20px;
        padding: 0 20px;
        display: flex;
        align-items: center;
        cursor: pointer;
        position: relative;

        .newCircle {
          width: 6px;
          height: 6px;
          background: #f37573;
          border-radius: 50%;
          position: absolute;
          top: 13px;
          right: 13px;
        }
      }

      .activeTabItem {
        background: #fff;
        border-radius: 8px 8px 0 0;
        font-size: 16px;
        font-weight: bold;
        color: #108ee9;
        line-height: 20px;
        padding: 0 16px;

        .text {
          height: 100%;
          border-bottom: 2px solid #108ee9;
          box-sizing: border-box;
          padding: 14px 0;

          &:hover {
            cursor: pointer;
          }
        }
      }
    }

    .totalArea {
      display: flex;
      font-size: 14px;
      color: #fff;
      line-height: 19px;
      padding: 23px 0 14px;
      align-items: center;

      .item {
        display: flex;
        align-items: baseline;
        font-size: 14px;
        color: #fff;
        line-height: 19px;
        margin-left: 30px;

        & span:last-child {
          font-size: 18px;
          font-weight: bold;
          line-height: 24px;
        }
      }

      .addBtn {
        width: 80px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        font-size: 14px;
        font-weight: bold;
        color: #108ee9;
        line-height: 20px;
        border-radius: 2px;
        cursor: pointer;
        margin-left: 30px;
      }
    }
  }

  .contentArea {
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
  }

  ::-webkit-scrollbar {
    display: none;
  }
}

.viewFlag {
  border: none;

  .contentArea {
    padding: 0;
  }
}
