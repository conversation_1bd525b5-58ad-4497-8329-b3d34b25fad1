.searchArea {
  display: flex;
  padding: 10px 0;

  & > .filterItem {
    margin-right: 10px;
    height: 30px;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;

    .asset {
      margin: -4px 10px 0 10px;
    }

    .productOption {
      display: flex;
      width: 100%;
      justify-content: space-between;

      .product {
        max-width: 230px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .match {
        color: #999;
      }

      .disbaled {
        color: #ccc;
      }
    }

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .lego-date-rangePicker-container {
        position: static;
        display: inline-block;
      }

      .lego-filter-menu {
        width: auto;
      }

      .lego-disabled .lego-selection--single,
      .lego-disabled .lego-selection__choice__remove {
        background-color: #f5f5f5;
        border: 1px solid #d9d9d9;
      }

      .lego-selection__rendered {
        height: 30px;
        line-height: 30px;

        .lego-selection-selected-filterName {
          font-size: 14px;
        }

        .lego-selection-selected-value {
          font-size: 14px;
        }
      }

      .lego-filter-filterWrapper .ant-btn[disabled],
      .lego-filter-filterWrapper .ant-btn[disabled]:hover {
        background-color: #f5f5f5;
        color: #ccc !important;
      }
    }
  }

  & > .custFilter {
    margin-right: 20px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-input {
        width: 228px;
        height: 30px;
      }

      .ant-select-dropdown {
        width: 400px !important;
      }

      .ant-select-dropdown-menu-item-disabled {
        color: #ccc;
      }
    }
  }
}

.splitLine {
  width: 100%;
  height: 1px;
  background: #ddd;
  margin-bottom: 20px;
}

.extraFilters {
  display: flex;

  .label {
    display: flex;
    align-items: center;
    width: 171px;
    height: 40px;
    background: #f0f2f5;
    box-shadow: 0 1px 0 0 #f7f7f7, inset 0 1px 0 0 #f7f7f7;
    font-size: 13px;
    color: #666;
    line-height: 18px;
    text-align: left;
    padding-left: 15px;
    box-sizing: border-box;
  }

  .value {
    font-size: 13px;
    color: #333;
    margin: 0 10px 0 20px;
    cursor: pointer;
    display: inline-block;
    height: 40px;
    line-height: 40px;
  }

  .valueLine {
    width: 949px;
    box-shadow: 0 1px 0 0 #f7f7f7, inset 0 1px 0 0 #f7f7f7;
  }

  .investLine {
    width: 880px;
    height: 40px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    overflow: hidden;
    box-shadow: none;
  }

  .investLineUnfold {
    height: auto;
  }

  .activeValue {
    color: #108ee9;
  }

  .more {
    width: 62px;
    height: 24px;
    border-radius: 2px;
    border: 1px solid #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    color: #666;
    margin-top: 8px;
    cursor: pointer;

    & > img {
      width: 8px;
      height: 4px;
      margin-left: 6px;
    }
  }

  /** 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
}
