/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-08 09:45:39
 * @description 资产配置-配置建议-添加产品-表格区域
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { sensors } from '@lego/bigbox-utils';
import {
  map,
  isEmpty,
  isNull,
  filter,
  find,
  includes
} from 'lodash';
import classnames from 'classnames';
import Table, { ToolTipCell } from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import Modal from '@/components/common/newUI/modal';
import SimplyProductItem from './SimplyProductItem';
import infoIcon from './images/info.svg';
import {
  TABLE_COLUMN,
  NODATA,
  SPACE_20,
  TABLE_PLACE_HOLDER,
} from './config';

import styles from './tableArea.less';

const { logable } = sensors;

export default class TableArea extends PureComponent {
  static propTypes = {
    // 列表数据
    data: PropTypes.object.isRequired,
    // 修改location中的query
    changeQuery: PropTypes.func.isRequired,
    changeProduct: PropTypes.func.isRequired,
    selectedRows: PropTypes.array.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      recommendReasonVisible: false,
      recommendReason: '',
      moreProductVisible: false,
    };
  }

  @autobind
  renderColumns() {
    return map(TABLE_COLUMN, (column) => {
      const { key } = column;
      if (key === 'productName') {
        return {
          ...column,
          render: (text, record) => (text ? (
            this.renderCell(`${record?.productName}(${record?.productCode})`, record)
          ) : (
            this.renderNoData()
          )),
        };
      }
      if (key === 'recommendReason') {
        return this.renderRecommendReason(column);
      }
      return {
        ...column,
        render: (text, record) => this.renderCell(text, record),
      };
    });
  }

  @autobind
  renderCell(text, record) {
    if (record.flag) {
      return null;
    }
    if (text === '' || isNull(text)) {
      return this.renderNoData();
    }
    return (
      <div className={classnames({ [styles.noData]: !record?.assetTypeMatch })}>
        <ToolTipCell tipContent={text} cellText={text} />
      </div>
    );
  }

  @autobind
  renderNoData() {
    return <span className={styles.noData}>{NODATA}</span>;
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-查看推荐理由',
    }
  })
  handleShowRecommendReason(text) {
    this.setState({
      recommendReasonVisible: true,
      recommendReason: text,
    });
  }

  @autobind
  handleCloseReasonModal() {
    this.setState({
      recommendReasonVisible: false,
      recommendReason: '',
    });
  }

  @autobind
  handleShowMoreProduct() {
    this.setState({
      moreProductVisible: true,
    });
  }

  @autobind
  handleCLoseMoreProduct() {
    this.setState({
      moreProductVisible: false,
    });
  }

  @autobind
  renderRecommendReason(column) {
    return {
      ...column,
      render: (text) => {
        if (isEmpty(text)) {
          return this.renderNoData();
        }
        return (
          <div
            className={styles.operateBtn}
            onClick={() => this.handleShowRecommendReason(text)}
          >
            查看
          </div>
        );
      }
    };
  }

  @autobind
  handlePageChange(pageNum) {
    this.props.changeQuery({
      pageNum: String(pageNum),
    });
  }

  @autobind
  @logable({
    type: 'Select',
    payload: {
      name: '资产配置-添加产品-选择产品',
      value: '$args[0]',
    }
  })
  handleRowSelect(record) {
    const { selectedRows, changeProduct } = this.props;
    let rows;
    if (isEmpty(find(selectedRows, (item) => item.tecPrdtId === record.tecPrdtId))) {
      rows = [...selectedRows, record];
    } else {
      rows = filter(selectedRows, (item) => item.tecPrdtId !== record.tecPrdtId);
    }
    changeProduct(rows);
  }

  @autobind
  handleSelectAll(selected, selectRows, changeRows) {
    const { selectedRows, changeProduct } = this.props;
    let rows;
    if (selected) {
      rows = [...selectedRows, ...changeRows];
    } else {
      const unSelectedRowKeys = map(changeRows, (item) => item.tecPrdtId);
      rows = filter(selectedRows, (item) => !includes(unSelectedRowKeys, item.tecPrdtId));
    }
    changeProduct(rows);
  }

  // 删除产品
  @autobind
  handleDeleteProduct(tecPrdtId) {
    const { selectedRows, changeProduct } = this.props;
    const finalData = filter(selectedRows, (item) => item.tecPrdtId !== tecPrdtId);
    changeProduct(finalData);
  }

  render() {
    const {
      data: { list = [], page = {} },
      selectedRows,
    } = this.props;
    const {
      recommendReasonVisible,
      recommendReason,
      moreProductVisible,
    } = this.state;
    const { pageNum, totalCount } = page;
    // 渲染表格
    const columns = this.renderColumns();

    const rowSelection = {
      onChange: this.handleSelectChange,
      selectedRowKeys: map(selectedRows, (item) => item.tecPrdtId),
      onSelect: this.handleRowSelect,
      onSelectAll: this.handleSelectAll,
      getCheckboxProps: (record) => ({
        disabled: !record?.assetTypeMatch ?? false,
        name: record?.name ?? '',
      }),
    };

    return (
      <div className={styles.tableArea}>
        <IFWrap when={!isEmpty(selectedRows)}>
          <div className={styles.selectedProduct}>
            <div className={styles.left}>
              <img src={infoIcon} alt="info" />
              已选择
              <span className={styles.amount}>{selectedRows?.length || 0}</span>
              个产品：
              <div className={styles.productArea}>
                {
                  map(selectedRows, (item) => (
                    <div className={styles.simplyProductItem} key={item.tecPrdtId}>
                      <SimplyProductItem data={item} onDeleteClick={this.handleDeleteProduct} />
                    </div>
                  ))
                }
              </div>
            </div>
            <div className={styles.moreBtn} onClick={this.handleShowMoreProduct}>查看</div>
          </div>
        </IFWrap>
        <Table
          useNewUI
          pagination={false}
          columns={columns}
          dataSource={list}
          rowKey={(record) => record?.tecPrdtId}
          spaceColumnProps={SPACE_20}
          placeHolderImageProps={TABLE_PLACE_HOLDER}
          rowSelection={rowSelection}
        />
        <IFWrap when={!isEmpty(list)}>
          <div className={styles.pageArea}>
            <div className={styles.pageAreaTip}>*列表中置灰产品为当前未选择配置资产大类所属标的</div>
            <Pagination
              pageSize={10}
              current={Number(pageNum) || 1}
              total={totalCount || 0}
              showTotal={(total) => `共${total}条`}
              onChange={this.handlePageChange}
            />
          </div>
        </IFWrap>
        <IFWrap when={recommendReasonVisible}>
          <Modal
            title="推荐理由"
            modalKey="reason"
            maskClosable={false}
            size="small"
            visible={recommendReasonVisible}
            onModalClose={this.handleCloseReasonModal}
            modalFooter={null}
          >
            <div className={styles.recommendReason}>{recommendReason}</div>
          </Modal>
        </IFWrap>
        <IFWrap when={moreProductVisible}>
          <Modal
            title="已选择产品"
            modalKey="product"
            maskClosable={false}
            size="small"
            visible={moreProductVisible}
            onModalClose={this.handleCLoseMoreProduct}
            modalFooter={null}
          >
            <div className={styles.productList}>
              {
                map(selectedRows, (item) => (
                  <div className={styles.simplyProductItem} key={item.tecPrdtId}>
                    <SimplyProductItem data={item} onDeleteClick={this.handleDeleteProduct} />
                  </div>
                ))
              }
            </div>
          </Modal>
        </IFWrap>
      </div>
    );
  }
}
