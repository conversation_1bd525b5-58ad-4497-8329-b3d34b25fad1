.tableArea {
  margin-top: 20px;

  .selectedProduct {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    margin-bottom: 10px;
    background: #f0faff;
    border-radius: 2px;
    font-size: 14px;
    color: #333;
    line-height: 18px;

    .left {
      display: flex;
      align-items: center;

      &>img {
        width: 14px;
        height: 14px;
        margin-right: 10px;
      }
    }

    .amount {
      color: #2384ca;
      line-height: 20px;
    }

    .productArea {
      display: flex;
      overflow: hidden;
      width: 900px;
      flex-wrap: wrap;
      height: 24px;

      .simplyProductItem {
        margin-right: 10px;
      }
    }

    .moreBtn {
      color: #108ee9;
      line-height: 20px;
      cursor: pointer;
    }
  }

  .activetyName {
    width: 210px;
  }

  .operateBtn {
    cursor: pointer;
    color: #108ee9;

    span {
      padding-right: 10px;
      cursor: pointer;
    }

    .disbaled {
      color: #ccc;
      cursor: default;
    }
  }

  .pageArea {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .pageAreaTip {
      font-size: 14px;
      color: #999;
    }
  }

  .noData {
    color: #ccc !important;
  }

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-checkbox-disabled {
      //&:after {
      //  height: 8px;
      //  border: 2px solid #ccc;
      //  border-top: 0;
      //  border-left: 0;
      //  transform: rotate(45deg) scale(1) translate(-50%,-50%);
      //  opacity: 1;
      //}
    }
  }
}

.recommendReason {
  padding: 20px 0 58px;
  font-size: 14px;
  color: #333;
  line-height: 24px;
}

.productList {
  width: 390px;
  padding: 20px 0 58px;
  display: flex;
  flex-wrap: wrap;

  .simplyProductItem {
    margin: 0 10px 16px 0;
  }
}