export const TABLE_COLUMN = [
  {
    key: 'productName',
    dataIndex: 'productName',
    title: '产品名称',
    width: 130,
  },
  {
    key: 'riskLevel',
    dataIndex: 'riskLevel',
    title: '风险等级',
    width: 60,
  },
  {
    key: 'assetType',
    dataIndex: 'assetType',
    title: '资产类别',
    width: 60,
  },
  {
    key: 'netWorth',
    dataIndex: 'netWorth',
    title: '单位净值',
    width: 60,
  },
  {
    key: 'netWorthDate',
    dataIndex: 'netWorthDate',
    title: '净值时间',
    width: 60,
  },
  {
    key: 'threeMonthYield',
    dataIndex: 'threeMonthYield',
    title: '近3个月收益率',
    width: 70,
  },
  {
    key: 'netWorthCumulative',
    dataIndex: 'netWorthCumulative',
    title: '累计净值',
    width: 60,
  },
  {
    key: 'track',
    dataIndex: 'track',
    title: '赛道分类',
    width: 60,
  },
  {
    key: 'investStrategy',
    dataIndex: 'investStrategy',
    title: '投资策略',
    width: 60,
  },
  {
    key: 'ifRecommend',
    dataIndex: 'ifRecommend',
    title: '是否推荐',
    width: 50,
  },
  {
    key: 'recommendReason',
    dataIndex: 'recommendReason',
    title: '推荐理由',
    width: 50,
  },
];

// 无数据
export const NODATA = '--';

// 列间距20
export const SPACE_20 = {
  width: 20,
};

export const TABLE_PLACE_HOLDER = {
  style: { height: '240px' },
};

export const PUBLIC = '0';
export const PRIVATE = '1';

export const DATE_FORMAT = 'MM-DD';

// eslint-disable-next-line no-useless-escape
export const NUMBER_TEST = /[^\d{1,}\.\d{1,}|\d{1,}]/g;

export const DEFAULT_FILTER_STRING = '[""]';
