import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import IECharts from '@/components/IECharts';
import IFWrap from '@/components/common/IFWrap';
import { size } from 'lodash';
import SetTitSvg from '@/components/assetConfigAdd/images/setTit.svg';
import ArrowIcon from '../../static/two_arrow.png';
import styles from './index.less';

// 图表颜色列表
export const COLOR_LIST = [
  '#66affd',
  '#3c71b9',
  '#708bf7',
  '#febf68',
  '#4085fe',
  '#2bc8db',
];

export default function AssetScheme(props) {
  const {
    data,
  } = props;
  const options = {
    color: COLOR_LIST,
    tooltip: {
      trigger: 'item',
      formatter: (params) => `${params?.data?.name} ${params?.data?.value}%`,
      textStyle: {
        fontStyle: 10,
      },
    },
    legend: {
      show: false,
      data: map(data?.tableData, (item) => item?.type),
    },
    animation: false,
    series: [
      {
        name: '大类资产配比分布为时点仓位',
        type: 'pie',
        radius: [60, 80],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: map(data?.tableData, (item) => ({
          value: item?.ratio,
          name: item?.type,
        })),
      },
    ],
  };
  const typeRender = () => map(data?.tableData, (item, index) => (
    <div className={styles.tableTd} key={index}>
      <div
        className={styles.tableTip}
        style={{ backgroundColor: COLOR_LIST[index] }}
      />
      <div className={styles.tableText}>{item?.type}</div>
    </div>
  ));
  const assetRender = () => map(data?.tableData, (item, index) => (
    <div className={styles.tableTd} key={index}>
      <div className={styles.tableTdPercent}>{item?.ratio}%</div>
    </div>
  ));
  return (
    <div className={styles.moduleDetail}>
      <div className={styles.moduleLeft}>
        <img src={SetTitSvg} className={styles.moduleTit} />
        <div className={styles.titBorder} />
        <div
          className={styles.moduleNodtes}
          // eslint-disable-next-line
          dangerouslySetInnerHTML={{
            __html: data?.conclusions ?? '',
          }}
        />
      </div>
      <IFWrap when={size(data?.tableData) !== 0}>
        <>
          <div className={styles.moduleArrow}>
            <img src={ArrowIcon} className={styles.arrowIcon} />
          </div>
          <div className={styles.moduleChart}>
            <div className={styles.chartTips}>
              <div>建议</div>
              <div>配置方案</div>
            </div>
            <IECharts option={options} resizable />
          </div>
          <div className={styles.moduleRight}>
            <div className={styles.tableCell}>
              <div className={styles.tableTr}>资产类别</div>
              {typeRender()}
            </div>
            <div className={styles.tableCell}>
              <div className={styles.tableTr}>建议配置</div>
              {assetRender()}
            </div>
          </div>
        </>
      </IFWrap>
    </div>
  );
}

AssetScheme.propTypes = {
  data: PropTypes.object.isRequired,
};
