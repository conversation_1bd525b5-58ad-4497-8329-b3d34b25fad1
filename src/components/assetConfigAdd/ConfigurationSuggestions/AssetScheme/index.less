.moduleDetail {
  width: 100%;
  min-width: 1200px;
  height: 280px;
  border-radius: 2px;
  display: flex;
  background: #ebf7ff url('./../../static/bgRight.png') no-repeat right bottom;
  background-size: auto 280px;
  justify-content: space-between;

  .moduleArrow {
    width: 84px;
    height: 72px;
    margin: auto 0;
    padding-left: 27px;
  }

  .module<PERSON>hart {
    width: 290px;
    height: 230px;
    position: relative;
    margin: auto 0;

    .chartTips {
      position: absolute;
      left: 0;
      top: 50%;
      font-size: 18px;
      color: #333;
      transform: translateY(-24px);
      line-height: 24px;
      text-align: center;
      width: 100%;
    }
  }

  .moduleRight {
    width: 390px;
    display: flex;
    padding-top: 36px;

    .tableCell {
      width: 120px;
      padding-right: 20px;
      box-sizing: border-box;

      .tableTr {
        font-size: 14px;
        color: #666;
        line-height: 18px;
        margin-bottom: 18px;
      }

      .tableTd {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        line-height: 24px;

        .tableTip {
          width: 6px;
          height: 6px;
          background: #66affd;
          border-radius: 50%;
        }

        .tableText {
          font-size: 14px;
          color: #333;
          margin-left: 8px;
        }

        .tableTdPercent {
          font-size: 18px;
          font-weight: bold;
          color: #333;
        }
      }
    }
  }

  .moduleLeft {
    width: 560px;
    padding-left: 60px;
    background: url('./../../static/bgLeft.png') no-repeat left top;
    background-size: auto 280px;

    .moduleTit {
      width: 168px;
      height: 33px;
      margin-top: 64px;
    }

    .titBorder {
      margin: 16px 0 15px;
      width: 35px;
      height: 4px;
      background: #108ee9;
    }

    .moduleNodtes {
      width: 500px;
      font-size: 14px;
      color: #333;
      letter-spacing: 0;
      line-height: 28px;
    }
  }
}