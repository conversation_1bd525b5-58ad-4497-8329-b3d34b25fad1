import React from 'react';
import PropTypes from 'prop-types';
import {
  includes,
  slice,
  map,
  isEmpty,
  ceil,
  forEach,
  indexOf,
  isNil,
} from 'lodash';
import moment from 'moment';
import echarts from 'echarts';

import IECharts from '@/components/IECharts';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import {
  formatRatio,
} from '@/helper/number';

import {
  LABEL_MY_PLAN,
  LABEL_DEFAULT_PLAN,
  CHART_SYMBOLS,
  RGBA_COLORS,
  LABEL_COLORS,
} from '../../config';

import styles from './index.less';

export default function IncomeTrend(props) {
  const {
    data,
    indexData,
    combinationData,
    viewFlag,
  } = props;

  const handleFormatTooltip = (param) => {
    const length = param?.length ?? 1;
    let classes = [];
    if (length > 1) {
      classes = [styles.averageCircle, styles.custCircle];
    } else {
      classes = param?.[0]?.seriesName === LABEL_MY_PLAN
        ? [styles.custCircle]
        : [styles.averageCircle];
    }
    const secondLine = length > 1
      ? `<div class=${styles.valueLine}>
        <div class=${styles.left}>
          <div class=${classes[1]}></div>
          <div>${param[1].seriesName}</div>
        </div>
        <div>${formatRatio(param[1].data[2]) || '--'}</div>
      </div>`
      : '';
    return (`
      <div class=${styles.tooltip}>
        <div class=${styles.title}>${param[0].axisValue}</div>
        <div class=${styles.valueLine}>
          <div class=${styles.left}>
            <div class=${classes[0]}></div>
            <div>${param[0].seriesName}</div>
          </div>
          <div>${formatRatio(param[0].data[1]) || '--'}</div>
        </div>
        ${secondLine}
      </div>`
    );
  };

  const handleTransferValues = () => {
    const [firstItem, ...rest] = data?.source || [];
    if (!isEmpty(data?.source)) {
      const dateValues = map(firstItem, (child) => {
        if (isEmpty(child)) {
          return child;
        }
        return moment(child).format('YYYY-MM-DD');
      });
      return [
        dateValues,
        ...rest,
      ];
    }
    return [];
  };

  const transferValues = handleTransferValues();

  const handleGenerateIntervalDates = () => {
    const graphDates = transferValues?.[0] ?? [];
    if (!isEmpty(graphDates)) {
      const dates = slice(graphDates, 1);
      const intervalDates = [dates[0]];
      const interval = ceil(dates.length / 12);
      for (let i = 1; i < 12; i++) {
        intervalDates.push(dates?.[i * interval]);
      }
      intervalDates.push(dates[dates.length - 1]);
      return intervalDates;
    }
    return [];
  };

  const handleFormatDate = (date) => {
    if (!isNil(date)) {
      return moment(date).format('YYYY-MM-DD');
    }
    return '--';
  };

  const intervalDates = handleGenerateIntervalDates();

  const handlegenerateLableList = () => {
    const labelList = [];
    forEach(data?.source || [], (item) => {
      if (item?.[0] !== '') {
        labelList.push(item?.[0]);
      }
    });
    return labelList;
  };

  const labelList = handlegenerateLableList();

  const options = {
    grid: {
      left: 0,
      right: 34,
      top: 60,
      bottom: 0,
      containLabel: true
    },
    legend: {
      top: 10,
      right: 0,
      itemWidth: 15,
      itemHeight: 2,
      icon: 'rect',
      selectedMode: false,
      textStyle: {
        color: '#8c8c8c'
      },
    },
    color: [],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'transparent',
      formatter: (param) => handleFormatTooltip(param),
    },
    dataset: {
      source: handleTransferValues(),
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: true,
        alignWithLabel: true,
        interval: (index, value) => {
          if (includes(intervalDates, value)) {
            return true;
          }
          return false;
        }
      },
      axisLabel: {
        interval: 0,
        formatter: (value, index) => {
          if (includes(intervalDates, value)) {
            return value;
          }
          return '';
        },
        textStyle: {
          color: '#666',
          fontSize: 12,
        }
      },
      axisLine: {
        onZero: false,
        lineStyle: {
          color: '#8c8c8c',
        }
      },
    },
    yAxis: {
      gridIndex: 0,
      axisLabel: {
        color: '#666',
        formatter: (value) => {
          if (value === 0) {
            return `{zero|${value}%}`;
          }
          return `${(value * 100).toFixed(0)}%`;
        },
        rich: {
          zero: {
            fontWeight: 'bold',
            color: '#333',
          },
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    series: [],
  };

  // 生成 echats 的 series
  const handleGenerateSeries = ({
    title,
    // 数据索引
    index,
    // 配置索引
    configIndex,
    // 表格数据索引
    tableDataIndex,
  }) => {
    const areaData = indexData?.[index];
    // 最大回撤值
    const max = combinationData?.[tableDataIndex]?.zuiDaHuiChe;
    const symbol = CHART_SYMBOLS[configIndex];
    const rgbaColor = RGBA_COLORS[configIndex];
    const color = LABEL_COLORS[configIndex];
    // 格式化后的最大回撤值
    let showMax = '--';
    if (!isNil(max)) {
      showMax = formatRatio(max);
    }
    const markAreaData = [
      {
        xAxis: handleFormatDate(areaData?.zuiGao?.date),
        yAxis: areaData?.zuiGao?.shouYiLv,
      },
      {
        xAxis: handleFormatDate(areaData?.zuiDi?.date),
        yAxis: areaData?.zuiDi?.shouYiLv,
      },
    ];
    const hasAreaData = !isEmpty(areaData);
    return {
      type: 'line',
      seriesLayoutBy: 'row',
      connectNulls: true,
      emphasis: { focus: 'series' },
      markArea: hasAreaData ? {
        label: {
          formatter: [
            `{a|${title}最大回撤率：${showMax}}`
          ].join('\n'),
          backgroundColor: '#fff',
          borderColor: '#e7e7e7',
          borderWidth: 1,
          borderRadius: 2,
          padding: [8, 10],
          shadowBlur: 4,
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowOffsetX: 0,
          shadowOffsetY: 1,
          opacity: 1,
          position: 'top',
          rich: {
            a: {
              lineHeight: 24,
              color,
              fontSize: 13,
            },
          },
        },
        data: [
          markAreaData,
        ],
        itemStyle: {
          color: rgbaColor,
        }
      } : {},
      markLine: hasAreaData ? {
        symbol: 0,
        data: [
          markAreaData,
        ],
      } : {},
      markPoint: hasAreaData ? {
        data: markAreaData,
        symbol,
        symbolSize: [27, 31],
      } : {}
    };
  };

  const getRenderOptions = () => {
    // 我的配置、推荐配置
    const series = [];
    const colors = [];
    const myPlanIndex = indexOf(labelList, LABEL_MY_PLAN);
    const defaultPlanIndex = indexOf(labelList, LABEL_DEFAULT_PLAN);
    // 如果存在方案，则按照方案的 index 存入 series
    if (myPlanIndex !== -1) {
      series[myPlanIndex] = {
        ...handleGenerateSeries({
          title: LABEL_MY_PLAN,
          index: myPlanIndex,
          configIndex: 0,
          tableDataIndex: 1,
        }),
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0.17,
              color: 'rgba(254, 191, 104, 0.16)'
            }, {
              offset: 0.85,
              color: 'rgba(254, 191, 104, 0)'
            }], false),
          }
        },
      };
      colors[myPlanIndex] = '#febf68';
    }
    if (defaultPlanIndex !== -1) {
      series[defaultPlanIndex] = {
        ...handleGenerateSeries({
          title: LABEL_DEFAULT_PLAN,
          index: defaultPlanIndex,
          configIndex: 1,
          tableDataIndex: 0,
        }),
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0.17,
              color: 'rgba(113, 178, 255, 0.16)'
            }, {
              offset: 0.85,
              color: 'rgba(113, 181, 255, 0)'
            }], false),
          }
        },
      };
      colors[defaultPlanIndex] = '#65aefc';
    }
    options.series = series;
    options.color = colors;
    return options;
  };

  const renderOptions = getRenderOptions();

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="收益趋势"
        rightContent=""
        viewFlag={viewFlag}
      />
      <div className={styles.chartArea}>
        <IECharts
          option={renderOptions}
          resizable
          style={{
            height: '335px',
          }}
          notMerge
        />
        <div className={styles.chartTitle}>
          收益率
          <span>（%）</span>
        </div>
      </div>
    </div>
  );
}

IncomeTrend.propTypes = {
  data: PropTypes.object.isRequired,
  indexData: PropTypes.array.isRequired,
  combinationData: PropTypes.array.isRequired,
  viewFlag: PropTypes.bool.isRequired,
};

IncomeTrend.defaultProps = {
};
