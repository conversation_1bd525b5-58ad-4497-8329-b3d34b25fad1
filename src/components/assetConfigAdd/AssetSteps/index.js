/*
 * @Description: 新建资产配置-步骤组件
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-20 13:51:18
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Button, Steps } from 'antd';
import _ from 'lodash';
import { autobind } from 'core-decorators';

import { getHost } from '@/utils/host';
import withRouter from '@/decorators/withRouter';
import IFWrap from '@/components/common/IFWrap';
import {
  BTN_STOP,
  BTN_SUBMIT,
  NORMAL_REJECT,
  NORMAL_REJECT_CHANGED
} from '@/routes/AssetConfigApproval/config';

import styles from './index.less';

const Step = Steps.Step;

@withRouter
export default class AssetSteps extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    steps: PropTypes.array.isRequired,
    onReplace: PropTypes.func.isRequired,
    disabledNextStepBtn: PropTypes.func,
    onCancel: PropTypes.func,
    onSubmit: PropTypes.func,
    onPreviousStep: PropTypes.func,
    onNextStep: PropTypes.func,
    children: PropTypes.element,
    // 操作按钮行的附加节点
    extraNode: PropTypes.element,
    // 方案回测的事件
    onHandleReturnTestFun: PropTypes.func,
    onReplay: PropTypes.func,
    errorFlag: PropTypes.bool.isRequired,
    isReject: PropTypes.bool.isRequired,
    onApprovalRecordClick: PropTypes.bool.isRequired,
    onStopClick: PropTypes.bool.isRequired,
    flowButtonsAndApprovers: PropTypes.object.isRequired,
  }

  static defaultProps = {
    disabledNextStepBtn: () => false,
    onPreviousStep: null,
    onNextStep: null,
    onCancel: () => null,
    onSubmit: () => null,
    children: null,
    extraNode: null,
    onHandleReturnTestFun: () => null,
    onReplay: () => null,
  }

  // 在具体的步骤, 依赖redux 数据校验下一步按钮是否禁用
  @autobind
  disabledNextStepBtn() {
    return this.props.disabledNextStepBtn();
  }

  @autobind
  async handlePreviousStep() {
    const {
      location: {
        query: {
          current = 0,
        }
      },
      onPreviousStep,
    } = this.props;
    const numberCurrent = Number(current);
    const result = await onPreviousStep(current);
    if (result) {
      this.props.onReplace(numberCurrent - 1);
    }
  }

  @autobind
  async handleNextStep() {
    const {
      location: {
        query: {
          current = 0,
        }
      },
      onNextStep,
    } = this.props;

    const result = await onNextStep(current);
    // 目前默认跳转到第三步
    if (result) {
      this.props.onReplace(+current + 1);
    }
  }

  render() {
    const {
      onCancel,
      disabledNextStepBtn,
      onSubmit,
      steps,
      children,
      extraNode,
      onHandleReturnTestFun,
      onReplay,
      errorFlag,
      isReject,
      location: {
        query: {
          current = 0,
        }
      },
      onApprovalRecordClick,
      onStopClick,
      flowButtonsAndApprovers,
      flowButtonsAndApprovers: {
        approvalStatecode,
      },
    } = this.props;
    const numberCurrent = Number(current);
    const stopButton = _.find(flowButtonsAndApprovers?.buttonList,
      (item) => item.actionName === BTN_STOP);
    const submitButton = _.find(flowButtonsAndApprovers?.buttonList,
      (item) => item.actionName === BTN_SUBMIT);

    const isPdfGrey = getHost().getState()?.global?.canary?.assetconfigpdf;
    return (
      <div className={styles.container}>
        <div className={styles.contentBox}>
          <div className={styles.steps}>
            <Steps current={+current} className={styles.stepsSection}>
              {_.map(steps, (item) => <Step key={item.title} title={item.title} />)}
            </Steps>
          </div>
          <div className={styles.box}>{children}</div>
        </div>
        <div className={styles.actionBox}>
          <div className={styles.action} ref={(node) => { this.container = node; }}>
            <div>{extraNode}</div>
            <div className={styles.buttons}>
              {
                isReject
                  ? (
                    <>
                      <Button
                        type="default"
                        onClick={onApprovalRecordClick}
                      >
                        审批记录
                      </Button>
                    </>
                  )
                  : ''
              }
              <IFWrap when={isReject && !_.isEmpty(stopButton)}>
                <Button
                  type="default"
                  onClick={onStopClick}
                >
                  终止
                </Button>
              </IFWrap>
              {/* 正常的驳回状态或者新建页面才显示按钮（5表示驳回时风险等级或者合投变更） */}
              <IFWrap when={approvalStatecode === NORMAL_REJECT
                || approvalStatecode === NORMAL_REJECT_CHANGED
                || !isReject}
              >
                <Button
                  type="default"
                  onClick={onCancel}
                >
                  取消
                </Button>
                {errorFlag ? (
                  <Button
                    type="default"
                    onClick={onReplay}
                  >
                    重试
                  </Button>
                ) : ''}

                {
                  (numberCurrent === 3) && (
                    <Button
                      type="default"
                      onClick={onHandleReturnTestFun}
                    >
                      方案回测
                    </Button>
                  )
                }
                {
                  (numberCurrent > 0) && (
                    <Button
                      type="default"
                      onClick={this.handlePreviousStep}
                    >
                      上一步
                    </Button>
                  )
                }
                {
                  (numberCurrent < steps.length - 1) && (
                    <Button
                      type="primary"
                      disabled={disabledNextStepBtn(numberCurrent)}
                      onClick={this.handleNextStep}
                    >
                      {(numberCurrent === 3) ? '报告预览' : '下一步' }
                    </Button>
                  )
                }
                {/* 最后一步并且后端返回了提交按钮并且有灰度权限才显示提交按钮 */}
                <IFWrap when={(numberCurrent === steps.length - 1)
                  && !_.isEmpty(submitButton)
                  && isPdfGrey}
                >
                  <Button
                    type="primary"
                    disabled={disabledNextStepBtn(numberCurrent)}
                    onClick={onSubmit}
                  >
                    提交
                  </Button>
                </IFWrap>
              </IFWrap>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
