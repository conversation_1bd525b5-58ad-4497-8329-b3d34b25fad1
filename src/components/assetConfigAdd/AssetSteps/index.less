.container {
  height: 100%;
  background: #fff;

  .contentBox {
    width: 100%;
    height: auto;
    background: #fff;

    .steps {
      padding: 30px 0;

      .stepsSection {
        margin: 0 auto;
        width: 680px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

          /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
          .@{ant-prefix}-steps-item-icon {
            margin-right: 4px;
          }

          .@{ant-prefix}-steps-item-title {
            font-size: 14px;
            padding-right: 10px;
            color: #999;
          }

          .@{ant-prefix}-steps-item {
            margin-right: 10px;
          }

          .@{ant-prefix}-steps-item-process>.@{ant-prefix}-steps-item-content>.@{ant-prefix}-steps-item-title::after {
            background: #ccc;
          }

          .@{ant-prefix}-steps-item-icon,
          .@{ant-prefix}-steps-item-content {
            vertical-align: baseline;
          }

          .@{ant-prefix}-steps-item-process .@{ant-prefix}-steps-item-icon {
            background: #108ee9;
          }

          .@{ant-prefix}-steps-item-wait .@{ant-prefix}-steps-item-icon {
            border-color: #ccc;
          }

          .@{ant-prefix}-steps-item-wait .@{ant-prefix}-steps-item-icon>.@{ant-prefix}-steps-icon {
            color: #999;
          }

          .@{ant-prefix}-steps-item-process>.@{ant-prefix}-steps-item-content>.@{ant-prefix}-steps-item-title {
            color: #333 !important;
            font-weight: bold;
            font-size: 14px;
          }

          .@{ant-prefix}-steps-item-finish>.@{ant-prefix}-steps-item-content>.@{ant-prefix}-steps-item-title {
            color: #333 !important;
          }

          .@{ant-prefix}-steps-item-wait>.@{ant-prefix}-steps-item-content>.@{ant-prefix}-steps-item-title {
            color: #999;
            font-size: 14px;
          }

          .@{ant-prefix}-steps-item-icon {
            width: 24px;
            height: 24px;
            font-size: 14px;
            line-height: 24px;
            display: inline-block;
            vertical-align: baseline;
          }
        }
      }
    }
  }

  .actionBox {
    width: 100%;
    box-shadow: 0 -2px 6px 0 rgba(0, 0, 0, 0.1);
    background: #fff;

    .action {
      padding: 0 20px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 70px;
      max-width: 1536px;
      min-width: 1280px;

      .buttons {
        display: flex;
        justify-content: flex-end;

        button {
          font-size: 14px;
          width: 80px;
          margin-left: 10px;
        }
      }
    }
  }
}