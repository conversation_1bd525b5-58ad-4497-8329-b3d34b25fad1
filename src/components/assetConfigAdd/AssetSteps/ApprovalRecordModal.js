/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-13 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-13 15:13:20
 * @description 资产配置-驳回修改-审批及弹窗
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { ApprovalHistory } from '@crm/biz-ui';
import _ from 'lodash';

import Modal from '@/components/common/newUI/modal';

export default class ApprovalRecordModal extends PureComponent {
  static propTypes = {
    visible: PropTypes.bool.isRequired,
    onCancel: PropTypes.func.isRequired,
    flowHistory: PropTypes.object.isRequired,
  }

  render() {
    const {
      visible,
      flowHistory,
    } = this.props;

    return (
      <Modal
        title="审批记录"
        modalKey="record"
        maskClosable={false}
        size="large"
        visible={visible}
        onModalClose={this.props.onCancel}
        modalFooter={[
          {
            key: 'choiceApproverCancelBtn',
            text: '取消',
            onClick: this.props.onCancel,
          },
        ]}
      >
        <ApprovalHistory
          history={flowHistory}
          visibleCurrentNode={_.isEmpty(flowHistory?.currentStepName)}
        />
      </Modal>
    );
  }
}
