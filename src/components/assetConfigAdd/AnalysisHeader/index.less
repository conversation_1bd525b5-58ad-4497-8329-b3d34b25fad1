.analysisHeader {
  display: flex;
  height: 22px;
  position: relative;
  justify-content: space-between;
  margin-top: 40px;

  &::before {
    content: '';
    position: absolute;
    top: 4px;
    left: 0;
    width: 4px;
    height: 14px;
    background: #108ee9;
  }

  .left {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    line-height: 22px;
    margin-left: 14px;
  }

  .right {
    height: 20px;
    font-size: 14px;
    color: #333;
    line-height: 22px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-switch {
        background: #ddd;
      }

      .ant-switch-checked {
        background: #108ee9;
      }
    }

    button {
      margin-left: 10px;
    }
  }
}
