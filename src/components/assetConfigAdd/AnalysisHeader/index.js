import React from 'react';
import PropTypes from 'prop-types';
import {
  Switch,
} from 'antd';
import noop from 'lodash/noop';
import isNil from 'lodash/isNil';
import IFWrap from '@/components/common/IFWrap';
import styles from './index.less';

export default function AnalysisHeader(props) {
  const {
    title,
    checked,
    switchDisabled,
    onSwitchChange,
    viewFlag,
    rightContent,
  } = props;
  return (
    <div className={styles.analysisHeader}>
      <div className={styles.left}>
        {title}
      </div>
      <IFWrap when={
        !viewFlag
      }
      >
        <div className={styles.right}>
          {
          isNil(rightContent)
            ? (
              <>
                报告展示
                <Switch
                  key={title}
                  checked={checked}
                  disabled={switchDisabled}
                  onChange={onSwitchChange}
                />
              </>
            )
            : rightContent
        }
        </div>
      </IFWrap>
    </div>
  );
}

AnalysisHeader.propTypes = {
  title: PropTypes.element.isRequired,
  checked: PropTypes.bool,
  switchDisabled: PropTypes.bool,
  viewFlag: PropTypes.bool,
  onSwitchChange: PropTypes.func,
  rightContent: PropTypes.element,
};

AnalysisHeader.defaultProps = {
  checked: false,
  switchDisabled: false,
  viewFlag: false,
  onSwitchChange: noop,
  rightContent: null,
};
