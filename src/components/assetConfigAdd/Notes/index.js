import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';

import styles from './index.less';

export default function Notes(props) {
  const {
    notes,
  } = props;

  return (
    <div className={styles.notes}>
      {
        map(notes, (note) => (
          <div
            key={note}
            // eslint-disable-next-line
            dangerouslySetInnerHTML={{
              __html: note,
            }}
          />
        ))
      }
    </div>
  );
}

Notes.propTypes = {
  notes: PropTypes.array,
};

Notes.defaultProps = {
  notes: [],
};
