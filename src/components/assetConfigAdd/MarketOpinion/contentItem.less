.contentItem {
  display: flex;
  justify-content: space-between;
  width: 100%;

  .leftWrap {
    font-size: 14px;
    color: #333;
    border-radius: 2px;
    flex: 1;
    margin-bottom: 10px;
    width: calc(100% - 34px);

    .titleBar {
      width: 100%;
      height: 48px;
      padding-left: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      background: linear-gradient(270deg, rgba(245, 245, 245, 0) 0%, #f5f5f5 100%);
      border-radius: 2px;

      .left {
        display: flex;
        align-items: center;
        width: calc(100% - 260px);
      }

      .right {
        display: flex;
        align-items: center;

        .updateTime {
          font-size: 14px;
          color: #999;
          line-height: 20px;
          margin-right: 20px;
        }
      }

      .shortGrade {
        font-size: 12px;
        margin-right: 12px;
        width: 67px;
        height: 21px;
        text-align: center;
        flex-shrink: 0;
        padding: 2px 11px 7px 8px;
        background-size: 100% 100%;
        color: #fff;
        line-height: 12px;
        white-space: nowrap;
        background-image: url("./images/default.png");
      }

      .neutral {
        background-image: url("./images/neutral.png");
      }

      .optimistic {
        background-image: url("./images/optimistic.png");
      }

      .prudent {
        background-image: url("./images/prudent.png");
      }

      .subdivideAssetName {
        font-weight: bold;
        line-height: 20px;
        white-space: nowrap;
      }

      .title {
        line-height: 20px;
        margin-right: 5px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        font-size: 14px;
      }

      .overTip {
        font-size: 14px;
        color: #ff706d;
        line-height: 22px;
        display: flex;
        align-items: center;
        margin-right: 20px;

        & > img {
          width: 14px;
          height: 14px;
          margin-right: 5px;
        }
      }

      .icon {
        width: 14px;
        height: 14px;
        margin-top: -4px;
      }
    }

    .contentArea {
      padding: 20px 30px;

      .item {
        text-indent: 2em;
        font-size: 13px;
        color: #333;
        line-height: 24px;
      }
    }
  }

  .hasBorder {
    border: 1px solid #eee;
    box-sizing: border-box;

    .titleBar {
      height: 46px;
      box-sizing: border-box;
      padding-left: 19px;
    }
  }

  .checkbox {
    width: 20px;
    height: 20px;
    margin: 14px 0 0 14px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-checkbox-inner {
        width: 20px;
        height: 20px;
        border-radius: 50%;

        &:after {
          top: 45%;
          width: 8px;
          height: 12px;
        }
      }
    }
  }
}

.overTooltip {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-tooltip-inner {
      background: #ff706d;
      box-shadow: 0 2px 4px 0 rgba(55, 61, 71, 0.1);
    }

    .ant-tooltip-arrow {
      border-top-color: #ff706d !important;
    }
  }
}
