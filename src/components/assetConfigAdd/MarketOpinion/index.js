/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-02-14 16:48:35
 * @description 资产配置列表页面-搜索区域
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'dva';
import { autobind } from 'core-decorators';
import classnames from 'classnames';
import { message } from 'antd';
import dayjs from 'dayjs';
import {
  map,
  find,
  forEach,
  flatten,
  isEmpty,
  noop,
} from 'lodash';
import { logCommon } from '@/decorators/logable';
import withRouter from '@/decorators/withRouter';
import PlaceHolderImage from '@/components/common/placeholderImage';
import { dva } from '@/helper';
import { STEP_PREVIEW, STEP_TEMPORARY } from '@/routes/assetConfigAdd/config';
import ContentItem from './ContentItem';

import styles from './index.less';

const effect = dva.generateEffect;
const mapStateToProps = (state) => ({
  // 市场观点
  marketOpinion: state.assetConfig.marketOpinion,
  // 选择的客户信息
  custMess: state.assetConfig.custMess,
  // 分析周期数据
  analysisPeriodData: state.assetConfig.analysisPeriodData,
  // 市场观点生成报告数据
  marketOpinionPreview: state.assetConfig.marketOpinionPreview,
});
const mapDispatchToProps = {
  // 获取资产配置步骤1-客户KYC
  getCustKyc: effect('assetConfig/getCustKyc'),
  // 获取市场观点
  getMarketOpinion: effect('assetConfig/getMarketOpinion'),
  //
  getMarketOpinionSuccess: effect('assetConfig/getMarketOpinionSuccess'),
};
@withRouter
@connect(mapStateToProps, mapDispatchToProps)
export default class MarketOpinion extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    // 市场观点
    getMarketOpinion: PropTypes.func.isRequired,
    marketOpinion: PropTypes.array.isRequired,
    marketOpinionPreview: PropTypes.array.isRequired,
    getMarketOpinionSuccess: PropTypes.func.isRequired,
    onMarketOpinionRef: PropTypes.func,
    custMess: PropTypes.object.isRequired,
    analysisPeriodData: PropTypes.object.isRequired,
    // 更新错误状态，重试按钮
    updateErrorFlag: PropTypes.func,
  };

  static defaultProps= {
    onMarketOpinionRef: noop,
    // 更新错误状态，重试按钮
    updateErrorFlag: noop,
  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  };

  componentDidMount() {
    const {
      onMarketOpinionRef,
      marketOpinion,
      location: {
        query: {
          current = 0
        }
      }
    } = this.props;
    onMarketOpinionRef(this);
    if (isEmpty(marketOpinion) || Number(current) === 4) {
      this.initData();
    }
  }

  @autobind
  initData() {
    const {
      getMarketOpinion,
      custMess,
      analysisPeriodData,
      updateErrorFlag,
      location: {
        query: {
          stepCode = '',
          id = '',
          custId,
        } = {}
      }
    } = this.props;
    const selectedPeriod = analysisPeriodData?.analysisPeriod?.[0];
    const ranges = selectedPeriod?.rang.split('-');
    getMarketOpinion({
      stepCode,
      assetAllocationId: id,
      custId: custMess?.custId || custId,
      timeRangeLevel: selectedPeriod?.code,
      timeRangeStartTimeMs: dayjs(ranges?.[0])?.startOf('date')?.valueOf(),
      timeRangeEndTimeMs: dayjs(ranges?.[1])?.endOf('date')?.valueOf(),
    }).then().catch(() => {
      updateErrorFlag();
    });
  }

  @autobind
  handleTabClick(tab) {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-市场观点-大类选择切换',
        value: tab,
      },
    });
    const {
      location: { query, pathname },
    } = this.props;
    this.context.replace({
      pathname,
      query: {
        ...query,
        activeAsset: tab,
      },
    });
  }

  @autobind
  handleOpinionClick(data) {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-市场观点-细分资产切换',
        value: data?.assetTypeName ?? '',
      },
    });
    const {
      marketOpinion,
      getMarketOpinionSuccess,
      location: {
        query,
        query: {
          stepCode = ''
        }
      }
    } = this.props;
    const activeAsset = query?.activeAsset || marketOpinion[0]?.assetName;

    const opinion = map(marketOpinion, (item) => {
      if (item.assetName === activeAsset) {
        return {
          ...item,
          opinionList: map(item.opinionList, (opinionItem) => {
            if (opinionItem.assetTypeName === data.assetTypeName) {
              return {
                ...opinionItem,
                unChecked: !opinionItem.unChecked,
              };
            }
            return opinionItem;
          })
        };
      }
      return item;
    });
    getMarketOpinionSuccess({ data: opinion, params: { stepCode } });
  }

  @autobind
  validate() {
    const { marketOpinion } = this.props;
    let opinion = [];
    forEach(marketOpinion, (item) => {
      opinion.push(item.opinionList);
    });
    opinion = flatten(opinion);
    if (find(opinion, (item) => !item.unChecked)) {
      return true;
    }
    message.error('至少选择一项子类资产观点展示');
    return false;
  }

  render() {
    const {
      marketOpinion,
      marketOpinionPreview,
      location: {
        query,
        query: {
          stepCode = ''
        } = {},
      },
    } = this.props;
    let actualActiveAsset = query?.activeAsset || '';
    const listArr = stepCode === STEP_PREVIEW ? marketOpinionPreview : marketOpinion;
    if (!query?.activeAsset) {
      actualActiveAsset = listArr?.[0]?.assetName;
    }
    const opinionList = find(listArr, (item) => item.assetName === actualActiveAsset)?.opinionList;
    return (
      <div className={styles.wrap}>
        <div className={styles.tabBar}>
          <div className={styles.tabs}>
            {
              map(listArr, (item) => (
                <div
                  key={item.idForFrontEnd}
                  className={classnames({
                    [styles.tabItem]: true,
                    [styles.activeTabItem]: actualActiveAsset === item.assetName
                  })}
                  onClick={() => this.handleTabClick(item.assetName)}
                >
                  <div className={styles.text}>
                    {item.assetName}
                  </div>
                </div>
              ))
            }
          </div>
          <div className={styles.period}>短期：3M</div>
        </div>
        <div className={styles.contentArea}>
          {
            isEmpty(opinionList)
              ? <PlaceHolderImage title="当前资产暂无观点" style={{ height: 500 }} />
              : map(opinionList, (item) => (
                <ContentItem
                  key={item.idForFrontEnd}
                  data={item}
                  onClick={this.handleOpinionClick}
                  viewFlag={stepCode !== STEP_TEMPORARY}
                />
              ))
          }
        </div>
      </div>
    );
  }
}
