.wrap {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding-bottom: 10px;
  .tabBar {
    width: 100%;
    height: 56px;
    background: #5097e4;
    border-radius: 2px 2px 0 0;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .tabs {
      display: flex;
      padding-top: 8px;

      .tabItem {
        color: #fff;
        font-size: 14px;
        line-height: 20px;
        padding: 0 20px;
        display: flex;
        align-items: center;
        cursor: pointer;
      }

      .activeTabItem {
        background: #fff;
        border-radius: 8px 8px 0 0;
        font-size: 16px;
        font-weight: bold;
        color: #108ee9;
        line-height: 20px;
        padding: 0 16px;

        .text {
          height: 100%;
          border-bottom: 2px solid #108ee9;
          box-sizing: border-box;
          padding: 14px 0;

          &:hover {
            cursor: pointer;
          }
        }
      }
    }

    .period {
      font-size: 14px;
      color: #fff;
      line-height: 19px;
      padding: 23px 0 14px;
    }
  }

  .contentArea {
    width: 100%;
    height: calc(100% - 76px);
    overflow-y: scroll;
    background: #fff;
    scrollbar-width: none;
  }

  ::-webkit-scrollbar {
    display: none;
  }
}
