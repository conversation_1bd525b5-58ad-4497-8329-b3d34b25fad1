/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-18 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-02-14 16:52:39
 * @description 资产配置列表页面-搜索区域
 */
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Checkbox, Tooltip } from 'antd';
import classnames from 'classnames';
import { map } from 'lodash';
import { logCommon } from '@/decorators/logable';
import IFWrap from '@/components/common/IFWrap';
import { formatRatio } from '@/helper/number';
import upImg from './images/up.svg';
import downImg from './images/down.svg';
import overStepIcon from './images/overStep.svg';
import {
  NEUTRAL,
  PRUDENT,
  OPTIMISTIC,
  DEFAULT_RATING_TEXT,
  DEFAULT_TEXT,
} from './config';

import styles from './contentItem.less';

export default function ContentItem(props) {
  const { data, onClick, viewFlag } = props;

  const [isOpen, setOpenStatus] = useState(false);

  const handleCheckboxChange = () => {
    onClick(data);
  };
  const handleToClickOpenTooltip = () => {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-市场观点-观点内容',
      },
    });
    onClick(data);
  };
  return (
    <div className={styles.contentItem}>
      <div className={classnames({
        [styles.leftWrap]: true,
        [styles.hasBorder]: isOpen || viewFlag,
      })}
      >
        <div className={styles.titleBar}>
          <div className={styles.left}>
            <div className={classnames({
              [styles.shortGrade]: true,
              [styles.neutral]: data.shortOpinionScoreLevel === NEUTRAL,
              [styles.optimistic]: data.shortOpinionScoreLevel === OPTIMISTIC,
              [styles.prudent]: data.shortOpinionScoreLevel === PRUDENT,
            })}
            >
              {data.shortOpinionRatingText || DEFAULT_RATING_TEXT}
            </div>
            <div className={styles.subdivideAssetName}>{`${data.assetTypeName}：`}</div>
            <Tooltip
              placement="top"
              title={data.overview}
              arrowPointAtCenter
              onClick={handleToClickOpenTooltip}
            >
              <div className={styles.title}>{data.overview}</div>
            </Tooltip>
            <IFWrap when={!viewFlag}>
              <div onClick={() => setOpenStatus(!isOpen)}>
                <img src={isOpen ? upImg : downImg} alt="图标" className={styles.icon} />
              </div>
            </IFWrap>
          </div>
          <div className={styles.right}>
            <div className={styles.updateTime}>{`更新于${data.updateTime || DEFAULT_TEXT}`}</div>
            <IFWrap when={data.ifOverstep}>
              <Tooltip
                placement="top"
                title={`客户当前赛道已配置${formatRatio(data.configValue)}，相对超配`}
                arrowPointAtCenter
                overlayClassName={styles.overTooltip}
              >
                <div className={styles.overTip}>
                  <img src={overStepIcon} alt="over" />
                  客户已超配
                </div>
              </Tooltip>
            </IFWrap>
          </div>
        </div>
        <IFWrap when={viewFlag || isOpen}>
          <div className={styles.contentArea}>
            {
              map(data?.conclusionParagraphs, (item) => (
                <div className={styles.item}>{item}</div>
              ))
            }
          </div>
        </IFWrap>
      </div>
      <IFWrap when={!viewFlag}>
        <Checkbox
          checked={!data.unChecked}
          className={styles.checkbox}
          onChange={handleCheckboxChange}
        />
      </IFWrap>

    </div>
  );
}

ContentItem.propTypes = {
  data: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
};
