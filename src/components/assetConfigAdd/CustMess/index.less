.custMessBox {
  padding: 20px;
  box-sizing: border-box;
  border-radius: 2px;
  border: 1px solid #5097e4;
  line-height: 20px;
  font-size: 14px;
  color: #333;

  .messTop {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 14px;
    border-bottom: 1px solid #dedede;

    .custInfo {
      display: flex;
      align-items: center;
    }

    .topLeft {
      font-size: 16px;
      font-weight: bold;
      margin-right: 40px;
    }

    .topRight {
      color: #4a4a4a;
    }

    .triggerArea {
      display: flex;
      align-items: center;

      .trigger {
        color: #f5aa1d;
        display: flex;
        align-items: center;

        img {
          width: 18px;
          height: 18px;
          margin-right: 10px;
        }

        .triggerBtn {
          width: 80px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #108ee9;
          border-radius: 15px;
          font-size: 14px;
          color: #108ee9;
          margin-left: 10px;
          cursor: pointer;
        }
      }

      .triggered {
        color: #ccc;

        .triggerBtn {
          color: #ccc;
          border: 1px solid #ccc;
        }
      }
    }
  }

  .messBottom {
    .cellBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .messCell {
      margin-top: 14px;
      display: flex;
      align-items: center;
      flex: 1;

      .cellTit {
        color: #999;
      }
    }
  }
}
