/*
 * @Description: 新建资产配置-客户基本信息组件
 * @Author: Liufuxiang
 * @Date: 2022-10-20 13:51:18
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import classnames from 'classnames';
import { message } from 'antd';

import { isNull } from '@/helper/check';
import { getHost } from '@/utils/host';
import warnSrc from '@/routes/assetConfigAdd/images/warn.svg';
import IFWrap from '@/components/common/IFWrap';
import { transformItemUnit } from './FixNumber';

import styles from './index.less';

export default class CustMess extends PureComponent {
  static propTypes = {
    custMess: PropTypes.object.isRequired,
    doSign: PropTypes.func.isRequired,
    isReject: PropTypes.bool.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      ifTriggered: false,
      // pdf是否有灰度
      isPdfGrey: false,
    };
  }

  async componentDidMount() {
    // 调用获取灰度字段的接口
    const res = await getHost().dispatch({
      type: 'global/queryIsCanary',
      payload: {
        loading: false,
        key: 'assetconfigpdf',
      },
    });
    if (res) {
      this.setInitCanary(res);
    }
  }

  @autobind
  setInitCanary(res) {
    this.setState({
      isPdfGrey: res
    });
  }

  @autobind
  getVlaue(value) {
    if (isNull(value)) {
      return '--';
    }
    return value;
  }

  @autobind
  handleTrigger() {
    const {
      doSign,
      custMess
    } = this.props;
    // 如果点过了 就不能点
    if (this.state.ifTriggered || !custMess?.overFiveDay) {
      return;
    }
    const params = {
      custId: custMess?.custId,
    };
    doSign(params).then((res) => {
      if (res) {
        message.success('签约流程已触发');
      }
      this.setState({
        ifTriggered: true,
      });
    });
  }

  render() {
    const { custMess, isReject } = this.props;
    const { ifTriggered, isPdfGrey } = this.state;
    return (
      <div className={styles.custMessBox}>
        <div className={styles.messTop}>
          <div className={styles.custInfo}>
            <div className={styles.topLeft}>
              {this.getVlaue(custMess?.custName)} {this.getVlaue(custMess?.custId)}
            </div>
            <div className={styles.topRight}>
              {this.getVlaue(custMess?.sex)} | {this.getVlaue(custMess?.age)}岁 |
              {custMess?.openPeriod} | {this.getVlaue(custMess?.riskLevel)}
            </div>
          </div>
          <div className={styles.triggerArea}>
            <IFWrap when={
                !custMess?.ifSignAgreement
                && !isReject && isPdfGrey
              }
            >
              <div className={classnames({
                [styles.trigger]: true,
                [styles.triggered]: ifTriggered || !custMess?.overFiveDay,
              })}
              >
                <IFWrap when={!ifTriggered && custMess?.overFiveDay}>
                  <img src={warnSrc} alt="图标" />
                </IFWrap>
                <div>客户未签署投顾服务协议，涨乐端签署后即可下载报告</div>
                <div
                  className={styles.triggerBtn}
                  onClick={this.handleTrigger}
                >
                  {ifTriggered || !custMess?.overFiveDay ? '已触发' : '立即触发'}
                </div>
              </div>
            </IFWrap>
          </div>
        </div>
        <div className={styles.messBottom}>
          <div className={styles.cellBox}>
            <div className={styles.messCell}>
              <div className={styles.cellTit}>总资产：</div>
              <div className={styles.cellContent}>
                {transformItemUnit(custMess?.totalAssets)}
              </div>
            </div>
            <div className={styles.messCell}>
              <div className={styles.cellTit}>年日均资产：</div>
              <div className={styles.cellContent}>
                {transformItemUnit(custMess?.dayAssets)}
              </div>
            </div>
            <div className={styles.messCell}>
              <div className={styles.cellTit}>历史峰值资产：</div>
              <div className={styles.cellContent}>
                {transformItemUnit(custMess?.historyTopAssets)}
              </div>
            </div>
            <div className={styles.messCell}>
              <div className={styles.cellTit}>计划投资期限：</div>
              <div className={styles.cellContent}>
                {this.getVlaue(custMess?.planInvestPeriod)}
              </div>
            </div>
          </div>
          <div className={styles.messCell}>
            <div className={styles.cellTit}>合格投资者类型：</div>
            <div className={styles.cellContent}>
              {this.getVlaue(custMess?.passInvesterType)}
            </div>
          </div>
          <div className={styles.messCell}>
            <div className={styles.cellTit}>当前持仓占比：</div>
            <div className={styles.cellContent}>
              {this.getVlaue(custMess?.positionRate)}
            </div>
          </div>
        </div>
      </div>
    );
  }
}
