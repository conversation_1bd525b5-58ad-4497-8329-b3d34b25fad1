/*
 * @Description: 金额的处理
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-21 09:47:42
 */
import _ from 'lodash';
import { number } from '@/helper';

const {
  wan: WAN,
  yi: YI,
} = number;
const EMPTY_INFO = '--';
// 判断数据是否为null
function checkValueIsNull(value) {
  return _.isEmpty(value) && !_.isNumber(value);
}

// 对小数点进行处理
function toFixedDecimal(value) {
  return Number.parseFloat(value.toFixed(2));
}
// 譬如123455.76747 换算成 12.34万元
export function transformItemUnit(item) {
  let newUnit = '元';
  let newItem = '';
  if (checkValueIsNull(item)) {
    newItem = EMPTY_INFO;
  } else {
    newItem = Math.abs(item);
    // 1. 全部在万元以下的数据不做处理
    // 2.超过万元的，以‘万元’为单位
    // 3.超过亿元的，以‘亿元’为单位
    if (newItem >= YI) {
      newUnit = '亿元';
      newItem = toFixedDecimal(newItem / YI);
    } else if (newItem >= WAN) {
      newUnit = '万元';
      newItem = toFixedDecimal(newItem / WAN);
    } else {
      newUnit = '元';
      newItem = toFixedDecimal(newItem);
    }

    // 保留符号
    if (item < 0) {
      // 负数
      newItem = `-${newItem}`;
    }
  }
  return `${newItem}${newUnit}`;
}
