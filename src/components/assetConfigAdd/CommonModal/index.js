/*
 * @Description: 公共弹窗
 * @Author: LiuFuXiang
 * @Date: 2021-03-09 09:53:56
 */
import React from 'react';
import propTypes from 'prop-types';
import { Modal } from 'antd';
import Button from '@/components/common/Button';
import { noop } from 'lodash';
import infoIcon from '../images/info.svg';
import styles from './index.less';

export default function CommonModal(props) {
  const {
    onClose,
    visible,
    recieveText,
    onOk,
    title,
    titleFlag,
    cancelFlag,
  } = props;
  return (
    <Modal
      visible={visible}
      centered
      closable={false}
      footer={null}
      maskClosable={false}
      width={400}
    >
      <div className={styles.modalBox}>
        <div className={styles.modalTop}>
          <img src={infoIcon} className={styles.modalIcon} />
          <div className={styles.modalTitle}>{titleFlag ? title : recieveText}</div>
        </div>
        {titleFlag ? <div className={styles.modalCenter}>{recieveText}</div> : ''}
        <div className={styles.modalBtn}>
          {cancelFlag ? (
            <Button key="cancel" onClick={onClose} className={styles.cancleBtn}>
              取消
            </Button>
          ) : ''}
          <Button key="sure" type="primary" onClick={onOk}>
            确定
          </Button>
        </div>
      </div>
    </Modal>
  );
}
CommonModal.propTypes = {
  // 是否展示弹窗
  visible: propTypes.bool.isRequired,
  // 关闭弹窗方法
  onClose: propTypes.func,
  // 展示文案
  recieveText: propTypes.string.isRequired,
  // 确认弹窗方法
  onOk: propTypes.func.isRequired,
  title: propTypes.string,
  // 展示标题
  titleFlag: propTypes.bool,
  // 是否有取消按钮
  cancelFlag: propTypes.bool,
};
CommonModal.defaultProps = {
  title: '提示！',
  titleFlag: false,
  cancelFlag: false,
  onClose: noop,
};
