import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';

import ResultPng from '../../images/result.png';

import styles from './index.less';

export default function Result(props) {
  const {
    title,
    description,
  } = props;
  return (
    <div className={styles.result}>
      <div className={styles.left}>
        <img src={ResultPng} />
      </div>
      <div className={styles.right}>
        <div>{title}</div>
        {map(description, (item, index) => (<div key={index}>{item}</div>))}
      </div>
    </div>
  );
}

Result.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.array.isRequired,
};

Result.defaultProps = {
};
