.moduleDetail {
  .centerBox {
    margin-top: 20px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .@{ant-prefix}-table-thead {
        tr {
          th {
            .@{ant-prefix}-table-column-sorter {
              position: static;
              display: inline-block;
              vertical-align: middle;
              margin-left: 6px;
            }
          }
        }
      }
    }

    .centerFilter {
      margin-bottom: 14px;
    }
  }

  .sortFlag {
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .@{ant-prefix}-table-thead {
        tr {
          th {
            .@{ant-prefix}-table-column-sorter {
              display: none;
            }
          }
        }
      }
    }
  }

  .reducerNum {
    color: #299c64;
  }

  .addNum {
    color: #e33c39;
  }
}