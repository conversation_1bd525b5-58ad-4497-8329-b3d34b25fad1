import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import isNil from 'lodash/isNil';
import includes from 'lodash/includes';
import filter from 'lodash/filter';
import Table from '@/components/common/table';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import HtFilter from '@/components/common/htFilter';
// import { dva } from '@/helper';
import { convertRate } from '@/helper/number';

import IFWrap from '@/components/common/IFWrap';
import classNames from 'classnames';
import {
  transferNumberColumn,
  checkStatusCode,
} from '../../utils';
import {
  PRODUCT_STOCK_INCOME_DETAILS_COLUMNS,
  FORMAT_PERCENT_COLUMNS,
  ZI_CHAN_FEN_LEI,
  SHOU_YI_LV,
} from '../../config';

import styles from './index.less';

export default function ProductStockIncomeDetails(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    bigTitList,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const [type, checkType] = useState('');
  const [dataSource, setDataSource] = useState([]);
  const [current, setCurrent] = useState(1);
  useEffect(() => {
    setDataSource(data?.tableData);
  }, [data]);
  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const handleTransferColumns = () => {
    const columns = map(PRODUCT_STOCK_INCOME_DETAILS_COLUMNS, (column) => {
      if (includes(FORMAT_PERCENT_COLUMNS, column.key)) {
        return {
          ...column,
          render: (text) => {
            if (isNil(text)) {
              return '--';
            }
            const value = convertRate(text);
            // 判断是否是负数
            const rudecerFlag = text < 0;
            if (column.key === SHOU_YI_LV) {
              return (
                <div
                  className={rudecerFlag ? styles.reducerNum : styles.addNum}
                >
                  {rudecerFlag ? '' : '+'}
                  {value}
                </div>
              );
            }
            return value;
          },
        };
      }
      return transferNumberColumn(column);
    });
    return columns;
  };

  const columns = handleTransferColumns();

  const handlePageNumChange = (pageNum) => {
    setCurrent(pageNum);
  };

  const handleStatusChange = ({ value }) => {
    checkType(value?.id);
    if (Number(value?.id) === 1) {
      setDataSource(data?.tableData);
    } else {
      setDataSource(
        filter(data?.tableData, (item) => item?.[ZI_CHAN_FEN_LEI] === value?.desc)
      );
    }
    setCurrent(1);
  };
  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="产品及股票收益详情"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      <IFWrap when={isOk}>
        <div className={styles.centerBox}>
          <IFWrap when={!viewFlag}>
            <div className={styles.centerFilter}>
              <HtFilter
                filterName="资产类别"
                filterId="status"
                ProductStockIncomeDetails
                type="single"
                defaultLabel="全部产品"
                dataMap={['id', 'desc']}
                data={bigTitList}
                needItemObj
                value={type}
                onChange={handleStatusChange}
              />
            </div>
          </IFWrap>
          <Table
            className={classNames(styles.table, { [styles.sortFlag]: viewFlag })}
            dataSource={dataSource}
            columns={columns}
            pagination={{ current, onChange: handlePageNumChange }}
            rowNumber={10}
            rowKey="custId"
            useNewUI
          />
        </div>
      </IFWrap>
    </div>
  );
}

ProductStockIncomeDetails.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  bigTitList: PropTypes.array.isRequired,
  rawData: PropTypes.object,
};

ProductStockIncomeDetails.defaultProps = {
  rawData: {},
};
