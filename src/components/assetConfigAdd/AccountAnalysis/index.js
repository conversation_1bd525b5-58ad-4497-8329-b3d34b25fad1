import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import { autobind } from 'core-decorators';
import {
  message,
  Modal,
  Select,
} from 'antd';
import moment from 'moment';
import to from 'await-to-js';
import {
  map,
  isNil,
  includes,
  forEach,
  slice,
  find,
  noop,
  every,
  isEmpty,
  filter,
  size,
} from 'lodash';

import withRouter from '@/decorators/withRouter';
import {
  STEP_SHOWREPORT,
  STEP_TEMPORARY,
  STEP_PREVIEW,
} from '@/routes/assetConfigAdd/config';
import { dva } from '@/helper';
import AnalysisWrapper from '@/components/assetConfigAdd/AnalysisWrapper';
import IFWrap from '@/components/common/IFWrap';
import EffectSpin from '@/components/common/EffectSpin';
import {
  formatToUnit,
  formatRatio,
} from '@/helper/number';
import Result from './Result';

import {
  ACCOUNT_INCOME_SITUATION_COLUMNS,
  FORMAT_AMOUNT_COLUMNS,
  FORMAT_PERCENT_COLUMNS,
  STATUS_MAP,
} from '../config';
import {
  checkStatusCode,
} from '../utils';
import {
  COMPONENT_MAP,
  TRIGGER_LIST,
  GUO_NEI_QUAN_YI_PEI_ZHI_FEN_XI03,
} from './config';

import InformationSvg from '../images/information.svg';

import styles from './index.less';

const { Option } = Select;

const effect = dva.generateEffect;
const mapStateToProps = (state) => ({
  // 模块列表
  moduleList: state.assetConfig.moduleList,
  // 模块列表预览数据
  moduleListPreview: state.assetConfig.moduleListPreview,
  // 模块详情
  moduleDetail: state.assetConfig.moduleDetail,
  // 选择的客户信息
  custMess: state.assetConfig.custMess,
  // 分析周期数据
  analysisPeriodData: state.assetConfig.analysisPeriodData,
  // 大类标题
  bigTitList: state.assetConfig.bigTitList,
  // 模块配置信息
  moduleConfig: state.assetConfig.moduleConfig,
  // 选中的对比维度指数
  selectedIndex: state.assetConfig.selectedIndex,
});
const mapDispatchToProps = {
  replace: routerRedux.replace,
  listModules: effect('assetConfig/listModules'),
  getAnalysisReport: effect('assetConfig/getAnalysisReport', { loading: false }),
  updateReduxData: effect('assetConfig/updateReduxData'),
  getBigTit: effect('assetConfig/getBigTit'),
  // 获取周期数据
  getAnalysisPeriod: effect('assetConfig/getAnalysisPeriod'),
};
@connect(mapStateToProps, mapDispatchToProps)
@withRouter
export default class AccountAnalysis extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    listModules: PropTypes.func.isRequired,
    moduleListPreview: PropTypes.array.isRequired,
    moduleList: PropTypes.array.isRequired,
    getAnalysisReport: PropTypes.func.isRequired,
    moduleDetail: PropTypes.object.isRequired,
    custMess: PropTypes.object.isRequired,
    analysisPeriodData: PropTypes.object.isRequired,
    updateReduxData: PropTypes.func.isRequired,
    getBigTit: PropTypes.func.isRequired,
    bigTitList: PropTypes.array.isRequired,
    // 更新错误状态，重试按钮
    updateErrorFlag: PropTypes.func,
    moduleConfig: PropTypes.array.isRequired,
    getAnalysisPeriod: PropTypes.func.isRequired,
    selectedIndex: PropTypes.object.isRequired,
  };

  static defaultProps= {
    // 更新错误状态，重试按钮
    updateErrorFlag: noop,
  }

  static contextTypes = {
    push: PropTypes.func.isRequired,
    replace: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      guoNeiQuanYiModal: false,
      loading: {},
    };
  }

  async componentDidMount() {
    // 请求一级模块列表
    const {
      getBigTit,
      location: {
        query: {
          analysisPeriodRang = '',
          current = 0,
        } = {}
      },
      updateReduxData,
      moduleList,
    } = this.props;
    getBigTit();
    if (isEmpty(moduleList) || Number(current) === 4) {
      this.getListModules();
    }
    // 更新选择的周期
    const periodData = await this.getPeriodValue(analysisPeriodRang);
    updateReduxData({
      selectedPeriod: periodData,
    });
  }

  componentDidUpdate(prevProps) {
    const {
      location: {
        query: nextQuery,
      },
    } = this.props;
    const {
      location: {
        query: prevQuery,
      },
    } = prevProps;
    if (nextQuery !== prevQuery && nextQuery?.stepCode === STEP_PREVIEW) {
      this.getListModules();
    }
  }

  @autobind
  async getListModules(params = {}) {
    const {
      listModules,
      updateErrorFlag,
    } = this.props;
    const payload = await this.generateListParams();
    listModules({
      ...payload,
      ...params,
    }).then((result) => {
      if (result?.code !== '0') {
        message.error(result?.msg || '获取模块列表失败，请重试');
      } else {
        this.queryDetails(result);
      }
    }).catch((rec) => {
      updateErrorFlag();
    });
  }

  @autobind
  async getPeriodValue(value) {
    const {
      analysisPeriodData,
      location: {
        query,
      },
      getAnalysisPeriod,
    } = this.props;
    let list = analysisPeriodData?.analysisPeriod;
    if (isEmpty(list)) {
      const result = await getAnalysisPeriod({ custId: query?.custId ?? '' });
      list = result?.analysisPeriod;
    }
    const periodData = find(list,
      (item) => item.rang === value) ?? {};
    return periodData;
  }

  @autobind
  async handleChangeSelect(value) {
    const {
      location: {
        pathname,
        query,
      },
      updateReduxData,
    } = this.props;
    const periodData = await this.getPeriodValue(value);
    updateReduxData({
      selectedPeriod: periodData,
    });
    this.context.replace({
      pathname,
      query: {
        ...query,
        analysisPeriodRang: value,
      },
    });
    this.setState({
      loading: {},
    }, () => {
      this.getListModules({
        timeRangeLevel: periodData?.code,
      });
    });
  }

  @autobind
  async generateListParams() {
    const {
      custMess,
      location: {
        query: {
          stepCode = '',
          analysisPeriodRang = '',
          id = ''
        } = {},
      },
    } = this.props;
    const periodData = await this.getPeriodValue(analysisPeriodRang);
    const ranges = analysisPeriodRang.split('-');
    const params = {
      custId: custMess?.custId,
      assetAllocationId: id,
      timeRangeLevel: periodData?.code,
      stepCode,
      timeRangeStartTimeMs: moment(ranges[0]).startOf('date').valueOf(),
      timeRangeEndTimeMs: moment(ranges[1]).endOf('date').valueOf(),
    };
    return params;
  }

  @autobind
  async generateDetailParams(reportCode) {
    const {
      custMess,
      location: {
        query: {
          stepCode = '',
          analysisPeriodRang = '',
          id = ''
        } = {}
      },
      selectedIndex,
    } = this.props;
    const periodData = await this.getPeriodValue(analysisPeriodRang);
    const ranges = analysisPeriodRang.split('-');
    const params = {
      reportCode,
      assetAllocationId: id,
      custId: custMess?.custId,
      timeRangeLevel: periodData?.code,
      timeRangeStartTimeMs: moment(ranges[0]).startOf('date').valueOf(),
      timeRangeEndTimeMs: moment(ranges[1]).endOf('date').valueOf(),
      paramJson: {
        compareToIndexCode: selectedIndex?.key ?? '000300',
        compareToIndexDisplayText: selectedIndex?.label ?? '沪深300',
      },
      stepCode,
    };
    return params;
  }

  @autobind
  queryDetails(result) {
    const {
      updateErrorFlag,
      location: {
        query: {
          stepCode = '',
        } = {},
      },
    } = this.props;
    if (result?.code !== '0') {
      message.error(result?.msg || '获取模块列表失败，请重试');
    } else {
      const {
        getAnalysisReport,
        updateReduxData,
      } = this.props;
      const newModuleList = [];
      forEach(result?.resultData, (module) => {
        // 遍历数据供第四步使用
        const switchFlag = module?.hasReportFlag;
        const moduleItem = {
          value: module?.id,
          label: module?.name,
          children: [],
          disabled: !switchFlag,
        };
        // 如果大模块符合触发条件，则发起子模块的数据请求
        if (module?.hasReportFlag) {
          forEach(module?.children, async (child) => {
            const payload = await this.generateDetailParams(child?.id);
            const [err, reportData] = await to(getAnalysisReport(payload));
            const statusCode = reportData?.statusCode;
            const {
              isOk,
              isFailed,
            } = checkStatusCode(statusCode);
            // 所有判断均需要在非查看 code 时才执行
            // 优先判断模块是否触发，不触发时，不弹报错
            // 如果触发，并且接口返回 FAILED，则弹窗
            // FSP-53872 需求将是否触发逻辑改掉， hasReportFlag = true 并且 statusCode = 'ALL_ZEro' 不触发
            // hasReportFlag = true，statusCode = FAIL || statusCode = OK 为触发
            const isTriggered = reportData?.hasReportFlag && (isOk || isFailed);
            if (stepCode !== STEP_SHOWREPORT && isTriggered && isFailed) {
              message.error(`${child.name}数据异常，请重试`, 2);
            }
            let disabled = false;
            // 如果模块触发
            if (isTriggered) {
              // 模块触发同时，数据请求正确
              if (isOk) {
                disabled = false;
              } else {
                disabled = true;
              }
            } else {
              disabled = true;
            }
            moduleItem.children.push({
              value: child?.id,
              label: child?.name,
              checked: disabled ? false : isTriggered,
              disabled,
              isFailed,
            });
            if (err) {
              message.error(`${child.name}接口错误，请重试`, 2);
              updateErrorFlag();
            }
            this.setState((prevState) => ({
              loading: {
                ...prevState.loading,
                [child?.id]: false,
              }
            }));
          });
        } else {
          const newChildren = map(module?.children, (child) => ({
            value: child?.id,
            label: child?.name,
            checked: switchFlag,
            disabled: !switchFlag,
          }));
          moduleItem.children = newChildren;
        }
        newModuleList.push(moduleItem);
      });
      if (stepCode !== STEP_PREVIEW) {
        updateReduxData({
          moduleConfig: newModuleList,
        });
      }
    }
  }

  @autobind
  handleTransferColumns() {
    const columns = map(ACCOUNT_INCOME_SITUATION_COLUMNS, (column) => {
      if (includes(FORMAT_AMOUNT_COLUMNS, column.key)) {
        return {
          ...column,
          render: (text) => {
            if (isNil(text)) {
              return '--';
            }
            const value = formatToUnit({
              num: text,
              floatLength: 2,
              unit: '元',
              isThousandFormat: false,
            });
            return value;
          },
        };
      }
      if (includes(FORMAT_PERCENT_COLUMNS, column.key)) {
        return {
          ...column,
          render: (text) => {
            if (isNil(text)) {
              return '--';
            }
            const value = formatRatio(text);
            return value;
          },
        };
      }
      return column;
    });
    return columns;
  }

  @autobind
  handleChangeSwitch(id, parentId, value) {
    const {
      updateReduxData,
      moduleDetail,
      moduleConfig,
    } = this.props;
    const newModuleConfig = map(moduleConfig, (module) => {
      if (module?.value === parentId) {
        const newChildren = map(module?.children, (child) => {
          if (child?.value === id) {
            return {
              ...child,
              checked: value,
            };
          }
          return child;
        });
        return {
          ...module,
          children: newChildren,
        };
      }
      return module;
    });
    updateReduxData({
      moduleDetail: {
        ...moduleDetail,
        [id]: {
          ...moduleDetail?.[id],
          switch: value,
        },
      },
      moduleConfig: newModuleConfig,
    });
  }

  @autobind
  getReallyList() {
    const {
      moduleList,
      moduleListPreview,
      location: { query: { stepCode = '' } = {} }
    } = this.props;
    if (stepCode === STEP_PREVIEW) {
      return moduleListPreview;
    }
    return moduleList;
  }

  @autobind
  async handleChangeSpecial(id, data, title) {
    const arr = this.getReallyList();
    if (id === arr?.[0]?.children?.[0]?.id) {
      const {
        getAnalysisReport,
        updateReduxData,
      } = this.props;
      updateReduxData({
        selectedIndex: data,
      });
      const second = arr?.[0]?.children?.[1];
      const secondId = second?.id;
      const firstPayload = await this.generateDetailParams(id);
      const secondPayload = await this.generateDetailParams(secondId);
      const paramJson = {
        compareToIndexCode: data?.key ?? '000300',
        compareToIndexDisplayText: data?.label ?? '沪深300',
      };
      this.setState((prevState) => ({
        loading: {
          ...prevState.loading,
          [id]: true,
          [secondId]: true,
        }
      }), async () => {
        // 第一个模块
        const [firstError, firstReportData] = await to(getAnalysisReport({
          ...firstPayload,
          paramJson,
        }));
        const statusCode = firstReportData?.statusCode;
        const {
          isFailed,
        } = checkStatusCode(statusCode);
        if (isFailed) {
          message.error(`${title}数据异常，请重试`, 2);
        }
        if (firstError) {
          message.error(`${title}接口错误，请重试`, 2);
        }
        this.setState((prevState) => ({
          loading: {
            ...prevState.loading,
            [id]: false,
          }
        }));
        // 第二个模块
        const [secondError, secondReportData] = await to(getAnalysisReport({
          ...secondPayload,
          paramJson,
        }));
        const secondStatusCode = secondReportData?.statusCode;
        const {
          isFailed: secondIsFailed,
        } = checkStatusCode(secondStatusCode);
        if (secondIsFailed) {
          message.error(`${second?.name}数据异常，请重试`, 2);
        }
        if (secondError) {
          message.error(`${second.name}接口错误，请重试`, 2);
        }
        this.setState((prevState) => ({
          loading: {
            ...prevState.loading,
            [secondId]: false,
          }
        }));
      });
    }
  }

  // 下拉框渲染事件
  @autobind
  renderOption() {
    const {
      analysisPeriodData,
    } = this.props;
    return map(analysisPeriodData?.analysisPeriod, (option) => (
      <Option
        key={option?.rang}
        value={option?.rang}
        disabled={option?.checkDisable}
      >
        {option?.desc}({option?.rang})
      </Option>
    ));
  }

  @autobind
  handleShowModal() {
    this.setState({
      guoNeiQuanYiModal: true,
    });
  }

  @autobind
  handleCloseModal() {
    this.setState({
      guoNeiQuanYiModal: false,
    });
  }

  // 提取所有国内权益配置分析子模块状态为allZero的集合
  @autobind
  getAllZeroList() {
    const {
      moduleDetail,
    } = this.props;
    const listModule = this.getReallyList();
    // 国内权益配置分析子模块集合
    const thirdBlockChildrenList = find(listModule,
      (item) => item.id === GUO_NEI_QUAN_YI_PEI_ZHI_FEN_XI03).children;
    // 提取所有子模块的statusCode
    return filter(
      map(thirdBlockChildrenList, (item) => ({
        statusCode: moduleDetail?.[item.id]?.statusCode,
      })),
      (statusItem) => statusItem.statusCode === STATUS_MAP.allZero
    );
  }

  // 国内权益配置分析子模块id集合
  @autobind
  getThirdBlockChildrenIdList() {
    const listModule = this.getReallyList();
    return map(
      find(listModule, (item) => item.id === GUO_NEI_QUAN_YI_PEI_ZHI_FEN_XI03).children,
      (blockItem) => blockItem.id
    );
  }

  // 渲染子模块组件
  @autobind
  renderModuleComponent(Component, id, parentId) {
    const {
      moduleDetail,
      bigTitList,
      location,
      location: {
        query: {
          stepCode = ''
        } = {}
      },
      selectedIndex,
    } = this.props;
    const {
      loading,
    } = this.state;
    const data = moduleDetail?.[id] ?? {};
    const statusCode = data?.statusCode;
    const {
      isFailed,
      isOk,
    } = checkStatusCode(statusCode);
    // 有数据：（标题+表格+图表）正常展示，开关可切换；
    // 数据为 null 或者查询外部接口失败：（标题+表格+图表）不显示，红色提示，无开关；
    // 后端范围数据都为 0： （表格+图表）不显示，不提示；
    // 后端返回有数据，但是数据为 0： （标题+表格+图表）显示，开关可切换；
    // FSP-53872 需求将是否触发逻辑改掉， hasReportFlag = true 并且 statusCode = 'ALL_ZEro' 不触发
    // hasReportFlag = true，statusCode = FAIL || statusCode = OK 为触发
    const isTriggered = data?.hasReportFlag && (isOk || isFailed);
    // 股票收益表现，权益公募基金收益表现 需要判断是否触发，其他都不需要判断
    let showModule = true;
    // 获取第三块下面所有的allZero模块
    const allZeroList = this.getAllZeroList();
    // 第三块下面的子模块id集合
    const thirdBlockChildrenIdList = this.getThirdBlockChildrenIdList();
    // 判断是国内权益配置分析下否所有子模块都是ALL_ZERO,如果都是的话，不展示子模块
    if (size(allZeroList) === size(thirdBlockChildrenIdList)
      && includes(thirdBlockChildrenIdList, id)) {
      showModule = false;
    } else if (includes(TRIGGER_LIST, id)) { // 如果在需要判断触发的模块列表里，并且是不触发，并且则显示
      showModule = true;
    } else if (isTriggered) {
      if (isFailed) {
        showModule = false;
      } else {
        showModule = true;
      }
    } else {
      showModule = false;
    }
    let showLoading = isNil(loading?.[id]) ? true : loading?.[id];
    if (!isEmpty(data)) {
      showLoading = false;
    }
    return (
      <IFWrap when={showModule} key={id}>
        <EffectSpin
          loading={showLoading}
          key={id}
        >
          <Component
            id={id}
            parentId={parentId}
            data={data?.reportData ?? {}}
            hasReportFlag={isTriggered}
            onSwitchChange={this.handleChangeSwitch}
            viewFlag={stepCode !== STEP_TEMPORARY}
            onSpecialChange={this.handleChangeSpecial}
            key={id}
            bigTitList={bigTitList}
            rawData={data}
            selectedIndex={selectedIndex}
            location={location}
          />
        </EffectSpin>
      </IFWrap>
    );
  }

  // 渲染子模块列表
  @autobind
  renderChildModule(module) {
    return (
      map(module.children,
        (child) => this.renderModuleComponent(COMPONENT_MAP[child?.id], child?.id, module?.id))
    );
  }

  @autobind
  handleCheckeIsAllNull(module) {
    const {
      moduleConfig,
    } = this.props;
    const findModule = find(moduleConfig, (item) => item.value === module.id);
    const isAllNull = every(findModule?.children ?? [], 'isFailed');
    return isAllNull;
  }

  // 渲染模块的包裹容器
  @autobind
  renderWrapperList() {
    const {
      location: {
        query: {
          stepCode = ''
        } = {}
      }
    } = this.props;
    const arr = this.getReallyList();
    const title = (
      <div className={styles.titleWithIcon}>
        国内权益配置分析
        {stepCode !== STEP_TEMPORARY ? '' : <img src={InformationSvg} onClick={this.handleShowModal} />}
      </div>
    );
    return map(arr, (module, index) => {
      // 是否是国内权益配置分析
      const isGuoNeiQuanYiPeiZhiFenXi = index === 2;
      const isAllNull = this.handleCheckeIsAllNull(module);
      const hasChildren = !isEmpty(module?.children);
      return (
        <IFWrap when={hasChildren && !isAllNull} key={module.id}>
          <AnalysisWrapper
            title={isGuoNeiQuanYiPeiZhiFenXi ? title : module.name}
            index={index + 1}
            key={module.id}
          >
            <>
              <IFWrap when={isGuoNeiQuanYiPeiZhiFenXi}>
                {
                  map(module?.notes, (note) => (
                    <div
                      className={styles.note}
                      key={note}
                      // eslint-disable-next-line
                      dangerouslySetInnerHTML={{ __html: note }}
                    />
                  ))
                }
              </IFWrap>
              {
                (!module.hasReportFlag && isGuoNeiQuanYiPeiZhiFenXi)
                  ? (
                    <Result
                      title="分析期内，可能存在以下情景，导致本模块无法触发："
                      description={[
                        '（1）国内权益资产配置持仓金额较少或持仓占比较低；',
                        '（2）国内权益资产实际持有天数较短。'
                      ]}
                    />
                  )
                  : this.renderChildModule(module)
              }
            </>
          </AnalysisWrapper>
        </IFWrap>
      );
    });
  }

  // 获得分析期描述
  getAnalysisPeriodDesc=() => {
    const {
      location: {
        query: {
          analysisPeriodRang = '',
          stepCode = '',
          analysisPeriodDesc = ''
        } = {}
      },
      analysisPeriodData
    } = this.props;
    if (stepCode === STEP_SHOWREPORT) {
      return `${analysisPeriodDesc}(${analysisPeriodRang})`;
    }
    const selectedPeriod = find(analysisPeriodData?.analysisPeriod, {
      rang: analysisPeriodRang,
    });
    return `${selectedPeriod?.desc}(${analysisPeriodRang})`;
  }

  render() {
    const {
      location: {
        query: {
          analysisPeriodRang = '',
          stepCode = '',
        } = {}
      },
    } = this.props;
    const {
      guoNeiQuanYiModal,
    } = this.state;
    const arr = this.getReallyList();
    return (
      <div className={styles.accountAnalysis}>
        <div className={styles.header}>
          <div className={styles.title}>账户分析周期</div>
          {stepCode === STEP_TEMPORARY ? (
            <Select
              filterOption={false}
              value={analysisPeriodRang}
              dropdownClassName={styles.dropdownSelect}
              onChange={this.handleChangeSelect}
            >
              {this.renderOption()}
            </Select>
          ) : <div className={styles.showAnalysisBox}>{this.getAnalysisPeriodDesc()}</div>}
        </div>
        {this.renderWrapperList()}
        <Modal
          className={styles.modal}
          visible={guoNeiQuanYiModal}
          title={(
            <div
              // eslint-disable-next-line
              dangerouslySetInnerHTML={{ __html: arr?.[2]?.description?.[0] }}
            />
          )}
          onCancel={this.handleCloseModal}
          footer={null}
        >
          {
            map(slice(arr?.[2]?.description, 1), (description, index) => (
              <p
                className={styles.modelContent}
                key={index}
                // eslint-disable-next-line
                dangerouslySetInnerHTML={{ __html: description }}
              />
            ))
          }
        </Modal>
      </div>
    );
  }
}
