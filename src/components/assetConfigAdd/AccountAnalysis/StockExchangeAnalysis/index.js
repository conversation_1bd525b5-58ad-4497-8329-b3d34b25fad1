import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import isNil from 'lodash/isNil';
import concat from 'lodash/concat';
import includes from 'lodash/includes';

import IFWrap from '@/components/common/IFWrap';
import Table from '@/components/common/table';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import {
  formatRatio,
} from '@/helper/number';

import {
  transferNumberColumn,
  checkStatusCode,
} from '../../utils';
import {
  SHUANG_BIAN_HUAN_SHOU_LV,
  CHI_CANG_SHI_JIAN_ZUI_CHANG_SHOU_YI_LV,
  CHI_CANG_SHI_JIAN_ZUI_DUAN_SHOU_YI_LV,
  STOCK_EXCHANGE_ANALYSIS_COLUMNS,
  STOCK_EXCHANGE_ANALYSIS_COLUMNS_ZERO,
  STOCK_EXCHANGE_ANALYSIS_COLUMNS_ONE,
  STOCK_EXCHANGE_ANALYSIS_COLUMNS_TWO,
  ZUI_GAO_SHOU_YI_LV,
  ZUI_DI_SHOU_YI_LV,
  HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO,
  HUAN_SHOU_ZHAN_SHI_CHANG_JING_ONE,
  HUAN_SHOU_ZHAN_SHI_CHANG_JING_TWO,
} from '../../config';

import styles from './index.less';

const NEED_FORMAT_PERCENT_COLUMNS = [
  CHI_CANG_SHI_JIAN_ZUI_CHANG_SHOU_YI_LV,
  CHI_CANG_SHI_JIAN_ZUI_DUAN_SHOU_YI_LV,
  ZUI_GAO_SHOU_YI_LV,
  ZUI_DI_SHOU_YI_LV,
];
export default function StockExchangeAnalysis(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const handleTransferColumns = () => {
    let tempColumns = [];
    const huanShouZhanShiChangJing = data?.tableData?.[0]?.huanShouZhanShiChangJing;
    // 持仓最长、最短时间不同，按照正常列显示
    let concatArray = [];
    if (huanShouZhanShiChangJing === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ZERO) {
      concatArray = STOCK_EXCHANGE_ANALYSIS_COLUMNS_ZERO;
    } else if (huanShouZhanShiChangJing === HUAN_SHOU_ZHAN_SHI_CHANG_JING_ONE) {
      // 最长、最短时间相同且股票数量大于1（也就是区分最高、最低收益率）
      concatArray = STOCK_EXCHANGE_ANALYSIS_COLUMNS_ONE;
    } else if (huanShouZhanShiChangJing === HUAN_SHOU_ZHAN_SHI_CHANG_JING_TWO) {
      // 最长、最短时间相同且股票数量为1（不区分最高、最低收益率）
      concatArray = STOCK_EXCHANGE_ANALYSIS_COLUMNS_TWO;
    } else {
      concatArray = [];
    }
    tempColumns = concat(STOCK_EXCHANGE_ANALYSIS_COLUMNS, concatArray);
    const columns = map(tempColumns, (column) => {
      if (column.key === SHUANG_BIAN_HUAN_SHOU_LV) {
        return {
          ...column,
          render: (text) => {
            if (isNil(text)) {
              return '--';
            }
            const value = formatRatio(text, { floatLength: 2, sign: '', radix: 1 });
            return value;
          },
        };
      }
      if (includes(NEED_FORMAT_PERCENT_COLUMNS, column.key)) {
        return {
          ...column,
          render: (text) => {
            if (isNil(text)) {
              return '--';
            }
            const value = formatRatio(text);
            // 判断是否是负数
            const rudecerFlag = text < 0;
            return (
              <div
                className={rudecerFlag ? styles.reducerNum : styles.addNum}
              >
                {rudecerFlag ? '' : '+'}{value}
              </div>
            );
          },
        };
      }
      return transferNumberColumn(column);
    });
    return columns;
  };

  const columns = handleTransferColumns();

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="股票持仓换手分析"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      <IFWrap when={isOk}>
        <Table
          className={styles.table}
          dataSource={data?.tableData}
          columns={columns}
          pagination={false}
          rowKey="custId"
          useNewUI
        />
        <Notes
          notes={data?.notes}
        />
        <Conclusions
          conclusions={data?.conclusions}
        />
      </IFWrap>
    </div>
  );
}

StockExchangeAnalysis.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

StockExchangeAnalysis.defaultProps = {
  rawData: {},
};
