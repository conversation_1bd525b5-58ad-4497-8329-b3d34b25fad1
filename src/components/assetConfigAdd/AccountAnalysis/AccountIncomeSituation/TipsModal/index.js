import React from 'react';
import PropTypes from 'prop-types';
import { sensors } from '@lego/bigbox-utils';

import CommonModal from '@/components/common/newUI/modal';

import FormulaImg from './formula.png';
import styles from './index.less';

const { logCommon } = sensors;

const TipsModal = (props) => {
  const {
    onClose,
  } = props;

  const handleCloseModal = () => {
    logCommon({
      type: 'Click',
      payload: {
        name: '关闭净值收益率算法说明弹窗',
      },
    });
    onClose();
  };

  return (
    <CommonModal
      title="净值收益率算法说明"
      onModalClose={handleCloseModal}
      size="normal"
      modalKey="AccountIncomeSituationTipsModal"
      modalFooter={false}
      wrapClassName={styles.tipsModal}
      visible
    >
      <div className={styles.modalContent}>
        <div className={styles.paragraph}>
          <span>算法公式：</span>
          假设期初初始净值Vo=1，初始份额为Ao，第i日终净值为Vi、市值为Mi，则:
          <img className={styles.formulaImg} src={FormulaImg} alt="" />
          第i日的净值收益率=Vi/Vo-1
        </div>
        <div className={styles.paragraph}>
          <span>说明：</span>
          Cashlni为第i-1日盘前到第i日盘前这段时间的资金流入。CashOuti为第i-1日盘后到第i日盘后这段时间的资金流出。
        </div>
        <div className={styles.paragraph}>
          <span>公式延伸：</span>
          第1日的净值收益率为R1，第i日的净值收益率为Ri，则第1至第i日区间的净值收益率=(1+R1)*(1+R2)*.…..(1+Ri)-1
        </div>
        <div className={styles.paragraph}>
          <span>优点：</span>
          能够客观反应客户的投资能力，完全剔除出入金带来的影响
        </div>
        <div className={styles.paragraph}>
          <span>缺点1：</span>
          若分析期前后成本差距很大，且前后的收益率差别也很大，这种情况下，误差较大，甚至会出现收益率与收益额符号相反的极端情况
        </div>
        <div className={styles.paragraph}>
          举例：客户1万元的收益率为10%(收益额为10000*10%=1000元)，
          存入10万元后的收益率为-5%(收益额为111000*-5%=-5550)，则净值收益率为+4.5%，但收益额为-4550元。
        </div>
        <div className={styles.paragraph}>
          <span>缺点2：</span>
          如客户在市场低点复购加仓，和客户不加仓所得到的收益率完全一致，与客户感知不符。
        </div>
        <div className={styles.paragraph}>
          举例：客户1万元购买某产品，一段时间后的收益率为-10%，
          此时客户选择在低点加仓1万元，后续产品收益率为20%，
          最终客户的收益额为2800元（前段收益额：10000*-10%=-1000，后段收益额：19000*20%=3800），净值收益率为8%。
          若客户不选择加仓，后续产品收益率同样为20%，那么客户的收益额为800元，净值收益率也为8%。
        </div>
      </div>
    </CommonModal>
  );
};

TipsModal.propTypes = {
  // 关闭弹窗
  onClose: PropTypes.func.isRequired,
};

export default TipsModal;
