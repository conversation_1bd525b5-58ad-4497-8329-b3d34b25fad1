import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import noop from 'lodash/noop';
import { Select } from 'antd';

import IFWrap from '@/components/common/IFWrap';
import Table, { ToolTipCell } from '@/components/common/table';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import TipsModal from './TipsModal';
import TipsImg from './tips.png';
import {
  transferNumberColumn,
  checkStatusCode,
} from '../../utils';
import {
  ACCOUNT_INCOME_SITUATION_COLUMNS,
} from '../../config';
import {
  COMPARE_TO_OPTIONS,
} from './config';

import styles from './index.less';

const title = '账户收益表现';

const { Option } = Select;
export default function AccountIncomeSituation(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    onSpecialChange,
    rawData,
    selectedIndex,
    location,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const [tipsModalVisible, setTipsModalVisible] = useState(false);

  useEffect(() => {
    // 这边做路由判断，在长赢配置路由下回显账户收益表现对比维度数据
    const pathname = '/webfsp/newAccountAnalysis/add';
    if (location.pathname === pathname) {
      try {
        const accountObj = localStorage.getItem('_STORAGE_GLOBAL_ACCOUNTID');
        const paramJson = JSON.parse(accountObj || '{}')?.paramJson;
        if (paramJson) {
          onSpecialChange(id, {
            key: paramJson?.compareToIndexCode,
            label: paramJson?.compareToIndexDisplayText,
          }, title);
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.info(err);
      }
    }
  }, []);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  // 下拉框渲染事件
  const optionRender = () => map(COMPARE_TO_OPTIONS, (option) => (
    <Option key={option.code} value={option.code}>
      {option.displayText}
    </Option>
  ));

  const handleChangeSelect = (item) => {
    onSpecialChange(id, {
      key: item.key,
      label: item.label,
    }, title);
  };

  const handleRenderSelect = (text, record) => {
    if (text !== '客户账户') {
      if (viewFlag) {
        return (
          <ToolTipCell
            cellText={text}
            cellClass={styles.duiBiWeiDu}
            tipContent={text}
          />
        );
      }
      return (
        <div>
          <Select
            labelInValue
            filterOption={false}
            value={selectedIndex}
            dropdownClassName={styles.dropdownSelect}
            suffixIcon={<span className={styles.dropdown} />}
            onChange={handleChangeSelect}
            className={styles.select}
          >
            {optionRender()}
          </Select>
        </div>
      );
    }
    return (
      <div className={styles.duiBiWeiDu}>
        {text}
      </div>
    );
  };

  const handleClickTitle = () => {
    setTipsModalVisible(true);
  };

  const handleTransferColumns = () => {
    const columns = map(ACCOUNT_INCOME_SITUATION_COLUMNS, (column) => {
      if (column.key === 'shouYiLv') {
        const current = Number(location?.query?.current || 0);
        let showIcon = false;
        // 新建时，第二步、第五步显示提示图表，并且可点击
        if (current === 1 || current === 4) {
          showIcon = true;
        }
        const columnTitle = (
          <div>
            {column?.title}
            {
              showIcon
                ? <img className={styles.tipsImg} src={TipsImg} alt="" onClick={handleClickTitle} />
                : null
            }
          </div>
        );
        const newColumn = {
          ...column,
          title: columnTitle,
        };
        return transferNumberColumn(newColumn);
      }
      if (column.key === 'duiBiWeiDu') {
        return {
          ...column,
          render: (text, record) => handleRenderSelect(text, record),
        };
      }
      return transferNumberColumn(column);
    });
    return columns;
  };

  const handleCloseTipsModal = () => {
    setTipsModalVisible(false);
  };

  const columns = handleTransferColumns();

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title={title}
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      <IFWrap when={isOk}>
        <Table
          className={styles.table}
          dataSource={data?.tableData}
          columns={columns}
          pagination={false}
          rowKey="custId"
          useNewUI
          withBorder
        />
        <Notes
          notes={data?.notes}
        />
        <Conclusions
          conclusions={data?.conclusions}
        />
      </IFWrap>
      <IFWrap when={tipsModalVisible}>
        <TipsModal
          onClose={handleCloseTipsModal}
        />
      </IFWrap>
    </div>
  );
}

AccountIncomeSituation.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  onSpecialChange: PropTypes.func,
  rawData: PropTypes.object,
  selectedIndex: PropTypes.object.isRequired,
  location: PropTypes.object.isRequired,
};

AccountIncomeSituation.defaultProps = {
  onSpecialChange: noop,
  rawData: {},
};
