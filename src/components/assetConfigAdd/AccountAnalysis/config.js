// 账户收益表现
import AccountIncomeSituation from './AccountIncomeSituation';
// 业绩趋势及回撤表现
import TrendAndWithdraw from './TrendAndWithdraw';
// 当前持仓配置分布
import CurrentPositionConfig from './CurrentPositionConfig';
// 大类资产收益贡献
import LargeAssetsIncomeContribution from './LargeAssetsIncomeContribution';
// 股票收益表现
import StockIncomeSituation from './StockIncomeSituation';
// 股票持仓换手分析
import StockExchangeAnalysis from './StockExchangeAnalysis';
// 股票持仓行业分析
import StockIndustryAnalysis from './StockIndustryAnalysis';
// 股票持仓风格分析
import StockHoldStyle from './StockHoldStyle';
// 股票盈亏TOP5
import StockProfitAndLossRank from './StockProfitAndLossRank';
// 权益公募基金收益表现
import PublicIncomePerformance from './PublicIncomePerformance';
// 权益公募换手分析
import PublicExchangeAnalysis from './PublicExchangeAnalysis';
// 赛道配置分析
import TrackConfigAnalysis from './TrackConfigAnalysis';
// 产品及股票收益详情
import ProductStockIncomeDetails from './ProductStockIncomeDetails';
// 组件的 key 值对应，根据后端返回的 id 进行匹配组件
export const COMPONENT_MAP = {
  ZhangHuShouYiBiaoXian: AccountIncomeSituation,
  YeJiQuShiJiHuiCheBiaoXian: TrendAndWithdraw,
  DangQianChiCangPeiZhiFenBu: CurrentPositionConfig,
  DaLeiZiChanShouYiGongXian: LargeAssetsIncomeContribution,
  GuPiaoShouYiBiaoXian: StockIncomeSituation,
  GuPiaoChiCangHuanShouFenXi: StockExchangeAnalysis,
  GuPiaoChiCangHangYeFenXi: StockIndustryAnalysis,
  GuPiaoChiCangFengGeFenXi: StockHoldStyle,
  GuPiaoYingKuiTOP5: StockProfitAndLossRank,
  QuanYiGongMuJiJinShouYiBiaoXian: PublicIncomePerformance,
  QuanYiGongMuHuangShouFenXi: PublicExchangeAnalysis,
  SaiDaoPeiZhiFenXi: TrackConfigAnalysis,
  GuPiaoShouYiXiangQing: ProductStockIncomeDetails,
};
// 需要判断是否触发的模块列表，
// 股票收益表现 和 权益公募基金收益表现
export const TRIGGER_LIST = [
  'GuPiaoShouYiBiaoXian',
  'QuanYiGongMuJiJinShouYiBiaoXian',
];
// 国内权益配置分析模块key
export const GUO_NEI_QUAN_YI_PEI_ZHI_FEN_XI03 = 'GuoNeiQuanYiPeiZhiFenXi03';
