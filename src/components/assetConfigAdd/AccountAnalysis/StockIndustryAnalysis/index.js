/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-01 15:13:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-11-16 11:09:48
 * @description 步骤二-国内权益配置分析-股票持仓行业分析
 */

import React from 'react';
import PropTypes from 'prop-types';

import IFWrap from '@/components/common/IFWrap';
import IECharts from '@/components/IECharts';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import { formatRatio } from '@/helper/number';

import {
  checkStatusCode,
} from '../../utils';

import styles from './index.less';

export default function StockIndustryAnalysis(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const options = {
    grid: {
      width: '100%',
      left: 0,
      top: 34,
      right: 10,
      bottom: 0,
      containLabel: true,
    },
    legend: {
      top: 0,
      right: 0,
      itemWidth: 15,
      itemHeight: 2,
      itemGap: 25,
      selectedMode: false,
      textStyle: {
        color: '#8c8c8c'
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'transparent',
      formatter: (param) => `<div class=${styles.tooltip}>
            <div class=${styles.title}>${param[0].axisValue}行业</div>
            <div class=${styles.valueLine}>
                <div class=${styles.left}>
                  <div class=${styles.custCircle} ></div>
                  <div>${param[0].seriesName}</div>
                </div>
                <div>${formatRatio(param[0].data[1]) || '--'}</div>
              </div>
              <div class=${styles.valueLine}>
                <div class=${styles.left}>
                  <div class=${styles.averageCircle} ></div>
                  <div>${param[1].seriesName}</div>
                </div>
                <div>${formatRatio(param[1].data[2]) || '--'}</div>
              </div>
          </div>`
    },
    dataset: {
      source: data?.chartData?.source || [],
    },
    xAxis: {
      type: 'category',
      axisTick: {
        alignWithLabel: true
      },
      axisLine: {
        onZero: false,
        lineStyle: {
          color: '#8c8c8c',
        },
      },
      axisLabel: {
        color: '#666',
        fontSize: 14,
        fontWeight: 'bold',
        margin: 15,
      }
    },
    yAxis: {
      gridIndex: 0,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        formatter: (value) => `${value * 100}%`
      },
      splitLine: {
        lineStyle: {
          color: '#e5e5e5'
        }
      }
    },
    series: [
      {
        type: 'bar',
        barWidth: '20px',
        barGap: 0,
        seriesLayoutBy: 'row',
        itemStyle: {
          color: '#febf68',
          barBorderRadius: [4, 4, 0, 0]
        },
        label: {
          show: false,
        }
      },
      {
        type: 'bar',
        barWidth: '20px',
        seriesLayoutBy: 'row',
        barGap: 0,
        itemStyle: {
          color: '#65aefc',
          barBorderRadius: [4, 4, 0, 0]
        }
      },
    ]
  };

  return (
    <div className={styles.moduleDetail}>
      <IFWrap when={isOk}>
        <AnalysisHeader
          title="股票持仓行业分析"
          onSwitchChange={handleChangeSwitch}
          checked={rawData?.switch}
          switchDisabled={!isOk}
          viewFlag={viewFlag}
        />
        <div className={styles.chartArea}>
          <IECharts
            option={options}
            resizable
            style={{
              height: '335px',
            }}
          />
          <div className={styles.chartTitle}>
            持仓行业占比
            <span>（%）</span>
          </div>
        </div>

        <Notes
          notes={data?.notes}
        />
        <Conclusions
          conclusions={data?.conclusions}
        />
      </IFWrap>
    </div>
  );
}

StockIndustryAnalysis.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

StockIndustryAnalysis.defaultProps = {
  rawData: {},
};
