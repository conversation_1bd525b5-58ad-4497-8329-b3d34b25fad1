.moduleDetail {
  .centerBox {
    margin-top: 20px;
    display: flex;

    .centerLeft {
      min-width: 463px;
      height: 267.5px;
      box-sizing: border-box;
      background: #fff;
      border: 1px solid #e9e9e9;
      margin-right: 20px;
      text-align: center;

      .chartBox {
        width: 100%;
        margin: 0 auto;
        height: 185px;
        position: relative;

        .allHoldBox {
          position: absolute;
          left: 0;
          width: 100%;
          top: 70px;
          height: 44px;

          .allHoldNum {
            line-height: 24px;
            margin-bottom: 5px;
            font-size: 18px;
            font-weight: bold;
            color: #333;
          }

          .allHoldTit {
            line-height: 15px;
            font-size: 12px;
            color: #666;
          }
        }
      }

      .legendBox {
        width: 100%;
        padding: 0 80px;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        height: 44px;
        overflow: hidden;

        .legendCell {
          width: 33.33%;
          display: flex;
          align-items: center;

          .legendColor {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 7px;
          }

          .legendName {
            font-size: 12px;
            color: #666;
            line-height: 18px;
          }
        }
      }
    }
  }
}