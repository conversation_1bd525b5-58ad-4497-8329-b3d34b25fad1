import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import isNil from 'lodash/isNil';
import sum from 'lodash/sum';
import IECharts from '@/components/IECharts';
import Table from '@/components/common/table';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import IFWrap from '@/components/common/IFWrap';
import { formatToUnit } from '@/helper/number';
import { times } from 'number-precision';
import {
  transferNumberColumn,
  checkStatusCode,
} from '../../utils';
import {
  CURRENT_POSITION_CONFIG_COLUMNS,
  ZI_CHAN_LEI_BIE,
  DANG_QIAN_SHI_ZHI,
  PEI_ZHI_ZHAN_BI,
} from '../../config';

import styles from './index.less';

export const COLOR_LIST = ['#66affd', '#3c71b9', '#708bf7', '#febf68', '#4085fe', '#2bc8db'];

export default function CurrentPositionConfig(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const handleTransferColumns = () => {
    const columns = map(CURRENT_POSITION_CONFIG_COLUMNS,
      (column) => transferNumberColumn(column));
    return columns;
  };

  const columns = handleTransferColumns();
  const options = {
    color: COLOR_LIST,
    tooltip: {
      trigger: 'item',
      formatter: (params) => `${params?.data?.name} ${times(params?.data?.percent, 100)}%`,
      textStyle: {
        fontStyle: 10,
      },
    },
    legend: {
      show: false,
    },
    animation: false,
    series: [
      {
        name: '大类资产配比分布为时点仓位',
        type: 'pie',
        radius: [50, 70],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: map(data?.tableData, (item) => ({
          value: item?.[DANG_QIAN_SHI_ZHI],
          name: item?.[ZI_CHAN_LEI_BIE],
          percent: item?.[PEI_ZHI_ZHAN_BI],
        })),
      },
    ],
  };
  const getSum = () => {
    const dataList = map(data?.tableData, (item) => item?.[DANG_QIAN_SHI_ZHI]);
    return sum(dataList);
  };
  // 标尺渲染
  const legendRender = () => map(data?.tableData, (item, index) => (
    <div className={styles.legendCell} key={index}>
      <div className={styles.legendColor} style={{ background: COLOR_LIST[index] }} />
      <div className={styles.legendTit}>{item?.[ZI_CHAN_LEI_BIE]}</div>
    </div>
  ));
  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="当前持仓配置分布"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      <IFWrap when={isOk}>
        <div className={styles.centerBox}>
          <div className={styles.centerLeft}>
            <div className={styles.chartBox}>
              <IFWrap when={!isNil(data?.tableData)}>
                <div className={styles.allHoldBox}>
                  <div className={styles.allHoldNum}>
                    {formatToUnit({
                      num: getSum(),
                      floatLength: 2,
                      unit: '元',
                      isThousandFormat: false,
                    })}
                  </div>
                  <div className={styles.allHoldTit}>当前持仓</div>
                </div>
              </IFWrap>
              <IECharts option={options} resizable />
            </div>
            <div className={styles.legendBox}>
              {legendRender()}
            </div>
            <Notes notes={data?.notes} />
          </div>
          <div className={styles.centerRight}>
            <Table
              className={styles.table}
              dataSource={data?.tableData}
              columns={columns}
              pagination={false}
              rowKey={ZI_CHAN_LEI_BIE}
              useNewUI
            />
          </div>
        </div>
        <Conclusions conclusions={data?.conclusions} />
      </IFWrap>
    </div>
  );
}

CurrentPositionConfig.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

CurrentPositionConfig.defaultProps = {
  rawData: {},
};
