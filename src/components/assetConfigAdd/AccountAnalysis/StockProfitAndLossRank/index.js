import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import isNil from 'lodash/isNil';

import IFWrap from '@/components/common/IFWrap';
import Table from '@/components/common/table';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import { formatRatio } from '@/helper/number';
import {
  transferNumberColumn,
  checkStatusCode,
} from '../../utils';
import {
  SHOU_YI_LV,
  STOCK_PROFIT_AND_LOSS_RANK_COLUMNS,
} from '../../config';

import styles from './index.less';

const ProfitPlaceHoldProps = { title: '分析期内暂无盈利标的', style: { height: 190 } };
const LossPlaceHoldProps = { title: '分析期内暂无亏损标的', style: { height: 190 } };

export default function StockProfitAndLossRank(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const handleTransferColumns = (flag) => {
    const columns = map(STOCK_PROFIT_AND_LOSS_RANK_COLUMNS(flag), (column) => {
      if (column.key === SHOU_YI_LV) {
        return {
          ...column,
          render: (text, record) => {
            if (record.flag) {
              return null;
            }
            if (isNil(text)) {
              return '--';
            }
            const value = formatRatio(text);
            // 判断是否是负数
            const rudecerFlag = text < 0;
            return (
              <div
                className={rudecerFlag ? styles.reducerNum : styles.addNum}
              >
                {rudecerFlag ? '' : '+'}{value}
              </div>
            );
          },
        };
      }
      return transferNumberColumn(column);
    });
    return columns;
  };

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="股票盈亏TOP5"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      <IFWrap when={isOk}>
        <div className={styles.tableArea}>
          <Table
            className={styles.profitTable}
            dataSource={data?.yingLiTableData}
            columns={handleTransferColumns(true)}
            pagination={false}
            rowKey="guPiaoTOP5"
            useNewUI
            isNeedEmptyRow
            placeHolderImageProps={ProfitPlaceHoldProps}
          />
          <Table
            className={styles.lossTable}
            dataSource={data?.kuiSunTableData}
            columns={handleTransferColumns(false)}
            pagination={false}
            rowKey="guPiaoTOP5"
            useNewUI
            isNeedEmptyRow
            placeHolderImageProps={LossPlaceHoldProps}
          />
        </div>
        <Notes
          notes={data?.notes}
        />
        <Conclusions
          conclusions={data?.conclusions}
        />
      </IFWrap>
    </div>
  );
}

StockProfitAndLossRank.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

StockProfitAndLossRank.defaultProps = {
  rawData: {},
};
