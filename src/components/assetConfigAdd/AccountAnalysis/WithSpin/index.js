import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Spin } from 'antd';
import isArray from 'lodash/isArray';
import forEach from 'lodash/forEach';
import some from 'lodash/some';

function WithSpin(props) {
  const {
    effects,
    effect,
    children,
  } = props;
  const [loading, setLoading] = useState(false);
  const toggleVisible = () => {
    setLoading(false);
  };

  useEffect(() => {
    // 判断 effect 是否是数组
    const result = [];
    if (isArray(effect)) {
      forEach(effect, (item) => {
        result.push(effects?.[item]);
      });
    }
    setLoading(some(result, Boolean));
  }, [effects, effect]);

  return [(
    <Spin spinning={loading}>
      {children}
    </Spin>
  ), toggleVisible];
}

export default WithSpin;

WithSpin.propTypes = {
  effects: PropTypes.object.isRequired,
  effect: PropTypes.array.isRequired,
  children: PropTypes.element.isRequired,
};

WithSpin.defaultProps = {
};
