import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import isNumber from 'lodash/isNumber';

import IFWrap from '@/components/common/IFWrap';
import Table from '@/components/common/table';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';

import TagPng from '../../images/tag.png';
import {
  transferNumberColumn,
  checkStatusCode,
} from '../../utils';
import {
  ZI_CHAN_LEI_BIE,
  SHOU_YI_LV,
  JI_ZHUN_ZHI_SHU_SHOU_YI_LV,
  LARGE_ASSETS_INCOME_CONTRIBUTION_COLUMNS,
} from '../../config';

import styles from './index.less';

export default function LargeAssetsIncomeContribution(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const handleTransferColumns = () => {
    const columns = map(LARGE_ASSETS_INCOME_CONTRIBUTION_COLUMNS, (column) => {
      if (column.key === ZI_CHAN_LEI_BIE) {
        return {
          ...column,
          // 客户持有的资产收益收益率＞基准指数收益率，则打标
          render: (text, record) => {
            const shouYilv = record?.[SHOU_YI_LV];
            const jiZhunZhiShuShouYiLv = record?.[JI_ZHUN_ZHI_SHU_SHOU_YI_LV];
            if (
              isNumber(shouYilv)
              && isNumber(jiZhunZhiShuShouYiLv)
              && shouYilv > jiZhunZhiShuShouYiLv) {
              return (
                <div>
                  {text}
                  <img src={TagPng} />
                </div>
              );
            }
            return text;
          }
        };
      }
      return transferNumberColumn(column);
    });
    return columns;
  };

  const columns = handleTransferColumns();

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="大类资产收益贡献"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      <IFWrap when={isOk}>
        <Table
          className={styles.table}
          dataSource={data?.tableData}
          columns={columns}
          pagination={false}
          rowKey="custId"
          useNewUI
        />
        <Notes
          notes={data?.notes}
        />
        <Conclusions
          conclusions={data?.conclusions}
        />
      </IFWrap>
    </div>
  );
}

LargeAssetsIncomeContribution.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

LargeAssetsIncomeContribution.defaultProps = {
  rawData: {},
};
