.moduleDetail {

  .chartArea {
    margin-top: 20px;
    position: relative;

    .chartTitle {
      position: absolute;
      top: 0;
      left: 0;
      font-size: 14px;
      color: #333;
      line-height: 20px;

      & > span {
        font-size: 12px;
        color: #999;
        line-height: 15px;
      }
    }
  }

  .tooltip {
    width: 220px;
    padding: 8px 12px;
    background: rgba(3, 23, 43, 0.69);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
    position: relative;

    .triangle {
      border-right: 5px solid rgba(3, 23, 43, 0.69);
      border-top: 5px solid transparent;
      border-bottom: 5px solid transparent;
      width: 0;
      height: 0;
      position: absolute;
      left: -5px;
      top: 22px;
    }

    .title {
      margin: 0 0 6px 8px;
    }

    .valueLine {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 10px;
      color: #fff;
      line-height: 14px;
      margin-bottom: 10px;

      .left {
        display: flex;
        align-items: center;
      }

      .custCircle {
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #febf68;
        border: 1px solid #fff;
        margin-right: 6px;
      }

      .averageCircle {
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #65aefc;
        border: 1px solid #fff;
        margin-right: 6px;
      }
    }
  }
}
