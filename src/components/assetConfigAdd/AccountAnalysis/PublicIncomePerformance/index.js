/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-01 15:13:20
 * @Last Modified by: LiuJianShu-K0180193
 * @Last Modified time: 2022-12-08 16:46:41
 * @description 步骤二-国内权益配置分析-权益公募基金收益表现
 */

import React from 'react';
import PropTypes from 'prop-types';

import IFWrap from '@/components/common/IFWrap';
import IECharts from '@/components/IECharts';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import { formatRatio } from '@/helper/number';

import {
  checkStatusCode,
} from '../../utils';

import Result from '../Result';

import styles from './index.less';

export default function PublicIncomePerformance(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    hasReportFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const options = {
    grid: {
      width: '100%',
      left: 0,
      right: 10,
      top: 34,
      bottom: 0,
      containLabel: true,
    },
    legend: {
      top: 0,
      right: 0,
      itemWidth: 15,
      itemHeight: 2,
      itemGap: 25,
      selectedMode: false,
      textStyle: {
        color: '#8c8c8c'
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'transparent',
      formatter: (param) => `<div class=${styles.tooltip}>
            <div class=${styles.title}>${param[0].axisValue}赛道</div>
            <div class=${styles.valueLine}>
                <div class=${styles.left}>
                  <div class=${styles.custCircle} ></div>
                  <div>${param[0].seriesName}</div>
                </div>
                <div>${formatRatio(param[0].data[1]) || '--'}</div>
              </div>
              <div class=${styles.valueLine}>
                <div class=${styles.left}>
                  <div class=${styles.averageCircle} ></div>
                  <div>${param[1].seriesName}</div>
                </div>
                <div>${formatRatio(param[1].data[2]) || '--'}</div>
              </div>
          </div>`
    },
    dataset: {
      source: data?.chartData?.source || [],
    },
    xAxis: {
      type: 'category',
      axisTick: {
        alignWithLabel: true
      },
      axisLine: {
        onZero: true,
        lineStyle: {
          color: '#8c8c8c',
        },
      },
      axisLabel: {
        color: '#666',
        fontSize: 14,
        fontWeight: 'bold',
        margin: 15,
      }
    },
    yAxis: {
      gridIndex: 0,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        formatter: (value) => `${value * 100}%`
      },
      splitLine: {
        lineStyle: {
          color: '#e5e5e5'
        }
      }
    },
    series: [
      {
        type: 'bar',
        barWidth: '20px',
        barGap: 0,
        seriesLayoutBy: 'row',
        itemStyle: {
          color: '#febf68',
        },
        label: {
          show: false,
        }
      },
      {
        type: 'bar',
        barWidth: '20px',
        seriesLayoutBy: 'row',
        barGap: 0,
        itemStyle: {
          color: '#65aefc',
        }
      },
    ]
  };

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="权益公募基金收益表现"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      {
        hasReportFlag
          ? (
            <IFWrap when={isOk}>
              <div className={styles.chartArea}>
                <IECharts
                  option={options}
                  resizable
                  style={{
                    height: '335px',
                  }}
                />
                <div className={styles.chartTitle}>
                  区间收益率
                  <span>（%）</span>
                </div>
              </div>
              <Notes
                notes={data?.notes}
              />
              <Conclusions
                conclusions={data?.conclusions}
              />
            </IFWrap>
          )
          : (
            <Result
              title="分析期内，可能存在以下情景，导致本模块无法触发："
              description={[
                '（1）国内权益公募持仓金额较少或持仓占比较低；',
                '（2）全市场型和赛道型公募实际持有天数较短；',
                '（3）全市场型和赛道型公募持仓不连续，均存在较长的空仓期。',
              ]}
            />
          )
      }
    </div>
  );
}

PublicIncomePerformance.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  hasReportFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

PublicIncomePerformance.defaultProps = {
  rawData: {},
};
