import React from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';

import IFWrap from '@/components/common/IFWrap';
import Table, { ToolTipCell } from '@/components/common/table';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';

import isNil from 'lodash/isNil';
import { formatRatio } from '@/helper/number';
import {
  transferNumberColumn,
  checkStatusCode,
} from '../../utils';
import {
  CHI_CANG_SHI_JIAN_ZUI_CHANG,
  CHI_CANG_SHI_JIAN_ZUI_DUAN,
  CHI_CANG_SHOU_YI_LV_ZUI_DI,
  CHI_CANG_SHOU_YI_LV_ZUI_GAO,
  PUBLIC_EXCHANGE_ANALYSIS_COLUMNS,
  ZUI_CHANG_SHOU_YI_LV,
  Z<PERSON>_DI_SHOU_YI_LV,
  Z<PERSON>_DUAN_SHOU_YI_LV,
  ZUI_GAO_SHOU_YI_LV,
} from '../../config';

import styles from './index.less';

export default function PublicExchangeAnalysis(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const handleTransferColumns = (flag) => {
    const columns = map(PUBLIC_EXCHANGE_ANALYSIS_COLUMNS(flag), (column) => {
      if (column.key === ZUI_DUAN_SHOU_YI_LV
        || column.key === ZUI_CHANG_SHOU_YI_LV
        || column.key === ZUI_GAO_SHOU_YI_LV
        || column.key === ZUI_DI_SHOU_YI_LV
      ) {
        return {
          ...column,
          render: (text) => {
            if (isNil(text)) {
              return '--';
            }
            const value = formatRatio(text);
            // 判断是否是负数
            const rudecerFlag = text < 0;
            return (
              <div
                className={rudecerFlag ? styles.reducerNum : styles.addNum}
              >
                {rudecerFlag ? '' : '+'}{value}
              </div>
            );
          },
        };
      }
      if (column.key === CHI_CANG_SHI_JIAN_ZUI_DUAN) {
        return {
          ...column,
          render: (text, record) => {
            if (isNil(text)) {
              return '--';
            }
            const displayName = `${record?.chiCangShiJianZuiDuan}(${record?.chiCangShiJianZuiDuanCode})`;
            return (
              <ToolTipCell tipContent={displayName} cellText={displayName} />
            );
          },
        };
      }
      if (column.key === CHI_CANG_SHI_JIAN_ZUI_CHANG) {
        return {
          ...column,
          render: (text, record) => {
            if (isNil(text)) {
              return '--';
            }
            const displayName = `${record?.chiCangShiJianZuiChang}(${record?.chiCangShiJianZuiChangCode})`;
            return (
              <ToolTipCell tipContent={displayName} cellText={displayName} />
            );
          },
        };
      }
      if (column.key === CHI_CANG_SHOU_YI_LV_ZUI_GAO) {
        return {
          ...column,
          render: (text, record) => {
            if (isNil(text)) {
              return '--';
            }
            const displayName = `${record?.chiCangShouYiLvZuiGao}(${record?.chiCangShouYiLvZuiGaoCode})`;
            return (
              <ToolTipCell tipContent={displayName} cellText={displayName} />
            );
          },
        };
      }
      if (column.key === CHI_CANG_SHOU_YI_LV_ZUI_DI) {
        return {
          ...column,
          render: (text, record) => {
            if (isNil(text)) {
              return '--';
            }
            const displayName = `${record?.chiCangShouYiLvZuiDi}(${record?.chiCangShouYiLvZuiDiCode})`;
            return (
              <ToolTipCell tipContent={displayName} cellText={displayName} />
            );
          },
        };
      }
      return transferNumberColumn(column);
    });
    return columns;
  };

  const columns = handleTransferColumns(data?.tableData?.[0]?.huanShouZhanShiChangJing);

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="权益公募换手分析"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      <IFWrap when={isOk}>
        <Table
          className={styles.table}
          dataSource={data?.tableData}
          columns={columns}
          pagination={false}
          rowKey="custId"
          useNewUI
        />
        <Notes
          notes={data?.notes}
        />
        <Conclusions
          conclusions={data?.conclusions}
        />
      </IFWrap>
    </div>
  );
}

PublicExchangeAnalysis.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

PublicExchangeAnalysis.defaultProps = {
  rawData: {},
};
