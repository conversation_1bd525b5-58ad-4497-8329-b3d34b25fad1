import React from 'react';
import PropTypes from 'prop-types';

import IFWrap from '@/components/common/IFWrap';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import {
  find, findIndex, map, max
} from 'lodash';

import {
  formatToUnit,
} from '@/helper/number';

import {
  checkStatusCode,
} from '../../utils';
import imgSrc from '../../static/two_arrow.png';
import { COLOR_LIST, ABSCISSA_LIST, ORDINATE_LIST } from './config';
import styles from './index.less';

export default function StockHoldStyle(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const getValue = (num, needInt = false) => formatToUnit({
    num,
    floatLength: 2,
    unit: '元',
    needInt,
    isThousandFormat: false,
  });
  // 九宫格渲染
  const spaceDataRender = () => map(ORDINATE_LIST, (item) => {
    const stockNode = map(ABSCISSA_LIST, (item1, index) => {
      // 首先先再列表数据找到横纵坐标的数据
      const findObjData = {
        guPiaoChiCangShiZhiGuiMo: item,
        guPiaoChiCangJiaZhiChengZhang: item1,
      };
        // eslint-disable-next-line max-len
      const findObj = find(data?.tableData, findObjData);
      // 需要获取该数据是不是排名前三且只数大于0
      const findObjN03Flag = findIndex(data?.tableData, findObjData) <= 2
          && findObj?.guPiaoZhiShu > 0;
        // eslint-disable-next-line max-len
      const findIndexNum = findIndex(data?.tableData, findObjData);
      // 颜色数组是从小到大的顺序，而后端返回的是从大到小的顺序，需要用8减去下标值，等到颜色对应的下标
      return (
        <div
          className={styles.spaceLi}
          key={index}
          style={{ background: COLOR_LIST[8 - findIndexNum] }}
        >
          <div className={styles.spaceLiTop}>
            <div className={styles.spaceLiCount}>
              {getValue(findObj?.guPiaoShiZhi)}
            </div>
            {findObjN03Flag ? (
              <div className={styles.spaceLiNum}>
                {findObj?.guPiaoZhiShu}只
              </div>
            ) : (
              ''
            )}
          </div>
          <div className={styles.spaceLiBottom}>
            {findObjN03Flag
              ? findObj?.guPiaoChiCangBiaoDiMingList
              : `${findObj?.guPiaoZhiShu}只`}
          </div>
        </div>
      );
    });
    return (
      <div className={styles.spaceCell} key={item?.key}>
        <div className={styles.spaceLiTit}>{item}</div>
        {stockNode}
      </div>
    );
  });
  // 横坐标渲染
  // eslint-disable-next-line max-len
  const abscissaListRender = () => map(ABSCISSA_LIST, (item, index) => (
    <div className={styles.spaceLi} key={index}>
      {item}
    </div>
  ));
  // 底部标尺渲染
  // eslint-disable-next-line max-len
  const colorDataRender = () => map(COLOR_LIST, (item) => <div key={item} style={{ background: item }} />);
  // 右侧表格渲染
  const detailDataRender = () => map(data?.chiCangFengGeListData, (item) => {
    const stockNode = map(item?.guPiaoChiCangBiaoDiList, (item1, index) => (
      <div className={styles.contentCell} key={item?.code}>
        <div className={styles.contentNo}>{index + 1}</div>
        <div className={styles.contentText}>
          {`${item1?.guPiaoJianCheng}（${item1?.guPiaoDaiMa}）`}
        </div>
        <div className={styles.contentText}>
          市值：{getValue(item1?.guPiaoShiZhi)}
        </div>
        <div className={styles.contentText}>
          持仓收益：{getValue(item1?.chiCangShouYi)}
        </div>
      </div>
    ));
    return (
      <div
        className={styles.detailCell}
        key={item?.guPiaoChiCangFengGeMingCheng}
      >
        <div className={styles.detailTit}>
          {item?.guPiaoChiCangFengGeMingCheng}
        </div>
        <div className={styles.detailContent}>{stockNode}</div>
      </div>
    );
  });
  // 获取最大值
  const getMax = () => getValue(
    max(map(data?.tableData, (item) => item?.guPiaoShiZhi ?? 0)),
    true
  );
  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="股票持仓风格分析"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      <IFWrap when={isOk}>
        <div className={styles.centerBox}>
          <div className={styles.centerLeft}>
            <div className={styles.spaceCellTit}>
              <div className={styles.spaceLiTit} />
              {abscissaListRender()}
            </div>
            {spaceDataRender()}
            <div className={styles.spaceBottom}>
              <div className={styles.spaceBottomTit}>持仓规模：</div>
              <div className={styles.spaceBottomTips}>0元</div>
              <div className={styles.tagList}>{colorDataRender()}</div>
              <div className={styles.spaceBottomTips}>{getMax()}</div>
            </div>
          </div>
          <div className={styles.centerCenter}>
            <img src={imgSrc} className={styles.imgBox} />
          </div>
          <div className={styles.centerRight}>{detailDataRender()}</div>
        </div>
        <Notes notes={data?.notes} />
        <Conclusions conclusions={data?.conclusions} />
      </IFWrap>
    </div>
  );
}

StockHoldStyle.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

StockHoldStyle.defaultProps = {
  rawData: {},
};
