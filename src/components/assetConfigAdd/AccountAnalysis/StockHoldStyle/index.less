.moduleDetail {
  .centerBox {
    margin-top: 20px;
    display: flex;
    align-items: center;

    .centerLeft {
      width: 710px;
      height: 348px;
      background: #fff;
      border: 1px solid #cedce7;
      padding: 20px;
      box-sizing: border-box;
      font-size: 14px;
      font-weight: bold;
      color: #666;
      line-height: 20px;
      text-align: center;

      .spaceCellTit {
        height: 20px;
        display: flex;

        margin-bottom: 10px;

        .spaceLi {
          width: 207px;
        }

        .spaceLiTit {
          text-align: left;
          width: 38px;
        }
      }

      .spaceCell {
        height: 72px;
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        &:last-child {
          margin: 0 !important;
        }

        .spaceLi {
          width: 207px;
          height: 72px;
          background: #d9edff;
          margin-right: 5px;
          padding: 16px 0;
          box-sizing: border-box;

          .spaceLiTop {
            margin-bottom: 2px;
            display: flex;
            align-items: center;
            justify-content: center;

            .spaceLiCount {
              color: #333;
            }

            .spaceLiNum {
              margin-left: 5px;
              font-size: 12px;
              color: #000;
              opacity: 0.4;
            }
          }

          .spaceLiBottom {
            font-size: 12px;
            color: #000;
            opacity: 0.4;
            line-height: 18px;
          }

          &:last-child {
            margin: 0 !important;
          }
        }

        .spaceLiTit {
          text-align: left;
          width: 38px;
        }
      }

      .spaceBottom {
        margin-top: 30px;
        display: flex;
        align-items: center;
        height: 18px;
        font-size: 12px;
        line-height: 18px;
        justify-content: center;

        .spaceBottomTit {
          color: #333;
        }

        .spaceBottomTips {
          color: #8c8c8c;
        }

        .tagList {
          margin-left: 5px;
          display: flex;
          align-items: center;

          &>div {
            width: 36px;
            height: 6px;
            margin-right: 5px;
          }
        }
      }
    }

    .centerCenter {
      height: 348px;
      width: 82px;
      display: flex;
      justify-content: center;
      align-items: center;

      .imgBox {
        width: 42px;
        height: 36px;
      }
    }

    .centerRight {
      flex: 1;

      .detailCell {
        margin-bottom: 12px;

        &:last-child {
          margin: 0;
        }

        .detailTit {
          line-height: 24px;
          height: 24px;
          padding-left: 10px;
          font-size: 14px;
          font-weight: bold;
          color: #333;
          background-color: linear-gradient(270deg, rgba(180, 224, 255, 0.2) 0%, #b4e0ff 100%);
          background-image: url('../../static/header_bg.png');
          background-repeat: no-repeat;
          background-position: left center;
          background-size: 94px 24px;
        }

        .detailContent {
          background: linear-gradient(270deg, rgba(245, 245, 245, 0) 0%, #f5f5f5 100%);
          padding: 8px 10px;

          .contentCell {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            box-sizing: border-box;

            &:last-child {
              margin: 0;
            }

            .contentNo {
              width: 16px;
              height: 16px;
              border-radius: 16px;
              background: #99ccf0;
              overflow: hidden;
              text-align: center;
              line-height: 16px;
              color: #fff;
              font-size: 12px;
              margin-right: 5px;
            }

            .contentText {
              font-size: 12px;
              color: #666;
              line-height: 20px;
              flex: 1;
            }
          }
        }
      }
    }
  }
}