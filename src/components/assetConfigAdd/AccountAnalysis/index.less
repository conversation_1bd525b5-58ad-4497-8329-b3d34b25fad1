.accountAnalysis {
  padding: 0 20px 30px;
  position: relative;

  .rightImg {
    position: absolute;
    top: 98px;
    right: 0;
    width: 200px;
    height: 43px;
    text-align: center;
    font-size: 12px;
    color: #fff;
    line-height: 16px;
    background: url('@/routes/newAccountAnalysis/img/tips-bg.png');
    background-size: 100% 100%;
    padding: 10px 10px 17px;
    z-index: 9;
  }

  .header {
    width: 399px;
    height: 30px;
    margin-bottom: -10px;
    background: url('../images/header.png') no-repeat 0 0;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .showAnalysisBox {
      font-size: 14px;
      color: #fff;
      line-height: 30px;
      margin-right: 32px;
    }

    .title {
      display: inline-block;
      margin: 0 20px;
      width: 96px;
      font-size: 16px;
      font-weight: bold;
      color: #fff;
      line-height: 30px;
    }

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-select {
        height: 26px;
        vertical-align: baseline;
        margin-right: 10px;
      }

      .ant-select-focused .ant-select-selection,
      .ant-select-selection:focus,
      .ant-select-selection:active {
        border-color: transparent;
        box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
      }

      .ant-select-selection:hover {
        border-color: transparent;
      }

      .ant-select-arrow {
        border-top-color: #fff;
      }

      .ant-select-selection {
        background-color: transparent;
        border: 0;
        transition: none;

        &:hover {
          transition: none;
          background-color: transparent;
        }
      }

      .ant-select-selection__rendered {
        margin-left: 0;
      }

      .ant-select-selection-selected-value {
        color: #fff;
      }

      .ant-select-arrow {
        margin-top: -6px;

        svg {
          fill: #fff;
        }
      }
    }
  }

  .table {
    margin-top: 21px;
  }

  .titleWithIcon {
    img {
      width: 16px;
      height: 16px;
      margin-left: 5px;
      margin-top: -2px;
      vertical-align: middle;
      cursor: pointer;
    }

    svg {
      fill: #108ee9;
    }
  }

  .note {
    font-size: 14px;
    color: #333;
    line-height: 22px;
    padding: 20px 20px 0;
    margin: 10px 0 -10px 0;
  }
}

.dropdownSelect {
  width: 280px !important;
  padding-top: 10px;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-select-dropdown-menu-item {
      padding: 0 16px !important;
      height: 38px;
      line-height: 38px;
    }

    .ant-select-dropdown-menu-item-disabled {
      color: #ccc !important;
    }
  }
}

.modal {
  width: 650px !important;
  height: 554px;
  background: #fff;
  border-radius: 2px;
}

.modelContent {
  font-size: 14px;
  color: #333;
  line-height: 24px;
  margin-top: 20px;

  &:first-child {
    margin-top: 0;
  }
}