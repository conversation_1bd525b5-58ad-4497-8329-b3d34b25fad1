import React from 'react';
import PropTypes from 'prop-types';
import includes from 'lodash/includes';
import slice from 'lodash/slice';
import map from 'lodash/map';
import isEmpty from 'lodash/isEmpty';
import ceil from 'lodash/ceil';
import isNil from 'lodash/isNil';
import echarts from 'echarts';

import IFWrap from '@/components/common/IFWrap';
import IECharts from '@/components/IECharts';
import Table from '@/components/common/table';
import Notes from '@/components/assetConfigAdd/Notes';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import {
  formatRatio,
} from '@/helper/number';

import Result from '../Result';

import {
  transferNumberColumn,
  checkStatusCode,
} from '../../utils';
import {
  STOCK_INCOME_SITUATION_COLUMNS,
  SHA_PU_BI_LV,
  CALMAR_BI_LV,
} from '../../config';

import styles from './index.less';

export default function StockIncomeSituation(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    hasReportFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const transferData = data?.dataset?.source || [];

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const handleTransferColumns = () => {
    const columns = map(STOCK_INCOME_SITUATION_COLUMNS, (column) => {
      if (column.key === 'duiBiWeiDu') {
        return {
          ...column,
          render: (text, record) => (
            <div className={styles.duiBiWeiDu}>
              {text}
            </div>
          ),
        };
      }
      if (column.key === SHA_PU_BI_LV || column.key === CALMAR_BI_LV) {
        return {
          ...column,
          render: (text) => {
            if (isNil(text)) {
              return '--';
            }
            const value = formatRatio(text, { floatLength: 3, sign: '', radix: 1 });
            return value;
          },
        };
      }
      return transferNumberColumn(column);
    });
    return columns;
  };

  const columns = handleTransferColumns();

  const handleGenerateIntervalDates = () => {
    const graphDates = transferData?.[0] ?? [];
    if (!isEmpty(graphDates)) {
      const dates = slice(graphDates, 1);
      const intervalDates = [dates[0]];
      const interval = ceil(dates.length / 12);
      for (let i = 1; i < 12; i++) {
        intervalDates.push(dates?.[i * interval]);
      }
      intervalDates.push(dates[dates.length - 1]);
      return intervalDates;
    }
    return [];
  };

  const handleFormatTooltip = (param) => {
    const length = param?.length ?? 1;
    let classes = [];
    if (length > 1) {
      classes = [styles.custCircle, styles.averageCircle];
    } else {
      classes = param?.[0]?.seriesName === '客户股票账户'
        ? [styles.custCircle]
        : [styles.averageCircle];
    }
    const secondLine = length > 1
      ? `<div class=${styles.valueLine}>
        <div class=${styles.left}>
          <div class=${classes[1]}></div>
          <div>${param[1].seriesName}</div>
        </div>
        <div>${formatRatio(param[1].data[2]) || '--'}</div>
      </div>`
      : '';
    return (`
      <div class=${styles.tooltip}>
        <div class=${styles.title}>${param[0].axisValue}</div>
        <div class=${styles.valueLine}>
          <div class=${styles.left}>
            <div class=${classes[0]}></div>
            <div>${param[0].seriesName}</div>
          </div>
          <div>${formatRatio(param[0].data[1]) || '--'}</div>
        </div>
        ${secondLine}
      </div>`
    );
  };

  const intervalDates = handleGenerateIntervalDates();

  const options = {
    grid: {
      left: 0,
      right: 34,
      top: 60,
      bottom: 0,
      containLabel: true
    },
    legend: {
      top: 0,
      right: 0,
      itemWidth: 15,
      itemHeight: 2,
      icon: 'rect',
      selectedMode: false,
      textStyle: {
        color: '#8c8c8c'
      },
    },
    color: ['#febf68', '#65aefc'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'transparent',
      formatter: (param) => handleFormatTooltip(param),
    },
    dataset: {
      source: transferData,
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: true,
        alignWithLabel: true,
        interval: (index, value) => {
          if (includes(intervalDates, value)) {
            return true;
          }
          return false;
        }
      },
      axisLabel: {
        interval: 0,
        formatter: (value, index) => {
          if (includes(intervalDates, value)) {
            return value;
          }
          return '';
        },
        textStyle: {
          color: '#666',
          fontSize: 12,
        }
      },
      axisLine: {
        onZero: false,
        lineStyle: {
          color: '#8c8c8c',
        }
      },
    },
    yAxis: {
      gridIndex: 0,
      axisLabel: {
        color: '#666',
        formatter: (value) => {
          if (value === 0) {
            return `{zero|${value}%}`;
          }
          return `${(value * 100).toFixed(0)}%`;
        },
        rich: {
          zero: {
            fontWeight: 'bold',
            color: '#333',
          }
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        type: 'line',
        seriesLayoutBy: 'row',
        connectNulls: true,
        emphasis: { focus: 'series' },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0.17,
              color: 'rgba(254, 191, 104, 0.16)'
            }, {
              offset: 0.85,
              color: 'rgba(254, 191, 104, 0)'
            }], false),
          }
        },
      },
      {
        type: 'line',
        connectNulls: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0.17,
              color: 'rgba(113, 178, 255, 0.16)'
            }, {
              offset: 0.85,
              color: 'rgba(113, 181, 255, 0)'
            }], false),
          }
        },
      }
    ]
  };

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="股票收益表现"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      {
        hasReportFlag
          ? (
            <IFWrap when={isOk}>
              <div className={styles.chartArea}>
                <IECharts
                  option={options}
                  resizable
                  style={{
                    height: '335px',
                  }}
                />
                <div className={styles.chartTitle}>
                  区间收益率
                  <span>（%）</span>
                </div>
              </div>
              <Table
                className={styles.table}
                dataSource={data?.tableData}
                columns={columns}
                pagination={false}
                rowKey="custId"
                useNewUI
                withBorder
              />
              <Notes
                notes={data?.notes}
              />
              <Conclusions
                conclusions={data?.conclusions}
              />
            </IFWrap>
          )
          : (
            <Result
              title="分析期内，可能存在以下情景，导致本模块无法触发："
              description={[
                '（1）股票账户持仓金额较少或持仓占比较低；',
                '（2）股票实际持有天数较短；',
                '（3）股票持仓不连续，存在较长的空仓期。',
              ]}
            />
          )
      }
    </div>
  );
}

StockIncomeSituation.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  hasReportFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

StockIncomeSituation.defaultProps = {
  rawData: {},
};
