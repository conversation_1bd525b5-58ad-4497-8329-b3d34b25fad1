import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import includes from 'lodash/includes';
import slice from 'lodash/slice';
import isEmpty from 'lodash/isEmpty';
import indexOf from 'lodash/indexOf';
import ceil from 'lodash/ceil';
import echarts from 'echarts';

import IFWrap from '@/components/common/IFWrap';
import IECharts from '@/components/IECharts';
import Conclusions from '@/components/assetConfigAdd/Conclusions';
import AnalysisHeader from '@/components/assetConfigAdd/AnalysisHeader';
import {
  formatRatio,
} from '@/helper/number';

import {
  checkStatusCode,
} from '../../utils';

import styles from './index.less';

export default function TrendAndWithdraw(props) {
  const {
    id,
    parentId,
    data,
    onSwitchChange,
    viewFlag,
    rawData,
  } = props;
  const statusCode = rawData?.statusCode;
  const {
    isOk,
  } = checkStatusCode(statusCode);

  const handleChangeSwitch = (value) => {
    onSwitchChange(id, parentId, value);
  };

  const handleGenerateIntervalDates = () => {
    const graphDates = data?.graphData?.[0] ?? [];
    if (!isEmpty(graphDates)) {
      const dates = slice(graphDates, 1);
      const intervalDates = [dates[0]];
      const interval = ceil(dates.length / 12);
      for (let i = 1; i < 12; i++) {
        intervalDates.push(dates?.[i * interval]);
      }
      intervalDates.push(dates[dates.length - 1]);
      return intervalDates;
    }
    return [];
  };

  const handleFormatTooltip = (param) => {
    const length = param?.length ?? 1;
    let classes = [];
    if (length > 1) {
      classes = [styles.custCircle, styles.averageCircle];
    } else {
      classes = param?.[0]?.seriesName === '客户账户'
        ? [styles.custCircle]
        : [styles.averageCircle];
    }
    const secondLine = length > 1
      ? `<div class=${styles.valueLine}>
        <div class=${styles.left}>
          <div class=${classes[1]}></div>
          <div>${param[1].seriesName}</div>
        </div>
        <div>${formatRatio(param[1].data[2]) || '--'}</div>
      </div>`
      : '';
    return (`
      <div class=${styles.tooltip}>
        <div class=${styles.title}>${param[0].axisValue}</div>
        <div class=${styles.valueLine}>
          <div class=${styles.left}>
            <div class=${classes[0]}></div>
            <div>${param[0].seriesName}</div>
          </div>
          <div>${formatRatio(param[0].data[1]) || '--'}</div>
        </div>
        ${secondLine}
      </div>`
    );
  };

  const handleFormatMarkTooltip = (text) => (`
      <div class=${styles.markTooltip}>
        ${text}
      </div>`
  );

  const formatDate = (dateString = '') => {
    const FORMAT = 'YYYY-MM-DD';
    const pattern = /年|月|日/g;
    return moment(dateString.replace(pattern, '')).format(FORMAT);
  };

  const getRange = (payload, valueList) => {
    const dateList = data?.graphData?.[0];
    const startDate = formatDate(payload?.zuiDaHuiCheQiShiRiQi ?? '');
    const endDate = formatDate(payload?.zuiDaHuiCheJieShuRiQi ?? '');
    const startIndex = indexOf(dateList, startDate);
    const endIndex = indexOf(dateList, endDate);
    return {
      startDate,
      startValue: valueList?.[startIndex] || '',
      endDate,
      endValue: valueList?.[endIndex] || '',
    };
  };

  const intervalDates = handleGenerateIntervalDates();

  const accountRange = getRange(data?.account, data?.graphData?.[1]);
  const indexRange = getRange(data?.index, data?.graphData?.[2]);

  const options = {
    grid: {
      left: 0,
      right: 34,
      top: 60,
      bottom: 0,
      containLabel: true
    },
    legend: {
      top: 0,
      right: 0,
      itemWidth: 15,
      itemHeight: 2,
      icon: 'rect',
      selectedMode: false,
      textStyle: {
        color: '#8c8c8c'
      },
    },
    color: ['#febf68', '#65aefc'],
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'transparent',
      formatter: (param) => handleFormatTooltip(param),
    },
    dataset: {
      // source: handleTransferValues(),
      source: data?.graphData ?? [],
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: true,
        alignWithLabel: true,
        interval: (index, value) => {
          if (includes(intervalDates, value)) {
            return true;
          }
          return false;
        }
      },
      axisLabel: {
        interval: 0,
        formatter: (value, index) => {
          if (includes(intervalDates, value)) {
            return value;
          }
          return '';
        },
        textStyle: {
          color: '#666',
          fontSize: 12,
        }
      },
      axisLine: {
        onZero: false,
        lineStyle: {
          color: '#8c8c8c',
        }
      },
    },
    yAxis: {
      gridIndex: 0,
      axisLabel: {
        color: '#666',
        formatter: (value) => {
          if (value === 0) {
            return `{zero|${value}%}`;
          }
          return `${(value * 100).toFixed(0)}%`;
        },
        rich: {
          zero: {
            fontWeight: 'bold',
            color: '#333',
          }
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        type: 'line',
        seriesLayoutBy: 'row',
        connectNulls: true,
        emphasis: { focus: 'series' },
        zlevel: 2,
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0.17,
              color: 'rgba(254, 191, 104, 0.16)'
            }, {
              offset: 0.85,
              color: 'rgba(254, 191, 104, 0)'
            }], false),
          }
        },
        markArea: {
          label: {
            formatter: [
              `{a|账户期间最大回撤率：${formatRatio(data?.account?.zuiDaHuiCheLv)}}`
            ].join('\n'),
            backgroundColor: '#fff',
            borderColor: '#e7e7e7',
            borderWidth: 1,
            borderRadius: 2,
            padding: [8, 10],
            shadowBlur: 4,
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowOffsetX: 0,
            shadowOffsetY: 1,
            opacity: 1,
            position: 'top',
            zIndex: 999,
            rich: {
              a: {
                lineHeight: 24,
                color: '#ec9319',
                fontSize: 13,
                zIndex: 999,
              },
            },
          },
          data: [
            [
              {
                xAxis: accountRange?.startDate,
                yAxis: accountRange?.startValue,
              },
              {
                xAxis: accountRange?.endDate,
                yAxis: accountRange?.endValue,
              }
            ]
          ],
          itemStyle: {
            color: 'rgba(254, 191, 104, .1)',
          }
        },
        markLine: {
          symbol: 0,
          data: [
            [
              {
                xAxis: accountRange?.startDate,
                yAxis: accountRange?.startValue,
              },
              {
                xAxis: accountRange?.endDate,
                yAxis: accountRange?.endValue,
              }
            ]
          ],
        },
        markPoint: {
          tooltip: {
            trigger: 'item',
            show: true,
            formatter: () => handleFormatMarkTooltip(`最高收益率${formatRatio(accountRange?.startValue) || '--'}`)
          },
          data: [
            {
              xAxis: accountRange?.startDate,
              yAxis: accountRange?.startValue,
            },
          ],
          symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAfCAMAAAAhm0ZxAAABPlBMVEUAAAAAAAD///+AgICAgP9VqqqqqqqAgL+Av79mmZmZmcyAgKqAqtVtkraSkraAn79xjsaAmcyLotGAlb92ncSJncSAkraApMh3mbuImcyAn7+HpcOAnMaGobyGocmAmb+ApsyAosWFm7yFm8iAn8qFmcKAncSEocaApMiEnsGAmcSAn8eDosmAnsOHnsODn8GDn8iAn8aGn8aGnsiCoMSCn8GFociCnMaCoMiEncSEosSEn8TK1+WCoMfH1OWEosjFzuLGzuO1xt25yuGxxN3e5/Hf5/LP2ujR2ur09/r09/rz9vrz9vnw8/jw8/js8fbs8fb9/f78/f79/f78/f79/f77/P3+/v/+///9/f7+/v7+/v/9/f7////+/v7+/v/////+v2j+wWz+yoH+yoL+yoP/6cv/6cz/6s3///9JdwqkAAAAYXRSTlMAAQECAgMDBAQFBQYGBwcICQoLDA0NDg4PDxAREhMTFBQWFxcYGRobHB0eICEiIiUlKCgqKy0uMTM0NDg6Ozs8Pj9MTVJeX29vpaanqautsLHl5ubn5+j09PX19fb5+vr6lU0slwAAAAFiS0dEabxrxLQAAAF3SURBVCjPnZLNbhRBDISryp4Jyy6ROCCFC+//XhyRokB2kyzTdnHoCUII5UD/qCV/7W7ZVcD/Df51AgD8R4xzc497x9wXyVdme97gnKRIgobtntQJkKQkCQSM7u62YeQkESJFwC13VTeABKiIUKRE2t2jyELPPEVqyUVBwMPb2AibSFARuskl706H0/np8Zuka9h2UgotuS5fPt0C73F/+Eq0o+0IRea6rp/vbmuMxpF4hnvmUczMPN7WBrj58TG7qkQKpKTMAwoAUFgzJZEQSSqo497hxkmMGQZISjzvUgQulEgCAkCC/InY2UaCxGQ27Mv3WIN5w4eLDXv2zHa3z4s+CMDD/cXdtoG07S53/xjn9d3L9fLU7Zr1we7uIfLyLHV3jTGmSmm3R4QETx1GjTGG2w7O+rn/W2Ns27aNru4gSFre3x6TdVU74S7y2t3M3/p1VRtpuAvhKm2vundV207ADTtcu188/eLdmf/yme23/PmWr38Bdg408EbhcLEAAAAASUVORK5CYII=',
          symbolSize: [27, 31],
        },
      },
      {
        type: 'line',
        seriesLayoutBy: 'row',
        connectNulls: true,
        emphasis: { focus: 'series' },
        zlevel: 1,
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0.17,
              color: 'rgba(113, 178, 255, 0.16)'
            }, {
              offset: 0.85,
              color: 'rgba(113, 181, 255, 0)'
            }], false),
          }
        },
        markLine: {
          symbol: 0,
          data: [
            [
              {
                xAxis: indexRange?.startDate,
                yAxis: indexRange?.startValue,
              },
              {
                xAxis: indexRange?.endDate,
                yAxis: indexRange?.endValue,
              }
            ]
          ],
        },
        markPoint: {
          data: [{
            xAxis: indexRange?.startDate,
            yAxis: indexRange?.startValue,
          },
          {
            xAxis: indexRange?.endDate,
            yAxis: indexRange?.endValue,
          }],
          symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAfCAMAAAAhm0ZxAAAAWlBMVEUAAACNocCEocODocSEncKFoMSEocWFosaGocj7+/2FociDoMT+/v/8/f7y9fn9/v7+/v/4+fvs8Pb19/rO2+nh5/ClutapvNi4yd64yuFrrvT////T5vuIvfbfL7xSAAAAGnRSTlMABgwWERwjKC/UOzT17aLr6tmrnGlVSkEvK/IQ+MUAAADBSURBVCjP1dLLDgIhDAVQlUcZGZz3s/D/vylUiOCYzMKVd3vS2yZw+btcU77KLeSgCXjiD+KchXBeIhHbhqquho0RljS1GNJOBRKtDVq3O4vNSpjGPIkH2j3E4kN49IPJhKzQkTmspCCLlUzIGvdXsJaCpVKqhGwOfGlh2b6DLSbeaRayfJ8aDYaYUdG+/E7Qc9/VXT9roDvfFgb1PUSHMbJYSghKaaWAiCrJIkoAkJGSJaREOnm/k3c/+y/lP/s1T+XhDc5Aof4vAAAAAElFTkSuQmCC',
          symbolSize: [27, 31],
        }
      },
      {
        type: 'line',
        seriesLayoutBy: 'row',
        connectNulls: true,
        emphasis: { focus: 'series' },
        zlevel: 1,
        markPoint: {
          tooltip: {
            trigger: 'item',
            show: true,
            formatter: () => handleFormatMarkTooltip(`最低收益率${formatRatio(accountRange?.endValue) || '--'}`)
          },
          data: [
            {
              xAxis: accountRange?.endDate,
              yAxis: accountRange?.endValue,
            }
          ],
          symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAfCAMAAAAhm0ZxAAABPlBMVEUAAAAAAAD///+AgICAgP9VqqqqqqqAgL+Av79mmZmZmcyAgKqAqtVtkraSkraAn79xjsaAmcyLotGAlb92ncSJncSAkraApMh3mbuImcyAn7+HpcOAnMaGobyGocmAmb+ApsyAosWFm7yFm8iAn8qFmcKAncSEocaApMiEnsGAmcSAn8eDosmAnsOHnsODn8GDn8iAn8aGn8aGnsiCoMSCn8GFociCnMaCoMiEncSEosSEn8TK1+WCoMfH1OWEosjFzuLGzuO1xt25yuGxxN3e5/Hf5/LP2ujR2ur09/r09/rz9vrz9vnw8/jw8/js8fbs8fb9/f78/f79/f78/f79/f77/P3+/v/+///9/f7+/v7+/v/9/f7////+/v7+/v/////+v2j+wWz+yoH+yoL+yoP/6cv/6cz/6s3///9JdwqkAAAAYXRSTlMAAQECAgMDBAQFBQYGBwcICQoLDA0NDg4PDxAREhMTFBQWFxcYGRobHB0eICEiIiUlKCgqKy0uMTM0NDg6Ozs8Pj9MTVJeX29vpaanqautsLHl5ubn5+j09PX19fb5+vr6lU0slwAAAAFiS0dEabxrxLQAAAF3SURBVCjPnZLNbhRBDISryp4Jyy6ROCCFC+//XhyRokB2kyzTdnHoCUII5UD/qCV/7W7ZVcD/Df51AgD8R4xzc497x9wXyVdme97gnKRIgobtntQJkKQkCQSM7u62YeQkESJFwC13VTeABKiIUKRE2t2jyELPPEVqyUVBwMPb2AibSFARuskl706H0/np8Zuka9h2UgotuS5fPt0C73F/+Eq0o+0IRea6rp/vbmuMxpF4hnvmUczMPN7WBrj58TG7qkQKpKTMAwoAUFgzJZEQSSqo497hxkmMGQZISjzvUgQulEgCAkCC/InY2UaCxGQ27Mv3WIN5w4eLDXv2zHa3z4s+CMDD/cXdtoG07S53/xjn9d3L9fLU7Zr1we7uIfLyLHV3jTGmSmm3R4QETx1GjTGG2w7O+rn/W2Ns27aNru4gSFre3x6TdVU74S7y2t3M3/p1VRtpuAvhKm2vundV207ADTtcu188/eLdmf/yme23/PmWr38Bdg408EbhcLEAAAAASUVORK5CYII=',
          symbolSize: [27, 31],
        },
      },
    ]
  };

  return (
    <div className={styles.moduleDetail}>
      <AnalysisHeader
        title="业绩趋势及回撤表现"
        onSwitchChange={handleChangeSwitch}
        checked={rawData?.switch}
        switchDisabled={!isOk}
        viewFlag={viewFlag}
      />
      <IFWrap when={isOk}>
        <div className={styles.chartArea}>
          <IECharts
            option={options}
            resizable
            style={{
              height: '335px',
            }}
            notMerge
          />
          <div className={styles.chartTitle}>
            区间收益率
            <span>（%）</span>
          </div>
        </div>
        <Conclusions
          conclusions={data?.conclusions}
        />
      </IFWrap>
    </div>
  );
}

TrendAndWithdraw.propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  onSwitchChange: PropTypes.func.isRequired,
  viewFlag: PropTypes.bool.isRequired,
  rawData: PropTypes.object,
};

TrendAndWithdraw.defaultProps = {
  rawData: {},
};
