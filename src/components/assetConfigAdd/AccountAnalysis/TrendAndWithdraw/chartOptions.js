export const chartOptions = {
  legend: {},
  tooltip: {
    trigger: 'axis',
  },
  dataset: {
    source: [
      ['维度'],
      ['客户账户'],
      ['沪深300'],
    ]
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: true,
      alignWithLabel: true,
    },
    axisLabel: {
      interval: 0,
      // rotate:40,
      textStyle: {
        color: '#bfbfbf',
        fontSize: 12
      }
    }
  },
  yAxis: { gridIndex: 0 },
  grid: { top: '5%' },
  series: [
    {
      type: 'line',
      smooth: true,
      seriesLayoutBy: 'row',
      emphasis: { focus: 'series' }
    },
    {
      type: 'line',
      smooth: true,
      seriesLayoutBy: 'row',
      emphasis: { focus: 'series' }
    },
  ]
};
