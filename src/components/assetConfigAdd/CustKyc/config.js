// 户不满6个月，提示跳转【市场观点】，无账户分析节点，话术提示
export const HASNOT_SIX_MONTH_TEXT = '客户开户未满6个月,下一步将进入【市场观点】';
// 无分析周期可选择时弹框提示
export const NO_CHECK_DATE_TEXT = '下一步将进入【市场观点】';
// 新增资金配置信息
export const KEY_ADD_CONFIG = 'addAssetsConfig';
// 整数的正则表达式
export const NUM_REG = /^([0-9]*)?$/;
// 两位小数的正则表达式
export const NUM_FLOOR_REG = /^(0|[1-9]\d*)(.\d{1,2})?$/;
// 请输入整数
export const NUM_PLACE_HOLDER = '请输入整数';
// 新增资金少于1万元提示
export const ADD_CAPITAL_TEXT = '新增资金不能低于1万元';
// 投资范围为私募时,新增资金少于300万元提示
export const SIMU_VALUE_TEXT = '当前投资范围含私募基金，新增资金配置规模需设置为300万起';

// 提示语
export const TIP_TEXT = '系统将根据您输入的收益、回撤水平匹配不同波动率水平的配置方案';
// 已过期的提示语以及选择非个人客户的弹窗提示
export const ORG_CUST_TEXT = '资产配置功能目前仅适用于个人客户发起,请重新选择客户';
export const RISK_EXPIRE_TEXT = '客户风险测评已过期,需要更新后再发起流程!';
export const RANGE_EXPECT_TEXT = '请在3%-30%之间输入';
export const NOT_SPECIFIED_PARENT_TEXT = '风格偏好';
export const NOT_SAIDAO_PARENT_TEXT = '赛道偏好';
export const NOT_SPECIFIED = 'ALL';
export const NOT_SPECIFIED_OTHER = 'NotSpecified';
export const TWONUM_EXPECT_TEXT = '请输入数据，支持两位小数';

export const PRIVATEFUND = 'PRIVATEFUND';

export const RISKLEVEL_LOWEST = '保守型（最低类别）';
export const RISKLEVEL_LOWEST_TEXT = '客户当前风险等级为保守型（最低类别），适当性不满足可配置产品范围！';
