/* eslint-disable no-console */
/*
 * @Description: 获取客户KYC
 * @Author: Liufuxiang
 * @Date: 2022-10-19 11:11:52
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  minus, plus, divide, times
} from 'number-precision';
import { connect } from 'dva';
import { dva } from '@/helper';
import { routerRedux } from 'dva/router';
import {
  isEmpty,
  map,
  filter,
  find,
  isNaN,
  size,
  trim,
  includes,
  isNil,
  first,
  noop,
} from 'lodash';
import {
  Form, Input, Popover, Modal, Radio
} from 'antd';
import withRouter from '@/decorators/withRouter';
import { logCommon } from '@/decorators/logable';
import { isNull } from '@/helper/check';
import InfoTitle from '@/components/common/InfoTitle';
import IfWrap from '@/components/common/IFWrap';
import AssetSearch from '@/components/assetConfigAdd/AssetSearch';
import CustMess from '@/components/assetConfigAdd/CustMess';
import InfoCell from '@/components/assetConfigAdd/CustInvestNeed/InfoCell';
import Options from '@/components/assetConfigAdd/CustInvestNeed/Options';
import CustIcon from '@/components/assetConfigAdd/static/checkCust.png';
import CommonModal from '@/components/assetConfigAdd/CommonModal';
import { autobind } from 'core-decorators';
import {
  KEY_ADD_CONFIG,
  NUM_REG,
  NUM_FLOOR_REG,
  NUM_PLACE_HOLDER,
  TIP_TEXT,
  ORG_CUST_TEXT,
  RISK_EXPIRE_TEXT,
  HASNOT_SIX_MONTH_TEXT,
  NO_CHECK_DATE_TEXT,
  RANGE_EXPECT_TEXT,
  NOT_SPECIFIED_PARENT_TEXT,
  NOT_SAIDAO_PARENT_TEXT,
  NOT_SPECIFIED,
  NOT_SPECIFIED_OTHER,
  TWONUM_EXPECT_TEXT,
  ADD_CAPITAL_TEXT,
  PRIVATEFUND,
  SIMU_VALUE_TEXT,
  RISKLEVEL_LOWEST,
  RISKLEVEL_LOWEST_TEXT,
} from './config';
import styles from './index.less';
import hoverIcon from '../images/hover.svg';

const FormItem = Form.Item;
const effect = dva.generateEffect;
const mapStateToProps = (state) => ({
  // 客户KYC
  custKyc: state.assetConfig.custKyc,
  // 客户下拉框数据
  custList: state.assetConfig.custList,
  // 客户偏好数据
  preferenceList: state.assetConfig.preferenceList,
  // 选择的客户信息
  custMess: state.assetConfig.custMess,
  // 分析周期数据
  analysisPeriodData: state.assetConfig.analysisPeriodData,
});
const mapDispatchToProps = {
  replace: routerRedux.replace,
  // 获取资产配置步骤1-客户KYC
  getCustKyc: effect('assetConfig/getCustKyc'),
  // 获取资产配置步骤1-客户KYC查询客户列表数据
  queryAllCustList: effect('assetConfig/queryAllCustList'),
  // 获取资产配置步骤1-客户KYC查询客户偏好数据
  getCustPreference: effect('assetConfig/getCustPreference'),
  // 获取资产配置步骤1-客户KYC查询客户偏好数据-默认字典
  getDefaultCustPreference: effect('assetConfig/getDefaultCustPreference'),
  // 获取资产配置步骤1-客户KYC配置客户的信息
  getCustMess: effect('assetConfig/getCustMess'),
  // 修改客户偏好数据
  setPreferenceList: effect('assetConfig/getCustPreferenceSuccess'),
  // 保存客户kyc信息
  saveCustKyc: effect('assetConfig/saveCustKyc'),
  // 获取周期数据
  getAnalysisPeriod: effect('assetConfig/getAnalysisPeriod'),
  // 周期数据更新
  getAnalysisPeriodSuccess: effect('assetConfig/getAnalysisPeriodSuccess'),
};
const create = Form.create;
@create()
@connect(mapStateToProps, mapDispatchToProps)
@withRouter
export default class Home extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    getCustKyc: PropTypes.func.isRequired,
    form: PropTypes.object.isRequired,
    queryAllCustList: PropTypes.func.isRequired,
    custList: PropTypes.array.isRequired,
    getCustPreference: PropTypes.func.isRequired,
    getDefaultCustPreference: PropTypes.func.isRequired,
    preferenceList: PropTypes.array.isRequired,
    getCustMess: PropTypes.func.isRequired,
    custMess: PropTypes.object.isRequired,
    isReject: PropTypes.bool.isRequired,
    setPreferenceList: PropTypes.func.isRequired,
    onCustKycRef: PropTypes.func.isRequired,
    saveCustKyc: PropTypes.func.isRequired,
    custKyc: PropTypes.object.isRequired,
    getAnalysisPeriod: PropTypes.func.isRequired,
    analysisPeriodData: PropTypes.object.isRequired,
    getAnalysisPeriodSuccess: PropTypes.func.isRequired,
    updateUrl: PropTypes.func,
    doSign: PropTypes.func.isRequired,
  };

  static defaultProps={
    updateUrl: noop,
  }

  static contextTypes = {
    empInfo: PropTypes.object.isRequired,
    push: PropTypes.func.isRequired,
    replace: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      commonVisible: false,
      commonVisibleText: '',
      // 最大回撤值的提示语
      maxReturnPlaceholder: '',
      // 最大回撤值校验错误的提示语
      maxReturnErrText: TWONUM_EXPECT_TEXT,
      minNum: 0,
      maxNum: 0,
      analysisModalVisible: false,
      closeFun: noop,
    };
  }

  componentDidMount() {
    this.props.onCustKycRef(this);
    const {
      location: { query: { id = '' } = {} },
      getCustKyc,
    } = this.props;
    if (!isNull(id)) {
      getCustKyc({ id }).then((res) => {
        //  页面init方法
        const { custId = '' } = res;
        this.handleToSelectCustomer({ custId }, true);
        //  请求成功后需要去给form,新增资金配置赋值
        const setObj = res?.configScenarios?.data ?? {};
        this.changeExpect(Number(setObj?.expectRate));
        this.props.form.setFieldsValue({
          maxReturn: setObj?.maxReturn,
          expectRate: setObj?.expectRate,
          capital: setObj?.capital,
        });
      });
    } else {
      this.handleGetDefaultCustPreference();
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const {
      location: { query: nextQuery },
    } = this.props;
    const {
      location: { query: prevQuery },
    } = prevProps;
    if (nextQuery?.id !== prevQuery?.id && !prevQuery?.id) {
      this.props.getCustKyc({ id: nextQuery?.id }).then((res) => {
        //  页面init方法
        const { custId = '' } = res;
        this.handleToSelectCustomer({ custId }, true);
        //  请求成功后需要去给form,新增资金配置赋值
        const setObj = res?.configScenarios?.data ?? {};
        this.changeExpect(Number(setObj?.expectRate));
        this.props.form.setFieldsValue({
          maxReturn: setObj?.maxReturn,
          expectRate: setObj?.expectRate,
          capital: setObj?.capital,
        });
      });
    }
  }

  // 修改客户偏好字典的方法
  onChangePreferenceList = (data) => {
    const {
      childCode, flag, code, text
    } = data;
    logCommon({
      type: 'Select',
      payload: {
        name: `资产配置-客户KYC-${text}`,
        value: childCode,
      },
    });
    const { setPreferenceList, preferenceList } = this.props;
    const newList = map(preferenceList, (item) => {
      if (item?.code === code) {
        let newOptionList = [];
        // 选择的是风格偏好，且选择的子风格是无特殊偏好需要特殊处理
        if (item?.code === NOT_SPECIFIED_PARENT_TEXT || item?.code === NOT_SAIDAO_PARENT_TEXT) {
          if (childCode === NOT_SPECIFIED || childCode === NOT_SPECIFIED_OTHER) {
            newOptionList = map(item?.options, (item1) => ({
              ...item1,
              defaultCheck: item1?.code === childCode ? flag : false,
            }));
          } else {
            newOptionList = map(item?.options, (item1) => {
              if (item1?.code === NOT_SPECIFIED || item1?.code === NOT_SPECIFIED_OTHER) {
                return {
                  ...item1,
                  defaultCheck: false,
                };
              }
              if (item1?.code === childCode) {
                return {
                  ...item1,
                  defaultCheck: flag,
                };
              }
              return {
                ...item1,
              };
            });
          }
        } else {
          newOptionList = map(item?.options, (item1) => {
            if (item1?.code === childCode) {
              return {
                ...item1,
                defaultCheck: flag,
              };
            }
            return {
              ...item1,
            };
          });
        }

        const checkList = map(
          filter(newOptionList, { defaultCheck: true }),
          (result) => result?.code
        );
        // 这里需要修改一下form的value值
        this.props.form.setFieldsValue({ [code]: checkList });
        return {
          ...item,
          options: newOptionList,
        };
      }

      return {
        ...item,
      };
    });
    setPreferenceList(newList);
  };

  // 偏好字典遍历
  optionRender = (data) => {
    const { arr, code, text } = data;
    return map(arr, (res) => (
      <div className={styles.optionBox} key={res?.index}>
        <Options
          key={res?.index}
          text={res?.displayText}
          disableFlag={!this.checkIsCanclick() || (!res?.enableFlag && !res?.defaultCheck)}
          isReject={this.props.isReject}
          enableFlag={res?.enableFlag}
          defaultCheck={res?.defaultCheck}
          onChange={(flag) => {
            this.onChangePreferenceList({
              childCode: res?.code,
              flag,
              code,
              text,
            });
          }}
        />
        <IfWrap when={res?.tipInfo !== ''}>
          <Popover
            overlayClassName={styles.popover}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
            placement="top"
            onClick={this.handleToClickInfoBox}
            content={(
              <div className={styles.tipInfoBox}>
                <div className={styles.tipInfoTit}>
                  {`${res?.displayText}：`}
                </div>
                <div>{res?.tipInfo}</div>
              </div>
            )}
            trigger="click"
          >
            <img src={hoverIcon} className={styles.hoverIcon} />
          </Popover>
        </IfWrap>
      </div>
    ));
  };

  // 检查是否可以编辑,当没有客户信息的时候所有的输入框都不能编辑,返回为true是可以点击
  checkIsCanclick = () => {
    const { custMess, isReject } = this.props;
    return !isEmpty(custMess) && !isReject;
  };

  // 计算接受最大回撤区间值，(0.0267+0.0094+(0.647+2*0.0554)*shouyi-0.0312+0.00574) /(0.44-2*0.022)
  // 乘100，向下取整，再除100，最后减0.01
  getMaxReturnNum=(rate) => {
    // eslint-disable-next-line max-len
    const num = minus(divide(Math.floor(times(times(divide(plus(divide(times(0.7578, rate), 100), 0.01064), 0.396), 100), 100)), 100), 0.01);
    return num < 0 ? 0 : num;
  }

  // 计算接受最小回撤区间值，(0.0267-0.0094+(0.647-2*0.0554)*shouyi-0.0312-0.00574)/(0.44+2*0.022)
  // 乘100，向上取整，再除100，最后加0.01
  getMinReturnNum=(rate) => {
    // eslint-disable-next-line max-len
    const num = plus(divide(Math.ceil(times(times(divide(minus(divide(times(0.5362, rate), 100), 0.01964), 0.484), 100), 100)), 100), 0.01);
    return num < 0 ? 0 : num;
  }

  formListRender = () => {
    const {
      preferenceList,
      form: { getFieldDecorator },
      custMess,
    } = this.props;
    // 每次渲染先获取一下字典选中数据
    const initListData = map(preferenceList, (item) => {
      const checkList = filter(item?.options, (result) => result?.defaultCheck);
      return {
        code: item?.code,
        value: map(checkList, (item1) => item1?.code),
      };
    });
    return map(preferenceList, (item) => (
      <div className={styles.formBox} key={item?.index}>
        <InfoCell
          label={item?.displayText}
          hasBg
          required={item?.requireFlag}
          hasColon={false}
          className={styles.formBoxCell}
        >
          <FormItem>
            {getFieldDecorator(item?.code, {
              rules: [{ required: item?.requireFlag, message: '请选择' }],
              initialValue: !isEmpty(custMess)
                ? find(initListData, { code: item?.code })?.value
                : '',
            })(
              <div className={styles.optionWrap}>
                {this.optionRender({
                  arr: item?.options,
                  code: item?.code,
                  text: item?.displayText,
                })}
              </div>
            )}
          </FormItem>
        </InfoCell>
      </div>
    ));
  };

  // 预期年化收益率修改
  handleChangeExpect = (e) => {
    this.changeExpect(Number(e.target.value));
  };

  // 修改
  changeExpect = (value) => {
    const minNum = this.getMinReturnNum(value);
    const maxNum = this.getMaxReturnNum(value);
    const text = `请在${minNum}%-${maxNum}%之间输入`;
    this.setState({
      maxReturnPlaceholder: isNaN(value) || isNil(value) ? '' : text,
      maxReturnErrText: text,
      minNum,
      maxNum,
    });
    this.props.form.setFieldsValue({ maxReturn: '' });
  };

  // 校验新增资金
  validateCapital = (rule, value, callback) => {
    const newInput = +trim(value);
    const investRange = this.props.form.getFieldValue('投资范围');
    if (!NUM_REG.test(newInput) || isNaN(newInput)) {
      callback(NUM_PLACE_HOLDER);
    } else if (!isNull(value) && includes(investRange, PRIVATEFUND) && newInput < 300) {
      // 如果选中了私募,资金需要300万起
      callback(SIMU_VALUE_TEXT);
    } else if ((newInput >= 1) || isNull(value)) {
      callback();
    } else {
      callback(ADD_CAPITAL_TEXT);
    }
  };

  // 校验年化收益
  validateExpect = (rule, value, callback) => {
    const newInput = +trim(value);
    if (!NUM_REG.test(newInput) || isNaN(newInput)) {
      callback(NUM_PLACE_HOLDER);
    } else if ((newInput >= 3 && newInput <= 30) || isNull(value)) {
      callback();
    } else {
      callback(RANGE_EXPECT_TEXT);
    }
  };

  // 校验最大回撤
  validateMaxReturn = (rule, value, callback) => {
    const { maxReturnErrText, minNum, maxNum } = this.state;
    const newInput = +trim(value);
    // 如果输入的是非请两位小数
    if (!NUM_FLOOR_REG.test(newInput) || isNaN(newInput)) {
      callback(TWONUM_EXPECT_TEXT);
    } else if ((newInput >= minNum && newInput <= maxNum) || isNull(value)) {
      callback();
    } else {
      callback(maxReturnErrText);
    }
  };

  // 如果选择了机构客户是否给提示？–弹框提示
  noOrgCustModal = () => {
    this.setState({
      commonVisible: true,
      commonVisibleText: ORG_CUST_TEXT,
      closeFun: this.handleClosleFun,
    });
  };

  // 校验风险测评是否过期，如过期
  noRiskExpire = () => {
    this.setState({
      commonVisible: true,
      commonVisibleText: RISK_EXPIRE_TEXT,
      closeFun: this.handleClosleFun,
    });
  };

  // 校验风险等级是否最低
  @autobind
  lowestRiskLevelModal() {
    this.setState({
      commonVisible: true,
      commonVisibleText: RISKLEVEL_LOWEST_TEXT,
      closeFun: this.handleClosleFun,
    });
  }

  @autobind
  handleToPopover() {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-客户KYC-可接受最大回撤提示',
      },
    });
  }

  @autobind
  handleToClickInfoBox() {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-客户KYC-投资范围提示',
      },
    });
  }

  // 点击选择客户后拿到合格投资者类型是不是为空然后重置投资者偏好字典数据
  handleToSelectCustomer = (data, initFlag = false) => {
    const { custId } = data;
    const {
      getCustMess,
      getAnalysisPeriod,
      updateUrl,
      getAnalysisPeriodSuccess,
      isReject,
    } = this.props;
    getCustMess({
      custId,
      isReject,
    }).then((result) => {
      const { ifOrgCust, ifRiskExpire, riskLevel } = result;
      // 如果是编辑进来就不用清空所有form数据
      if (!initFlag) {
        this.props.form.resetFields();
      }
      if (ifOrgCust) {
        this.noOrgCustModal();
        return;
      }
      if (ifRiskExpire && !isReject) {
        this.noRiskExpire();
        return;
      }
      // 新建页面风险等级最低的话，弹出提示，不允许往下走
      if (riskLevel === RISKLEVEL_LOWEST && !isReject) {
        this.lowestRiskLevelModal();
        return;
      }
      // 成功请求一下分析周期接口
      getAnalysisPeriod({ custId }).then((res) => {
        // 默认选中第一个
        updateUrl({
          analysisPeriodRang: first(filter(res?.analysisPeriod, (item) => !item?.checkDisable))?.rang ?? '',
        });
      }).catch((rec) => {
        getAnalysisPeriodSuccess({});
      });
      this.handleGetCustPreference({
        custId,
        passFlag: !isNull(custId?.passInvesterType),
        initFlag,
      });
    });
  };

  // 请求客户偏好字典数据
  handleGetCustPreference = (data) => {
    const { custId, passFlag, initFlag } = data;
    const { getCustPreference, setPreferenceList, custKyc } = this.props;
    getCustPreference({
      custId,
      passFlag,
    }).then((resultData) => {
      if (initFlag) {
        const newList = map(resultData, (item) => {
          // 获取到当前偏好下的选中的数据
          const checkOptions = find(custKyc?.investPreference, { code: item?.code })
              ?.checkedOptions ?? [];
          const newOptions = map(item?.options, (item1) => ({
            ...item1,
            defaultCheck: includes(checkOptions, item1?.code),
          }));
          return {
            ...item,
            options: newOptions,
          };
        });
        setPreferenceList(newList);
      } else {
        setPreferenceList(resultData);
      }
    });
  };

  // 请求客户偏好字典数据-默认
  handleGetDefaultCustPreference = () => {
    const { getDefaultCustPreference } = this.props;
    getDefaultCustPreference();
  };

  // 提交事件
  handleSubmit = async () => {
    try {
      const resultData = await this.props.form.validateFields({ force: true });
      if (resultData) {
        this.saveKycDataFun();
      }
    } catch (error) {
      console.error(error);
    }
  };

  // 保存事件
  saveKycDataFun = async () => {
    try {
      const resultData = await this.props.form.validateFields({ force: true });
      const {
        custMess,
        saveCustKyc,
        preferenceList,
        custKyc,
        location,
      } = this.props;
      const { capital, expectRate, maxReturn } = resultData;
      // 取出投资偏好key的数组
      const investPreference = map(preferenceList, (item) => ({
        code: item?.code,
        checkedOptions: resultData?.[item?.code],
      }));
      this.setState({
        commonVisible: false,
      }, () => {
        saveCustKyc({
          custId: custMess?.custId ?? '',
          custName: custMess?.custName ?? '',
          serveOrgId: custMess?.serveOrgId ?? '',
          kycId: custKyc?.kycId ?? '',
          riskLevel: custMess?.riskLevel ?? '',
          id: location?.query?.id ?? '',
          configScenarios: {
            type: KEY_ADD_CONFIG,
            data: {
              capital: Number(capital),
              expectRate: Number(expectRate),
              maxReturn: Number(maxReturn),
            },
          },
          investPreference,
          custCenterDetail: {
            totalAssets: `${custMess?.totalAssets || 0}`,
            dayAssets: `${custMess?.dayAssets || 0}`,
            historyTopAssets: `${custMess?.historyTopAssets || 0}`,
          }
        }).then((result) => {
          const { moreSixMonth = false } = custMess;
          const { analysisPeriodData, updateUrl } = this.props;
          const { id = '' } = result;
          // 增加id参数给url
          updateUrl({
            id,
          });
          if (!moreSixMonth) {
            this.setState({
              commonVisible: true,
              commonVisibleText: HASNOT_SIX_MONTH_TEXT,
              closeFun: this.jumpToOpinion,
            });
          }
          if (moreSixMonth && size(analysisPeriodData?.analysisPeriod) === 0) {
            this.setState({
              commonVisible: true,
              commonVisibleText: NO_CHECK_DATE_TEXT,
              closeFun: this.jumpToOpinion,
            });
          }
          if (moreSixMonth && size(analysisPeriodData?.analysisPeriod) !== 0) {
            // 弹出分析周期弹窗
            this.setState({
              analysisModalVisible: true,
            });
          }
        }).catch((error) => {
          console.warn(error);
        });
      });
    } catch (error) {
      console.error(error);
    }
  };

  // 关闭有分析周期弹窗
  onCloseAnalysisModal = () => {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-客户KYC-选择分析期弹窗-关闭',
      },
    });
    this.setState({
      analysisModalVisible: false,
    });
  };

  // 分期周期单选框渲染
  renderAnalysisList = () => {
    const { analysisPeriodData: { analysisPeriod = [] } = {} } = this.props;
    return map(analysisPeriod, (item) => (
      <Radio value={item?.rang} key={item?.rang} disabled={item?.checkDisable}>
        {`${item?.desc}(${item?.rang})`}
      </Radio>
    ));
  };

  // 搜索客户
  handleQueryAllCustList = (data) => {
    const { queryAllCustList } = this.props;
    queryAllCustList(data);
  };

  // 点击确定后返回的数据
  handleSureModal = () => {
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-客户KYC-选择分析期弹窗-保存',
      },
    });
    const { updateUrl } = this.props;
    updateUrl({ current: 1 });
  };

  // 点击分析期弹窗选项去修改选中的分析期id
  onAnalysisChange = (e) => {
    const value = e.target.value;
    logCommon({
      type: 'Click',
      payload: {
        name: '资产配置-客户KYC-选择分析期弹窗-选择分析期',
        value,
      },
    });
    const { updateUrl } = this.props;
    updateUrl({ analysisPeriodRang: value });
  };

  // 跳转到市场观点
  jumpToOpinion = () => {
    this.setState(
      {
        commonVisible: false,
      },
      () => {
        const { updateUrl } = this.props;
        updateUrl({ current: 2 });
      }
    );
  };

  // 弹窗关闭事件
  handleClosleFun = () => {
    this.setState({
      commonVisible: false,
    });
  };

  getDefaultValue = () => {
    const {
      custList,
      location: {
        query: {
          custId = '',
        } = {}
      },
    } = this.props;
    const res = filter(custList, (item) => item.custId === custId)[0];
    return {
      key: res?.custId,
      label: res?.custName,
    };
  };

  render() {
    const {
      custList,
      custMess,
      form: { getFieldDecorator },
      preferenceList,
      analysisPeriodData: { disableReason = '' } = {},
      location: {
        query: {
          analysisPeriodRang = '',
          custId = '',
        } = {}
      },
      isReject,
      doSign,
    } = this.props;
    const {
      commonVisible,
      commonVisibleText,
      maxReturnPlaceholder,
      analysisModalVisible,
      closeFun,
    } = this.state;

    return (
      <div className={styles.content}>
        <Modal
          modalKey="checkModal"
          width={450}
          maskClosable={false}
          centered
          size="normal"
          className={styles.checkModal}
          title={<div className={styles.modalTit}>请选择</div>}
          visible={analysisModalVisible}
          onOk={this.handleSureModal}
          onCancel={this.onCloseAnalysisModal}
          onModalClose={this.onCloseAnalysisModal}
        >
          <div className={styles.analysisBox}>
            <div className={styles.analysisTop}>
              <div className={styles.analysisTit}>账户分析期:</div>
              <div className={styles.analysisGrounp}>
                <Radio.Group onChange={this.onAnalysisChange} value={analysisPeriodRang}>
                  {this.renderAnalysisList()}
                </Radio.Group>
                <IfWrap when={!isNull(disableReason)}>
                  <div className={styles.analysisReason}>{disableReason}</div>
                </IfWrap>
              </div>
            </div>
          </div>
        </Modal>
        {/* 通用弹窗 */}
        { commonVisible ? (
          <CommonModal
            visible
            recieveText={commonVisibleText}
            onOk={closeFun}
          />
        ) : ''}
        {/* 头部搜索 */}
        <div className={styles.header}>
          <img src={CustIcon} className={styles.headerIcon} />
          <AssetSearch
            onSelect={this.handleToSelectCustomer}
            onSearch={this.handleQueryAllCustList}
            custList={custList}
            isReject={isReject}
            defaultValue={this.getDefaultValue()}
            custId={custId}
          />
        </div>
        {/* 客户基本信息 */}
        <IfWrap when={!isEmpty(custMess)}>
          <CustMess custMess={custMess} doSign={doSign} isReject={isReject} />
        </IfWrap>
        {/* 客户投资需求 */}
        <Form>
          <div className={styles.formWrapper}>
            <InfoTitle head="客户投资需求" />
            <div className={styles.formTopWrapper}>
              <div className={styles.formCell}>
                <div className={styles.cellBox}>
                  <FormItem
                    label="配置场景"
                  >
                    {getFieldDecorator('type', {
                      rules: [{ required: true, message: '请选择' }],
                      initialValue: KEY_ADD_CONFIG,
                    })(<div className={styles.optionText}>新增资金配置</div>)}
                  </FormItem>
                </div>
              </div>
              <div className={styles.formCell}>
                <div className={styles.cellBox}>
                  <FormItem
                    label="新增配置资金"
                  >
                    {getFieldDecorator('capital', {
                      rules: [
                        { required: true, message: NUM_PLACE_HOLDER },
                        { validator: this.validateCapital },
                      ],
                      initialValue: 1,
                    })(
                      <Input
                        suffix="万元"
                        autoComplete="off"
                        disabled={!this.checkIsCanclick()}
                      />
                    )}
                  </FormItem>
                </div>
                <div className={styles.cellBox}>
                  <FormItem
                    label="预期年化收益率"
                  >
                    {getFieldDecorator('expectRate', {
                      rules: [
                        { required: true, message: NUM_PLACE_HOLDER },
                        { validator: this.validateExpect },
                      ],
                    })(
                      <Input
                        suffix="%"
                        autoComplete="off"
                        onChange={this.handleChangeExpect}
                        placeholder={RANGE_EXPECT_TEXT}
                        disabled={!this.checkIsCanclick()}
                      />
                    )}
                  </FormItem>
                </div>
                <div className={styles.cellBox}>

                  <FormItem
                    label="可接受最大回撤"
                  >
                    {getFieldDecorator('maxReturn', {
                      rules: [
                        { required: true, message: TWONUM_EXPECT_TEXT },
                        { validator: this.validateMaxReturn },
                      ],
                    })(
                      <Input
                        className={styles.returnInput}
                        suffix="%"
                        autoComplete="off"
                        placeholder={maxReturnPlaceholder}
                        disabled={!this.checkIsCanclick()}
                      />
                    )}
                  </FormItem>
                  <Popover
                    overlayClassName={styles.popover}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    placement="topRight"
                    content={TIP_TEXT}
                    trigger="click"
                    onClick={this.handleToPopover}
                  >
                    <img src={hoverIcon} className={styles.hoverIcon} />
                  </Popover>
                </div>
              </div>
            </div>
            <IfWrap when={size(preferenceList) !== 0}>
              <div className={styles.formBottomWrapper}>
                <div className={styles.formBottomTit}>
                  请选择以下客户投资偏好：
                </div>
                {this.formListRender()}
              </div>
            </IfWrap>
          </div>
        </Form>
      </div>
    );
  }
}
