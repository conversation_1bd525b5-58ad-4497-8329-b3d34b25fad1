.checkModal {
  .modalTit {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    line-height: 21px;
  }

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .@{ant-prefix}-modal-footer {
      border: 0;
    }
  }
}

.content {
  background: #fff;
  padding: 0 20px 10px;

  .header {
    width: 100%;
    height: 56px;
    background: #5097e4 url('../images/titBg.png') no-repeat right center;
    background-size: 805px 56px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    box-sizing: border-box;
    border-radius: 2px 2px 0 0;

    .headerIcon {
      width: 120px;
      height: 24px;
      margin-right: 20px;
    }
  }

  .formWrapper {
    margin-top: 29px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .@{ant-prefix}-form-item {
        margin: 0;
      }

      .@{ant-prefix}-input-suffix {
        font-size: 14px;
        color: #333;
      }

      .@{ant-prefix}-popover-inner-content {
        background: #2b3b55;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.3);
        border-radius: 3px;
        font-size: 12px;
        color: #fff;
        padding: 10px;
        width: 348px;
      }

      .@{ant-prefix}-popover-arrow {
        border-right-color: #2b3b55 !important;
        border-bottom-color: #2b3b55 !important;
      }
    }

    .formTopWrapper {
      .formCell {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 14px;
        color: #333;
        font-size: 14px;
        flex: 1;

        .cellBox {
          display: flex;
          width: 100%;

            /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
            .@{ant-prefix}-form-item {
              display: flex;
              width: 100%;
            }

            .@{ant-prefix}-form-item-label {
              width: 153px;
              vertical-align: top;

              label {
                color: #666;
                line-height: 32px;
                font-size: 14px;
                text-align: right;
              }
            }

            .@{ant-prefix}-form-item-control-wrapper {
              flex: 1;

              .@{ant-prefix}-form-item-control {
                width: 100%;
                display: inherit;

                .@{ant-prefix}-form-item-children {
                  width: 100%;
                }
              }
            }
          }

          .optionText {
            font-size: 14px;
            color: #333;
            width: 200px;
          }

          .hoverIcon {
            width: 12px;
            height: 12px;
            margin: 9px 0 0 10px;
            cursor: pointer;
          }

          .returnInput {
              /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
              .ant-input {
                &::-webkit-input-placeholder {
                  color: #f5a623 !important;
                }

                &::-moz-input-placeholder {
                  color: #f5a623 !important;
                }

                &:-moz-input-placeholder {
                  color: #f5a623 !important;
                }

                &:-ms-input-placeholder {
                  color: #f5a623 !important;
                }
              }
            }
          }
        }
      }
    }

    .formBottomWrapper {
      width: 100%;
      background: rgba(246, 251, 255, 0);
      border: 1px solid #e4ecf1;
      border-radius: 2px;
      padding: 20px;
      box-sizing: border-box;

      .formBottomTit {
        font-size: 14px;
        color: #333;
        line-height: 20px;
        margin-bottom: 14px;
        background: url('../images/titBottomBg.png') no-repeat left bottom;
        background-size: 168px 10px;
      }

      .formBox {
        .formBoxCell {
          .optionWrap {
            display: flex;
            flex-wrap: wrap;

            .optionBox {
              display: flex;
              align-items: center;
              padding-right: 40px;
              position: relative;

              .hoverIcon {
                cursor: pointer;
                position: absolute;
                left: 123px;
                top: 14px;
                width: 12px;
                height: 12px;
              }
            }
          }
        }
      }
    }
  }
}

.analysisBox {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .@{ant-prefix}-radio-wrapper {
      margin-bottom: 8px;
    }
  }

  .analysisTop {
    display: flex;
    align-items: flex-start;

    .analysisTit {
      min-width: 84px;
      height: 20px;
      font-size: 14px;
      color: #666;
      line-height: 20px;
    }

    .analysisReason {
      font-size: 12px;
      color: #f0af41;
      line-height: 16px;
      padding-top: 8px;
      word-break: break-all;
    }
  }
}

.tipInfoBox {
  .tipInfoTit {
    margin-bottom: 9px;
    font-weight: bold;
  }
}