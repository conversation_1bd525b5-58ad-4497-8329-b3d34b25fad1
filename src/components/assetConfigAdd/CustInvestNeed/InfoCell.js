/*
 * @Description: 刘付祥
 * @Author: Liufuxiang
 * @Date: 2022-10-21 15:23:02
 */

import React from 'react';
import PropTypes from 'prop-types';
import styles from './InfoCell.less';

export default function InfoCell(props) {
  const {
    label,
    required,
    children,
    style,
    className,
    hasBg,
    hasColon
  } = props;
  return (
    <div
      className={`${styles.infoForm} ${className} ${
        hasBg ? styles.infoFormLabelBg : ''
      }`}
    >
      <div style={style} className={styles.infoFormLabel}>
        {required ? <i>*</i> : null}
        {label}
        {hasColon ? <span className={styles.colon}>:</span> : ''}
      </div>
      <div className={styles.infoFormContent}>{children}</div>
    </div>
  );
}

InfoCell.propTypes = {
  label: PropTypes.string,
  required: PropTypes.bool,
  hasBg: PropTypes.bool,
  hasColon: PropTypes.bool,
  children: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object,
    PropTypes.element,
  ]),
  style: PropTypes.object,
  className: PropTypes.string,
};
InfoCell.defaultProps = {
  label: 'label',
  required: false,
  hasBg: false,
  hasColon: true,
  children: 'form内容区域',
  style: {},
  className: '',
};
