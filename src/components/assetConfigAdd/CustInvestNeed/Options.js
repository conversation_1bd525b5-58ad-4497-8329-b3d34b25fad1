/*
 * @Description: 新建资产配置-客户偏好字典组件
 * @Author: Liu<PERSON>xia<PERSON>
 * @Date: 2022-10-20 13:51:18
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';

import styles from './Options.less';

export default class Options extends PureComponent {
  static propTypes = {
    text: PropTypes.string.isRequired,
    disableFlag: PropTypes.bool.isRequired,
    enableFlag: PropTypes.bool.isRequired,
    defaultCheck: PropTypes.bool.isRequired,
    onChange: PropTypes.func.isRequired,
    isReject: PropTypes.bool.isRequired,
  };

  handleChangeOptions=() => {
    const { onChange, defaultCheck } = this.props;
    onChange(!defaultCheck);
  }

  render() {
    const {
      text,
      disableFlag,
      enableFlag,
      defaultCheck,
      isReject,
    } = this.props;

    const cls = classnames({
      [styles.options]: true,
      [styles.reject]: isReject,
      [styles.optionActive]: defaultCheck,
      [styles.optionDisable]: disableFlag,
    });
    return (
      <div
        className={cls}
        onClick={
          enableFlag && !disableFlag ? this.handleChangeOptions : null
        }
      >
        <div>{text}</div>
      </div>
    );
  }
}
