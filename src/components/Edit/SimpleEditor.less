.editWrapper {
  min-width: 90px;
  display: inline-block;
  height: 30px;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
  vertical-align: top;
  .tooltipTop {
      top: 20px !important;
    }
  .editContent {
    display: inline-block;
    padding-left: 4px;
    height: 28px;
    width: 100%;
    box-sizing: border-box;
    font-size: 15px;
    color: #33393c;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 28px;
  }
  .editIcon {
    position: absolute;
    display: none;
    top: 2px;
    right: 2px;
    width: 28px;
    height: 28px;
    color: #2096ea;
    box-sizing: border-box;
    text-align: center;
    line-height: 28px;
    z-index: 3;
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .anticon {
        font-size: 17px;
        opacity: 1;
      }
    }
  }
  .editButtonGroup {
    display: none;
  }
}
.editable {
  position: relative;
  cursor: pointer;
  &:hover {
    border-color: #49a9ee;
    box-shadow: 0 0 0 2px rgba(16, 142, 233, 0.2);
  }
  .showIcon {
    display: inline-block;
  }
  & .editContent {
    width: 100%;
    height: 30px;
    z-index: 1;
    opacity: 1;
    padding-right: 30px;
  }
  .editButtonGroup {
    display: none;
    position: absolute;
    right: 0;
    top: 32px;
    width: 50px;
    height: 25px;
    background-color: #fff;
    z-index: 5;
    box-shadow:
      0 6px 10px 0 rgba(0, 0, 0, 0.14),
      0 1px 18px 0 rgba(0, 0, 0, 0.12),
      0 3px 5px -1px rgba(0, 0, 0, 0.3);
    &.editButtonGroupShow {
      display: inline-block;
    }
    & > .editBt {
      display: inline-block;
      width: 25px;
      height: 25px;
      background-color: #fff;
      line-height: 25px;
      text-align: center;
      font-size: 17px;
      color: #acacac;
      &:hover {
        background-color: #ededed;
        color: #2096ea;
      }
    }
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    & .ant-form {
      position: absolute;
      width: 100%;
      height: 30px;
      top: -1px;
      left: 0;
      z-index: -1;
      opacity: 0;
      & .ant-form-item {
        margin-bottom: 0;
      }
      & .ant-form-item-control {
        line-height: 30px;
        & .ant-input-lg {
          height: 30px;
        }
      }
    }
  }
}
.editing {
  &:hover {
    border-color: transparent;
    box-shadow: none;
  }
  & .editContent {
    width: 100%;
    height: 30px;
    z-index: -1;
    opacity: 0;
    padding-right: 30px;
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
  & .ant-form {
    z-index: 1;
    opacity: 1;
    background-color: #fff;
  }
}
}

