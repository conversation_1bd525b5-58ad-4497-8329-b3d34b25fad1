/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-06 10:30:32
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-04-06 18:09:06
 * @description 新增编辑时的联合员工列表
 */

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import Table, { ToolTipCell } from '@/components/common/table';
import confirm from '@/components/common/newUI/confirm';
import withRouter from '@/decorators/withRouter';
import _ from 'lodash';
import {
  EMP_COLUMNS,
  SPACE_20,
} from '../config';
import styles from './empList.less';

@withRouter
export default class EmpList extends Component {
  static propTypes = {
    location: PropTypes.object.isRequired,
    dataSource: PropTypes.array.isRequired,
    onDelete: PropTypes.func.isRequired,
  }

  @autobind
  handleDelete(data) {
    const {
      location: {
        query: {
          key
        },
      },
    } = this.props;
    const { empId, empName } = data;
    if (key) {
      confirm({
        title: '提示！',
        content: `删除【${empName}${empId}】, 分配至该联合服务员工的未转化断点客户将重新派发给其他员工，是否确认从列表删除？`,
        onOk: () => {
          this.props.onDelete(data);
        },
      });
    } else {
      this.props.onDelete(data);
    }
  }

  @autobind
  renderColumns() {
    return _.map(EMP_COLUMNS, (item) => {
      const {
        dataIndex,
      } = item;
      if (dataIndex === 'empId') {
        return this.renderEmpId(item);
      }
      if (dataIndex === 'empName') {
        return this.renderEmpName(item);
      }
      if (dataIndex === 'operation') {
        return this.renderOperation(item);
      }
      return this.renderToolTipCell(item);
    });
  }

  @autobind
  renderToolTipCell(item) {
    return {
      ...item,
      render: (text) => (
        <ToolTipCell
          clickable={false}
          tipContent={text}
          cellText={text}
        />
      ),
    };
  }

  @autobind
  renderEmpId(item) {
    return {
      ...item,
      render: (text, record, index) => `${index + 1}`,
    };
  }

  @autobind
  renderEmpName(item) {
    return {
      ...item,
      render: (text, record) => {
        const { empId, empName } = record;
        const finalText = `${empName || '--'}(${empId || '--'})`;
        return (
          <ToolTipCell
            clickable={false}
            tipContent={finalText}
            cellText={finalText}
          />
        );
      },
    };
  }

  @autobind
  renderOperation(item) {
    return {
      ...item,
      render: (text, record) => (
        <span className={styles.operation}>
          <span onClick={() => { this.handleDelete(record); }}>删除</span>
        </span>
      ),
    };
  }

  render() {
    const { dataSource } = this.props;

    return (
      <div className={styles.tableArea}>
        <Table
          useNewUI
          pagination={false}
          columns={this.renderColumns()}
          dataSource={dataSource}
          rowKey="key"
          spaceColumnProps={SPACE_20}
        />
      </div>
    );
  }
}
