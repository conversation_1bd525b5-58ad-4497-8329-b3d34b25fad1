/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-06 10:30:32
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-04-06 18:09:06
 * @description 新增编辑时的渠道列表
 */

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import Table, { ToolTipCell } from '@/components/common/table';
import confirm from '@/components/common/newUI/confirm';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import _ from 'lodash';
import withRouter from '@/decorators/withRouter';
import {
  CHANNEL_COLUMNS,
  SPACE_20,
} from '../config';
import styles from './empList.less';

@withRouter
export default class ChannelList extends Component {
  static propTypes = {
    location: PropTypes.object.isRequired,
    dataSource: PropTypes.array.isRequired,
    onDelete: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      pageSize: 10,
      current: 1,
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.dataSource !== this.props.dataSource) {
      this.updataDataSource(this.props.dataSource);
    }
  }

  // 更新页码器配置
  @autobind
  updataDataSource(data) {
    this.setState({
      current: 1,
    });
  }

  @autobind
  handleDelete(data) {
    const {
      location: {
        query: {
          key
        },
      },
    } = this.props;
    if (key) {
      const { channelName } = data;
      confirm({
        title: '提示！',
        content: `删除【${channelName}】, 该渠道潜客将停止分发，且所有联合服务员工名下未完成转化的断点客户将自动回到原开户绑定工号员工名下。是否确认删除？`,
        onOk: () => {
          this.props.onDelete(data);
        },
      });
    } else {
      this.props.onDelete(data);
    }
  }

  @autobind
  renderColumns() {
    return _.map(CHANNEL_COLUMNS, (item) => {
      const {
        dataIndex,
      } = item;
      if (dataIndex === 'no') {
        return this.renderEmpId(item);
      }
      if (dataIndex === 'operation') {
        return this.renderOperation(item);
      }
      return this.renderToolTipCell(item);
    });
  }

  @autobind
  renderToolTipCell(item) {
    return {
      ...item,
      render: (text) => (
        <ToolTipCell
          clickable={false}
          tipContent={text}
          cellText={text}
        />
      ),
    };
  }

  @autobind
  renderEmpId(item) {
    return {
      ...item,
      render: (text, record, index) => {
        const { current, pageSize } = this.state;
        return pageSize * (current - 1) + index + 1;
      }
    };
  }

  @autobind
  renderOperation(item) {
    return {
      ...item,
      render: (text, record) => (
        <span className={styles.operation}>
          <span onClick={() => { this.handleDelete(record); }}>删除</span>
        </span>
      ),
    };
  }

  @autobind
  handlePageChange(pageNum) {
    this.setState({
      current: pageNum,
    });
  }

  render() {
    const { dataSource } = this.props;
    // 当前页码
    const { pageSize, current } = this.state;
    // 当前第几页的数据
    const chunkArr = _.chunk(dataSource, 10);
    // 页码器的属性
    const pageProps = {
      pageSize,
      current,
      total: dataSource.length,
    };

    return (
      <div className={styles.tableArea}>
        <Table
          useNewUI
          pagination={false}
          columns={this.renderColumns()}
          dataSource={chunkArr[current - 1]}
          rowKey="key"
          spaceColumnProps={SPACE_20}
        />
        <IFWrap when={!_.isEmpty(dataSource)}>
          <div className={styles.pageArea}>
            <Pagination
              {...pageProps}
              onChange={this.handlePageChange}
            />
          </div>
        </IFWrap>
      </div>
    );
  }
}
