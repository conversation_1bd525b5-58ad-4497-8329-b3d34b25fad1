/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-06 10:30:32
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-04-06 13:57:16
 * @description 联合服务员搜索
 */

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { Icon, Input, AutoComplete } from 'antd';
import _ from 'lodash';

import styles from './empSearch.less';

const DROPDOWN_STYLE = {
  width: '220px',
};

const AUTOCOMPLETE_STYLE = {
  width: '100%',
};

const Option = AutoComplete.Option;

export default class EmpSearch extends Component {
  static propTypes = {
    dataSource: PropTypes.array.isRequired,
    onSearch: PropTypes.func.isRequired,
    onSelect: PropTypes.func.isRequired,
    onRef: PropTypes.func.isRequired,
    disabled: PropTypes.bool.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      inputValue: '',
    };
  }

  componentDidMount() {
    this.props.onRef(this);
  }

  @autobind
  handleSearch(keyword) {
    this.setState({
      inputValue: keyword,
    }, () => {
      this.props.onSearch(keyword);
    });
  }

  @autobind
  clearValue() {
    this.setState({
      inputValue: '',
    });
  }

  @autobind
  setValue(key) {
    this.setState({
      inputValue: key,
    });
  }

  @autobind
  handleSelect(value) {
    const { dataSource } = this.props;
    const data = _.find(dataSource, (item) => item.empId === value);
    this.props.onSelect(data);
  }

  @autobind
  getOptions() {
    const { dataSource } = this.props;
    if (_.isEmpty(dataSource)) {
      return [
        <Option key="empty" text="empty" disabled>
          暂无员工数据
        </Option>
      ];
    }
    return _.map(dataSource, (item, index) => {
      const { empId, empName } = item;

      return (
        <Option key={empId} text={`${empName}(${empId})`}>
          {`${empName}(${empId})`}
        </Option>
      );
    });
  }

  render() {
    const options = this.getOptions();
    const { disabled } = this.props;
    const { inputValue } = this.state;
    return (
      <div className={styles.empSearch}>
        <AutoComplete
          disabled={disabled}
          value={inputValue}
          className={styles.autoComplete}
          dropdownMatchSelectWidth={false}
          dropdownStyle={DROPDOWN_STYLE}
          style={AUTOCOMPLETE_STYLE}
          dataSource={options}
          placeholder="工号或姓名"
          optionLabelProp="text"
          onSearch={this.handleSearch}
          onSelect={this.handleSelect}
        >
          <Input suffix={<Icon type="search" />} />
        </AutoComplete>
      </div>
    );
  }
}
