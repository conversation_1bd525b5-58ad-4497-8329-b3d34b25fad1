.channelPotlistWrap {
  min-height: calc(~"100vh - 230px");
  background: #fff;

  .redText {
    color: #f00;
  }

  .filterArea {
    padding: 0 20px 10px;
    border-bottom: 1px solid #e9e9e9;
    display: flex;
    margin-bottom: 14px;
    padding-top: 10px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-input {
        border-radius: 2px;
      }
    }

    .titleSearch {
      margin-top: 2px;
      width: 228px;
    }

    .filterItem {
      margin-left: 10px;
      margin-top: 2px;

      &:first-child {
        margin-left: 0;
      }
    }

    .filterDate {
      margin-left: 10px;
    }
  }

  .tableArea {
    padding: 0 20px;

    .title {
      position: relative;
      cursor: pointer;
    }

    .tag {
      position: absolute;
      width: 17px;
      height: 17px;
      color: #fff;
      cursor: pointer;
      left: -20px;
      top: -6px;
      transform: rotate(-45deg) scale(0.7);

      &:before {
        content: "";
        position: absolute;
        border: 17px solid;
        border-color: #f18200 transparent transparent #f18200;
        transform: rotate(45deg) scale(1.3);
        z-index: -1;
        left: -3px;
        top: 7px;
      }
    }

    .readRate {
      font-size: 12px;
      color: #ccc;
    }

    .operation {
      color: #108ee9;
      cursor: pointer;

      span {
        margin-left: 10px;

        &:first-child {
          margin-left: 0;
        }
      }

      .disableClick {
        color: #ccc;
        cursor: not-allowed;
      }
    }

    .noData {
      color: #ccc;
    }
  }
}

.blueText {
  color: #108ee9;
}

.relative {
  position: relative;
}

.popconfirmContent {
  width: 360px;
}
