/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-12 17:01:40
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-04-06 17:29:52
 * @Description:  联合服务渠道潜客管理列表
 */

import React, { PureComponent } from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import {
  Icon,
  Input,
  AutoComplete,
  message,
} from 'antd';
import logable from '@/decorators/logable';
import { emp } from '@/helper';
import { MultiFilterWithSearch, SingleFilter } from '@lego/filters/src';
import DateRangePick from '@lego/date/src';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import Table, { ToolTipCell } from '@/components/common/table';
import confirm from '@/components/common/newUI/confirm';
import { formatChannelBranch, getQueryOrgIds, getChannelBranchInfo } from '../utils';
import {
  LIST_COLUMNS,
  PLACE_HOLDER_PROPS,
  SPACE_20,
} from '../config';

import styles from './index.less';

export default class index extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    // 列表数据
    potentialtCustList: PropTypes.object.isRequired,
    // 查询列表数据
    queryPotentialtCustList: PropTypes.func.isRequired,
    // 删除联合潜客服务
    deletePotentialCust: PropTypes.func.isRequired,
    // 查询渠道分公司
    queryChannelBranch: PropTypes.func.isRequired,
    getEmpList: PropTypes.func.isRequired,
    getOperatorList: PropTypes.func.isRequired,
    // 联合员工模糊搜索
    empList: PropTypes.array.isRequired,
    // 操作人模糊搜索
    operatorList: PropTypes.array.isRequired,

  };

  static contextTypes = {
    push: PropTypes.func.isRequired,
    replace: PropTypes.func.isRequired,
    dict: PropTypes.object.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      // 渠道分公司
      channelBranch: [],
      // 关键字查询（渠道编号或者公司）
      channelNumOrName: '',
    };
  }

  componentDidMount() {
    this.props.queryChannelBranch().then((result) => {
      this.setState({
        channelBranch: result
      });
    });
    this.updateURL();
  }

  // 标题输入关键字
  @autobind
  onTitleSearch(channelNumOrName) {
    this.setState({
      channelNumOrName: channelNumOrName || '',
    }, () => {
      if (_.isEmpty(channelNumOrName)) {
        this.handleTitleSearch();
      }
    });
  }

  @autobind
  @logable({
    type: 'Search',
    payload: {
      name: '总分零距离管理-搜索公告标题',
    },
  })
  handleTitleSearch() {
    const { channelNumOrName } = this.state;
    this.updateURL({
      pageNum: 1,
      channelNumOrName,
    });
  }

  // 联合员工模糊搜索
  @autobind
  handleEmpSearch(keyword) {
    this.props.getEmpList({
      keyword,
    });
  }

  // 最新操作人模糊搜索
  @autobind
  handleOperateSearch(keyword) {
    this.props.getOperatorList({
      keyword,
    });
  }

  // 选择联合员工
  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '联合服务潜客管理查询-联合服务员',
      value: '$args[0].value.ptyMngName',
    }
  })
  handleEmpSelect(item) {
    const { ptyMngId, ptyMngName } = item?.value || {};
    this.updateURL({
      pageNum: 1,
      serviceEmpId: ptyMngId || '',
      serviceEmpName: ptyMngName || '',
    });
  }

  // 选择最新操作人
  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '联合服务潜客管理查询-最新操作人',
      value: '$args[0].value.ptyMngName',
    }
  })
  handleOperateSelect(item) {
    const { ptyMngId, ptyMngName } = item?.value || {};
    this.updateURL({
      pageNum: 1,
      lastOperatorId: ptyMngId || '',
      lastOperatorName: ptyMngName || '',
    });
  }

  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '联合服务潜客管理查询-渠道所属分公司',
      value: '$args[0].value',
    },
  })
  handleOrgSelect(labelItem) {
    const { value } = labelItem;
    const channelIdList = [];
    const channelNameList = [];

    _.map(value, (item) => {
      channelIdList.push(item.id);
      channelNameList.push(item.name);
    });

    const params = {
      pageNum: 1,
      channelId: channelIdList.join(','),
      channelName: channelNameList.join(','),
    };

    this.updateURL(params);
  }

  // 选择创建时间
  @autobind
  @logable({
    type: 'DropdownSelect',
    payload: {
      name: '联合服务潜客管理查询-最新操作时间',
      min: '$args[0].value.startTime',
      max: '$args[0].value.endTime',
    },
  })
  handleSelectCreateTime(date) {
    const { value: [startTime, endTime] } = date;
    this.updateURL({
      pageNum: 1,
      lastOperateStartTime: startTime,
      lastOperateEndTime: endTime,
    });
  }

  @autobind
  disabledEndTime(startTime, endTime) {
    return endTime < startTime;
  }

  // 替换url中的query并重新查询列表数据
  @autobind
  updateURL(filterQuery = {}) {
    const {
      location: {
        query,
      },
      queryPotentialtCustList,
    } = this.props;
    const newQuery = {
      ...query,
      ...filterQuery,
    };
    this.context.replace({
      query: newQuery,
    });

    queryPotentialtCustList({
      orgId: emp.getOrgId(),
      pageSize: 10,
      pageNum: newQuery.pageNum || 1,
      orgIds: getQueryOrgIds(newQuery.channelId),
      lastOperateStartTime: newQuery.lastOperateStartTime || '',
      lastOperateEndTime: newQuery.lastOperateEndTime || '',
      channelNumOrName: newQuery.channelNumOrName || '',
      serviceEmpId: newQuery.serviceEmpId || '',
      serviceEmpName: newQuery.serviceEmpName && newQuery.serviceEmpName !== '不限' ? newQuery.serviceEmpName : '',
      lastOperatorId: newQuery.lastOperatorId || '',
      lastOperatorName: newQuery.lastOperatorName && newQuery.lastOperatorName !== '不限' ? newQuery.lastOperatorName : '',
    });
  }

  @autobind
  renderColumns() {
    return _.map(LIST_COLUMNS, (item) => {
      const {
        dataIndex,
      } = item;
      if (dataIndex === 'channelList') {
        return this.renderChannelList(item);
      }
      if (dataIndex === 'serviceStaffList') {
        return this.renderStaffList(item);
      }
      if (dataIndex === 'operation') {
        return this.renderOperation(item);
      }
      return this.renderToolTipCell(item);
    });
  }

  @autobind
  renderToolTipCell(item) {
    return {
      ...item,
      render: (text) => (
        <ToolTipCell
          clickable={false}
          tipContent={text}
          cellText={text}
        />
      ),
    };
  }

  @autobind
  renderChannelList(item, key) {
    return {
      ...item,
      render: (text, record) => {
        const { channelList = [] } = record;
        return (
          <ToolTipCell
            clickable={false}
            tipContent={channelList.join('；')}
            cellText={channelList.join('；')}
          />
        );
      },
    };
  }

  @autobind
  renderStaffList(item, key) {
    return {
      ...item,
      render: (text, record) => {
        const { serviceStaffList = [] } = record;
        return (
          <ToolTipCell
            clickable={false}
            tipContent={serviceStaffList.join('；')}
            cellText={serviceStaffList.join('；')}
          />
        );
      },
    };
  }

  @autobind
  renderCreator(item) {
    return {
      ...item,
      render: (text, record) => {
        const { creatorId, creatorName } = record;
        const finalText = `${creatorName || '--'}(${creatorId || '--'})`;
        return (
          <ToolTipCell
            clickable={false}
            tipContent={finalText}
            cellText={finalText}
          />
        );
      },
    };
  }

  @autobind
  renderNoticeType(item) {
    const { noticeTypeList = [] } = this.context.dict;
    return {
      ...item,
      render: (text) => {
        const typeObj = _.find(noticeTypeList, (obj) => obj.key === text) || {};
        const { value } = typeObj;
        return (
          <ToolTipCell
            clickable={false}
            tipContent={value}
            cellText={value}
          />
        );
      },
    };
  }

  @autobind
  renderOperation(item) {
    return {
      ...item,
      render: (text, record) => {
        const { key } = record;

        const buttonList = [];
        buttonList.push(<span onClick={() => { this.handleEdit(key); }}>编辑</span>);
        buttonList.push(<span onClick={() => { this.handleDelete(key); }}>删除</span>);
        return (
          <span className={styles.operation}>
            {_.map(buttonList, (button) => button)}
          </span>
        );
      },
    };
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '联合服务潜客管理-编辑',
      value: '$args[0]',
    },
  })
  handleEdit(key) {
    this.context.push({
      pathname: '/channelPotential/bulletinEdit',
      query: {
        key,
      },
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '联合服务潜客管理-删除',
      value: '$args[0]',
    },
  })
  handleDelete(key) {
    confirm({
      title: '提示',
      content: '该配置记录下的所有渠道潜客将停止分发，且所有联合服务员工名下未完成转化的断点客户将自动回到原开户绑定工号员工名下。是否确认删除？',
      onOk: () => {
        this.doDelete(key);
      },
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '联合服务潜客管理-确认删除',
      value: '$args[0]',
    },
  })
  doDelete(key) {
    const { deletePotentialCust } = this.props;
    deletePotentialCust({
      key,
    }).then((result) => {
      if (result) {
        message.success('删除成功！');
        this.updateURL({ pageNum: 1 }); // 重新查一遍列表
      }
    });
  }

  @autobind
  getPage(page = {}) {
    return {
      pageSize: 10,
      current: (page && page.pageNum) || 1,
      total: (page && page.totalCount) || 0,
    };
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '联合服务潜客管理-列表翻页',
      value: '$args[0]',
    },
  })
  handlePageChange(pageNum) {
    this.updateURL({
      pageNum,
    });
  }

  render() {
    const {
      potentialtCustList: {
        list,
        page,
      },
      empList,
      operatorList,
      location: {
        query: {
          channelId: channelIdList,
          channelName: channelNameList,
          serviceEmpId,
          serviceEmpName,
          lastOperatorId,
          lastOperatorName,
          lastOperateEndTime,
          lastOperateStartTime,
        },
      },
    } = this.props;

    // 渠道分公司数据
    const { channelBranch } = this.state;
    const channelBranchArr = formatChannelBranch(channelBranch);

    // 选中的联合员工信息;
    const unionInfo = {
      ptyMngId: serviceEmpId || '',
      ptyMngName: serviceEmpName || '不限',
    };
    // 选中的操作人信息;
    const operatorInfo = {
      ptyMngId: lastOperatorId || '',
      ptyMngName: lastOperatorName || '不限',
    };
    // 页码器的属性
    const pageProps = this.getPage(page);

    return (
      <div className={styles.channelPotlistWrap}>
        <div className={styles.filterArea}>
          <div>
            <AutoComplete
              placeholder="渠道编号或渠道名称"
              onSearch={this.onTitleSearch}
              allowClear
              className={styles.titleSearch}
            >
              <Input
                onPressEnter={this.handleTitleSearch}
                suffix={(<Icon type="search" className="certain-category-icon" />)}
              />
            </AutoComplete>
          </div>
          <div className={styles.filterItem}>
            <MultiFilterWithSearch
              defaultLabel="不限"
              filterName="渠道分公司"
              filterId="id"
              dataMap={['id', 'name']}
              showSearch
              onChange={this.handleOrgSelect}
              data={channelBranchArr}
              needItemObj
              type="multiSearch"
              value={getChannelBranchInfo(channelIdList, channelNameList)}
              placeholder="请输入分公司名称"
              isAlwaysVisible
              dropdownStyle={{
                maxHeight: 324,
                overflowY: 'auto',
                width: 250,
              }}
            />
          </div>
          <div className={styles.filterItem}>
            <SingleFilter
              menuContainer="body"
              type="singleSearch"
              className={styles.serverManageWrap}
              value={unionInfo}
              defaultLabel="不限"
              filterName="联合服务员工"
              placeholder="工号或姓名"
              data={empList}
              dataMap={['ptyMngId', 'ptyMngName']}
              showSearch
              needItemObj
              useLabelInValue
              onChange={this.handleEmpSelect}
              onInputChange={_.debounce(this.handleEmpSearch, 300)}
            />
          </div>
          <div className={styles.filterItem}>
            <SingleFilter
              menuContainer="body"
              type="singleSearch"
              className={styles.serverManageWrap}
              value={operatorInfo}
              defaultLabel="不限"
              filterName="最新操作人"
              placeholder="工号或姓名"
              data={operatorList}
              dataMap={['ptyMngId', 'ptyMngName']}
              showSearch
              needItemObj
              useLabelInValue
              onChange={this.handleOperateSelect}
              onInputChange={_.debounce(this.handleOperateSearch, 300)}
            />
          </div>
          <div className={styles.filterDate}>
            <DateRangePick
              menuContainer="body"
              type="date"
              filterName="最新操作时间"
              filterId="created"
              filterValue={[lastOperateStartTime, lastOperateEndTime]}
              onChange={this.handleSelectCreateTime}
              disabledEnd={this.disabledEndTime}
              allowClear
            />
          </div>
        </div>
        <div className={styles.tableArea}>
          <Table
            useNewUI
            pagination={false}
            columns={this.renderColumns()}
            dataSource={list}
            rowKey="key"
            spaceColumnProps={SPACE_20}
            placeHolderImageProps={PLACE_HOLDER_PROPS}
          />
          <IFWrap when={!_.isEmpty(list)}>
            <div className={styles.pageArea}>
              <Pagination
                {...pageProps}
                onChange={this.handlePageChange}
              />
            </div>
          </IFWrap>
        </div>
      </div>
    );
  }
}
