import React, { Component } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import {
  Icon,
  Input,
  Menu,
  Checkbox
} from 'antd';
import { autobind } from 'core-decorators';
import { Button } from '@lego/filters/src';
import IFWrap from '@/components/common/IFWrap';
import styles from './index.less';

export default class CustomMultiFilter extends Component {
  static propTypes = {
    onChange: PropTypes.func.isRequired,
    data: PropTypes.array.isRequired,
  }

  constructor(props) {
    super(props);
    const { data } = this.props;
    this.state = {
      pageNum: 1,
      filterData: data,
      checkedData: [],
      inputValue: '',
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.data !== this.props.data) {
      this.updataFilter(this.props.data);
    }
  }

  @autobind
  updataFilter(data) {
    this.setState({
      inputValue: '',
      checkedData: [],
      filterData: data
    });
  }

  // 匹配渠道id或者value
  @autobind
  handleInputChange(e) {
    const value = e.target.value;
    const { data } = this.props;
    const arr = [];
    if (value) {
      _.map(data, (item) => {
        const { channelId, channelName } = item;
        if (channelId.indexOf(value) !== -1 || channelName.indexOf(value) !== -1) {
          arr.push(item);
        }
      });
      this.setState({ filterData: arr, inputValue: value, pageNum: 1 });
    } else {
      this.setState({ filterData: data, inputValue: value });
    }
  }

  // 取消
  handleCancelBtnClick = () => {
    this.setState({
      inputValue: '',
      checkedData: [],
    });
    this.props.onChange({}, {
      inVisible: true,
    });
  }

  // 确定
  handleSubmitBtnClick = () => {
    const { checkedData } = this.state;
    this.setState({
      inputValue: '',
      checkedData: [],
    });
    this.props.onChange({ checkedData }, {
      inVisible: true,
    });
  }

  @autobind
  generateCheckStatus(channelId, channelName) {
    const { checkedData } = this.state;
    if (_.some(checkedData, ['channelId', channelId])) {
      return true;
    }
    return false;
  }

  @autobind
  handleClick(event, item) {
    let empArr = [];
    const { channelId } = item;
    const { checkedData } = this.state;
    if (_.some(checkedData, ['channelId', channelId])) {
      empArr = _.pullAllBy(checkedData, [{ channelId }], 'channelId');
    } else {
      empArr = _.concat([item], checkedData);
    }

    this.setState({
      checkedData: empArr
    });
  }

  @autobind
  showMoreContent() {
    const { pageNum } = this.state;
    this.setState({
      pageNum: pageNum + 1
    });
  }

  @autobind
  renderFilterChoice() {
    let mapArr = [];
    const { filterData, pageNum } = this.state;
    const filterDataChunk = _.chunk(filterData, 20);
    _.forEach(filterDataChunk, (value, key) => {
      if (key < pageNum) {
        mapArr = _.concat(mapArr, value);
      }
    });

    if (!_.isEmpty(mapArr) && pageNum !== filterDataChunk.length) {
      mapArr.push({ channelId: '000000000', channelName: '更多', expanKey: 'expaned' });
    }

    return (
      <IFWrap when={!_.isEmpty(mapArr)}>
        <Menu className={styles.dropDownMenu}>
          {
            mapArr.map((item) => {
              if (!item.expanKey) {
                return (
                  <Menu.Item key={item.channelId} className={styles.filterItem}>
                    <Checkbox
                      checked={this.generateCheckStatus(item.channelId, item.channelName)}
                      className={styles.overflowAction}
                      onChange={(e) => this.handleClick(e, item)}
                    >
                      <span className={styles.channelName} title={`${item.channelName}(${item.channelId})`}>
                        {item.channelName}{item.channelId ? `(${item.channelId})` : ''}
                      </span>
                    </Checkbox>
                  </Menu.Item>
                );
              }
              return (
                <Menu.Item key={item.channelId} className={styles.moreItem}>
                  <span className={styles.moreItemText} onClick={this.showMoreContent}>更多</span>
                </Menu.Item>
              );
            })
          }
        </Menu>
      </IFWrap>
    );
  }

  render() {
    const { inputValue } = this.state;
    return (
      <div className={styles.filterWarp}>
        <Input
          value={inputValue}
          className={styles.filterInput}
          onChange={this.handleInputChange}
          placeholder="请输入渠道名称或编号"
          suffix={(<Icon type="search" className="certain-category-icon" />)}
        />
        {this.renderFilterChoice()}
        <div className={styles.btnGroup}>
          <Button onClick={this.handleCancelBtnClick} type="cancel">取消</Button>
          <Button onClick={this.handleSubmitBtnClick} type="submit">确定</Button>
        </div>
      </div>
    );
  }
}
