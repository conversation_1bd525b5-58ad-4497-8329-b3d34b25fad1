@import "../../../css/variable.less";

.filterWarp {
  display: inline-block;
  min-height: 100px;
  overflow-x: hidden;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {

    .@{ant-prefix}-menu-inline,
    .@{ant-prefix}-menu-vertical,
    .@{ant-prefix}-menu-vertical-left {
      border-right: none;
    }
  }

  .filterInput {
    margin: 10px 0 0 10px;
    width: 320px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .@{ant-prefix}-input-suffix {
        right: 15px;
      }
    }
  }

  .dropDownMenu {
    margin-top: 5px;
    overflow-y: hidden;

    .filterItem {
      width: 320px;
      height: 30px;
      line-height: 30px;
      padding: 0 10px;

        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .@{ant-prefix}-checkbox-wrapper {
          width: 300px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .moreItem {
      height: 20px;
      line-height: 20px;
      width: 320px;
      text-align: center;
      background-color: #fff;

      .moreItemText {
        color: #108ee9;
        cursor: pointer;
      }
    }
  }

  .btnGroup {
    width: 320px;
    padding: 15px 0 15px 10px;
    text-align: center;
    background-color: rgba(255, 255, 255, 1);

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .@{ant-prefix}-btn {
        width: 80px;
        border-radius: 2px;
      }

      .lego-btn-buttonContainer :first-child {
        margin-right: 10px;
      }
    }
  }
}
