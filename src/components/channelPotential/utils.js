/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-01 14:36:42
 * @Last Modified by: z<PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-04-01 14:43:58
 * @Description: 联合服务渠道潜客辅助方法
 */

import _ from 'lodash';

// 遍历去除数组中每个item的children
export function formatChannelBranch(array) {
  if (!_.isEmpty(array)) {
    const newArr = [];
    _.map(array, (item) => {
      newArr.push(_.omit(item, ['children']));
    });
    return newArr;
  }
  return array;
}

// 获取地址栏的渠道分公司ids
export function getQueryOrgIds(channelId) {
  if (channelId) {
    return channelId && channelId.split(',');
  }
  return '';
}

// 列表筛选项选中的渠道分公司信息;
export function getChannelBranchInfo(channelIdList, channelNameList) {
  const channelIdArray = channelIdList && channelIdList.split(',');
  const channelNameArray = channelNameList && channelNameList.split(',');
  const tempArr = [];
  _.map(channelIdArray, (itema, index) => {
    tempArr.push({
      id: itema,
      name: channelNameArray[index],
    });
  });
  return tempArr;
}
// 过滤掉已经选择的数组;
export function filteringStoredData(openChannelList, channelList) {
  return _.differenceBy(openChannelList, channelList, 'channelId');
}

// 对比两个数组
export function getCompareList(oriArray, currentArr, key) {
  const addPart = _.differenceBy(currentArr, oriArray, key);
  const delPart = _.differenceBy(oriArray, currentArr, key);
  const tempArr1 = [];
  const tempArr2 = [];
  _.map(addPart, (itema, index) => {
    tempArr1.push({
      ...itema,
      updateType: 0,
    });
  });
  _.map(delPart, (itema, index) => {
    tempArr2.push({
      ...itema,
      updateType: 1,
    });
  });

  return _.concat(tempArr1, tempArr2);
}
