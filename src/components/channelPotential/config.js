// 联合服务渠道列表表格配置
export const LIST_COLUMNS = [
  {
    title: '渠道所属分公司',
    dataIndex: 'channelCompany',
    width: 160,
  },
  {
    title: '开户渠道',
    dataIndex: 'channelList',
  },
  {
    title: '联合服务员工',
    dataIndex: 'serviceStaffList',
  },
  {
    title: '最新操作人',
    dataIndex: 'lastOperator',
    width: 150,
  },
  {
    title: '最新操作时间',
    dataIndex: 'lastOperatorTime',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 66,
  },
];

// 联合服务渠道员工列表表格配置
export const EMP_COLUMNS = [
  {
    title: '序号',
    dataIndex: 'empId',
    width: 80,
  },
  {
    title: '员工',
    dataIndex: 'empName',
  },
  {
    title: '所属营业部',
    dataIndex: 'orgName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 28,
  },
];

// 联合服务渠道员工列表表格配置
export const CHANNEL_COLUMNS = [
  {
    title: '序号',
    dataIndex: 'no',
    width: 80,
  },
  {
    title: '渠道名称',
    dataIndex: 'channelName',
  },
  {
    title: '渠道编号',
    dataIndex: 'channelId',
  },
  {
    title: '渠道所属营业部',
    dataIndex: 'privilegeGradeTowOrgName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 28,
  },
];

// 列间距20
export const SPACE_20 = {
  width: 20,
};

// 文本样式
export const TEXT_STYLES = {
  marginLeft: '10px',
};
// 文本样式
export const INFOFORM_LABEL_STYLE = { width: '120px', marginLeft: '20px', color: '#999' };

export const INFOFORM_LABEL_LONG_STYLE = { width: '140px', marginLeft: '2px', color: '#999' };

// 无数据展位图属性
export const PLACE_HOLDER_PROPS = {
  title: '暂无联合服务潜客数据',
  style: {
    height: '435px',
  },
};

export const EMPPLACE_HOLDER_PROPS = {
  style: {
    height: '0',
  },
};
