@import "../../css/util.less";
.modalContent {
  padding: 20px 0;
  /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
    table {
      table-layout: fixed;
      width: 100%;
    }
    .ant-table-tbody > tr > td {
      .one-line-ellipsis;
      & > div {
        position: relative;
        .one-line-ellipsis;
      }
    }
    .ant-table-thead > tr > th, .ant-table-tbody > tr > td {
      padding: 9px 12px;
    }
    .ant-table-thead > tr > th {
      background: #fff;
      border-bottom: 2px solid #e8e8e8;
    }
    .ant-table-tbody > tr {
      background: #fff;
      &:nth-child(odd) {
        background: #f9f9f9;
      }
    }
  }
  .contentItem {
    margin-top: 12px;
    &:first-child {
      margin-top: 0;
    }
    .inlineInfoForm {
      display: inline-block;
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
      :global {
        .ant-radio-group {
          width: 160px !important;
        }
      }
    }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-select-selection__choice {
        background: #fef7f0;
        border: 1px solid #fcd2a6;
        font-size: 12px;
        color: #d67c1e;
        .ant-select-selection__choice__content {
          & > div > span {
            color: #d67c1e;
          }
        }
        .ant-select-selection__choice__remove {
          color: #d67c1e;
        }
      }
    }
    .infoFormSelect {
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .ant-select {
          height: auto;
        }
      }
    }
    .title {
      font-size: 16px;
      color: #4a4a4a;
      text-indent: 14px;
      position: relative;
      &:before {
        position: absolute;
        display: block;
        content: "";
        width: 5px;
        height: 16px;
        background: #4897f1;
        top: 5px;
        left: 0;
      }
    }
    .operateDiv {
      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .ant-input {
          height: 28px;
        }
      }
      & > div:first-child {
        margin-right: 20px;
      }
      .linkSpan {
        float: right;
        vertical-align: bottom;
        button {
          background: #108ee9;
        }
        & > a, & > span {
          display: inline-block;
          box-sizing: border-box;
          width: 110px;
          height: 28px;
          line-height: 26px;
          background: #fff;
          color: #333;
          border: 1px solid #ddd;
          text-align: center;
          border-radius: 4px;
          margin-left: 20px;
          a {
            color: #333;
          }
        }
        & > span:first-child {
          width: auto;
          border: 0;
        }
      }
    }
    .tableDiv {
        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .ant-table-body {
          overflow: hidden !important;
        }
      }
      margin-top: 14px;
      .operateColumn {
        a {
          margin-right: 10px;
        }
        i:first-child {
          margin-right: 10px;
          vertical-align: text-top;
        }
      }
    }
  }
}
.mr10 {
  margin-right: 10px;
}
.keyWord {
  color: #ff784e;
}
