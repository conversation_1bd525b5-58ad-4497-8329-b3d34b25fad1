/*
 * @Author: yuduo.zhang
 * @Date: 2021-09-16 14:32:44
 * @LastEditTime: 2022-06-28 15:15:43
 * @LastEditors: yuduo.zhang
 * @Description: 参与客户自动流转申请-新建弹窗
 */

import React, { PureComponent } from 'react';
import { autobind } from 'core-decorators';
import {
  Form,
  Alert,
  DatePicker,
  InputNumber,
  Checkbox,
} from 'antd';
import PropTypes from 'prop-types';
import moment from 'moment';
import isNull from 'lodash/isNull';
import includes from 'lodash/includes';

import { notNumber } from '@/helper/regexp';
import { sensors } from '@lego/bigbox-utils';
import confirm from '@/components/common/newUI/confirm';
import Modal from '@/components/common/newUI/modal';
import IFWrap from '@/components/common/IFWrap';

import styles from './index.less';

const { logable, logCommon } = sensors;

const FormItem = Form.Item;
const create = Form.create;

@create()
export default class CreateModal extends PureComponent {
  static propTypes = {
    form: PropTypes.object.isRequired,
    // 弹窗的key
    modalKey: PropTypes.string.isRequired,
    // 在有效状态的申请类型
    applyType: PropTypes.array.isRequired,
    // 可以填写的客户数上限
    serviceTop: PropTypes.number.isRequired,
    // 点击提交
    onSubmit: PropTypes.func.isRequired,
    // 关闭弹窗
    onClose: PropTypes.func.isRequired,
  }

  @logable({ type: 'Click', payload: { name: '客户分配-参与客户自动流转申请-新建-勾选公司自动流转' } })
  handleBranchCheckboxChange() { }

  @logable({ type: 'Click', payload: { name: '客户分配-参与客户自动流转申请-新建-选择公司自动流转参与截止时间' } })
  handleBranchDatePickerChange() { }

  @logable({ type: 'Click', payload: { name: '客户分配-参与客户自动流转申请-新建-勾选涨乐客户选择' } })
  handleZhangleCheckboxChange() { }

  @logable({ type: 'Click', payload: { name: '客户分配-参与客户自动流转申请-新建-选择涨乐客户选择参与截止时间' } })
  handleZhangleDatePickerChange() { }

  // 根据后台要求格式化日期
  @autobind
  formatDate(date) {
    return moment(date).format('YYYY-MM-DD 23:59:00');
  }

  @autobind
  handleAllChoose(params, serviceTop) {
    // 判断公司自动流转及涨乐客户选择承接客户数是否超过最大可承接客户数
    if ((params.branchCustCount + params.zhangleCustCount) <= serviceTop) {
      const transitionalBranchDate = this.formatDate(params.branchDate);
      const transitionalZhangleDate = this.formatDate(params.zhangleDate);
      const finalParams = {
        branchCirculation: {
          custCount: params.branchCustCount,
          date: transitionalBranchDate,
        },
        zhangleCirculation: {
          custCount: params.zhangleCustCount,
          date: transitionalZhangleDate,
        }
      };
      return finalParams;
    }
    return false;
  }

  @autobind
  handleBranchChoose(params, serviceTop) {
    // 判断公司自动流转承接客户数是否超过最大可承接客户数
    if (params.branchCustCount <= serviceTop) {
      const transitionalBranchDate = this.formatDate(params.branchDate);
      const finalParams = {
        branchCirculation: {
          custCount: params.branchCustCount,
          date: transitionalBranchDate,
        },
        zhangleCirculation: null
      };
      return finalParams;
    }
    return false;
  }

  @autobind
  handleZhangleChoose(params, serviceTop) {
    // 判断涨乐客户选择承接客户数是否超过最大可承接客户数
    if (params.zhangleCustCount <= serviceTop) {
      const transitionalZhangleDate = this.formatDate(params.zhangleDate);
      const finalParams = {
        branchCirculation: null,
        zhangleCirculation: {
          custCount: params.zhangleCustCount,
          date: transitionalZhangleDate,
        }
      };
      return finalParams;
    }
    return false;
  }

  @autobind
  doSubmit(params, serviceTop) {
    // 两种都勾选的情况
    if (params.branchCheckbox && params.zhangleCheckbox) {
      return this.handleAllChoose(params, serviceTop);
    }
    // 勾选公司自动流转的情况
    if (params.branchCheckbox) {
      return this.handleBranchChoose(params, serviceTop);
    }
    // 勾选涨乐客户选择的情况
    return this.handleZhangleChoose(params, serviceTop);
  }

  @autobind
  @logable({ type: 'Click', payload: { name: '客户分配-参与客户自动流转申请-新建-提交' } })
  handleSubmit() {
    const { form, serviceTop } = this.props;
    if (isNull(serviceTop)) {
      confirm({
        content: '服务半径上限未设置，请联系杜嘉怡016829设置。',
        okText: '确定',
        cancelVisible: false,
      });
      return;
    }
    const { getFieldsValue, validateFields } = form;
    const params = getFieldsValue();
    if (!(params.branchCheckbox || params.zhangleCheckbox)) {
      confirm({
        content: '请勾选一种承接来源。',
        okText: '确定',
        cancelVisible: false,
      });
      return;
    }
    validateFields((err) => {
      if (!err) {
        const data = this.doSubmit(params, serviceTop);
        if (data) {
          this.props.onSubmit(data);
        } else {
          confirm({
            content: `基于服务半径上限计算，公司自动流转及涨乐客户选择可填写的承接客户数合计上限${serviceTop < 0 ? '0' : serviceTop}人，请重新填写。`,
            okText: '确定',
            cancelVisible: false,
            onOk: () => {
              logCommon({
                type: 'Click',
                payload: {
                  name: '客户分配-参与客户自动流转申请-新建-提交提示框确认',
                },
              });
            },
          });
        }
      }
    });
  }

  @autobind
  handleCancel() {
    this.handleCloseModal();
    logCommon({
      type: 'Click',
      payload: {
        name: '客户分配-参与客户自动流转申请-新建-取消',
      },
    });
  }

  @autobind
  @logable({ type: 'Click', payload: { name: '客户分配-参与客户自动流转申请-新建-关闭页面' } })
  handleCloseModal() {
    const { resetFields } = this.props.form;
    resetFields();
    this.props.onClose();
  }

  // 渲染底部按钮
  @autobind
  renderModalButtons() {
    return [
      {
        key: 'ok',
        text: '提交',
        type: 'primary',
        onClick: this.handleSubmit,
      },
      {
        key: 'cancel',
        text: '取消',
        type: 'default',
        onClick: this.handleCancel,
      },
    ];
  }

  // InputNumber不能输入非数字
  replaceInputNumber(value) {
    return value.replace(notNumber, '');
  }

  render() {
    const {
      form,
      modalKey,
      applyType,
      serviceTop,
    } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    // 是否勾选了公司自动流转
    const branchCheckbox = getFieldValue('branchCheckbox') || false;
    // 是否勾选了涨乐客户选择
    const zhangleCheckbox = getFieldValue('zhangleCheckbox') || false;
    // 提示框显示内容
    const message = isNull(serviceTop)
      ? '服务半径上限未设置，请联系杜嘉怡016829设置。'
      : `基于服务半径上限计算，公司自动流转及涨乐客户选择可填写的承接客户数合计上限≤${serviceTop < 0 ? '0' : serviceTop}人。`;
    return (
      <Modal
        title="参与客户自动流转申请"
        size="middle"
        wrapClassName={styles.createModal}
        modalKey={modalKey}
        modalFooter={this.renderModalButtons()}
        onModalClose={this.handleCloseModal}
        visible
      >
        <div className={styles.modalContent}>
          <Alert message={message} type="info" showIcon />
          <Form layout="inline">
            <IFWrap when={!includes(applyType, 'CUSTIMER_AUTO_TURNING')}>
              <div className={styles.checkbox}>
                {getFieldDecorator('branchCheckbox')(
                  <Checkbox onChange={this.handleBranchCheckboxChange}>
                    公司自动流转
                  </Checkbox>
                )}
              </div>
              <div className={styles.itemBox}>
                <div className={styles.item}>
                  <FormItem label="承接客户数（人）">
                    {getFieldDecorator('branchCustCount', {
                      rules: [{
                        required: branchCheckbox,
                        message: '请输入承接客户数（人）',
                      }],
                    })(
                      <InputNumber
                        min={1}
                        formatter={this.replaceInputNumber}
                        parser={this.replaceInputNumber}
                        onChange={this.handleBranchInputNumberChange}
                      />
                    )}
                  </FormItem>
                </div>
                <div className={styles.item}>
                  <FormItem label="参与截止时间">
                    {getFieldDecorator('branchDate', {
                      rules: [{
                        required: branchCheckbox,
                        message: '请选择参与截止时间',
                      }],
                      initialValue: moment().add(1, 'months').add(1, 'days'),
                    })(
                      <DatePicker
                        format="YYYY-MM-DD"
                        disabledDate={(current) => current < moment().add(1, 'months')}
                        showTime={false}
                        allowClear={false}
                        onChange={this.handleBranchDatePickerChange}
                      />
                    )}
                  </FormItem>
                </div>
              </div>
            </IFWrap>
            <IFWrap when={!includes(applyType, 'CUSTOMER_DRIVING')}>
              <div className={styles.checkbox}>
                {getFieldDecorator('zhangleCheckbox')(
                  <Checkbox onChange={this.handleZhangleCheckboxChange}>
                    涨乐客户选择
                  </Checkbox>
                )}
              </div>
              <div className={styles.itemBox}>
                <div className={styles.item}>
                  <FormItem label="承接客户数（人）">
                    {getFieldDecorator('zhangleCustCount', {
                      rules: [{
                        required: zhangleCheckbox,
                        message: '请输入承接客户数（人）',
                      }],
                    })(
                      <InputNumber
                        min={1}
                        formatter={this.replaceInputNumber}
                        parser={this.replaceInputNumber}
                      />
                    )}
                  </FormItem>
                </div>
                <div className={styles.item}>
                  <FormItem label="参与截止时间">
                    {getFieldDecorator('zhangleDate', {
                      rules: [{
                        required: zhangleCheckbox,
                        message: '请选择参与截止时间',
                      }],
                      initialValue: moment().add(1, 'months').add(1, 'days'),
                    })(
                      <DatePicker
                        format="YYYY-MM-DD"
                        disabledDate={(current) => current < moment().add(1, 'months')}
                        showTime={false}
                        allowClear={false}
                        onChange={this.handleZhangleDatePickerChange}
                      />
                    )}
                  </FormItem>
                </div>
              </div>
            </IFWrap>
          </Form>
        </div>
      </Modal>
    );
  }
}
