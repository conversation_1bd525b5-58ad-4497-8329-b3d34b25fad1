.wrap {
  padding: 20px;
  background-color: #fff;
  height: 100%;
  overflow-y: auto;
  position: relative;

    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-bizDetailBlock {
      position: relative;
    }
  }

  .title {
    font-size: 14px;
    color: #666;
    font-weight: bold;
    line-height: 19px;

    &::after {
      content: "";
      display: block;
      margin-top: 14px;
      border-bottom: 1px solid #ddd;
    }
  }

  .headerRow {
    display: flex;
    justify-content: space-between;

    .operateDiv {
      margin-top: 6px;
    }
  }

  .textWarp {
    height: 90px !important;
  }

  .flowBtns {
    background: #fff;
    display: flex;
    justify-content: flex-end;
    padding: 0 30px 30px;
    box-sizing: border-box;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .@{ant-prefix}-v2-compatible .@{ant-prefix}-btn {
        padding: 0;
        margin-left: 10px;
      }
    }
  }
}

.confirmContent {
  h3 {
    font-size: 14px;
    color: #666;
  }

  h4 {
    margin-top: 16px;
    font-size: 12px;
    color: #999;
  }
}
