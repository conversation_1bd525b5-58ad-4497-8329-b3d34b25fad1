/**
 * @description 分公司集中分配-统一集中分配-驳回修改页面
 * <AUTHOR>
 * @Last Modified by:
 * @Last Modified time:
 */

import React, { PureComponent } from 'react';
import { autobind } from 'core-decorators';
import PropTypes from 'prop-types';
import _ from 'lodash';
import {
  Input,
  Button,
  message,
} from 'antd';
import {
  DetailBlock,
  InfoCell,
  InfoGroup,
} from '@crm/biz-ui';
import { emp } from '@aorta-pc/utils';

import BigFileUploadWithProgress from '@/newUI/BigFileUploadWithProgress';
import logable, { logCommon } from '@/decorators/logable';
import FlowInfo from '@/components/custDistribute/FlowInfo';
import FlowHistoryBlock from '@/components/custDistribute/FlowHistoryBlock';
import BranchCentralCommonTable from '@/components/branchCentralAllot/BranchCentralCommonTable';
import confirm from '@/components/common/newUI/confirm';
import IfWrap from '@/components/common/IFWrap';
import TableDialog from '@/components/common/biz/TableDialog';
import Uploader from '@/newUI/storageUpload';
import InfoTitle from '@/components/common/InfoTitle';
import {
  PAGE_SIZE_FIFTEEN,
  APPROVAL_COLUMNS,
  HIGH_LIGHT_TEXT_LIST,
} from '@/components/branchCentralAllot/config';
import {
  SUBTYPES_FOCUS_CUST_ASSIGN,
  FOCUS_CUST_ASSIGN_BPNNAME,
  FOCUS_CUST_ASSIGN_TITLE,
  BIZ_TYPE,
  MAX_COUNT,
} from '@/config/custDistribute';
import { viewPdf, fieldMapping } from '../branchCentralAllot/utils';

import styles from './branchAllotChange.less';

const { TextArea } = Input;

export default class CentralAllotChange extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    // 获取按钮数据和下一步审批人
    queryFlowButton: PropTypes.func.isRequired,
    flowButtonList: PropTypes.array.isRequired,
    // 新的权重校验
    checkWeight: PropTypes.func.isRequired,
    // 驳回修改重新提交
    doApprove: PropTypes.func.isRequired,
    queryApplicationDetail: PropTypes.func.isRequired,
    // 轮询获取解析进度
    queryProgress: PropTypes.func.isRequired,
    // 获取解析进度详细信息
    queryProgressDetail: PropTypes.func.isRequired,
    // 获取解析数据
    querybatchDataResult: PropTypes.func.isRequired,
    // 获取客户列表（用于驳回修改等）
    queryCustListPaged: PropTypes.func.isRequired,
    // 驳回保存
    updateApplicationData: PropTypes.func.isRequired,
    // 查询文件导入数量阈值
    queryBatchFileReadConfig: PropTypes.func.isRequired,
    // 校验导入数据是否过期
    checkImportDataExpired: PropTypes.func.isRequired,
  };

  static contextTypes = {
    push: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);

    this.state = {
      // 批次 ID
      batchId: null,
      // 附件信息
      attachList: [],
      // 点击的审批按钮
      buttonItem: {},
      // 审批意见
      approvalAdvices: '',
      // 审批按钮禁用
      btnDisabled: false,
      // 选择审批人弹窗开关
      showApproverModal: false,
      // 详情信息
      detailInfo: {},
      // 分配信息主键id
      id: null,
      // 专门用于展示的客户表格数据
      tableCustDataSource: [],
      tableCustPage: {
        current: 1,
        total: 0,
        pageSize: 15,
      },
      // 文件导入数量最大阈值
      maxCount: 0,
      // 是否点击提交
      submitFlag: false,
    };

    this.uploadData = {
      // 分公司集中分配的业务类型：固定值
      bizType: '001',
      extendValidateParam: {
        empId: emp.getId(),
        orgId: emp.getOrgId(),
        subType: SUBTYPES_FOCUS_CUST_ASSIGN,
      }
    };
  }

  componentDidMount() {
    this.getBatchFileReadConfig();
    this.getDetailInfo();
  }

  @autobind
  getBatchFileReadConfig() {
    this.props.queryBatchFileReadConfig({
      type: BIZ_TYPE
    }).then((res) => {
      this.setState({
        maxCount: res || MAX_COUNT
      });
    });
  }

  @autobind
  getQueryParams() {
    const {
      location: {
        query: {
          taskId,
          flowId,
        },
      },
    } = this.props;
    return { taskId, flowId };
  }

  @autobind
  getDetailInfo() {
    const { flowId } = this.getQueryParams();
    this.props.queryApplicationDetail({
      flowId,
    }).then((result) => {
      this.setState({
        attachList: result.attachList || '',
        detailInfo: result,
        id: result.id,
      }, () => {
        this.getFlowButton();
        this.getCustomerListInDetail();
      });
    });
  }

  @autobind
  getFlowButton() {
    const { flowId, taskId } = this.getQueryParams();
    const { id } = this.state;
    // 获取下一步骤按钮列表
    this.props.queryFlowButton({
      flowId,
      taskId,
      bpmnName: FOCUS_CUST_ASSIGN_BPNNAME,
      orgId: emp.getOrgId(),
      id,
    });
  }

  // NOTE: SWB 2024-10-10 因为驳回修改可以重新上传，重新上传的需要调用另一个接口上传校验结果接口
  // 而驳回修改页面初始化以及在不重新上传的情况下，则需要调用专门的客户列表接口
  @autobind
  getCustomerListInDetail(params = {}) {
    const { pageNum = 1 } = params;

    const { id } = this.state;
    this.props.queryCustListPaged({
      appId: id,
      pageNum,
      pageSize: 15,
    }).then((resultData) => {
      this.setState({
        tableCustDataSource: resultData?.custList || [],
        tableCustPage: {
          current: resultData?.pageNum || 1,
          total: resultData?.totalCount || 0,
          pageSize: 15,
        }
      });
    });
  }

  // 客户分页事件
  @autobind
  @logable({
    type: 'Click',
    payload: { name: '客户列表分页' }
  })
  handleCustPageChange(pageNum) {
    const { batchId } = this.state;

    if (_.isEmpty(batchId)) {
      // 如果batchId没有值，代表投顾没有重新导入，则切换客户列表需要使用查询详情客户列表数据
      this.getCustomerListInDetail({ pageNum });
    } else {
      // 如果batchId有值，代表投顾已经重新导入，则切换页码需要调用上传校验结果后的数据
      this.getTableDataSource({
        pageNum,
        batchId,
      });
    }
  }

  // 上传成功后的数据
  @autobind
  handleUploadSuccess(apiResult) {
    // 新的存储网关附件上传接口，上传后，直接将附件信息返回
    const { resultData } = apiResult;
    const { attachList } = this.state;
    this.setState({
      attachList: [...attachList, resultData],
    });
    return Promise.resolve(resultData);
  }

  // 删除已经上传成功的附件
  @autobind
  handleFileRemove({ attachId }) {
    const { attachList } = this.state;
    // 因为附件在本组件里面只保存上传成功的，所以不需要判断上传失败的附件的删除处理
    // 删除上传失败的附件由 Uploader 组件内部处理
    const notDealAttaches = _.filter(attachList, (item) => {
      if (!_.isEmpty(attachId)) {
        return item.attachId !== attachId;
      }
      return true;
    });
    this.setState({
      attachList: notDealAttaches,
    });
    // 默认表示删除成功
    return Promise.resolve(true);
  }

  @autobind
  handleViewPdf(fileInfo) {
    logCommon({
      type: 'Click',
      payload: {
        name: '分公司集中分配-PDF预览',
        value: JSON.stringify(fileInfo)
      }
    });

    viewPdf(fileInfo);
  }

  // 填写审批意见
  @autobind
  handleChangeApprovalAdvices(e) {
    this.setState({
      approvalAdvices: e.target.value,
    });
  }

  @autobind
  saveSuccessCallBack(res) {
    this.setSubmitFlag(false);
    if (res?.resultData === 'success') {
      confirm({
        title: '提示！',
        content: '数据提交中，请在列表页面查看结果。提交成功则进入审批环节，失败则显示提交失败，失败需要重新发起',
        okText: '确定',
        onOk: () => {
          this.context.push({
            pathname: '/customerPool/customerPartition/custDistribute'
          });
        },
        cancelVisible: false,
      });
    }
    this.setBtnDisable();
  }

  @autobind
  setBtnDisable() {
    this.setState({
      btnDisabled: true,
    });
  }

  // 渲染审批按钮
  @autobind
  renderButtonGroup() {
    const { flowButtonList } = this.props;
    const { btnDisabled, submitFlag } = this.state;
    return _.map(flowButtonList, (item, index) => {
      const type = _.includes(HIGH_LIGHT_TEXT_LIST, item.actionName) ? 'primary' : 'default';
      const name = item?.actionName === '提交' && submitFlag ? '提交中...' : item?.actionName;
      return (
        <Button
          disabled={btnDisabled || (submitFlag && item?.actionName === '提交')}
          key={index}
          type={type}
          onClick={
            () => this.handleButtonClick(item)
          }
        >
          {name}
        </Button>
      );
    });
  }

  // 点击页面的按钮事件处理
  @autobind
  @logable({ type: 'Click', payload: { name: '$args[0].btnName' } })
  handleButtonClick(btn) {
    const { actionName } = btn;
    if (actionName === '提交') {
      // 提交按钮
      this.handleSubmitClick(btn);
    }
    if (actionName === '终止') {
      // 终止按钮
      this.handleReject(btn);
    }
  }

  @autobind
  @logable({
    type: 'Click',
    payload: { name: '统一集中分配-驳回-拒绝' }
  })
  handleReject(btn) {
    const { taskId, flowId } = this.getQueryParams();
    const { approvalAdvices, id } = this.state;
    const commParam = {
      taskId,
      flowId,
      approvalAdvices,
      actionName: btn.actionName,
      bpmnName: FOCUS_CUST_ASSIGN_BPNNAME,
      submitter: emp.getId(),
      businessId: id,
    };
    this.props.doApprove(commParam).then(this.setBtnDisable);
  }

  // 点击提交按纽
  @autobind
  async handleSubmitClick(btn) {
    const {
      tableCustPage,
      attachList,
      batchId,
    } = this.state;

    if (tableCustPage.total <= 0) {
      confirm({
        title: '提示',
        content: '请添加客户',
        okText: '确定',
        onOk: () => false,
        cancelVisible: false,
      });
      return;
    }

    if (_.isEmpty(attachList)) {
      confirm({
        title: '提示',
        content: '请上传附件',
        okText: '确定',
        onOk: () => false,
        cancelVisible: false,
      });
      return;
    }

    if (batchId) {
      const expired = await this.props.checkImportDataExpired({ batchId });
      if (expired) {
        confirm({
          title: '提示',
          content: '超时提交，客户列表已清空，请重新导入客户',
          okText: '确定',
          onOk: () => false,
          cancelVisible: false,
        });
        return;
      }
    }

    this.setState({
      buttonItem: btn,
    }, this.validateWeightCount);
  }

  // 校验权重客户信息
  @autobind
  validateWeightCount() {
    const { batchId, submitFlag } = this.state;

    if (_.isEmpty(batchId)) {
      this.setState({
        showApproverModal: true,
      });
      return;
    }

    // 发起权重校验
    this.props.checkWeight({
      batchId,
    }).then((resultData) => {
      const { number, failExcelPath } = resultData;
      if (number > 0) {
        confirm({
          title: '提示',
          content: (
            <>
              <div>待分配名单中有{number}位客户名下有投顾服务分成，请确认是否提交？确认后，现有投顾服务分成将终止</div>
              附件：<a href={failExcelPath}>投顾服务分成名单.xlsx</a>
            </>
          ),
          onOk: () => {
            this.setState({
              showApproverModal: true,
            });
          },
          okButtonProps: {
            disabled: submitFlag
          }
        });
        return;
      }
      this.setState({
        showApproverModal: true,
      });
    });
  }

  @autobind
  setSubmitFlag(flag = false) {
    this.setState({
      submitFlag: flag,
    });
  }

  // 选完审批人后的提交
  @autobind
  async handleApprove(auth) {
    if (this.state.submitFlag) {
      return;
    }

    await this.setSubmitFlag(true);

    const { taskId, flowId } = this.getQueryParams();
    const {
      batchId,
      attachList,
      approvalAdvices,
      buttonItem,
      id,
    } = this.state;

    const payload = {
      orgId: emp.getOrgId(),
      title: FOCUS_CUST_ASSIGN_TITLE,
      subType: SUBTYPES_FOCUS_CUST_ASSIGN,
      batchId,
      id,
      flowId,
      taskId,
      attachment: { attachList },
    };

    this.props.updateApplicationData(payload).then((saveChangeData) => {
      this.props.doApprove({
        assigneeList: [auth.empId],
        businessId: saveChangeData.id,
        taskId,
        flowId,
        approvalAdvices,
        actionName: buttonItem.actionName,
        bpmnName: FOCUS_CUST_ASSIGN_BPNNAME,
        submitter: emp.getId(),
      }).then(this.saveSuccessCallBack);
      this.setState({
        showApproverModal: false,
      });
    });
    logCommon({
      type: 'Submit',
      payload: {
        title: '选择审批人后总部客户分配提交',
        value: JSON.stringify({ ...payload }),
        name: '选择审批人后总部客户分配提交',
      },
    });
  }

  @autobind
  handleDownloadTemplateFile() {
    logCommon({
      type: 'Click',
      payload: {
        name: '分公司集中分配-新建-统一集中分配-导入模板下载',
      }
    });
  }

  @autobind
  handleUploadFile() {
    logCommon({
      type: 'Click',
      payload: {
        name: '分公司集中分配-新建-统一集中分配-导入',
      }
    });
  }

  @autobind
  handleStartProgress() {
    this.setState({
      progressLock: true
    });
  }

  @autobind
  beforeUpload(file) {
    const { size } = file;
    if (size > (20 * 1024 * 1024)) {
      message.error('文件大小不能超过 20 Mb');
      return false;
    }
    return true;
  }

  @autobind
  handleUploadFinish(data) {
    this.getTableDataSource({
      pageNum: 1,
      batchId: data.batchId,
    });
  }

  @autobind
  handleMaxCount() {
    logCommon({
      type: 'Click',
      payload: {
        name: '分公司集中分配-新建-统一集中分配-导入数据超过50000-确定',
      }
    });
  }

  @autobind
  getTableDataSource({ pageNum, batchId }) {
    const { batchId: currentBatchId, progressLock } = this.state;
    const nextBatchId = batchId || currentBatchId;

    this.props.querybatchDataResult({
      batchId: nextBatchId,
      pageNum,
      pageSize: PAGE_SIZE_FIFTEEN,
      // 筛选校验成功数据
      checkResult: '1',
    }).then((res) => {
      if (res.code === '0') {
        const { totalCount, currentPage, data } = res?.resultData || {};
        let custList = [];
        try {
          custList = data.map((item) => JSON.parse(item.dataInfo));
        } catch (e) {
          //
        }
        this.setState({
          batchId: nextBatchId,
          tableCustDataSource: fieldMapping(custList),
          tableCustPage: {
            current: currentPage,
            total: totalCount,
            pageSize: 15,
          },
          // 变更时取消锁
          progressLock: nextBatchId !== currentBatchId ? false : progressLock,
        });
      }
    });
  }

  render() {
    const {
      batchId,
      approvalAdvices,
      attachList,
      showApproverModal,
      buttonItem,
      detailInfo,
      id,
      tableCustDataSource,
      tableCustPage,
      maxCount,
    } = this.state;

    const flowAuditors = buttonItem.flowAuditors || [];

    // 审批人弹窗
    const approvalProps = {
      visible: showApproverModal,
      onOk: this.handleApprove,
      onCancel: () => { this.setState({ showApproverModal: false }); },
      dataSource: flowAuditors,
      columns: APPROVAL_COLUMNS,
      title: '选择下一审批人员',
      placeholder: '员工号/员工姓名',
      modalKey: 'showApproverModal',
      rowKey: 'empId',
      searchShow: false,
      okDisabled: _.isEmpty(flowAuditors),
      // 一个审批人时需要默认选中
      needDefaultSelect: _.size(flowAuditors) === 1,
    };

    const extendValidateParam = JSON.stringify({
      ...this.uploadData?.extendValidateParam,
      appId: id
    });

    return (
      <div className={styles.wrap}>
        <div className={styles.title}>编号{id}</div>
        <FlowInfo data={detailInfo} />
        <div className={styles.headerRow}>
          <div className={styles.leftTitle}>
            <InfoTitle head="客户列表" warpStyle={{ marginTop: '15px' }} />
          </div>
          <div className={styles.operateDiv}>
            <BigFileUploadWithProgress
              hasData={!(_.isEmpty(batchId) && _.isEmpty(tableCustDataSource))}
              templateFile="/fspa/aorta/dmz/api/template/download/branchaltogether"
              uploadData={{ ...this.uploadData, extendValidateParam }}
              onDownloadTemplate={this.handleDownloadTemplateFile}
              maxCount={maxCount}
              onMaxCount={this.handleMaxCount}
              beforeUpload={this.beforeUpload}
              onStartProgress={this.handleStartProgress}
              queryProgress={this.props.queryProgress}
              queryProgressDetail={this.props.queryProgressDetail}
              onFinish={this.handleUploadFinish}
            />
          </div>
        </div>
        <BranchCentralCommonTable
          needOperation={false}
          list={tableCustDataSource}
          pagination={tableCustPage}
          onChange={this.handleCustPageChange}
        />
        <DetailBlock title="附件">
          <Uploader
            key="specialAllotChangeUploader"
            defaultFileList={attachList}
            onSuccess={this.handleUploadSuccess}
            onRemove={this.handleFileRemove}
            onViewFile={this.handleViewPdf}
            previewable
            isRequired
          />
        </DetailBlock>
        <DetailBlock title="审批意见">
          <InfoGroup labelWidth="110px">
            <InfoCell
              label="审批意见"
              content={(
                <TextArea
                  placeholder="审批意见"
                  value={approvalAdvices}
                  onChange={this.handleChangeApprovalAdvices}
                  className={styles.textWarp}
                />
              )}
            />
          </InfoGroup>
        </DetailBlock>
        <FlowHistoryBlock flowHistory={detailInfo?.flowHistory || {}} />
        <div className={styles.flowBtns}>
          {this.renderButtonGroup()}
        </div>
        <IfWrap when={showApproverModal}>
          <TableDialog {...approvalProps} />
        </IfWrap>
      </div>
    );
  }
}
