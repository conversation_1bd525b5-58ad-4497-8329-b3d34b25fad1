/*
 * @Author: tian<PERSON><PERSON><PERSON>
 * @Date: 2024-08-09 14:54:50
 * @LastEditors: tiannen<PERSON>u
 * @LastEditTime: 2024-09-24 14:10:13
 * @FilePath: /performance_report/src/components/CancelLabelApply/RightDetailMulti.js
 * @Description:取消客户选投顾标识申请--多客户-右侧详情
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { logCommon } from '@/decorators/logable';
import Upload from '@/newUI/storageUpload';
import Table from '@/components/common/table';

import InfoTitle from '@/components/common/InfoTitle';
import FlowHistory from '@/components/common/flowHistory';

import {
  MULTI_CUST_LIST_COLUMNS,
} from '@/components/CancelLabelApply/config';
import BaseInfo from './BaseInfo';

import styles from './rightDetail.less';

export default class RightDetail extends PureComponent {
  static propTypes = {
    data: PropTypes.object.isRequired,
    custInfo: PropTypes.object.isRequired,
    onPageChange: PropTypes.func.isRequired,
  }

  @autobind
  handleViewPdf(fileInfo) {
    const { attachId } = fileInfo;
    const host = window.location.origin;
    const pdrviewerUrl = `${host}/fspa/fsp-host/static/public/pdf/web/viewer.html`;
    const params = `${host}/fspa/mcrm/api/storage/download?fileId=${attachId}`;
    const url = `${pdrviewerUrl}?&file=${encodeURIComponent(params)}`;
    window.open(url, '_blank');
    logCommon({
      type: 'Click',
      payload: {
        name: '取消客户选投顾标识申请详情--查看附件',
        value: JSON.stringify(fileInfo)
      }
    });
  }

  render() {
    const {
      data,
      data: {
        id,
        attachment,
        flowHistory,
      },
      custInfo,
      onPageChange
    } = this.props;

    const {
      custList, pageNum, pageSize, totalCount
    } = custInfo;

    return (
      <div className={styles.detailBox} key={id}>
        <div className={styles.title}>
          <span className={styles.text}>编号{id}</span>
        </div>
        <div className={styles.contentBox}>
          <BaseInfo data={data} />
          <InfoTitle head="客户列表" />
          <Table
            columns={MULTI_CUST_LIST_COLUMNS}
            rowKey="custId"
            dataSource={custList || []}
            useNewUI
            pagination={{
              current: pageNum || 0,
              pageSize: pageSize || 0,
              total: totalCount || 0,
              onChange: onPageChange
            }}
          />
          <InfoTitle head="附件" />
          <Upload
            key={id}
            disabled
            previewable
            removeable={false}
            onViewFile={this.handleViewPdf}
            defaultFileList={attachment?.attachList}
          />
          <InfoTitle head="审批轨迹" />
          <FlowHistory
            data={flowHistory}
          />
        </div>
      </div>
    );
  }
}
