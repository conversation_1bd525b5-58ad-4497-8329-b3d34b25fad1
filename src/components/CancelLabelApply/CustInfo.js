/*
 * @Description: 取消客户选投顾标识申请--右侧详情--客户信息
 * @Author: yuduo.zhang
 * @Date: 2022-02-22 17:41:54
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-02-24 11:03:31
 */

import React from 'react';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import {
  InfoGroup,
  InfoCell,
  DetailBlock,
} from '@crm/biz-ui';

import { LABEL_112 } from './config';

export default function CustInfo(props) {
  const {
    data: {
      customerId,
      customerName,
      contractTime,
      customerOrgName,
    },
  } = props;

  // 无数据
  const noData = <span style={{ color: '#999' }}>--</span>;

  // 处理文字
  const renderNormalCell = (value) => (
    isEmpty(value) ? noData : value
  );

  return (
    <DetailBlock title="客户信息">
      <InfoGroup>
        <InfoCell
          span={35}
          labelWidth={LABEL_112}
          label="经纪客户号"
          ellipsis
          content={renderNormalCell(customerId)}
        />
        <InfoCell
          span={35}
          labelWidth={LABEL_112}
          label="客户名称"
          ellipsis
          content={renderNormalCell(customerName)}
        />
        <InfoCell
          span={30}
          labelWidth={LABEL_112}
          label="最新划转时间"
          ellipsis
          content={renderNormalCell(contractTime)}
        />
      </InfoGroup>
      <InfoGroup>
        <InfoCell
          span={35}
          labelWidth={LABEL_112}
          label="服务营业部"
          ellipsis
          content={renderNormalCell(customerOrgName)}
        />
      </InfoGroup>
    </DetailBlock>
  );
}

CustInfo.propTypes = {
  data: PropTypes.object,
};

CustInfo.defaultProps = {
  data: {},
};
