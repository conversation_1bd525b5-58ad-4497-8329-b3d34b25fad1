/*
 * @Description: 取消客户选投顾标识申请--右侧详情--流程信息
 * @Author: yuduo.zhang
 * @Date: 2022-02-22 17:41:54
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-02-24 11:05:25
 */

import React from 'react';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import {
  InfoGroup,
  InfoCell,
  DetailBlock,
} from '@crm/biz-ui';

import { LABEL_112 } from './config';

export default function FlowInfo(props) {
  const {
    data: {
      empName,
      empId,
      createTime,
      empOrgName,
    },
  } = props;

  // 无数据
  const noData = <span style={{ color: '#999' }}>--</span>;

  // 处理文字
  const renderNormalCell = (value) => (
    isEmpty(value) ? noData : value
  );

  // 创建者信息
  const renderCreatorInfo = () => {
    const name = renderNormalCell(empName);
    const groupId = renderNormalCell(empId);
    return <span>{name}({groupId})</span>;
  };

  return (
    <DetailBlock title="流程信息">
      <InfoGroup>
        <InfoCell
          span={35}
          labelWidth={LABEL_112}
          label="创建者"
          content={renderCreatorInfo()}
        />
        <InfoCell
          span={35}
          labelWidth={LABEL_112}
          label="创建时间"
          content={renderNormalCell(createTime)}
        />
        <InfoCell
          span={30}
          labelWidth={LABEL_112}
          label="部门"
          content={renderNormalCell(empOrgName)}
        />
      </InfoGroup>
    </DetailBlock>
  );
}

FlowInfo.propTypes = {
  data: PropTypes.object,
};

FlowInfo.defaultProps = {
  data: {},
};
