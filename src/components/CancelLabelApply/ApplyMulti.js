/*
 * @Description: 取消客户选投顾标识申请
 * @Author: yuduo.zhang
 * @Date: 2022-02-22 11:32:18
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-03-02 16:47:18
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { connect } from 'dva';
import { emp } from '@aorta-pc/utils';
import head from 'lodash/head';
import parseInt from 'lodash/parseInt';
import isEmpty from 'lodash/isEmpty';
import includes from 'lodash/includes';

import connectHost from '@/decorators/connectHost';
import withRouter from '@/decorators/withRouter';
import { logCommon } from '@/decorators/logable';
import confirm from '@/components/common/newUI/confirm';
import { SplitPanel, ApplyList } from '@crm/biz-ui';
import FilterMulti from '@/components/CancelLabelApply/FilterMulti';
import RightDetailMulti from '@/components/CancelLabelApply/RightDetailMulti';
import ListItem from '@/components/CancelLabelApply/ListItem';
import {
  dva,
} from '@/helper';
import {
  TYPE,
  NO_PERMISSION_ORGIDS,
} from '@/components/CancelLabelApply/config';

const effect = dva.generateEffect;

const PAGE_SIZE = 10;

const mapStateToProps = (state) => ({
  empOrgInfo: state.cancelLabelApplyMulti.empOrgInfo,
  listData: state.cancelLabelApplyMulti.listData,
  custInfo: state.cancelLabelApplyMulti.custInfo
});

const mapDispatchToProps = {
  queryOrgInfo: effect('cancelLabelApplyMulti/queryOrgInfo'),
  queryList: effect('cancelLabelApplyMulti/queryList'),
  queryApproveDetail: effect('cancelLabelApplyMulti/queryApproveDetail'),
  queryApproveCustList: effect('cancelLabelApplyMulti/queryApproveCustList'),
};

@connect(mapStateToProps, mapDispatchToProps)
@withRouter
@connectHost
export default class CancelLabelApply extends PureComponent {
  static propTypes = {
    hostData: PropTypes.object.isRequired,
    // 部门机构树数据
    empOrgInfo: PropTypes.object.isRequired,
    // 查询部门机构树数据
    queryOrgInfo: PropTypes.func.isRequired,
    // 左侧审批客户列表数据
    listData: PropTypes.object.isRequired,
    // 查询左侧审批客户列表数据
    queryList: PropTypes.func.isRequired,
    // 查询右侧客户审批数据
    queryApproveDetail: PropTypes.func.isRequired,
    // 查询客户列表
    queryApproveCustList: PropTypes.func.isRequired,
    // 客户列表
    custInfo: PropTypes.object.isRequired
  };

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      // 筛选条件-经济客户号ID
      custNumber: '',
      // 筛选条件-部门ID
      orgId: emp.getOrgId(),
      // 筛选条件-状态key
      status: '',
      // 当前选中项的ID
      currentId: '',
      // 选中项的下标索引
      activeRowIndex: 0,
      // 右侧审批详情数据
      approveDetail: {},
    };
  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
    push: PropTypes.func.isRequired,
  }

  componentDidMount() {
    const { orgId } = this.state;
    if (includes(NO_PERMISSION_ORGIDS, orgId)) {
      confirm({
        content: '当前岗位无该菜单权限',
        onOk: this.handleGoHome,
        cancelVisible: false,
      });
    }
    // 查询左侧客户审批列表
    this.getLeftList();
    // 查询部门机构树
    this.props.queryOrgInfo({ orgCode: emp.getOrgId() });
  }

  // 跳转到主页
  @autobind
  handleGoHome() {
    const { hostData } = this.props;
    // 根据菜单返回获取首页地址
    const homepage = hostData?.global?.menus?.primaryMenu[0]?.path || '/customerPool';
    this.context.push({
      pathname: homepage,
    });
  }

  // 头部筛选回调
  @autobind
  handleUpdateFilter(filterValue = {}) {
    const { custNumber, orgId, status } = this.state;
    this.setState({
      custNumber,
      orgId,
      status,
      ...filterValue,
      currentId: '',
    }, this.getLeftList);
  }

  // 获取左侧列表
  @autobind
  getLeftList(pageNum = 1) {
    const { custNumber, orgId, status } = this.state;
    const params = {
      custNumber,
      orgId,
      status,
      type: TYPE,
      pageSize: 15,
      pageNum,
    };
    this.props.queryList(params).then(this.getRightDetail);
  }

  // 获取列表后再获取某个Detail
  @autobind
  getRightDetail() {
    const { listData: { list } } = this.props;
    // 判断左侧列表是否获取完毕
    if (!isEmpty(list)) {
      // 第一条记录高亮
      const currentItem = head(list);
      this.setState({
        currentId: currentItem.appId,
        activeRowIndex: 0,
      });
      // 查询右侧详情信息
      this.getDetailInfo(currentItem?.flowId);
    }
  }

  // 调用查询右侧详情数据的接口
  @autobind
  getDetailInfo(flowId) {
    this.props.queryApproveDetail({ flowId }).then((result) => {
      this.setState({
        approveDetail: result,
      }, () => {
        this.props.queryApproveCustList({
          appId: result.id,
          pageNum: 1,
          pageSize: PAGE_SIZE
        });
      });
    });
  }

  // 点击列表每条的时候对应请求详情
  @autobind
  handleListRowClick(record, index) {
    const { appId, flowId } = record;
    const { currentId } = this.state;
    // 如果已经展示了当前的列表详情，不需要刷新，否则刷新详情
    if (currentId === String(appId)) {
      return;
    }
    this.setState({
      currentId: appId,
      activeRowIndex: index,
    }, () => this.getDetailInfo(flowId));
    logCommon({
      type: 'Click',
      payload: {
        name: '取消客户选投顾标识申请-点击左侧列表项',
        value: appId,
      },
    });
  }

  // 渲染左侧审批客户列表项里面的每一项
  @autobind
  renderListRow(record, index) {
    const { activeRowIndex } = this.state;
    return (
      <ListItem
        key={record.appId}
        data={{ ...record, orgName: ' ' }}
        active={index === activeRowIndex}
        onClick={() => this.handleListRowClick(record, index)}
        index={index}
      />
    );
  }

  @autobind
  handlePageChange(pageNum) {
    this.getLeftList(pageNum);
    logCommon({
      type: 'Click',
      payload: {
        name: '取消客户选投顾标识申请-页码选择',
        value: pageNum,
      },
    });
  }

  @autobind
  onPageChange(nextPage) {
    const { approveDetail } = this.state;
    this.props.queryApproveCustList({
      appId: approveDetail?.id,
      pageNum: nextPage,
      pageSize: PAGE_SIZE
    });
  }

  render() {
    const {
      orgId,
      status,
      approveDetail,
    } = this.state;

    const {
      listData: {
        list = [],
        page = {},
      },
      empOrgInfo,
      queryApproveCustList,
      custInfo
    } = this.props;

    const {
      pageSize = 15,
      pageNum = 1,
      totalCount = 0,
    } = page;

    const paginationOptions = {
      current: parseInt(pageNum, 10),
      total: totalCount,
      pageSize,
      onChange: this.handlePageChange,
    };

    // 头部筛选
    const topPanel = (
      <FilterMulti
        empOrgInfo={[empOrgInfo]}
        updateFilter={this.handleUpdateFilter}
        orgId={orgId}
        status={status}
        showSearch={false}
      />
    );

    // 左侧列表
    const leftPanel = (
      <ApplyList
        list={list}
        renderItem={this.renderListRow}
        pagination={paginationOptions}
      />
    );

    // 右侧详情
    const rightPanel = (
      <RightDetailMulti
        data={approveDetail}
        queryApproveCustList={queryApproveCustList}
        custInfo={custInfo}
        onPageChange={this.onPageChange}
      />
    );

    return (
      <SplitPanel
        isEmpty={isEmpty(list)}
        topPanel={topPanel}
        leftPanel={leftPanel}
        rightPanel={rightPanel}
      />
    );
  }
}
