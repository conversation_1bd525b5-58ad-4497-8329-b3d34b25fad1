/*
 * @Description 取消客户选投顾标识申请的一些方法
 * @Author: yuduo.zhang
 * @Date: 2022-03-02 15:22:01
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-03-02 15:24:39
 */

import filter from 'lodash/filter';
import map from 'lodash/map';

import { data as dataHelper } from '@/helper';

import { STATUS_LIST } from './config';

// 根据状态code过滤最终的状态标签
export function convertStatus(statusCode) {
  return filter(STATUS_LIST, (statusItem) => statusItem.code === statusCode);
}

// 针对选中状态下的状态标签type做处理，如果是选中状态则type为ghost
export function transTagType(statusTags, active) {
  const transTags = map(statusTags, (tag) => {
    if (active) {
      return {
        key: dataHelper.uuid(),
        ...tag,
        type: 'ghost',
      };
    }
    return {
      ...tag,
      key: dataHelper.uuid(),
    };
  });
  return transTags;
}
