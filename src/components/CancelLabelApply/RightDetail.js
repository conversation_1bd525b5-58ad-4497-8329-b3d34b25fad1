/*
 * @Description: 取消客户选投顾标识申请--右侧详情
 * @Author: yuduo.zhang
 * @Date: 2022-02-22 17:40:54
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-03-01 18:40:16
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import isEmpty from 'lodash/isEmpty';
import { ApprovalHistory } from '@crm/biz-ui';

import { logCommon } from '@/decorators/logable';
import Upload from '@/newUI/storageUpload';

import InfoTitle from '@/components/common/InfoTitle';
import CustInfo from './CustInfo';
import FlowInfo from './FlowInfo';

import styles from './rightDetail.less';

export default class RightDetail extends PureComponent {
  static propTypes = {
    data: PropTypes.object.isRequired,
  }

  @autobind
  handleViewPdf(fileInfo) {
    const { attachId } = fileInfo;
    const host = window.location.origin;
    const pdrviewerUrl = `${host}/fspa/fsp-host/static/public/pdf/web/viewer.html`;
    const params = `${host}/fspa/mcrm/api/storage/download?fileId=${attachId}`;
    const url = `${pdrviewerUrl}?&file=${encodeURIComponent(params)}`;
    window.open(url, '_blank');
    logCommon({
      type: 'Click',
      payload: {
        name: '取消客户选投顾标识申请详情-PDF预览',
        value: JSON.stringify(fileInfo)
      }
    });
  }

  render() {
    const {
      data,
      data: {
        id,
        attachment,
        flowHistory,
      },
    } = this.props;

    return (
      <div className={styles.detailBox}>
        <div className={styles.title}>
          <span className={styles.text}>编号{id}</span>
        </div>
        <div className={styles.contentBox}>
          <CustInfo data={data} />
          <FlowInfo data={data} />
          <InfoTitle head="附件" />
          <Upload
            key={id}
            disabled
            previewable
            removeable={false}
            onViewFile={this.handleViewPdf}
            defaultFileList={attachment?.attachList}
          />
          <InfoTitle head="审批轨迹" />
          <ApprovalHistory
            history={flowHistory}
            visibleCurrentNode={isEmpty(flowHistory?.currentStepName)}
          />
        </div>
      </div>
    );
  }
}
