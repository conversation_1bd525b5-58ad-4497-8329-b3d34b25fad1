.headerWraper {
  display: flex;
  justify-content: left;
  align-items: center;
  flex-wrap: nowrap;
  height: 48px;
  background-color: #fff;
  margin-top: 2px;

  .filterItem {
    height: 30px;
    margin-right: 20px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .@{ant-prefix}-calendar-picker-input {
        border: 1px solid #fff;
      }

      .lego-filter-filterWrapper > button:first-child {
        .lego-filter-contentShowOnButton {
          height: 32px;
          line-height: 32px;
        }
      }

      .lego-selection--single .lego-selection__rendered {
        font-size: 14px;
        line-height: 32px;
      }
    }
  }
}
