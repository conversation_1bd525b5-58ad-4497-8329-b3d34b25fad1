.detailBox {
  background: #fff;
  padding: 14px 20px 14px 8px;
  height: 100%;
  overflow-y: scroll;

  .title {
    padding-bottom: 20px;
    border-bottom: 1px solid #ddd;
    font-weight: bold;

    .text {
      font-size: 14px;
      color: #333;
      line-height: 20px;
      padding-left: 12px;
    }
  }

  .contentBox {
    padding-left: 22px;

      /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .@{ant-prefix}-uploader {
        min-height: auto;
        padding: 0 0 20px 14px;

        .@{ant-prefix}-uploader-attachList {
          .@{ant-prefix}-uploader-file {
            margin: 0 20px 0 0;
          }

          .@{ant-prefix}-uploader-button {
            margin-bottom: 0;
            color: #108ee9;
          }
        }
      }
    }
  }
}
