/*
 * @Description: 取消客户选投顾标识申请-顶部查询条件
 * @Author: yuduo.zhang
 * @Date: 2022-02-22 14:11:44
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-03-02 15:44:44
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import debounce from 'lodash/debounce';
import isEmpty from 'lodash/isEmpty';
import map from 'lodash/map';
import trim from 'lodash/trim';
import noop from 'lodash/noop';
import { autobind } from 'core-decorators';
import { SingleFilter } from '@lego/filters/src';
import TreeFilter from '@lego/treeSelect/src';

import { logCommon } from '@/decorators/logable';
import AutoComplete from '@/components/common/similarAutoComplete';
import IFWrap from '@/components/common/IFWrap';

import {
  STATUS_LIST,
  DROPDOWN_STYLE,
} from './config';

import styles from './filter.less';

export default class Filter extends PureComponent {
  static propTypes = {
    // 全国客户列表
    allCustList: PropTypes.array,
    // 查询全国客户列表数据
    queryAllCustList: PropTypes.func,
    // 部门机构树下拉数据
    empOrgInfo: PropTypes.array.isRequired,
    // 改变路由参数并查询数据
    updateFilter: PropTypes.func.isRequired,
    // 部门ID
    orgId: PropTypes.string.isRequired,
    // 状态key
    status: PropTypes.string.isRequired,
    showSearch: PropTypes.bool
  }

  static defaultProps={
    showSearch: true,
    allCustList: [],
    queryAllCustList: noop
  }

  constructor(props) {
    super(props);
    this.debounceSearch = debounce(this.searchAllCustList, 250);
  }

  @autobind
  searchAllCustList(value) {
    const keyword = trim(value);
    if (isEmpty(keyword)) {
      return;
    }
    this.props.queryAllCustList({
      keyword,
      pageSize: 10,
      pageNum: 1,
    });
  }

  // 选择经纪客户号
  @autobind
  handleAllCustSelect({ custNumber = '' }) {
    this.props.updateFilter({ custNumber });
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '取消客户选投顾标识申请--选择经纪客户号',
        value: custNumber,
      },
    });
  }

  // 选择状态
  @autobind
  handleStatusSelect(current) {
    const status = current?.value?.code;
    this.props.updateFilter({ status });
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '取消客户选投顾标识申请-选择状态',
        value: `${status}`,
      },
    });
  }

  // 选择部门
  @autobind
  handleDepartmentSelect(orgId) {
    this.props.updateFilter({ orgId });
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '取消客户选投顾标识申请-选择部门',
        value: orgId,
      },
    });
  }

  // 处理通过接口获取的部门下拉数据
  @autobind
  transformOrgInfoData(list) {
    return map(list, (item) => {
      const obj = {
        label: item.name,
        value: item.id,
        key: item.rowId,
      };
      if (!isEmpty(item.children)) {
        obj.children = this.transformOrgInfoData(item.children);
      }
      return obj;
    });
  }

  // 处理部门下拉数据
  @autobind
  getOrgTreeList() {
    const { empOrgInfo } = this.props;
    const list = this.transformOrgInfoData(empOrgInfo);
    const newList = [
      {
        label: '不限',
        value: '',
        key: 0
      },
      ...list,
    ];
    return newList;
  }

  render() {
    const {
      orgId,
      status,
      allCustList,
      showSearch
    } = this.props;

    return (
      <div className={styles.headerWraper}>
        <IFWrap when={showSearch}>
          <div className={styles.filterItem}>
            <AutoComplete
              placeholder="请输入经纪客户号"
              showNameKey="custName"
              showIdKey="custNumber"
              optionKey="custNumber"
              optionList={allCustList}
              onSelect={this.handleAllCustSelect}
              onSearch={this.debounceSearch}
              dropdownMatchSelectWidth={false}
              isImmediatelySearch
              dropdownStyle={DROPDOWN_STYLE}
            />
          </div>
        </IFWrap>
        <div className={styles.filterItem}>
          <TreeFilter
            filterName="部门"
            filterId="id"
            key="id"
            showSearch
            treeDefaultExpandAll
            treeData={this.getOrgTreeList()}
            onSelect={this.handleDepartmentSelect}
            value={orgId}
          />
        </div>
        <div className={styles.filterItem}>
          <SingleFilter
            filterId="status"
            filterName="状态"
            value={status}
            data={STATUS_LIST}
            dataMap={['code', 'text']}
            onChange={this.handleStatusSelect}
            needItemObj
          />
        </div>
      </div>
    );
  }
}
