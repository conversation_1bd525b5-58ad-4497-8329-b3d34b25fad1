/*
 * @Description: 取消客户选投顾标识申请-配置项
 * @Author: yuduo.zhang
 * @Date: 2022-02-22 14:19:42
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-03-02 15:27:56
 */

// 下拉框样式
export const DROPDOWN_STYLE = {
  width: 228,
  minHeight: 120,
  maxHeight: 400,
  overflow: 'auto',
};

// 取消客户选投顾标识申请type
export const TYPE = '24';

// 信息标签宽度
export const LABEL_112 = '112px';

// 状态
export const STATUS_LIST = [
  {
    code: '',
    text: '不限',
  },
  {
    code: '01',
    text: '处理中',
  },
  {
    code: '02',
    text: '完成',
  },
  {
    code: '03',
    text: '终止',
  },
  {
    code: '04',
    text: '驳回',
  },
];

// 营业部level
export const DEPARTMENT_LEVEL = '4';

// 当前页面名称
export const BPMN_NAME = '取消客户选投顾标识申请';

// 需要弹窗的流程节点
export const MODAL_STEP_NAME = ['发起人提交至分公司审批', '发起人提交至客户归属分公司审批', '发起人提交至总部审批'];

// 显示白色的按钮名称
export const DEFAULT_BUTTON_NAME = ['拒绝', '终止'];

// 无数据
export const NODATA = '--';

// InfoForm样式
export const INFO_FORM_STYLE = { width: '96px', marginRight: '0' };

// 没有取消客户选投顾标识申请页面任何权限的两个私行
// 南京中山东路华泰证券大厦营业部(ZZ001041056)
// 上海浦东新区东方路证券营业部(ZZ500104)
export const NO_PERMISSION_ORGIDS = ['ZZ001041056', 'ZZ500104'];

// 单客户申请/多客户申请
export const APPLY_TYPE = {
  single: 'single',
  multi: 'multi'
};

export const dropdownOptions = [
  {
    label: '单客户申请',
    value: APPLY_TYPE.single
  },
  {
    label: '多客户申请',
    value: APPLY_TYPE.multi
  }
];

export const MULTI_CUST_LIST_COLUMNS = [
  {
    title: '客户号',
    dataIndex: 'custId',
    key: 'custId',
  },
  {
    title: '客户名称',
    dataIndex: 'custName',
    key: 'custName',
  },
  {
    title: '服务营业部',
    dataIndex: 'orgName',
    key: 'orgName',
  },
  {
    title: '服务经理',
    dataIndex: 'serviceName',
    key: 'serviceName',
    render: (text, record) => (`${record?.serviceName}(${record?.serviceId})`)
  },
];

// 当前页面名称
export const MULTI_BPMN_NAME = '批量取消涨乐选投顾标识审批流程';
// 当前页面名称
export const MULTI_OA_NAME = '批量取消客户选投顾标识申请';
