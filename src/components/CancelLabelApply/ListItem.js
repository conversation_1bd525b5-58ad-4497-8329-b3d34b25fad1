/*
 * @Description: 取消客户选投顾标识申请-左侧审批客户列表项
 * @Author: yuduo.zhang
 * @Date: 2022-03-02 15:21:19
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-03-02 15:24:57
 */

import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import isEmpty from 'lodash/isEmpty';
import map from 'lodash/map';

import Tag from '@/components/common/tag';
import { format as formatTime } from '@/helper/time';
import { convertStatus, transTagType } from './utils';
import { NODATA } from './config';

import styles from './listItem.less';

const StatusTag = Tag.statusTag;

export default function ListItem(props) {
  const {
    data = {},
    onClick,
    active,
  } = props;
  const {
    title,
    empName,
    empId,
    orgName,
    createTime,
    status,
  } = data;
  const itemCls = cx({
    [styles.listItem]: true,
    [styles.active]: active,
  });
  // 创建人信息拼接
  const creatorInfo = `${empName || NODATA}(${empId || NODATA})${orgName || NODATA}`;
  // 时间
  const transCreateTime = isEmpty(createTime) ? NODATA : formatTime(createTime);
  // 状态标签
  const statusTags = convertStatus(status);
  // 针对选中状态下的状态标签type做处理，如果是选中状态则type为ghost
  const tags = transTagType(statusTags, active);

  return (
    <div className={itemCls} onClick={onClick}>
      {/* 第一行 */}
      <div className={styles.itemHeader}>
        <div className={styles.title} title={title}>
          {title}
        </div>
        <div className={styles.tagArea}>
          {
            map(tags, (tagProps) => <StatusTag {...tagProps} />)
          }
        </div>
      </div>
      {/* 第二行 */}
      <div className={styles.secondLine}>
        <div className={styles.leftArea}>
          <span className={styles.label}>创建者：</span>
          <span className={styles.creatorInfo}>{creatorInfo}</span>
        </div>
        <div className={styles.timeInfo}>
          {transCreateTime}
        </div>
      </div>
    </div>
  );
}

ListItem.propTypes = {
  data: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
  // 是否是选中高亮状态
  active: PropTypes.bool.isRequired,
};
