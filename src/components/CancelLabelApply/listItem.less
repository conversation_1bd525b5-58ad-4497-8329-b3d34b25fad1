.listItem {
  padding: 14px 20px;
  width: 100%;
  background-color: #fff;
  cursor: pointer;
  box-sizing: border-box;
  border-bottom: 1px solid #ddd;

  &:hover {
    background-color: #eaeef1;
  }

  &.active {
    background-color: #4897f1;

    &:hover {
      background-color: #4897f1;
    }

    .title, .label, .creatorInfo, .timeInfo {
      color: #fff;
    }
  }
}

.itemHeader {
  display: flex;
  justify-content: space-between;
  height: 20px;

  .title {
    max-width: 70%;
    font-size: 14px;
    color: #333;
    font-weight: bold;
    line-height: 20px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .tagArea {
    text-align: right;
  }
}

.secondLine {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;

  .leftArea {
    width: 75%;
    font-size: 12px;
    line-height: 18px;
  }

  .label {
    color: #999;
  }

  .creatorInfo {
    color: #333;
  }

  .timeInfo {
    font-size: 12px;
    line-height: 18px;
    text-align: right;
  }
}
