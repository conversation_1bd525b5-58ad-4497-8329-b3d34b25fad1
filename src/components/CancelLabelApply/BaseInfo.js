/*
 * @Author: tian<PERSON><PERSON><PERSON>
 * @Date: 2024-08-09 14:56:29
 * @LastEditors: tiannengyu
 * @LastEditTime: 2024-08-19 15:06:47
 * @FilePath: /performance_report/src/components/CancelLabelApply/BaseInfo.js
 * @Description: 取消客户选投顾标识申请--多客户-右侧详情--基本信息
 */
import React from 'react';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import {
  InfoGroup,
  InfoCell,
  DetailBlock,
} from '@crm/biz-ui';

import { LABEL_112 } from './config';

export default function BaseInfo(props) {
  const {
    data: {
      empId,
      empName,
      createTime,
      empOrgName,
      reason
    },
  } = props;

  // 无数据
  const noData = <span style={{ color: '#999' }}>--</span>;

  // 处理文字
  const renderNormalCell = (value) => (
    isEmpty(value) ? noData : value
  );

  return (
    <DetailBlock title="基本信息">
      <InfoGroup>
        <InfoCell
          span={50}
          labelWidth={LABEL_112}
          label="创建者"
          ellipsis
          content={renderNormalCell(`${empName}(${empId})`)}
        />
        <InfoCell
          span={50}
          labelWidth={LABEL_112}
          label="部门"
          ellipsis
          content={renderNormalCell(empOrgName)}
        />
        <InfoCell
          span={100}
          labelWidth={LABEL_112}
          label="创建时间"
          ellipsis
          content={renderNormalCell(createTime)}
        />
      </InfoGroup>
      <InfoGroup>
        <InfoCell
          span={100}
          labelWidth={LABEL_112}
          label="说明"
          ellipsis
          content={renderNormalCell(reason)}
        />
      </InfoGroup>
    </DetailBlock>
  );
}

BaseInfo.propTypes = {
  data: PropTypes.object,
};

BaseInfo.defaultProps = {
  data: {},
};
