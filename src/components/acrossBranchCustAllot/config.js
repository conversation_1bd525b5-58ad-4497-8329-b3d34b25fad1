/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 跨分公司客户分配配置项
 * @Date: 2019-06-19 14:26:16
 * @Last Modified by: l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-10-31 14:06:54
 */
import _ from 'lodash';

import {
  customer
} from '../../config/busApplyFilters';

export const CUST_NAME_KEY = 'custName';
export const CUST_SOURCE = 'custSource';
export const STATUS_NAME_KEY = 'status';
export const OLD_DEPARTMENT_NAME_KEY = 'oldOrgName';
export const OLD_BRANCH_NAME_KEY = 'oldBranchName';
export const OPERATE_NAME_KEY = 'operate';

export const YGT = '一柜通';

// 跨分公司客户分配的流程名称，用于发起流程时传参
export const ACROSS_BRANCH_FLOW_NAME = '跨分公司分配流程';
// 一柜通跨分公司客户分配的流程名称，用于发起流程时传参
export const YGT_ACROSS_BRANCH_FLOW_NAME = '跨分公司分配一柜通流程';

// 取消按钮的值
export const CANCEL_BTN_VALUE = 'cancel';

// 打通从跨公司级分配到营业部分配的功能-点击通知提醒后，跳转至跨分公司分配页面,有权限时提示信息
export const IS_HAS_PERMISSION = '新客户转入，是否需要进行分配？';
// 打通从跨公司级分配到营业部分配的功能-点击通知提醒后，跳转至跨分公司分配页面,无权限时提示信息
export const NO_HAS_PERMISSION = '您无客户分配权限，请及时进行分配';

// 新建弹窗的 key 值
export const CREATE_MODAL_KEY = 'createModal';

// 两融信用黑名单客户
export const TWO_FINANCE_BLACKLIST_CUSTOMERS = 'two_finance_blacklist_customers';
// 当前未完成的“风险提示处置”类任务对应的客户
export const UNFINISHED_RISK_TASKS = 'unfinished_risk_tasks';
// 洗钱风险为高风险的客户
export const HIGH_RISK_CUSTOMER_OF_MONEY_LAUNDERING = 'high_risk_customer_of_money_laundering';
// 交易所重点监控账户的客户
export const KEY_CONTROL_ACCOUNT_OF_THE_EXCHANGE = 'key_control_account_of_the_exchange';
// 交易所监管类函件客户
export const REGULATORY_LETTER_CUSTOMER_OF_THE_EXCHANGE = 'regulatory_letter_customer_of_the_exchange';
// 投诉关注名单客户
export const COMPLAINTS_CUSTOMER_NOT_BE_TRANSFERRED = 'complaints_customer_not_be_transferred';

// 添加客户时的校验配置
export const VALDATE_MAP = {
  privateCustomer: {
    // 此项校验失败时是否能添加客户,如果是ture的话，需要弹一个提示框，点击确认之后才可添加，false的话直接不允许添加
    canPass: false,
    message: '当前客户为私密客户，不予划转',
  },
  marketingBinding: {
    canPass: false,
    message: '当前客户在营销系统中有有效的营销人员开发关系绑定，不予划转。',
  },
  validProtocol: {
    canPass: false,
    message: '当前客户有生效中或审批中的合约或协议，不予划转。',
  },
  onApproveAssign: {
    canPass: false,
    message: '当前客户中有在途的营业部分配/分公司分配/跨分公司分配申请单，不予划转。',
  },
  treasureCustomer: {
    canPass: false,
    message: '当前选定客户为财富类客户，请通知分公司发起客户分配流程',
  },
  mainEmpWeightAssign: {
    canPass: true,
    message: '当前服务团队成员有服务权重，是否重新分配',
  },
  belongsAdvisor: {
    canPass: true,
    message: '当前客户为投顾名下客户，请确认客户是否需要划出',
  },
  relationshipModifiedByCustomer: {
    canPass: false,
    message: '客户已自主选择服务经理，不允许划转',
  },
  NOT_ALLOWED_DISTRIBUTE_IN_30_DAYS: {
    canPass: false,
    message: '该客户为总部划转客户',
  },
  two_finance_blacklist_customers: {
    canPass: false,
    message: '该客户为两融黑名单客户，不予划转',
  },
  high_risk_customer_of_money_laundering: {
    canPass: false,
    message: '该客户为洗钱高风险客户，不予划转',
  },
  key_control_account_of_the_exchange: {
    canPass: false,
    message: '该客户为交易所重点监控账户，不予划转',
  },
  regulatory_letter_customer_of_the_exchange: {
    canPass: false,
    message: '该客户为交易所监管类函件客户，不予划转',
  },
  complaints_customer_not_be_transferred: {
    canPass: false,
    message: '该客户为投诉关注客户，不予划转',
  },
  unfinished_risk_tasks: {
    canPass: false,
    message: '当前客户有未完成的“风险提示处置”任务，不予划转',
  },
  cust_account_cancellation: {
    canPass: false,
    message: '该客户为销户客户，不予划转',
  },
  cust_not_exist: {
    canPass: false,
    message: '客户不存在',
  },
  cust_cross_border: {
    canPass: false,
    message: '客户正在/已开通跨境通业务，仅能在9市内营业部（广州、深圳、珠海、佛山、惠州、东莞、中山、江门、肇庆）流转。',
  },
  no_emp: {
    canPass: false,
    message: '服务经理不存在',
  },
};

const config = {
  // 跨分公司客户分配
  custAllot: {
    pageName: '跨分公司客户分配',
    pageType: '07', // 查询列表接口中的type值
    status: [
      {
        show: true,
        label: '不限',
        value: '',
      },
      {
        show: true,
        label: '审批中',
        value: '01',
      },
      {
        show: true,
        label: '完成',
        value: '02',
      },
      {
        show: true,
        label: '终止',
        value: '03',
      },
      {
        show: true,
        label: '失败',
        value: '05',
      },
    ],
  },
  // 子类型 type
  subType: '0705',
  titleList: {
    approvalColumns: [
      {
        title: '工号',
        dataIndex: 'empId',
        key: 'empId',
      }, {
        title: '姓名',
        dataIndex: 'empName',
        key: 'empName',
      }, {
        title: '所属部门',
        dataIndex: 'orgName',
        key: 'orgName',
      },
    ],
    custColumns: [
      {
        title: '客户',
        dataIndex: CUST_NAME_KEY,
        key: CUST_NAME_KEY,
        width: 191,
      },
      {
        title: '客户来源',
        dataIndex: CUST_SOURCE,
        key: CUST_SOURCE,
        width: 112,
      },
      {
        title: '状态',
        dataIndex: STATUS_NAME_KEY,
        key: STATUS_NAME_KEY,
        width: 86,
      },
      {
        title: '原服务营业部',
        dataIndex: OLD_DEPARTMENT_NAME_KEY,
        key: OLD_DEPARTMENT_NAME_KEY,
        width: 210,
      },
      {
        title: '原服务分公司',
        dataIndex: OLD_BRANCH_NAME_KEY,
        key: OLD_BRANCH_NAME_KEY,
        width: 140,
      },
    ],
    operateColumnItem: {
      title: '操作',
      dataIndex: OPERATE_NAME_KEY,
      key: OPERATE_NAME_KEY,
      width: 28,
    }
  },
  basicFilters: [
    customer,
  ],
};

// 跨分公司分配相关接口传参的type
const BUSSINESS_TYPE = 'crossBranch';

// 一柜通跨分公司分配相关接口传参的type
const YGT_BUSSINESS_TYPE = 'crossBranchYGT';

// 财富类客户
export const TREASURE_CUSTOMER = 'treasureCustomer';

// 根据客户列表数据判断
// hasYGT, 列表否有一柜通客户
// notYGT, 列表是否有非一柜通客户
// numberOfYGT, 列表一柜通客户数量
// bpmnName, 根据是否有一柜通客户发起流程的传参
// businessType 根据是否有一柜通客户给客户发起流程的传参
export function getCustListInfo(data) {
  let hasYGT = false;
  let notYGT = false;
  let numberOfYGT = 0;
  if (!_.isEmpty(data)) {
    hasYGT = _.some(data, (item) => item.custSource === YGT);
    notYGT = _.some(data, (item) => item.custSource !== YGT);
    numberOfYGT = _.filter(data, (item) => item.custSource === YGT).length;
  }
  const bpmnName = YGT_ACROSS_BRANCH_FLOW_NAME;
  const businessType = hasYGT ? YGT_BUSSINESS_TYPE : BUSSINESS_TYPE;
  return {
    hasYGT,
    notYGT,
    numberOfYGT,
    bpmnName,
    businessType
  };
}

// 新建弹窗底部按钮config
export const BUTTON_LIST = [
  {
    actionName: '取消',
    btnName: '取消',
    operate: CANCEL_BTN_VALUE,
  },
  {
    actionName: '提交',
    btnName: '提交',
    operate: 'primary',
  },
];

export default config;
