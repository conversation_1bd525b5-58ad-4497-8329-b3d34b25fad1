.createModal {
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-modal-footer {
      background-color: #000;
      .ant-btn {
        &:last-child {
          margin-left: 10px;
         }
      }
    }
  }
}
.modalContent {
  padding: 15px 0;
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {

    .ant-select-arrow {
      &::before {
        content: '\E606';
      }
    }
    .ant-form-item {
      margin-bottom: 0;
    }
    .ant-form-item-label {
      width: 120px;
      overflow: inherit;
      label {
        font-size: 14px;
        color: #9b9b9b;
        font-style: normal;
        font-weight: normal;
        &::after {
          margin: 0 4px;
        }
      }
    }
    .ant-form-item-control-wrapper {
      width: calc(100% - 120px);
      display: inline-block;
      vertical-align: top;
      font-size: 14px;
    }
    .ant-select, .ant-input {
      width: 228px;
    }
    .lego-selection {
      border: 1px solid #d9d9d9;
    }
    .has-error {
      .lego-selection {
        border-color: #f5222d;
        &:hover {
          border-color: #f5222d;
        }
      }
    }
    .lego-filter-filterWrapper {
      button.ant-btn {
        width: 200px;
        height: 28px;
        text-align: left;
        border: 1px solid #d9d9d9;
        &:hover {
          border-color: #40a9ff;
        }
      }
      & > button:first-child .lego-filter-contentShowOnButton {
        padding-left: 0;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.65);
        & > span:nth-child(1) {
          display: none;
        }
        .lego-filter-valueShowOnButton {
          max-width: 200px;
        }
      }
      .lego-single-filter-searchInput {
        width: 228px;
        border: 1px solid #d9d9d9;
        &:focus {
          box-shadow: none;
        }
        &:hover {
          border-color: #d9d9d9;
        }
      }
    }
    .ant-select-selection, .ant-input {
      width: 200px;
    }
    textarea.ant-input {
      height: 72px;
    }
  }
  .contentItem {
    margin-top: 12px;
    &:first-child {
      margin-top: 0;
    }
    .title {
      font-size: 16px;
      color: #4a4a4a;
      text-indent: 14px;
      position: relative;
      &:before {
        position: absolute;
        display: block;
        content: "";
        width: 5px;
        height: 16px;
        background: #4897f1;
        top: 5px;
        left: 0;
      }
    }
    .operateDiv {
        /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
        .ant-input {
          height: 28px;
        }
      }
      .linkSpan {
        float: right;
        vertical-align: bottom;
        button {
          background: #108ee9;
        }
        & > a, & > span {
          display: inline-block;
          box-sizing: border-box;
          width: 110px;
          height: 28px;
          line-height: 26px;
          background: #fff;
          color: #333;
          border: 1px solid #ddd;
          text-align: center;
          border-radius: 4px;
          margin-left: 20px;
          a {
            color: #333;
          }
        }
        & > span:first-child {
          width: auto;
          border: 0;
        }
      }
    }
    .tableDiv {
      margin: 14px 0 20px;
    }
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .lego-selection--single .lego-selection__rendered .lego-selection-selected-value {
      max-width: 156px;
      width: 156px;
    }
  }
  .infoForm > div {
    padding-left: 0;
  }
  .branchName {
    padding-left: 12px;
    color: #333;
  }
}
.delete {
  font-size: 14px;
  color: #108ee9;
  cursor: pointer;
}
.mr10 {
  margin-right: 10px;
}

