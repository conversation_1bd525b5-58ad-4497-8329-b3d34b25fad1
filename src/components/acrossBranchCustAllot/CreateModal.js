/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 跨分公司客户分配新建弹窗
 * @Date: 2019-06-19 16:08:47
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-08-24 14:36:14
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import {
  message,
  Popconfirm,
  Form,
  Input,
} from 'antd';
import _ from 'lodash';
import TreeFilter from '@lego/treeSelect/src';

import Uploader from '@/newUI/upload';
import logable, { logPV, logCommon } from '@/decorators/logable';
import InfoTitle from '@/components/common/InfoTitle';
import CommonTable, { ToolTipCell } from '@/components/common/table';
import confirm from '@/components/common/newUI/confirm';
import CommonModal from '@/components/common/newUI/modal';
import IfWrap from '@/components/common/IFWrap';
import TableDialog from '@/components/common/biz/TableDialog';
import Icon from '@/components/common/Icon';
import AutoComplete from '@/components/common/similarAutoComplete';
import { emp } from '@/helper';
import config, {
  CUST_NAME_KEY,
  STATUS_NAME_KEY,
  OPERATE_NAME_KEY,
  YGT_ACROSS_BRANCH_FLOW_NAME,
  CANCEL_BTN_VALUE,
  OLD_DEPARTMENT_NAME_KEY,
  OLD_BRANCH_NAME_KEY,
  VALDATE_MAP,
  TREASURE_CUSTOMER,
  getCustListInfo,
  BUTTON_LIST
} from './config';
// import RemindOfYGT from './RemindOfYGT';
import styles from './createModal.less';

const { create } = Form;
const FormItem = Form.Item;
const { TextArea } = Input;

const {
  custAllot: {
    pageType,
  },
  subType,
  titleList: {
    custColumns,
    operateColumnItem,
    approvalColumns,
  },
} = config;

// 登陆人的组织 ID
const empOrgId = emp.getOrgId();
const empId = emp.getId();

// 营业部层级的level
const DERPARTMENT_LEVEL_KEY = '3';

// 客户列表最大长度
const MAX_CUST_LENGTH = 50;

function getTransformedItem(data) {
  return {
    ...data,
    label: data.name,
    key: data.id,
    value: data.id,
    // 此处需求只能选择营业部，不是营业部的不让选
    selectable: data.level === DERPARTMENT_LEVEL_KEY,
    children: _.map(data.children, getTransformedItem),
  };
}

function getTransformedData(list) {
  // 组织机构列表里第一个元素是最顶级的部门，此处不需要展示，所以剔除掉
  const branchList = _.tail(list);
  return _.map(branchList, getTransformedItem);
}

 @create()
export default class CreateModal extends PureComponent {
   static propTypes = {
     location: PropTypes.object.isRequired,
     dict: PropTypes.object.isRequired,
     form: PropTypes.object.isRequired,
     // 获取按钮数据和下一步审批人
     flowButtonList: PropTypes.array.isRequired,
     queryButtonList: PropTypes.func.isRequired,
     // 获取客户数据
     custData: PropTypes.object.isRequired,
     queryCustList: PropTypes.func.isRequired,
     // 提交保存
     saveChange: PropTypes.func.isRequired,
     // 发起流程
     startFlow: PropTypes.func.isRequired,
     // 弹窗的key
     modalKey: PropTypes.string.isRequired,
     // 关闭弹窗
     closeModal: PropTypes.func.isRequired,
     // 组织机构树
     custRangeList: PropTypes.array.isRequired,
     // 校验后的客户信息
     checkCustInfo: PropTypes.func.isRequired,
     // 查询左侧列表
     queryAppList: PropTypes.func.isRequired,
     // 删除附件
     deleteAttachment: PropTypes.func.isRequired,
   }

   constructor(props) {
     super(props);
     this.state = {
       // 当前选择的客户
       currentCust: {},
       // 待添加的客户列表
       custList: [],
       // 当前选择的营业部所属的分公司
       currentBranch: {},
       // 当前点击的按钮
       currentBtn: {},
       // 是否显示审批人弹窗
       approverModal: false,
       // 附件信息id
       attachment: '',
     };
     this.orgTreeData = getTransformedData(props.custRangeList);
     this.custColumnList = this.getColumns();
   }

   @autobind
   setQueryCustComponentRef(ref) {
     this.queryCustComponent = ref;
   }

   @autobind
   getColumns() {
     return _.map([...custColumns, operateColumnItem], (item) => {
       if (item.key === CUST_NAME_KEY) {
         return {
           ...item,
           render: (text, record) => {
             const custName = `${record.custName || ''}(${record.custId || ''})`;
             return (
               <ToolTipCell
                 cellText={custName}
                 tipContent={custName}
               />
             );
           },
         };
       }
       if (item.key === OLD_DEPARTMENT_NAME_KEY) {
         return {
           ...item,
           render: (text) => (
             <ToolTipCell
               cellText={text}
               tipContent={text}
             />
           ),
         };
       }
       if (item.key === OLD_BRANCH_NAME_KEY) {
         return {
           ...item,
           render: (text) => (
             <ToolTipCell
               cellText={text}
               tipContent={text}
             />
           ),
         };
       }
       if (item.key === STATUS_NAME_KEY) {
         return {
           ...item,
           render: (text, record) => {
             const { dict: { accountStatusList = [] } } = this.props;
             const statusItem = _.filter(accountStatusList, (o) => o.key === text);
             const statusText = statusItem.length ? statusItem[0].value : '';
             return (<div title={statusText}>{statusText}</div>);
           },
         };
       }
       if (item.key === OPERATE_NAME_KEY) {
         return {
           ...item,
           render: this.renderPopconfirm,
         };
       }
       return {
         ...item,
       };
     });
   }

   // 添加单客户的搜索事件
   @autobind
   @logable({
     type: 'ButtonClick',
     payload: {
       name: '搜索客户',
       value: '$args[0]',
     },
   })
   handleSearchClient(v) {
     if (!v) {
       return;
     }
     const { queryCustList } = this.props;
     queryCustList({
       orgIdKeyword: empOrgId,
       orgId: empOrgId,
       custKeyword: v,
       pageSize: 3,
       pageNum: 1,
       type: 'department',
     });
   }

   // 选择客户
   @autobind
   @logable({
     type: 'DropdownSelect',
     payload: {
       name: '选择客户',
       value: '$args[0].custName',
     },
   })
   handleSelectClient(value) {
     if (!_.isEmpty(value)) {
       this.setState({
         currentCust: value,
       }, () => {
         this.queryCustComponent.clearValue();
         this.handleAddBtnClick();
       });
     }
   }

   // 删除客户
   @autobind
   @logable({
     type: 'Click',
     payload: {
       name: '删除客户',
     },
   })
   handleDeleteTableData(type, record) {
     this.setState((prevState) => ({
       custList: _.differenceBy(prevState.custList, [record], 'custId'),
     }));
   }

   // 渲染点击删除按钮后的确认框
   @autobind
   renderPopconfirm(type, record) {
     return (
       <Popconfirm
         placement="top"
         onConfirm={() => this.handleDeleteTableData(type, record)}
         okText="是"
         cancelText="否"
         title="是否删除此条数据？"
       >
         <Icon className={styles.delete} type="shanchu" />
       </Popconfirm>
     );
   }

   // 根据传入的营业部id，找到所属的分公司
   @autobind
   getBranchByDepartmentId(id) {
     return _.find(this.orgTreeData,
       (branchItem) => !!_.find(branchItem.children, (item) => item.value === id));
   }

   // 选择营业部时保存对应的分公司数据
   @autobind
   setBranchName(id) {
     const currentBranch = this.getBranchByDepartmentId(id);
     this.setState({
       currentBranch,
     });
   }

   // 检查选择的营业部id是否和当前登录人的营业部id一致
   @autobind
   checkIsSameDepartment(id) {
     if (id === empOrgId) {
       message.error('转出与转入营业部一致，请重新选择转入营业部');
       return false;
     }
     return true;
   }

   // 检查当前是否可以添加客户
   @autobind
   checkIsCanAddCust() {
     const { form } = this.props;
     const { currentCust, custList } = this.state;
     const orgData = form.getFieldValue('department');
     if (_.isEmpty(orgData) || _.isEmpty(orgData.value)) {
       message.error('添加客户前，请先设置转入营业部信息');
       return false;
     }
     if (custList.length > MAX_CUST_LENGTH) {
       message.error('最多添加50个客户');
       return false;
     }
     if (_.isEmpty(currentCust)) {
       return false;
     }
     // 如果列表里已经存在该客户则不进行操作
     if (_.findIndex(custList, (item) => item.custId === currentCust.custId) > -1) {
       return false;
     }
     return true;
   }

   @autobind
   @logable({
     type: 'DropdownSelect',
     payload: {
       name: '选择转入营业部',
       value: '$args[0].value.label',
     },
   })
   handleChangeOrg(data) {
     if (!this.checkIsSameDepartment(data.value)) {
       return;
     }
     const lastValue = this.props.form.getFieldValue('department');
     // 切换营业部并且当前已选客户列表不为空
     if (!_.isEmpty(lastValue) && !_.isEmpty(this.state.custList)) {
       confirm({
         title: '提示',
         content: '切换转入营业部将清空客户列表中已选择客户，确认要切换吗？',
         onOk: () => {
           this.saveChangedOrgData(data);
           // 清空客户列表
           this.setState({
             custList: [],
           });
         },
       });
       return;
     }
     this.saveChangedOrgData(data);
   }

   // 保存当前选择的营业部数据
   @autobind
   saveChangedOrgData(data) {
     this.setBranchName(data.value);
     this.props.form.setFieldsValue({
       department: data,
     });
   }

   @autobind
   addCust() {
     const { currentCust } = this.state;
     this.setState((prevState) => ({
       custList: [
         ...prevState.custList,
         currentCust
       ],
     }));
   }

   // 检查当前选择的客户是否合法
   @autobind
   checkCustIsLegal(list) {
     return new Promise((resolve, reject) => {
       const checkData = (index = 0) => {
         if (index >= list.length) {
           resolve(true);
           return;
         }
         const item = list[index];
         const mapItem = VALDATE_MAP[item.key];
         const nextIndex = index + 1;
         if (!mapItem) {
           checkData(nextIndex);
           return;
         }
         if (!mapItem.canPass && item.value) {
           // 业务要求财富类客户的校验用confirm的展现形式提示
           if (item?.key === TREASURE_CUSTOMER) {
             confirm({
               title: '提示',
               content: mapItem.message,
               onOk: () => resolve(false),
               cancelVisible: false,
             });
             return;
           }
           message.error(mapItem.message);
           resolve(false);
           return;
         }
         if (mapItem.canPass && item.value) {
           confirm({
             title: '提示',
             content: mapItem.message,
             onOk: () => checkData(nextIndex),
             onCancel: () => resolve(false),
           });
           return;
         }
         checkData(nextIndex);
       };
       checkData();
     });
   }

   @autobind
   @logable({
     type: 'ButtonClick',
     payload: {
       name: '添加客户',
     },
   })
   handleAddBtnClick() {
     if (!this.checkIsCanAddCust()) {
       return;
     }
     const {
       checkCustInfo,
       form
     } = this.props;
     const { currentCust } = this.state;
     const { businessType } = getCustListInfo([currentCust]);
     const values = form.getFieldsValue();
     checkCustInfo({
       businessType,
       custId: currentCust.custId,
       orgId: empOrgId,
       targetOrgId: values.department.value,
     }).then(({ resultData }) => {
       const list = _.map(Object.keys(resultData), (key) => ({
         key,
         value: resultData[key],
       }));
       this.checkCustIsLegal(list).then((checkReuslt) => {
         if (checkReuslt) {
           this.addCust();
         }
       });
     });
   }

   @autobind
   @logable({
     type: 'ButtonClick',
     payload: {
       name: '$args[0].actionName',
     },
   })
   handleModalButtonClick(btnItem) {
     if (btnItem.operate === CANCEL_BTN_VALUE) {
       // 关闭弹窗，不请求列表
       this.props.closeModal();
     } else {
       // 显示审批人弹窗，设置审批人数据
       const { custList } = this.state;
       const { bpmnName } = getCustListInfo(custList);
       this.props.form.validateFields((errors, values) => {
         if (!_.isEmpty(errors)) {
           return;
         }
         if (_.isEmpty(custList)) {
           message.error('客户列表不能为空');
           return;
         }
         // 客户列表存在一柜通客户 也有非一柜通客户
         // if (hasYGT && notYGT) {
         //   confirm({
         //     title: '提示',
         //     content: '当前客户列表存在一柜通客户, 请单独发起划转流程',
         //   });
         //   return;
         // }
         // // 客户列表只有一柜通客户的时候弹出提示 否则直接弹出选择审批人
         // if (hasYGT && !notYGT) {
         //   confirm({
         //     title: '提示',
         //     content: '待划转客户划转后所产生的贡献将归属转入营业部, 请确认是否划转',
         //     onOk: () => this.handleShowApprovalModal(),
         //   });
         // } else {
         // }
         this.handleShowApprovalModal();
         this.setState({
           currentBtn: btnItem,
         });
         // 获取下一步骤按钮列表
         this.props.queryButtonList({
           bpmnName,
           orgId: empOrgId,
         });
       });
     }
   }

   @autobind
   @logPV({ pathname: '/modal/createAcrossBranchCustAllotChoiceApproval', title: '新建跨分公司客户分配-选择审批人' })
   handleShowApprovalModal() {
     this.setState({
       approverModal: true,
     });
   }

   @autobind
   @logable({
     type: 'ButtonClick',
     payload: {
       name: '关闭审批人弹窗',
     },
   })
   handleCloseApprovalModal() {
     this.setState({
       approverModal: false,
     });
   }

   // 选完审批人后的提交
   @autobind
   handleApproverModalOK(auth) {
     const {
       form,
       saveChange,
       startFlow,
       closeModal,
       queryAppList,
     } = this.props;
     const {
       custList,
       currentBranch,
       currentBtn,
       attachment,
     } = this.state;
     const { bpmnName, businessType } = getCustListInfo(custList);
     const values = form.getFieldsValue();
     const payload = {
       orgId: empOrgId,
       departmentId: values.department.value,
       branchId: currentBranch?.id,
       custList,
       reason: values.reason,
       businessType,
       type: pageType,
       subType,
       attachment,
     };
     logCommon({
       type: 'Submit',
       payload: {
         title: '跨分公司客户分配提交',
         value: JSON.stringify({ ...payload }),
         name: '跨分公司客户分配提交',
       },
     });
     saveChange(payload).then(({ resultData }) => {
       const approvalAayload = {
         bpmnName,
         actionName: currentBtn.actionName,
         assigneeList: [auth.empId],
         businessId: resultData,
         oaTitle: `${empId}发起的${YGT_ACROSS_BRANCH_FLOW_NAME}`,
       };
       logCommon({
         type: 'Submit',
         payload: {
           title: '选择审批人后跨分公司客户分配发起流程',
           value: JSON.stringify({ ...approvalAayload }),
           name: '选择审批人后跨分公司客户分配发起流程',
         },
       });
       startFlow(approvalAayload).then(() => {
         const {
           location: {
             query,
           },
         } = this.props;
         // 提交之后关闭弹窗更新左侧列表
         this.handleCloseApprovalModal();
         closeModal({
           isNeedConfirm: false,
         });
         queryAppList(query);
       });
     });
   }

   // 获取新建Modal的底部按钮组
   @autobind
   renderModalButton() {
     return _.map(BUTTON_LIST, (item) => {
       const { actionName, operate = '' } = item;
       return {
         key: actionName,
         text: actionName,
         type: operate,
         onClick: () => this.handleModalButtonClick(item),
       };
     });
   }

   // 上传成功后的数据
   @autobind
   handleUploadSuccess(apiResult) {
     const { attachment, attaches } = apiResult.resultData;
     this.setState({ attachment });
     return Promise.resolve(_.last(attaches));
   }

   // 删除已经上传成功的附件
   @autobind
   handleFileRemove({ attachId, status }) {
     // 如果要删除的文件没有attachId，或者status的状态为error，则表示该附件没有上传到服务器中
     const { attachment } = this.state;
     if (attachment && !_.isEmpty(attachId) && status !== 'error') {
       return this.props.deleteAttachment({
         empId: emp.getId(),
         attachId,
         attachment,
       });
     }
     // 默认表示删除成功
     return Promise.resolve(true);
   }

   render() {
     const {
       modalKey,
       closeModal,
       custData: {
         list = [],
       },
       form,
       flowButtonList,
     } = this.props;
     const { getFieldDecorator, getFieldValue } = form;
     const {
       currentBranch,
       custList,
       approverModal,
       attachment,
     } = this.state;
     // 从接口获取下一审批人列表数据
     const dataSource = _.get(flowButtonList, ['0', 'flowAuditors'], []);
     const orgData = getFieldValue('department') || {};
     // 根据客户列表是否有一柜通客户和一柜通客户数
     // const { hasYGT, numberOfYGT } = getCustListInfo(custList);
     // 审批人弹窗
     const approvalProps = {
       visible: approverModal,
       onOk: this.handleApproverModalOK,
       onCancel: this.handleCloseApprovalModal,
       dataSource,
       columns: approvalColumns,
       title: '选择下一审批人员',
       placeholder: '员工号/员工姓名',
       modalKey: 'approverModal',
       rowKey: 'empId',
       searchShow: false,
     };

     const pageData = {
       pageSize: 5,
     };

     return (
       <CommonModal
         title="新建跨分公司客户分配"
         visible
         onModalClose={closeModal}
         size="large"
         wrapClassName={styles.createModal}
         modalKey={modalKey}
         modalFooter={this.renderModalButton()}
       >
         <div className={styles.modalContent}>
           <Form>
             <div className={styles.contentItem}>
               <InfoTitle head="转入营业部设置" />
               <FormItem label="转入营业部">
                 {
                   getFieldDecorator('department', {
                     rules: [{
                       required: true, message: '请选择转入营业部',
                     }],
                   })(
                     <Input type="hidden" />
                   )
                 }
                 <TreeFilter
                   dropdownClassName={styles.custFilterWrap}
                   value={{
                     label: orgData.label || '请选择营业部',
                     value: orgData.value || '',
                   }}
                   treeData={this.orgTreeData}
                   onChange={this.handleChangeOrg}
                   treeNodeFilterProp="title"
                   treeCheckable={false}
                   showSearch
                   labelInValue
                   dropdownMatchSelectWidth={false}
                   getPopupContainer={() => document.querySelector(`.${styles.modalContent}`)}
                   dropdownStyle={{
                     width: 240,
                     maxHeight: 400,
                     overflow: 'auto'
                   }}
                   searchPlaceholder="营业部名称"
                 />
               </FormItem>
               <FormItem label="所属分公司">
                 <span className={styles.branchName}>{currentBranch?.label}</span>
               </FormItem>
             </div>
             <div className={styles.contentItem}>
               <InfoTitle head="客户列表" />
               {/* 操作按钮容器 */}
               <div className={`${styles.operateDiv} clearfix`}>
                 <AutoComplete
                   placeholder="客户号/客户名称"
                   showNameKey="custName"
                   showIdKey="custId"
                   optionList={list}
                   onSelect={this.handleSelectClient}
                   onSearch={_.debounce(this.handleSearchClient, 250)}
                   ref={this.setQueryCustComponentRef}
                   dropdownMatchSelectWidth={false}
                   isImmediatelySearch
                 />
               </div>
               {/* <RemindOfYGT
                 hasYGT={hasYGT}
                 numberOfYGT={numberOfYGT}
               /> */}
               <div className={styles.tableDiv}>
                 <CommonTable
                   dataSource={custList}
                   columns={this.custColumnList}
                   pagination={pageData}
                   rowKey="custId"
                   useNewUI
                   withBorder={_.isEmpty(custList)}
                 />
               </div>
               <div className={styles.contentItem}>
                 <InfoTitle head="划转原因" />
                 <FormItem label="划转原因">
                   {
                     getFieldDecorator('reason', {
                       rules: [{
                         required: true, message: '请输入划转原因',
                       }, {
                         max: 250, message: '长度不能超过250',
                       }],
                     })(
                       <TextArea />
                     )
                   }
                 </FormItem>
               </div>
               {/* 附件信息 */}
               <div className={styles.contentItem}>
                 <InfoTitle head="附件" />
                 <Uploader
                   key="acrossBranchCustAllotCreateModalUploader"
                   attachment={attachment}
                   onSuccess={this.handleUploadSuccess}
                   onRemove={this.handleFileRemove}
                   attachmentCountLimit={2}
                 />
               </div>
             </div>
           </Form>
           <IfWrap when={approverModal}>
             <TableDialog {...approvalProps} />
           </IfWrap>
         </div>
       </CommonModal>
     );
   }
 }
