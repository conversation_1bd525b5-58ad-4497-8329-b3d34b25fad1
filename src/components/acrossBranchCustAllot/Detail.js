/**
 * @Description: 分公司客户分配详情
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-05-23 15:19:51
 * @Last Modified by: li-ke
 * @Last Modified time: 2019-09-26 16:04:12
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { autobind } from 'core-decorators';

import Uploader from '@/newUI/upload';
import InfoTitle from '../common/InfoTitle';
import InfoItem from '../common/infoItem';
import CommonTable, { ToolTipCell } from '../common/table';
import Pagination from '../common/Pagination';
import FlowHistory from '../common/flowHistory';
import { time } from '../../helper';
import config, {
  CUST_NAME_KEY,
  STATUS_NAME_KEY,
  OLD_DEPARTMENT_NAME_KEY,
  OLD_BRANCH_NAME_KEY,
  // getCustListInfo
} from './config';
// import RemindOfYGT from './RemindOfYGT';
import styles from './detail.less';
import logable from '../../decorators/logable';

// 表头
const {
  titleList: {
    custColumns,
  },
} = config;
export default class Detail extends PureComponent {
  static propTypes = {
    dict: PropTypes.object.isRequired,
    location: PropTypes.object.isRequired,
    data: PropTypes.object.isRequired,
    // 详情中客户列表
    queryDetailCustList: PropTypes.func.isRequired,
    detailCustData: PropTypes.object.isRequired,
    // 审批历史流程
    historyFlow: PropTypes.object.isRequired,
    // 详情中的附件列表
    attachmentList: PropTypes.array.isRequired,
  }

  constructor(props) {
    super(props);
    this.columns = this.getCustColumns();
  }

  // 翻页
  @autobind
  @logable({ type: 'ButtonClick', payload: { name: '点击分页' } })
  handlePageNumberChange(pageNum) {
    const { queryDetailCustList, data: { id } } = this.props;
    const payload = {
      id,
      pageNum,
      pageSize: 5,
    };
    queryDetailCustList(payload);
  }

  @autobind
  getCustColumns() {
    return _.map(custColumns, (item) => {
      if (item.key === CUST_NAME_KEY) {
        return {
          ...item,
          render: (text, record) => {
            const custName = `${record.custName || ''}(${record.custId || ''})`;
            return (
              <ToolTipCell
                cellText={custName}
                tipContent={custName}
              />
            );
          },
        };
      }
      if (item.key === OLD_DEPARTMENT_NAME_KEY) {
        return {
          ...item,
          render: text => (
            <ToolTipCell
              cellText={text}
              tipContent={text}
            />
          ),
        };
      }
      if (item.key === OLD_BRANCH_NAME_KEY) {
        return {
          ...item,
          render: text => (
            <ToolTipCell
              cellText={text}
              tipContent={text}
            />
          ),
        };
      }
      if (item.key === STATUS_NAME_KEY) {
        return {
          ...item,
          render: (text, record) => {
            const { dict: { accountStatusList = [] } } = this.props;
            const statusItem = _.filter(accountStatusList, o => o.key === text);
            const statusText = statusItem.length ? statusItem[0].value : '';
            return (<div title={statusText}>{statusText}</div>);
          },
        };
      }
      return {
        ...item,
      };
    });
  }

  render() {
    const {
      data: {
        id,
        branchName,
        departmentName,
        reason,
        empId,
        empName,
        orgName,
        createTime,
        statusName,
        attachment,
      },
      detailCustData: {
        list = [],
        page = {},
      },
      historyFlow,
      attachmentList,
    } = this.props;

    if (_.isEmpty(this.props.data)) {
      return null;
    }
    // 拟稿人信息
    const drafter = `${orgName} - ${empName} (${empId})`;
    // 分页
    const paginationOption = {
      current: page.pageNum || 1,
      total: page.totalCount || 0,
      pageSize: page.pageSize || 5,
      onChange: this.handlePageNumberChange,
    };

    // 根据客户列表是否有一柜通 一柜通客户数量
    // const { hasYGT, numberOfYGT } = getCustListInfo(list);
    return (
      <div className={styles.detailBox}>
        <h1 className={styles.detailTitle}>编号{id}</h1>
        <div className={styles.detailModule}>
          <InfoTitle head="转入营业部信息" />
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="转入营业部"
              value={departmentName}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="所属分公司"
              value={branchName}
            />
          </div>
        </div>
        <div className={styles.tableModule}>
          <InfoTitle head="客户列表" />
          {/* <RemindOfYGT
            hasYGT={hasYGT}
            numberOfYGT={numberOfYGT}
          /> */}
          <div className={styles.line}>
            <CommonTable
              columns={this.columns}
              dataSource={list}
              rowKey="custId"
              pagination={false}
              useNewUI
            />
            <Pagination
              {...paginationOption}
            />
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="划转原因" />
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="划转原因"
              value={reason}
            />
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="附件" />
          <div className={styles.line}>
            <Uploader
              key={attachment}
              disabled
              removeable={false}
              defaultFileList={attachmentList}
            />
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="拟稿信息" />
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="拟稿人"
              value={drafter}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="申请时间"
              value={time.format(createTime)}
            />
          </div>
          <div className={styles.line}>
            <InfoItem
              className={styles.detailInfoItem}
              label="状态"
              value={statusName}
            />
          </div>
        </div>
        <div className={styles.detailModule}>
          <InfoTitle head="审批记录" />
          <div className={styles.line}>
            <FlowHistory data={historyFlow} />
          </div>
        </div>
      </div>
    );
  }
}
