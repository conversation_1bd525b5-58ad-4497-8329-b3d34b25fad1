/**
 * @Author: li-ke
 * @Description: 一柜通提醒栏 一柜通客户数提醒
 * @Date: 2019-09-26 15:39:25
 * @Last Modified by: li-ke
 * @Last Modified time: 2019-09-26 19:53:38
 */


import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@/components/common/Icon';

import styles from './remindOfYGT.less';

export default function RemindOfYGT(props) {
  const {
    hasYGT,
    numberOfYGT,
  } = props;

  return (
    <div>
      {
        hasYGT
          ? (
            <div className={styles.remind}>
              <Icon className={styles.tixing} type="tixing" />
              一柜通客户数:
              <span className={styles.numberOfYGT}>{numberOfYGT}</span>
              , 划转客户所产生贡献将归属转入营业部
            </div>
          )
          : null
      }
    </div>
  );
}

RemindOfYGT.propTypes = {
  hasYGT: PropTypes.bool.isRequired,
  numberOfYGT: PropTypes.number.isRequired,
};
