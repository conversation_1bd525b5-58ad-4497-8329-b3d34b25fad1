/**
 * @Description: 期权佣金调整申请-工具项
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by:  yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 */
import { emp } from '@aorta-pc/utils';
import _ from 'lodash';
import { ImagePreviewTypes } from './config';

// 校验附件信息格式
export function validFileFormat(file) {
  const { name } = file;
  const fileRex = /^.*\.(pdf|doc|docx|xlsx|xls|7z|zip|rar|jpg|png|jpeg)$/i;
  return fileRex.test(name);
}

// 客户确认结果pdf文件预览处理
export function viewCustConfirmPdf(fileInfo) {
  const downloadUrl = fileInfo?.downloadURL || '';
  const host = window.location.origin;
  const ifShowWaterMark = true;
  const waterMarkText = `内部资料 请勿外传  (${emp.getName()} ${emp.getId()}) `;
  const query = `showWaterMark=${ifShowWaterMark}&waterMarkTextParam=${waterMarkText}`;
  const pdrviewerUrl = `${host}/fspa/pdfPreview.html#/`;
  const params = `link=${host}/fspa${downloadUrl}&${query}`;
  const url = `${pdrviewerUrl}?${encodeURIComponent(params)}`;
  window.open(
    url,
    '_blank'
  );
}

// 查看pdf预览
export function viewPdf(fileInfo) {
  const { attachId } = fileInfo;
  const host = window.location.origin;
  const ifShowWaterMark = true;
  const waterMarkText = `内部资料 请勿外传  (${emp.getName()} ${emp.getId()}) `;
  const query = `showWaterMark=${ifShowWaterMark}&waterMarkTextParam=${waterMarkText}`;
  const pdrviewerUrl = `${host}/fspa/pdfPreview.html#/`;
  const params = `link=${host}/fspa/mcrm/api/storage/download?fileId=${attachId}&${query}`;
  const url = `${pdrviewerUrl}?${encodeURIComponent(params)}`;
  window.open(
    url,
    '_blank'
  );
}

// 获取部门
export function transformCustRangeData(list) {
  return list.map((item) => {
    const obj = {
      label: item.name,
      value: item.id,
      key: item.id,
    };
    if (item.children && item.children.length) {
      obj.children = transformCustRangeData(item.children);
    }
    return obj;
  });
}

// 需要预览的文件类型
export function needPreviewFileType(file) {
  const { name } = file;
  if (_.isEmpty(ImagePreviewTypes)) {
    return false;
  }
  const startNumber = name?.lastIndexOf('.') + 1;
  const type = name?.substring(startNumber) || '';
  return _.includes(ImagePreviewTypes, type);
}
