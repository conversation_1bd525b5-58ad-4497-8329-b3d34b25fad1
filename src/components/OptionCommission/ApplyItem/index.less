@import "../../../css/util.less";
.applyItemWarp {
  padding: 14px 20px;
  max-height: 72px;
  background-color: #fff;
  cursor: pointer;
  box-sizing: border-box;
  border-bottom: 1px solid #e9e9e9;

  &:hover {
    background-color: #eaeef1;
  }

  .itemHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 18px;
    line-height: 18px;
  }

  .custName {
    width: 60%;
    font-weight: bold;
    color: #333;
    font-size: 14px;
    word-break: break-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .tagArea {
    display: inline-block;
    height: 18px;
    text-align: center;
    line-height: 18px;
    padding: 0;
    font-size: 12px;
    border: none;
    margin: 0;
    border-radius: 2px;

    span {
      width: 50px;
    }

    .status {
      width: 56px;
      height: 18px;
      border-radius: 2px;
      margin-right: 11px;
      line-height: 18px;
      padding: 0 4px;
      text-align: center;
    }

    .success {
      .status;

      color: #00a985;
      border: 1px solid rgba(0, 169, 133, 0.4);
    }

    .fail {
      .status;

      color: #f3444a;
      border: 1px solid #fbb2b2;
    }
  }

  .secondLine {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: #aaa;
  }

  .secondItem {
    font-size: 12px;
    color: #999;
    line-height: 18px;

    .value {
      color: #333;
    }
  }

  &.active {
    background-color: #4897f1;

    &:hover {
      background-color: #4897f1;
    }

    .custName {
      color: #fff;
    }

    .secondItem {
      color: #fff;

      .value {
        color: #fff;
      }
    }

    .tagArea {
      span {
        background-color: #fff;
        color: #108ee9;
        border-color: #fff;
      }
    }
  }
}
