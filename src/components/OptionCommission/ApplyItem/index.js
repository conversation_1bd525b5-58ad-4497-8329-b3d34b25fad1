/**
 * @Description: 期权佣金调整申请列表-申请项
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by:  yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 */

import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import cx from 'classnames';
import Tag from '@/components/common/tag';

import { FlowStatusConfig, OrderStatusCode } from '../config';
import styles from './index.less';

const StatusTag = Tag.statusTag;

function ApplyItem(props) {
  const {
    data,
    active,
    onClick,
  } = props;

  const {
    custId = '',
    custName = '',
    creatorId = '',
    creatorName = '',
    status = '',
    orderStatus = '',
    orderStatusName = '',
    createTime = '--',
  } = data || {};

  const applyItemCls = cx({
    [styles.applyItemWarp]: true,
    [styles.active]: active
  });

  // 客户名称（经纪客户号）
  const custText = `${custName}(${custId})`;
  // 创建人（工号
  const creatorText = `${creatorName}(${creatorId})`;
  // 流程状态
  const statusObj = !_.isEmpty(status)
    ? _.find(FlowStatusConfig, (item) => item.code === status) : {};
  // 渲染调佣状态
  const renderOrderStatus = () => (
    !_.isEmpty(orderStatus) && _.includes(OrderStatusCode, orderStatus)
      ? (
        <span className={styles[`${orderStatus}`]}>
          {orderStatusName}
        </span>
      ) : null
  );

  return (
    <div className={applyItemCls} onClick={onClick}>
      {/* 第一行 */}
      <div className={styles.itemHeader}>
        <div className={styles.custName} title={custText}>
          {custText}
        </div>
        <div className={styles.tagArea}>
          {renderOrderStatus()}
          <StatusTag
            type={statusObj?.type}
            text={statusObj?.text}
          />
        </div>
      </div>
      {/* 第二行 */}
      <div className={styles.secondLine}>
        <div className={styles.secondItem}>
          创建者：
          <span className={styles.value} title={creatorText}>
            {creatorText}
          </span>
        </div>
        <div className={styles.secondItem}>
          <span className={styles.value}>{createTime}</span>
        </div>
      </div>
    </div>
  );
}

ApplyItem.propTypes = {
  // 申请单项的基本数据
  data: PropTypes.object.isRequired,
  // 点击申请项触发事件
  onClick: PropTypes.func.isRequired,
  // 是否选中状态,选中状态需要展示镂空样式
  active: PropTypes.bool,
};

ApplyItem.defaultProps = {
  active: false,
};

export default React.memo(ApplyItem);
