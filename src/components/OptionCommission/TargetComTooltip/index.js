/*
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by: yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 * @description 期权佣金申请-目标佣金率-提示ToolTip组件
 */

import React from 'react';
import PropTypes from 'prop-types';
import { Tooltip } from 'antd';
import infoIcon from '@/components/OptionCommission/static/info.svg';

import styles from './index.less';

function TargetComTooltip(props) {
  return (
    <div className={styles.targetComToolWrap}>
      <Tooltip
        placement="topLeft"
        trigger="click"
        overlayClassName={styles.tooltipWrap}
        onVisibleChange={props.onClick}
        title={(
          <ul className={styles.popupContent}>
            <li>佣金档说明</li>
            <li>
              调整到的佣金档按X元/张，是指买开、卖平、买平收取佣金X-0.3元，结算费0.3元；主动行权方收取佣金X-0.6元，
              结算费0.6元；被行权方收取佣金X元；如果经手费、结算费调整，总费用相应调整。
            </li>
            <li>由于目前交易所和结算公司对卖出开仓暂免收一切费用，公司佣金设置中对卖出开仓也不收任何费用，如果交易所和结算公司恢复收取，公司将按和买开相同的标准收取费用。</li>
            <li>公司对投资者沪市和深市期权交易收取的佣金标准保持一致。</li>
          </ul>
        )}
      >
        <div className={styles.actionWarp}>
          <img src={infoIcon} className={styles.infoIcon} />
        </div>
      </Tooltip>
    </div>
  );
}

TargetComTooltip.propTypes = {
  // 点击目标佣金率配置说明事件
  onClick: PropTypes.func.isRequired,
};

export default React.memo(TargetComTooltip);
