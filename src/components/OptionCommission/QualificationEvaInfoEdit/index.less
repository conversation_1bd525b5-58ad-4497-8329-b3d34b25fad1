.qualificatWarp {
  margin: 30px 0 0 14px;

  /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
    .ant-table-expand-icon-th,
    .ant-table-row-expand-icon-cell {
      width: 0;
    }

    .ant-table-row-expand-icon {
      display: none;
    }

    table>tbody>tr>td, table>thead>tr>th {
      height: 54px;
      max-height: 80px;
    }

    tr.ant-table-expanded-row {
      background-color: #fff;
    }

    .ant-form-item {
      padding: 0 20px !important;
    }

    .ant-form-item-label {
      height: auto !important;
      width: auto !important;
    }

    .ant-form-item-control-wrapper {
      width: 100%;
    }

    .ant-form-item-control {
      height: auto !important;
      line-height: 30px !important;
    }

    .ant-input {
      border-radius: 2px;
      width: 100% !important;
      height: 30px;
    }

    .ant-radio-wrapper {
      margin-right: 20px;
    }

    .ant-form-item-with-help {
      margin-top: 12px;
    }
  }

  .selectResult {
    padding: 0 20px 0 0 !important;
  }

  .tableTitle {
    margin-bottom: 14px;
    height: 20px;
    font-size: 14px;
    font-weight: bold;
    color: #666;
    line-height: 20px;
  }

  .tableWarp {
    padding: 0 !important;
  }

  .remark {
    margin-top: 20px;
    padding: 0 0 0 20px !important;
  }
}