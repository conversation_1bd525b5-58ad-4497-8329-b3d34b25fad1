/**
 * @description 期权佣金调整-资质评估编辑组件
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by:  yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { autobind } from 'core-decorators';
import {
  Radio,
  Form,
  Input,
} from 'antd';

import Table from '@/components/common/table';
import { QualificationTableConfig } from '../config';

import styles from './index.less';

const FormItem = Form.Item;
const { TextArea } = Input;

class QualificationEvaInfoEdit extends PureComponent {
  static propTypes = {
    // 表单
    form: PropTypes.object.isRequired,
    // 资质评估表单数据
    dataSource: PropTypes.array,
    // 备注
    remark: PropTypes.string,
    // 选择评估结果回调事件
    onChangeRadio: PropTypes.func,
  }

  static defaultProps = {
    dataSource: [],
    remark: '',
    onChangeRadio: _.noop,
  }

  constructor(props) {
    super(props);
    this.columns = this.getColumns(QualificationTableConfig);
  }

  // 资质评估表格中，选择评估结果
  @autobind
  handleChangeRadio(e, record) {
    const { dataSource, form } = this.props;
    const value = e?.target?.value;
    // 修改当前行的评估结果
    // 1、遍历渲染资质评估列表
    // 2、通过id匹配到对应行
    // 3、设置该行的评估结果、并将说明置为空
    // 4、将修改后的资质评估列表更新到form表单
    if (!_.isEmpty(value)) {
      const data = _.map(dataSource, (item) => {
        if (item?.rowId === record?.rowId) {
          return {
            ...item,
            evaluationResult: value,
            description: ''
          };
        }
        return item;
      });
      form.setFieldsValue({ qualificationEvaInfo: data });
    }
    this.props.onChangeRadio(value, record);
  }

  @autobind
  updateResultColumn(column) {
    const { form } = this.props;
    return ({
      ...column,
      render: (text, record) => (
        <FormItem className={styles.selectResult}>
          {form.getFieldDecorator(`${record?.rowId}-result`, {
            initialValue: record?.evaluationResult,
            rules: [
              { required: true, message: '请选择评估结果' },
            ],
          })(
            <Radio.Group
              onChange={(e) => this.handleChangeRadio(e, record)}
            >
              <Radio value="Y">是</Radio>
              <Radio value="N">否</Radio>
            </Radio.Group>
          )}
        </FormItem>
      )
    });
  }

  @autobind
  getColumns(columns) {
    return _.map(columns, (column) => {
      const { dataIndex } = column;

      // 渲染评估结果列
      if (dataIndex === 'evaluationResult') {
        return this.updateResultColumn(column);
      }

      return column;
    });
  }

  // 资质评估表格中，输入评估结果-说明
  @autobind
  handleChangeInput(e, id) {
    const { dataSource, form } = this.props;
    // 修改当前行的说明
    // 1、遍历渲染资质评估列表
    // 2、通过id匹配到对应行
    // 3、设置该行的说明内容
    // 4、将修改后的资质评估列表更新到form表单
    const data = _.map(dataSource, (item) => {
      if (item?.rowId === id) {
        return {
          ...item,
          description: e?.target?.value
        };
      }
      return item;
    });
    form.setFieldsValue({ qualificationEvaInfo: data });
  }

  @autobind
  expandedRowRender(record) {
    if (record?.evaluationResult === 'N') {
      const { form } = this.props;
      return (
        <FormItem label="说明">
          {form.getFieldDecorator(`${record?.rowId}-desc`, {
            initialValue: record?.description,
            rules: [
              { required: true, message: '请输入说明' },
            ],
          })(
            <Input
              onChange={(e) => this.handleChangeInput(e, record?.rowId)}
              autoComplete="off"
              maxLength={100}
              placeholder="请输入说明，最多100字"
            />
          )}
        </FormItem>
      );
    }
    return null;
  }

  @autobind
  expandedKeys() {
    const { dataSource } = this.props;
    if (_.isEmpty(dataSource)) {
      return [];
    }
    const expandedRows = [];
    _.map(dataSource, (item) => {
      if (item?.evaluationResult === 'N') {
        expandedRows.push(item?.rowId);
      }
    });
    return expandedRows;
  }

  render() {
    const { form, dataSource, remark } = this.props;
    const { getFieldDecorator } = form;
    return (
      <div className={styles.qualificatWarp}>
        <div className={styles.tableTitle}>资质评估</div>
        <FormItem className={styles.tableWarp}>
          {getFieldDecorator('qualificationEvaInfo')(
            <Table
              dataSource={dataSource}
              columns={this.columns}
              rowKey="rowId"
              expandedRowRender={this.expandedRowRender}
              expandedRowKeys={this.expandedKeys()}
              useNewUI
              pagination={false}
            />
          )}
        </FormItem>
        <FormItem
          label="备注"
          className={styles.remark}
        >
          {getFieldDecorator('remark', {
            initialValue: remark
          })(
            <TextArea
              autoComplete="off"
              maxLength={100}
              placeholder="请输入备注，最多100字"
            />
          )}
        </FormItem>
      </div>
    );
  }
}

export default QualificationEvaInfoEdit;
