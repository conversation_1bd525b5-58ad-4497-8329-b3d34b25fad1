/*
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by: yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 * @description 期权佣金申请-面包屑组件
 */

import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import cx from 'classnames';
import { Button } from 'antd';
import { sensors } from '@lego/bigbox-utils';
import IFWrap from '@/components/common/IFWrap';
import optionCommissionNotePdf from '@/components/OptionCommission/static/optionCommissionNote.pdf';

import styles from './index.less';

const { logCommon } = sensors;

const handleDownLoadTemplate = () => {
  logCommon({
    type: 'Click',
    payload: {
      name: '期权佣金申请-《股票期权投资者佣金调整说明文档》下载',
    },
  });
};
function Breadcrum(props, context) {
  const {
    crumbItems,
    isCreate,
    onClick,
    showNoteDownload,
  } = props;
  if (_.isEmpty(crumbItems)) {
    return null;
  }

  return (
    <div className={styles.crumbWrap}>
      <div className={styles.textMenu}>
        {_.map(crumbItems, (item, index) => {
          const islastCrumb = index === crumbItems.length - 1;
          const itemCls = cx({
            [styles.crumbName]: true,
            [styles.clickable]: item?.clickable,
          });

          if (islastCrumb) {
            return (
              <span key={item?.name} className={styles.blackMenu}>
                {item?.name}
              </span>
            );
          }
          return (
            <span
              key={item?.name}
              onClick={item?.clickable ? context.goBack : _.noop}
              className={itemCls}
            >
              <span className={styles.preMenu}>{item?.name}</span>
              <span className={styles.slash}>/</span>
            </span>
          );
        })}
      </div>
      <div className={styles.rightArea}>
        <IFWrap when={showNoteDownload}>
          <a
            className={styles.downloadLink}
            href={optionCommissionNotePdf}
            onClick={handleDownLoadTemplate}
            download="股票期权投资者佣金调整说明文档.pdf"
          >
            期权佣金调整说明
          </a>
        </IFWrap>
        <IFWrap when={isCreate}>
          <div className={styles.createBtn}>
            <Button type="primary" icon="plus" onClick={onClick}>新建</Button>
          </div>
        </IFWrap>
      </div>

    </div>
  );
}

Breadcrum.propTypes = {
  crumbItems: PropTypes.array.isRequired,
  isCreate: PropTypes.bool,
  onClick: PropTypes.func,
  // 是否需要展示《股票期权投资者佣金调整说明文档》
  showNoteDownload: PropTypes.bool,
};

Breadcrum.defaultProps = {
  isCreate: false,
  onClick: _.noop,
  showNoteDownload: false,
};

Breadcrum.contextTypes = {
  goBack: PropTypes.func.isRequired,
};

export default Breadcrum;
