/**
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by:  yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 * @Description: 期权佣金调整申请列表-头部过滤器
 */
import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import _ from 'lodash';

import DateRangePick from '@lego/date/src';
import TreeFilter from '@lego/treeSelect/src';
import { SingleFilter } from '@lego/filters/src';
import { logCommon } from '@/decorators/logable';

import {
  DropdownStyleConfig,
  FlowStatusConfig,
  AdjustCommissionWayConfig,
  OrderStatusConfig,
  DateFormat,
  DEFAULT_OPTION
} from '../config';
import { transformCustRangeData } from '../utils';
import styles from './index.less';

function HeaderFilter(props) {
  const {
    location: { query },
    custList,
    custRange,
  } = props;
  const {
    custId = '',
    custName = '',
    status = '',
    orderStatus = '',
    adjustCommissionWay = '',
    createTime = '',
    createTimeTo = '',
    orgId
  } = query || {};

  // 申请时间-开始时间
  const startDate = createTime ? moment(createTime, DateFormat) : null;
  // 申请时间-结束时间
  const endDate = createTimeTo ? moment(createTimeTo, DateFormat) : null;
  // 客户信息
  const customer = custId ? [custId, custName] : ['', ''];

  const handleCustChange = (option) => {
    const { value } = option;
    logCommon({
      type: 'Click',
      payload: {
        name: '期权佣金申请列表-客户姓名搜索',
        value: value || '',
      },
    });
    props.filterCallback({
      custId: value?.custNumber,
      custName: value?.custName,
    });
  };

  const handleChangeAdjustCommissionWay = (option) => {
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '期权佣金申请列表-调佣方式',
        value: option?.value,
      },
    });
    props.filterCallback({
      adjustCommissionWay: option?.value || '',
    });
  };

  const handleChangeStatus = (option) => {
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '期权佣金申请列表-流程状态',
        value: option?.value,
      },
    });
    props.filterCallback({
      status: option?.value || '',
    });
  };

  const handleChangeOrderStatus = (option) => {
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '期权佣金申请列表-调佣状态',
        value: option?.value,
      },
    });
    props.filterCallback({
      orderStatus: option?.value || '',
    });
  };

  const handleChangeAprovalTime = (date) => {
    const { value } = date;
    const start = value[0] || '';
    const end = value[1] || '';
    logCommon({
      type: 'CalendarSelect',
      payload: {
        name: '期权佣金申请列表-申请时间',
        value: {
          min: start,
          max: end,
        }
      },
    });
    props.filterCallback({
      createTime: start,
      createTimeTo: end,
    });
  };

  const setDisableStart = (start, end) => (start && end) && (end < start);

  const getTreeData = () => {
    if (_.isEmpty(custRange)) {
      return [DEFAULT_OPTION];
    }
    const treeCustRange = transformCustRangeData(custRange);
    return [DEFAULT_OPTION, ...treeCustRange];
  };

  const handleSelectDepartment = (value) => {
    logCommon({
      type: 'CalendarSelect',
      payload: {
        name: '期权佣金申请列表-部门',
        value: value || ''
      },
    });
    props.filterCallback({
      orgId: value,
    });
  };

  return (
    <div className={styles.filterArea}>
      <div className={styles.filterItem}>
        <SingleFilter
          filterId="custId"
          filterName="客户"
          placeholder="经纪客户号或客户名称"
          dataMap={['custId', 'custName']}
          data={custList}
          value={customer}
          onChange={handleCustChange}
          onInputChange={props.onCustSearch}
          dropdownStyle={DropdownStyleConfig}
          showSearch
          needItemObj
        />
      </div>
      <div className={styles.filterItem}>
        <SingleFilter
          filterId="adjustCommissionWay"
          filterName="调佣方式"
          dataMap={['value', 'label']}
          data={AdjustCommissionWayConfig}
          value={adjustCommissionWay}
          onChange={handleChangeAdjustCommissionWay}
        />
      </div>
      <div className={styles.filterItem}>
        <SingleFilter
          filterId="status"
          filterName="流程状态"
          dataMap={['code', 'text']}
          data={FlowStatusConfig}
          value={status}
          onChange={handleChangeStatus}
        />
      </div>
      <div className={styles.filterItem}>
        <SingleFilter
          filterId="orderStatus"
          filterName="调佣状态"
          dataMap={['value', 'label']}
          data={OrderStatusConfig}
          value={orderStatus}
          onChange={handleChangeOrderStatus}
        />
      </div>
      <div className={styles.filterItem}>
        <DateRangePick
          filterId="aprovalTime"
          filterName="申请时间"
          filterValue={[startDate, endDate]}
          onChange={handleChangeAprovalTime}
          disabledStart={setDisableStart}
          disabledEnd={setDisableStart}
          allowClear
          startDateProps={{ placeholder: '开始日期' }}
          endDateProps={{ placeholder: '结束日期' }}
        />
      </div>
      <div className={styles.filterItem}>
        <TreeFilter
          key="id"
          filterId="id"
          filterName="部门"
          searchPlaceholder="搜索"
          value={orgId || ''}
          treeData={getTreeData()}
          onSelect={handleSelectDepartment}
          showSearch
          treeDefaultExpandAll
        />
      </div>
    </div>
  );
}

HeaderFilter.propTypes = {
  // 路由地址信息
  location: PropTypes.object,
  // 头部筛选回调
  filterCallback: PropTypes.func.isRequired,
  // 搜索客户结果信息
  custList: PropTypes.array,
  // 搜索客户
  onCustSearch: PropTypes.func.isRequired,
  // 部门信息
  custRange: PropTypes.array,
};

HeaderFilter.defaultProps = {
  location: {},
  custList: [],
  custRange: [],
};

export default React.memo(HeaderFilter);
