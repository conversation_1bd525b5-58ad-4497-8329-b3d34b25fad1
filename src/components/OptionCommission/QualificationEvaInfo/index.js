/**
 * @description 期权佣金调整-资质评估
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by:  yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { autobind } from 'core-decorators';
import Table from '@/components/common/table';

import { QualificationTableConfig, EvaResultCode } from '../config';
import styles from './index.less';

class QualificationEvaInfo extends PureComponent {
  static propTypes = {
    // 资质评估数据信息
    dataSource: PropTypes.array,
  }

  static defaultProps = {
    dataSource: []
  }

  constructor(props) {
    super(props);
    this.columns = this.getColumns(QualificationTableConfig);
  }

  @autobind
  updateResultColumn(column) {
    return ({
      ...column,
      render: (text, record) => {
        if (_.isEmpty(text)) {
          return '--';
        }
        return EvaResultCode[text];
      }
    });
  }

  @autobind
  getColumns(columns) {
    return _.map(columns, (column) => {
      const { dataIndex } = column;
      if (dataIndex === 'evaluationResult') {
        return this.updateResultColumn(column);
      }
      return column;
    });
  }

  @autobind
  expandedKeys() {
    const { dataSource } = this.props;
    if (_.isEmpty(dataSource)) {
      return [];
    }
    const expandedRows = [];
    _.map(dataSource, (item, index) => {
      if (item?.evaluationResult === 'N') {
        expandedRows.push(index);
      }
    });
    return expandedRows;
  }

  render() {
    const { dataSource } = this.props;
    return (
      <div className={styles.tableWarp}>
        <div className={styles.tableTitle}>资质评估</div>
        <Table
          dataSource={dataSource}
          columns={this.columns}
          expandedRowRender={(record) => (
            <div className={styles.description}>说明：{record?.description || '--'}</div>
          )}
          expandedRowKeys={this.expandedKeys()}
          useNewUI
          pagination={false}
        />
      </div>
    );
  }
}
export default QualificationEvaInfo;
