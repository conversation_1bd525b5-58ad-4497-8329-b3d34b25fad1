/**
 * @Description: 期权佣金调整申请-配置项
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by:  yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 */
// 流程状态枚举值配置
export const FlowStatusConfig = [
  {
    code: '',
    type: '',
    text: '不限',
  },
  {
    code: '01',
    text: '处理中',
    type: 'processing',
  },
  {
    code: '04',
    text: '驳回',
    type: 'reject',
  },
  {
    code: '02',
    text: '完成',
    type: 'complete',
  },
  {
    code: '03',
    text: '终止',
    type: 'stop',
  },
];

// 调佣方式枚举值配置
export const AdjustCommissionWayConfig = [
  {
    label: '不限',
    value: '',
  },
  {
    label: '线上',
    value: 'ONLINE',
  },
  {
    label: '线下',
    value: 'OFFLINE',
  },
];

// 调佣状态-枚举值配置
export const OrderStatusConfig = [
  {
    label: '不限',
    value: '',
  },
  {
    label: '调佣成功',
    value: 'success',
  },
  {
    label: '调佣失败',
    value: 'fail',
  },
  {
    label: '佣金待处理',
    value: 'pending',
  },
];

// 资质评估配置表格配置项
export const QualificationTableConfig = [
  {
    dataIndex: 'evaluationEle',
    key: 'evaluationEle',
    title: '评估要素',
    width: 84,
  },
  {
    dataIndex: 'requirement',
    key: 'requirement',
    title: '资质要求',
    width: 336,
  },
  {
    dataIndex: 'evaluationResult',
    key: 'evaluationResult',
    title: '评估结果',
    width: 154,
  },
];

// 资质评估-表格默认数据
export const QualificationDefaultData = [
  {
    evaluationEle: '诚信评估',
    requirement: '无不良诚信记录，未发生过强行平仓等违规事件',
    evaluationResult: '',
    rowId: 1,
  },
  {
    evaluationEle: '交易风格评估',
    requirement: '交易风格是否为频繁交易类型、是否具备一定的交易量',
    evaluationResult: '',
    rowId: 2,
  },
  {
    evaluationEle: '资产规模评估',
    requirement: '是否为优质客户托管资产规模、是否达到一定标准',
    evaluationResult: '',
    rowId: 3
  },
];

// 个人客户的附件信息配置
export const ArrachmentTypesPer = [
  {
    key: 'optionApplyAttachId',
    name: '股票期权投资者佣金调整申请',
    saveKey: 'optionApplyAttachment',
  },
  {
    key: 'otherAttachId',
    name: '其他',
    saveKey: 'otherAttachment',
  }
];

// 机构客户的附件信息配置
export const ArrachmentTypesOrg = [
  {
    key: 'optionApplyAttachId',
    name: '股票期权投资者佣金调整申请',
    saveKey: 'optionApplyAttachment',
  },
  {
    key: 'orgLetterAttorAttachId',
    name: '机构投资者授权委托书（期权佣金）',
    saveKey: 'orgLetterAttorAttachment',
  },
  {
    key: 'otherAttachId',
    name: '其他',
    saveKey: 'otherAttachment',
  }
];

// 产品客户的附件信息配置
export const ArrachmentTypesPro = [
  {
    key: 'optionApplyAttachId',
    name: '股票期权投资者佣金调整申请',
    saveKey: 'optionApplyAttachment',
  },
  {
    key: 'orgLetterAttorAttachId',
    name: '机构投资者授权委托书（期权佣金）',
    saveKey: 'orgLetterAttorAttachment',
  },
  {
    key: 'proRecCertificateAttachId',
    name: '产品备案证明',
    saveKey: 'proRecCertificateAttachment'
  },
  {
    key: 'proContractAttachId',
    name: '产品合同',
    saveKey: 'proContractAttachment'
  },
  {
    key: 'otherAttachId',
    name: '其他',
    saveKey: 'otherAttachment',
  }
];

// 下拉框样式配置
export const DropdownStyleConfig = {
  maxHeight: 324,
  overflowY: 'auto',
  width: 250,
};
// 调佣状态配置-调佣成功、调佣失败
export const OrderStatusCode = ['success', 'fail'];
// 评估结果code配置
export const EvaResultCode = { Y: '是', N: '否' };
// 日期格式化配置
export const DateFormat = 'YYYY/MM/DD';
// 附件信息支持上传格式
export const AttachAccept = '.pdf,.doc,.docx,.xlsx,.xls,.7z,.zip,.rar,.jpg,.png,.jpeg';
// 默认值
export const DefaultValue = '--';
// 下一步审批节点名称配置-不需要展示审批人弹框的
export const NextNodeNames = ['待发起人办结', '待提交分公司衍生品审核岗审批', '待提交衍生品经纪业务部审核岗审批', '待客户确认'];
// 需要二次确认弹框的当前审批节点
export const CurrentStepNames = ['衍生品经纪业务部审核岗审批', '衍生品经纪业务部负责人审批'];
// 预览图片类型
export const ImagePreviewTypes = ['png', 'jpg', 'jpeg'];
// 部门-默认下拉选项配置
export const DEFAULT_OPTION = {
  label: '不限',
  value: '',
  key: 0,
};
// 客户服务中心部门orgId 目前固定为ZZ323404
export const CustServiceCenterOrgId = 'ZZ323404';
// 审批办结节点名称
export const CompletionNodeName = '待发起人办结';
