/**
 * @description 期权佣金调整申请-选择客户组件
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by:  yanfaping
 * @Last Modified time: 2023-04-26 14:15:36
 */
import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import confirm from '@/components/common/newUI/confirm';
import AutoComplete from '@/components/common/similarAutoComplete';

// 选择菜单自定义样式
const DropdownStyle = {
  width: 228,
  maxHeight: 400,
  overflow: 'auto'
};

function CustSelect(props) {
  const { custList } = props;
  const custSearchRef = useRef(null);

  // 清空搜索框
  const clearCustValue = () => custSearchRef?.current?.clearValue();

  // 验证失败提示弹框展示
  const validateFailure = (content) => confirm({
    title: '提示',
    content,
    onOk: clearCustValue,
    cancelVisible: false,
  });

  // 校验所选客户信息
  async function validateCustInfo(item) {
    const result = await props.validateCustomer({
      custId: item?.custId,
    });
    if (_.isEmpty(result)) {
      return;
    }
    if (result?.openOptAccFlag === 'N') {
      validateFailure('客户未开通期权账户，无法进行佣金设置');
      return;
    }
    if (result?.hasOrder === 'Y') {
      validateFailure('当前客户有未办结期权佣金申请流程，不可再发起流程');
      return;
    }
    if (result?.riskRt === 'N') {
      validateFailure('客户风险测评已过期，需更新风险测评！');
      return;
    }
    props.onSelect({
      ...item,
      naturalPersonFlag: result?.naturalPersonFlag
    });
  }

  const handleCustSelect = (item) => {
    if (!_.isEmpty(item)) {
      validateCustInfo(item);
    } else {
      props.onClearCust();
    }
  };

  const handleCustSearch = (keyword) => {
    props.queryCustList(keyword);
  };

  return (
    <AutoComplete
      placeholder="经纪客户号/客户名称"
      showNameKey="custName"
      showIdKey="custId"
      optionKey="custId"
      optionList={custList}
      onSelect={handleCustSelect}
      onSearch={_.debounce(handleCustSearch, 250)}
      dropdownMatchSelectWidth={false}
      isImmediatelySearch
      ref={custSearchRef}
      dropdownStyle={DropdownStyle}
    />
  );
}

CustSelect.propTypes = {
  // 客户列表数据
  custList: PropTypes.array,
  // 选择客户回调
  onSelect: PropTypes.func.isRequired,
  // 模糊查询客户
  queryCustList: PropTypes.func.isRequired,
  // 校验客户信息
  validateCustomer: PropTypes.func.isRequired,
  // 清除客户信息
  onClearCust: PropTypes.func.isRequired,
};

CustSelect.defaultProps = {
  custList: [],
};

export default React.memo(CustSelect);
