.detailWarp {
  padding-bottom: 50px;

  .title {
    font-size: 15px;
    color: #333;
    line-height: 20px;
    text-align: left;
    font-weight: bold;
    padding: 21px 0 13px;
    border-bottom: 1px solid #ddd;

    .tipText {
      position: relative;
      display: inline-block;
      text-indent: 20px;
      font-size: 14px;
      color: #f0af41;
      margin-left: 10px;
      font-weight: normal;

      .warningIcon {
        position: absolute;
        left: -8px;
        top: -3px;
        width: 28px;
        height: 28px;
        margin-right: 5px;
      }
    }
  }

  /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
    .ant-uploader {
      padding-left: 14px;
    }
  }

  .success {
    color: #00a985;
  }

  .fail {
    color: #f3444a;
  }

  .pending {
    color: #108ee9;
  }
}