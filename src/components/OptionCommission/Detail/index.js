/**
 * @description 期权佣金调整申请-详情
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by:  yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { autobind } from 'core-decorators';
import cx from 'classnames';
import {
  DetailContainer,
  DetailBlock,
  ApprovalHistory,
  InfoGroup,
  InfoCell,
} from '@crm/biz-ui';
import { time, number } from '@/helper';
import Upload from '@/newUI/storageUpload';
import IFWrap from '@/components/common/IFWrap';
import logable, { logCommon } from '@/decorators/logable';
import ImagePreviewModal from '@/newUI/ImagePreviewModal';
import warningIcon from '../static/warning.svg';

import {
  ArrachmentTypesPer,
  ArrachmentTypesOrg,
  ArrachmentTypesPro,
  DefaultValue,
  ImagePreviewTypes,
} from '../config';
import { viewCustConfirmPdf, viewPdf, needPreviewFileType } from '../utils';
import QualificationEvaInfo from '../QualificationEvaInfo';
import styles from './index.less';

class Detail extends PureComponent {
  static propTypes = {
    // 详情信息
    detailData: PropTypes.object.isRequired,
    // 附件信息
    attachmentInfo: PropTypes.object.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      imageUrl: '',
    };
  }

  @autobind
  getAttachTypeList(custType) {
    switch (custType) {
      case 'per':
        return ArrachmentTypesPer;
      case 'org':
        return ArrachmentTypesOrg;
      case 'prod':
        return ArrachmentTypesPro;
      default:
        return ArrachmentTypesPer;
    }
  }

  @autobind
  handleViewPdf(fileInfo, name) {
    logCommon({
      type: 'Click',
      payload: {
        name: `期权佣金申请详情-${name}PDF预览`,
        value: JSON.stringify(fileInfo)
      },
    });
    if (needPreviewFileType(fileInfo)) {
      const host = window.location.origin;
      const url = `${host}/fspa/mcrm/api/storage/download?fileId=${fileInfo?.attachId}`;
      this.setState({
        imageUrl: url
      });
      return;
    }
    viewPdf(fileInfo);
  }

  @autobind
  handleViewCustConfirmPdf(fileInfo) {
    logCommon({
      type: 'Click',
      payload: {
        name: '期权佣金申请详情-股票期权投资者佣金调整申请PDF预览',
        value: JSON.stringify(fileInfo)
      },
    });
    return viewCustConfirmPdf(fileInfo);
  }

  @autobind
  getAttachDownloadURL(v) {
    return `/fspa${v}`;
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '期权佣金申请详情-下载附件',
    },
  })
  handleDownLoad() {}

  @autobind
  renderAttachInfo(custType, isOnline) {
    const { attachmentInfo, detailData } = this.props;
    const attachmentList = this.getAttachTypeList(custType);
    return _.map(attachmentList, ({ key, name }) => {
      // 附件类型为股票期权投资者佣金调整申请
      const isOptionApply = key === 'optionApplyAttachId';
      // 客户类型为个人
      const isPer = custType === 'per';
      // 附件是否展示客户确认附件
      // 1、为‘股票期权投资者佣金调整申请’
      // 2、客户类型为个人
      // 3、调佣方式为线上
      // 满足以上展示客户确认附件
      // 由于客户确认附件是涨乐端回传的附件信息
      // 使用聊Ta端的附件接口获取不到，这里是后端直接将附件信息返回fileInfo，在点击下载、预览的时候直接使用/fspa/downloadUrl
      const custConfirmAttch = (isOptionApply && isPer && isOnline);

      // 附件类型为股票期权投资者佣金调整申请 且 客户确认附件为空，不展示该附件
      if (custConfirmAttch && _.isEmpty(detailData?.fileInfo)) {
        return null;
      }

      if (custConfirmAttch) {
        return (
          <Upload
            key={key}
            disabled
            previewable
            removeable={false}
            title={name}
            showTitle
            onViewFile={this.handleViewCustConfirmPdf}
            defaultFileList={detailData?.fileInfo || []}
            getAttachDownloadURL={this.getAttachDownloadURL}
            onDownload={this.handleDownLoad}
          />
        );
      }

      return (
        <Upload
          key={key}
          disabled
          previewable
          removeable={false}
          title={name}
          showTitle
          onViewFile={(fileInfo) => this.handleViewPdf(fileInfo, name)}
          defaultFileList={attachmentInfo[key] || []}
          onDownload={this.handleDownLoad}
          previewTypes={ImagePreviewTypes}
        />
      );
    });
  }

  @autobind
  getAmountFormat(amount) {
    if (_.isNil(amount)) {
      return DefaultValue;
    }
    const numberValue = number.toFixed(amount);
    return number.thousandFormat(_.toNumber(numberValue));
  }

  @autobind
  renderOrderStatus(status, name) {
    return (
      <span className={styles[`${status}`]}>
        {name || DefaultValue}
      </span>
    );
  }

  @autobind
  renderCustConfirmResult(result) {
    const classNames = cx({
      [styles.success]: result === '已确认',
      [styles.pending]: result === '待确认',
      [styles.fail]: result === '已过期',
    });
    return (
      <span className={classNames}>
        {result || DefaultValue}
      </span>
    );
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '期权佣金申请详情-图片预览-关闭',
    },
  })
  handleImagePreviewModalClose() {
    this.setState({
      imageUrl: '',
    });
  }

  render() {
    const { detailData } = this.props;
    const {
      id,
      creatorId,
      creatorName,
      orgName,
      createTime,
      flowHistory = {},
      custInfo = {},
      remark,
      originCom,
      targetCom,
      targetComEffectTime,
      orderStatus,
      orderStatusName,
      adjustCommissionWayCode,
      adjustCommissionWayName,
      custConfirmResult,
    } = detailData || {};

    const {
      custId,
      custName,
      custType,
      custTypeName,
      totalAsset,
      lastSeasonTrans,
      lastSeasonOptTrans,
      lastMonthAvgOptTrans,
      custTotalOptTrans,
      totOptQuotaSh,
      totOptQuotaSz,
      serviceOrgName,
    } = custInfo || {};

    const { imageUrl } = this.state;
    const previewModalVisible = !_.isEmpty(imageUrl);
    // 客户
    const customer = !_.isEmpty(custId) ? `${custName}(${custId})` : DefaultValue;
    // 拟稿人
    const creator = !_.isEmpty(creatorId) ? `${creatorName}(${creatorId})` : DefaultValue;
    // 创建时间
    const timeText = !_.isEmpty(createTime) ? time.format(createTime, 'YYYY-MM-DD HH:mm') : DefaultValue;
    // 是否展示当前审批节点
    const showCurrentNode = _.isEmpty(flowHistory?.currentStepName);
    // 总资产
    const totAsset = this.getAmountFormat(totalAsset);
    // 上季度股基交易量
    const lastSeasonTransText = this.getAmountFormat(lastSeasonTrans);
    // 调佣方式是否为线上
    const isOnline = adjustCommissionWayCode === 'ONLINE';

    return (
      <DetailContainer isEmpty={_.isEmpty(detailData)}>
        <div className={styles.detailWarp}>
          <div className={styles.title}>
            编号{id || DefaultValue}
            <IFWrap when={orderStatus === 'success'}>
              <span className={styles.tipText}>
                <img src={warningIcon} className={styles.warningIcon} />
                调佣设置已完成，佣金模板已匹配，请跟踪客户首笔交易。
              </span>
            </IFWrap>
          </div>
          <DetailBlock title="基本信息">
            <InfoGroup labelWidth="164px">
              <InfoCell
                span={50}
                label="客户"
                content={customer}
              />
              <InfoCell
                span={50}
                label="调佣方式"
                content={adjustCommissionWayName || DefaultValue}
              />
              <InfoCell
                span={50}
                label="调佣状态"
                content={this.renderOrderStatus(orderStatus, orderStatusName)}
              />
              <IFWrap when={isOnline}>
                <InfoCell
                  span={50}
                  labelWidth="164px"
                  label="客户确认结果"
                  content={this.renderCustConfirmResult(custConfirmResult)}
                />
              </IFWrap>
              <InfoCell
                span={50}
                label="客户分类"
                content={custTypeName || DefaultValue}
              />
              <InfoCell
                span={50}
                label="净资产(元)"
                content={totAsset}
              />
              <InfoCell
                span={50}
                label="上季度股基交易量(元)"
                content={lastSeasonTransText}
              />
              <InfoCell
                span={50}
                label="上一季度期权交易量(张)"
                content={this.getAmountFormat(lastSeasonOptTrans)}
              />
              <InfoCell
                span={50}
                label="上月日均期权交易量(张)"
                content={this.getAmountFormat(lastMonthAvgOptTrans)}
              />
              <InfoCell
                span={50}
                label="客户期权累计交易量(张)"
                content={this.getAmountFormat(custTotalOptTrans)}
              />
              <InfoCell
                span={50}
                label="沪市权利仓持仓限额(张)"
                content={this.getAmountFormat(totOptQuotaSh)}
              />
              <InfoCell
                span={50}
                label="深市权利仓持仓限额(张)"
                content={this.getAmountFormat(totOptQuotaSz)}
              />
              <InfoCell
                span={50}
                label="服务营业部"
                content={serviceOrgName || DefaultValue}
              />
            </InfoGroup>
            <QualificationEvaInfo
              dataSource={detailData?.qualificationEvaInfo || []}
            />
            <InfoCell
              labelWidth="60px"
              label="备注"
              content={remark || DefaultValue}
            />
          </DetailBlock>
          <DetailBlock title="佣金信息">
            <InfoGroup labelWidth="164px">
              <InfoCell
                span={33}
                label="原佣金档"
                content={originCom || DefaultValue}
                labelWidth="68px"
              />
              <InfoCell
                span={33}
                label="目标佣金生效时间"
                content={targetComEffectTime || DefaultValue}
              />
              <InfoCell
                span={33}
                label="目标佣金档"
                content={targetCom || DefaultValue}
              />
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="拟稿信息">
            <InfoGroup labelWidth="164px">
              <InfoCell
                span={33}
                label="拟稿人"
                content={creator}
                labelWidth="68px"
              />
              <InfoCell
                span={33}
                label="创建时间"
                content={timeText}
              />
              <InfoCell
                span={33}
                label="部门"
                content={orgName || DefaultValue}
              />
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="附件信息">
            {this.renderAttachInfo(custType, isOnline)}
          </DetailBlock>
          <DetailBlock title="审批记录">
            <ApprovalHistory
              history={flowHistory}
              visibleCurrentNode={showCurrentNode}
            />
          </DetailBlock>
        </div>
        {previewModalVisible && (
          <ImagePreviewModal
            visible={previewModalVisible}
            imageUrl={imageUrl}
            onModalClose={this.handleImagePreviewModalClose}
          />
        )}
      </DetailContainer>
    );
  }
}

export default Detail;
