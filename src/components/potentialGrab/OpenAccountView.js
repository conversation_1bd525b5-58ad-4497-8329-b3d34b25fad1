/*
  * @Author: <PERSON><PERSON><PERSON><PERSON>
  * @Date: 2020-03-02 17:04:15
 * @Last Modified by: weiting
 * @Last Modified time: 2022-07-11 13:33:26
  * @description 客户中心-潜客商机-潜客抢单-开户潜客
  */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Input } from 'antd';
import { autobind } from 'core-decorators';
import _ from 'lodash';

import ascSvg from 'htsc/static/svg/asc.svg';
import descSvg from 'htsc/static/svg/desc.svg';
import normalSvg from 'htsc/static/svg/normal.svg';
import DateRangePick from '@lego/date/src';
import logable, { logCommon } from '@/decorators/logable';
// import { Icon as AntdIcon, Input, AutoComplete } from 'antd';
import Icon from '@/components/common/Icon';
import Table from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import HtFilter from '@/components/common/htFilter';
import { convertToTimeStamp } from '@/helper/time';
import classnames from 'classnames';
import PlaceHolderImage from './PlaceHolderImage';
import { renderOpeningStepText } from '../potentialCustomer/utils';
import {
  getPage,
  toDateFormat,
  filterFormat,
  renderCell,
  formatQuery,
  openAccountDefaultQuery,
  endOfDayToTimeStamp,
  hasGrabPermission,
  convertApprovalStatusTypes,
  convertCurrentStepTypes,
  getBrushTip,
  judgeShowBrushEmptyPage,
} from './utils';
import {
  OPEN_ACCOUNT_COLUMNS,
  IS_APPLIED_TYPES,
  IS_SAMECITY_TYPES,
  DESC,
  ASC,
  OPEN_ACCOUNT_TYPE,
  SPACE_20,
  PAGE_NUM,
  WAIT_DEPOSITORY_FULL_TEXT,
  WAIT_DEPOSITORY_TEXT,
} from './config';

import styles from './listView.less';

// const Option = AutoComplete.Option;
// const NONE_INFO = '按回车键发起搜索';
const Search = Input.Search;

export default class OpenAccountView extends PureComponent {
  static propTypes = {
    dictList: PropTypes.object.isRequired,
    queryOpenAccountGrabList: PropTypes.func.isRequired,
    queryOpenPotentialCustChannels: PropTypes.func.isRequired,
    openAccountData: PropTypes.object.isRequired,
    channelInfoList: PropTypes.array.isRequired,
    showServiceRecord: PropTypes.func.isRequired,
    location: PropTypes.object.isRequired,
    // 抢单
    potentialGrabClick: PropTypes.func.isRequired,
    // 是否是A类员工
    isClassA: PropTypes.bool.isRequired,
    // 刷单信息
    brushInfoData: PropTypes.object.isRequired,
    isShowEmpty: PropTypes.bool.isRequired,
  };

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      // 渠道下拉列表数据
      finalChannelInfoList: [],
    };
  }

  componentDidMount() {
    this.getOpenAccountList();
    this.getChannelList();
    const custSearchBox = document.querySelector(`.${styles.custSearchBox}`);
    if (custSearchBox) {
      custSearchBox.addEventListener('click', this.handleClickClearDataBtn);
    }
  }

  componentWillUnmount() {
    const custSearchBox = document.querySelector(`.${styles.custSearchBox}`);
    if (custSearchBox) {
      custSearchBox.removeEventListener('click', this.handleClickClearDataBtn);
    }
  }

  @autobind
  handleClickClearDataBtn(e) {
    // 点击时有时会点击到svg，有时会点击到svg下面的path
    const isAntdClearIcon = e.target?.parentNode?.parentNode.className === 'anticon anticon-close-circle ant-select-clear-icon';
    const isSelsctClearIcon = e.target?.parentNode?.parentNode.className === 'ant-select-selection__clear';
    if (isAntdClearIcon || isSelsctClearIcon) {
      this.clearData();
    }
  }

  // 当查询条件改变的时候重新调接口
  componentDidUpdate(prevProps) {
    const {
      location: {
        query: prevQuery,
      },
    } = prevProps;
    const {
      location: {
        query,
        query: {
          potentialGrabactiveKey = OPEN_ACCOUNT_TYPE,
        },
      },
    } = this.props;
    if (prevQuery !== query && potentialGrabactiveKey === OPEN_ACCOUNT_TYPE) {
      this.getOpenAccountList();
    }
    if (!_.isEqual(this.handleOmitParmas(prevQuery), this.handleOmitParmas(query))) {
      this.getChannelList();
    }
  }

  @autobind
  handleOmitParmas(query = {}) {
    const omitParams = ['openSourceIds'];
    return _.omit(query, omitParams);
  }

  @autobind
  getOpenAccountList() {
    const { location: { query }, isClassA } = this.props;
    const finalQuery = _.omit({ ...openAccountDefaultQuery, ...formatQuery(query) }, 'potentialGrabactiveKey');
    if (isClassA) {
      this.props.queryOpenAccountGrabList(finalQuery);
    }
  }

  @autobind
  getChannelList() {
    const { location: { query } } = this.props;
    const channelsFinalQuery = _.omit({
      ...openAccountDefaultQuery,
      pageNum: 1,
      pageSize: 20,
      ...formatQuery(query),
      openSourceIds: [],
      channelId: [],
      channelName: [],
    }, 'potentialGrabactiveKey');
    this.props.queryOpenPotentialCustChannels(channelsFinalQuery).then(() => {
      const {
        channelInfoList, location: {
          query: {
            openSourceIds,
          }
        }
      } = this.props;
      const selectList = openSourceIds;
      this.removalDuplicate(selectList, channelInfoList);
    });
  }

  @autobind
  removalDuplicate(selectList = [], channelInfoList = []) {
    _.forEach(selectList, (itemSelect) => {
      const index = _.findIndex(channelInfoList, { channelId: itemSelect.channelId });
      if (index > -1) {
        channelInfoList.splice(index, 1);
      }
    });
    this.setState({
      finalChannelInfoList: _.map(channelInfoList,
        (item) => ({ code: item.channelId, value: item.channelName }))
    }, this.updateParams);
  }

  @autobind
  updateParams() {
    const { finalChannelInfoList } = this.state;
    if (_.isEmpty(finalChannelInfoList)) {
      this.setNewQuery({
        openSourceIds: [],
        channelName: [],
        channelId: [],
      });
    }
  }

  @autobind
  setNewQuery(params) {
    const { location: { query } } = this.props;
    const newQuery = formatQuery(query);
    this.context.replace({
      query: {
        ...newQuery,
        ...params,
      },
    });
  }

  @autobind
  changeChannelName(value) {
    const newValue = value.replace(/\s+/g, '');
    this.setNewQuery({
      keyWord: newValue,
      pageNum: PAGE_NUM,
    });

    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '客户中心-潜在客户-潜客抢单-开户潜客-渠道名称/姓名',
        value: JSON.stringify(value),
      },
    });
  }

  @autobind
  handleCurrentStepSelect(obj) {
    const { value } = obj;
    const currentStep = _.map(value, 'code');
    this.setNewQuery({
      currentStep,
      pageNum: PAGE_NUM,
    });

    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '客户中心-潜在客户-潜客抢单-开户潜客-当前步骤',
        value: JSON.stringify(value),
      },
    });
  }

  @autobind
  handleApprovalStatusSelect(obj) {
    const { value } = obj;
    const approvalStatus = _.map(value, 'code');
    this.setNewQuery({
      approvalStatus,
      pageNum: PAGE_NUM,
    });

    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '客户中心-潜在客户-潜客抢单-开户潜客-审核状态',
        value: JSON.stringify(value),
      },
    });
  }

  @autobind
  handleOpenSourceIdsSelect(obj) {
    const { value } = obj;
    const openSourceIds = [];
    const channelIdList = [];
    const channelNameList = [];
    // 取消选中时值可能为undefined
    const newValue = _.filter(value, (item) => Boolean(item));
    _.map(newValue, (item) => {
      openSourceIds.push(item.code);
      channelIdList.push(item.code);
      channelNameList.push(item.value);
    });
    this.setNewQuery({
      channelId: channelIdList.join(','),
      channelName: channelNameList.join(','),
      openSourceIds,
      pageNum: PAGE_NUM,
    });

    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '客户中心-潜在客户-潜客抢单-开户潜客-渠道',
        value: JSON.stringify(value),
      },
    });
  }

  @autobind
  handleIsAppliedSelect(obj) {
    const { value } = obj;
    this.setNewQuery({
      isApplied: value,
      pageNum: PAGE_NUM,
    });

    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '客户中心-潜在客户-潜客抢单-开户潜客-是否已被申领过',
        value: JSON.stringify(value),
      },
    });
  }

  @autobind
  handleIsSameCitySelect(obj) {
    const { value } = obj;
    this.setNewQuery({
      isSameCity: value,
      pageNum: PAGE_NUM,
    });

    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '客户中心-潜在客户-潜客抢单-开户潜客-所在地区',
        value: JSON.stringify(value),
      },
    });
  }

  @autobind
  handleSelectCreateTime(date) {
    const { value: [startTime, endTime] } = date;
    this.setNewQuery({
      createdTimeStart: convertToTimeStamp(startTime),
      createdTimeEnd: endOfDayToTimeStamp(endTime),
      pageNum: PAGE_NUM,
    });

    logCommon({
      type: 'CalendarSelect',
      payload: {
        name: '客户中心-潜在客户-潜客抢单-开户潜客-创建时间',
        min: startTime,
        max: endTime,
      },
    });
  }

  @autobind
  handleSelectLastUpdatedTime(date) {
    const { value: [startTime, endTime] } = date;
    this.setNewQuery({
      lastUpdatedTimeStart: convertToTimeStamp(startTime),
      lastUpdatedTimeEnd: endOfDayToTimeStamp(endTime),
      pageNum: PAGE_NUM,
    });

    logCommon({
      type: 'CalendarSelect',
      payload: {
        name: '客户中心-潜在客户-潜客抢单-开户潜客-最近操作时间',
        min: startTime,
        max: endTime,
      },
    });
  }

  // 切换开户潜客页码
  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '客户中心-潜在客户-潜客抢单-开户潜客-页码',
      value: '$args[0]'
    },
  })
  handlePageChange(pageNum) {
    this.setNewQuery({
      pageNum,
    });
  }

  // 渲染table列之前判断是否有权限展示checkbox框
  @autobind
  renderColumns() {
    const columns = hasGrabPermission() ? OPEN_ACCOUNT_COLUMNS : _.dropRight(OPEN_ACCOUNT_COLUMNS);
    const newColumns = _.map(columns, (column) => {
      const { key } = column;
      if (key === 'createdTime') {
        return this.renderSortColumn(column);
      }
      if (key === 'lastUpdatedTime') {
        return this.renderSortColumn(column);
      }
      if (key === 'approvalStatus') {
        return this.renderApprovalStatus(column);
      }
      if (key === 'hasServiceRecord') {
        return this.renderHasServiceRecordColumn(column);
      }
      // 我要抢单列
      if (key === 'grabOrders') {
        return this.renderGrabOrdersColumn(column);
      }
      if (key === 'currentStep') {
        return {
          ...column,
          render: (text, record) => renderOpeningStepText(text, record),
        };
      }
      return {
        ...column,
        render: (text, record) => renderCell(text, record),
      };
    });
    return newColumns;
  }

  @autobind
  renderSortColumn(column) {
    const { location: { query: { sortType = 'lastUpdatedTime', sortDirection = DESC } } } = this.props;
    // 默认排序图标
    let direImg = normalSvg;
    if (sortType === column.dataIndex) {
      direImg = sortDirection === DESC ? descSvg : ascSvg;
    }
    return {
      ...column,
      title: (
        <span className={styles.sortContent}>
          {column.title}
          <img src={direImg} className={styles.sortImg} alt="排序方向" />
        </span>
      ),
      render: (text, record) => renderCell(toDateFormat(text), record),
      onHeaderCell: () => ({
        onClick: () => this.handleSortChange(column),
      })
    };
  }

  // 审核状态
  @autobind
  renderApprovalStatus(column) {
    return {
      ...column,
      render: (text, record) => {
        const showText = text === WAIT_DEPOSITORY_FULL_TEXT ? WAIT_DEPOSITORY_TEXT : text;
        return renderCell(showText, record);
      },
    };
  }

  // 是否有服务记录
  @autobind
  renderHasServiceRecordColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        const { hasServiceRecord, id } = record;
        if (_.isEmpty(id)) {
          return null;
        }
        return (
          <div className={styles.hasServiceRecord}>
            {
              hasServiceRecord
                ? (
                  <Icon
                    type="chakanlishibanben"
                    className={styles.hasServiceRecordIcon}
                    onClick={() => {
                      this.props.showServiceRecord(
                        { custId: id, type: OPEN_ACCOUNT_TYPE }
                      );
                    }}
                  />
                )
                : '--'
            }
          </div>
        );
      },
    };
  }

  // 抢单按钮列
  @autobind
  renderGrabOrdersColumn(column) {
    return {
      ...column,
      render: (text, record) => (
        <div className={styles.grabBtnContainer}>
          <span
            className={styles.btn}
            onClick={() => this.props.potentialGrabClick(record.id, this.getOpenAccountList)}
          >
            抢
          </span>
        </div>
      ),
    };
  }

  // 点击排序
  @autobind
  handleSortChange(column) {
    const { location: { query: { sortType, sortDirection } } } = this.props;
    const { dataIndex } = column;
    let nextSortDirection = DESC;
    if (sortType === dataIndex) {
      nextSortDirection = sortDirection === DESC ? ASC : DESC;
    }
    this.setNewQuery({
      sortType: dataIndex,
      sortDirection: nextSortDirection,
      pageNum: PAGE_NUM,
    });

    logCommon({
      type: 'Click',
      payload: {
        name: `客户中心-潜在客户-潜客抢单-开户潜客-${column.title}排序`,
        value: nextSortDirection === ASC ? '升序' : '降序',
      },
    });
  }

  // 无数据文字判断，优先判断刷单行为
  @autobind
  getPlaceHolderTitle() {
    const {
      brushInfoData,
    } = this.props;
    // 如果有刷单行为还未恢复使用，展示刷单相关提示语，否则都按之前的无数据逻辑展示
    if (judgeShowBrushEmptyPage(brushInfoData)) {
      return getBrushTip(brushInfoData);
    }
    return '暂无开户潜客数据';
  }

  @autobind
  clearData() {
    this.setNewQuery({
      channelAndName: '',
      channelAndNameType: '',
      pageNum: PAGE_NUM,
    });
  }

  @autobind
  getOpenChannelValue() {
    const {
      location: {
        query: {
          channelId: channelIdList,
          channelName: channelNameList,
        }
      }
    } = this.props;
    const channelIdArray = channelIdList && channelIdList.split(',');
    const channelNameArray = channelNameList && channelNameList.split(',');
    const tempArr = [];
    _.map(channelIdArray, (itema, index) => {
      tempArr.push({
        code: itema,
        value: channelNameArray[index],
      });
    });
    return tempArr;
  }

  render() {
    const {
      openAccountData: { list = [], page = {} },
      dictList: {
        currentStepTypes = [],
        approvalStatusTypes = [],
      },
      location: {
        query: {
          keyword,
          isSameCity,
          isApplied,
          // openSourceIds,
          createdTimeStart,
          createdTimeEnd,
          lastUpdatedTimeStart,
          lastUpdatedTimeEnd,
          currentStep,
          approvalStatus,
        },
      },
      isClassA,
      isShowEmpty,
      brushInfoData,
    } = this.props;
    const { finalChannelInfoList } = this.state;
    // 获取分页器属性
    const pageProps = getPage(page);
    // 渲染表格
    const columns = this.renderColumns();
    // 时间选择组件样式，主要是位置上移与单选组件对齐
    const dateRangePickerClass = classnames({
      [styles.filterItem]: true,
      [styles.dateRangePick]: true,
    });

    if (!isClassA) {
      return <PlaceHolderImage />;
    }

    // 开户潜客无数据展位图属性
    const placeHolderProps = {
      title: this.getPlaceHolderTitle(),
      style: {
        height: '235px',
      },
    };
    // 如果列表数据为空 || 未查询完刷单信息 || 刷单行为导致当前应该展示无数据时
    const isShowEmptyList = isShowEmpty
      || _.isEmpty(list)
      || judgeShowBrushEmptyPage(brushInfoData);
    // 列表数据，因为table是数据为空才展示无数据，但是刷单行为有数据也有可能会展示无数据，这里对数据做个处理
    const transList = isShowEmptyList ? [] : list;

    return (
      <div className={styles.listView}>
        <div className={styles.headerFilter}>
          <div className={styles.filterItem}>
            <Search
              placeholder="渠道名称/姓名"
              style={{ width: 228 }}
              onSearch={this.changeChannelName}
              defaultValue={keyword}
            />
          </div>
          <div className={styles.filterItem}>
            <HtFilter
              menuContainer="body"
              value={isSameCity}
              data={IS_SAMECITY_TYPES}
              onChange={this.handleIsSameCitySelect}
              filterName="所在地区"
              filterId="isSameCity"
              type="single"
              dataMap={['code', 'value']}
              filterOption={['isSameCity']}
            />
          </div>
          <div className={styles.filterItem}>
            <HtFilter
              menuContainer="body"
              filterName="当前步骤"
              value={currentStep}
              data={convertCurrentStepTypes(currentStepTypes)}
              onChange={this.handleCurrentStepSelect}
              filterId="currentStep"
              type="multi"
              dataMap={['code', 'value']}
              filterOption={['currentStep']}
              needItemObj
            />
          </div>
          <div className={styles.filterItem}>
            <HtFilter
              menuContainer="body"
              value={approvalStatus}
              data={convertApprovalStatusTypes(approvalStatusTypes)}
              onChange={this.handleApprovalStatusSelect}
              filterName="审核状态"
              filterId="approvalStatus"
              type="multi"
              dataMap={['code', 'value']}
              filterOption={['approvalStatus']}
              needItemObj
            />
          </div>
          <div className={styles.filterItem}>
            <HtFilter
              menuContainer="body"
              value={isApplied}
              data={IS_APPLIED_TYPES}
              onChange={this.handleIsAppliedSelect}
              filterName="是否已被申领过"
              filterId="isApplied"
              type="single"
              dataMap={['code', 'value']}
              filterOption={['isApplied']}
            />
          </div>
          <div className={styles.filterItem}>
            <HtFilter
              menuContainer="body"
              filterName="渠道"
              value={this.getOpenChannelValue()}
              data={[{ code: '', value: '不限' }, ...finalChannelInfoList]}
              onChange={this.handleOpenSourceIdsSelect}
              filterId="openSourceIds"
              type="multi"
              dataMap={['code', 'value']}
              filterOption={['openSourceIds']}
              dropdownStyle={{
                maxHeight: 324,
                overflowY: 'auto',
                width: 278,
              }}
              needItemObj
            />
          </div>
          <div className={dateRangePickerClass}>
            <DateRangePick
              allowClear
              type="date"
              filterName="创建时间"
              filterId="createTime"
              menuContainer="body"
              filterValue={[filterFormat(createdTimeStart), filterFormat(createdTimeEnd)]}
              onChange={this.handleSelectCreateTime}
            />
          </div>
          <div className={dateRangePickerClass}>
            <DateRangePick
              allowClear
              type="date"
              filterName="最近操作时间"
              filterId="lastUpdatedTime"
              menuContainer="body"
              filterValue={[filterFormat(lastUpdatedTimeStart), filterFormat(lastUpdatedTimeEnd)]}
              onChange={this.handleSelectLastUpdatedTime}
            />
          </div>
        </div>
        <div className={styles.tableArea}>
          <Table
            useNewUI
            pagination={false}
            columns={columns}
            dataSource={transList}
            scroll={{ x: 1290 }}
            rowKey="id"
            spaceColumnProps={SPACE_20}
            placeHolderImageProps={placeHolderProps}
          />
          <IFWrap when={!_.isEmpty(transList)}>
            <div className={styles.pageArea}>
              <Pagination
                {...pageProps}
                onChange={this.handlePageChange}
              />
            </div>
          </IFWrap>
        </div>
      </div>
    );
  }
}
