/**
 * @Author: yanfaping
 * @Date: 2021-03-15 15:21:10
 * @Last Modified by: yanfaping
 * @Last Modified time: 2021-03-15 15:21:10
 * @description 无权限展示样式
 */

import React from 'react';
import noAccess from './images/noAccess.png';
import styles from './placeHolderImage.less';

export default function PlaceHolderImage() {
  return (
    <div className={styles.placeHolderWrap}>
      <div className={styles.placeHolderContent}>
        <img src={noAccess} className={styles.placeholderImage} />
        <div className={styles.placeholderTitle}>无抢单权限哦</div>
      </div>
    </div>
  );
}
