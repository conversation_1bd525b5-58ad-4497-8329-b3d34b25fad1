/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-03-06 15:41:30
 * @Last Modified by: weiting
 * @Last Modified time: 2022-07-04 18:06:06
 * @description 潜客抢单的辅助函数
 */
import _ from 'lodash';
import { emp, permission } from '@/helper';
import { convertToTimeStamp } from '@/helper/time';
import moment from 'moment';
import React from 'react';
import { ToolTipCell } from '@/components/common/table';
import {
  PAGE_NUM,
  PAGE_SIZE,
  DESC,
  DATE_FORMAT_ALL,
  YINGYEBU_CODE,
  WAIT_DEPOSITORY_FULL_TEXT,
  WAIT_DEPOSITORY_TEXT,
  NUM_ONE,
  NUM_TWO,
  NUM_THREE,
  SEVEN_DAY,
  THIRTY_DAY,
  NINETY_DAY,
  NODATA,
} from './config';

const noDataStyle = {
  color: '#ccc'
};

// 将接口返回的分页器数据转换成分页器组件的props
export function getPage(page = {}) {
  return {
    pageSize: PAGE_SIZE,
    current: page.pageNum || PAGE_NUM,
    total: page.totalCount || 0,
  };
}
// 将参数转为数组（多选组建需要）
export function stringToArray(params) {
  if (!_.isArray(params)) {
    return _.isEmpty(params) ? [] : _.split(params, ',');
  }
  return params;
}
// 将参数转为数字（时间戳需要）
export function stringToNumber(params) {
  if (_.isNil(params)) {
    return null;
  }
  if (!_.isNumber(params)) {
    return _.toInteger(params);
  }
  return params;
}
// 表格列的展示值将时间戳转换成年月日时分秒
export function toDateFormat(date) {
  return _.isNil(date) ? null : moment(date).format('YYYY-MM-DD HH:mm:ss');
}
// 日历组建中filterValue中的开始时间需要为字符串，故将选择的时间戳转为字符串
export function filterFormat(date) {
  return _.isNil(date) ? null : moment(stringToNumber(date)).format('YYYY-MM-DD');
}
// 登陆人的组织 ID
export const empOrgId = emp.getOrgId();
// 展示Tooltip，对于没数据的列用--代替
export function renderCell(text, record) {
  if (record.flag) {
    return null;
  }
  if (text === '' || _.isNull(text)) {
    return <span style={noDataStyle}>--</span>;
  }
  return (
    <ToolTipCell
      tipContent={text}
      cellText={text}
    />
  );
}
// 从URL中拿下的参数都为字符串型，实际需要Number型或者数组型，进行query参数的转换
export function formatQuery(query) {
  const newQuery = {};
  const queryKeys = Object.keys(query);
  _.forEach(queryKeys, (key) => {
    if (key === 'createdTimeStart' || key === 'createdTimeEnd'
      || key === 'lastUpdatedTimeStart' || key === 'lastUpdatedTimeEnd'
      || key === 'recentLoginTimeStart' || key === 'recentLoginTimeEnd'
      || key === 'pageNum' || key === 'pageSize') {
      newQuery[key] = stringToNumber(query[key]);
      return;
    }
    if (key === 'currentStep' || key === 'approvalStatus') {
      newQuery[key] = stringToArray(query[key]);
      return;
    }
    newQuery[key] = query[key];
  });
  return newQuery;
}
// 开户潜客默认查询条件
export const openAccountDefaultQuery = {
  orgId: empOrgId,
  sortType: 'lastUpdatedTime',
  sortDirection: DESC,
  pageNum: PAGE_NUM,
  pageSize: PAGE_SIZE,
};
// 注册潜客默认查询条件
export const registerDefaultQuery = {
  sortType: 'recentLoginTime',
  sortDirection: DESC,
  orgId: empOrgId,
  pageNum: PAGE_NUM,
  pageSize: PAGE_SIZE,
};
// 取某天23:59:59的时间戳
export function endOfDayToTimeStamp(dateString) {
  if (_.isNil(dateString)) {
    return null;
  }
  const dateStringEndDay = moment(dateString).endOf('day');
  const finalTimeStamp = convertToTimeStamp(dateStringEndDay, DATE_FORMAT_ALL);
  return finalTimeStamp;
}
// 是否具有抢单按钮权限
export function hasGrabPermission() {
  const { level } = emp.getPstnDetail();
  // 当前登录人在营业部并且不为管理用户时可以抢单
  return level === YINGYEBU_CODE && permission.hasBaseDuty();
}

// 将等待建立三方存管改为待建三方存管
export function convertApprovalStatusTypes(data) {
  return _.map(data, (item) => {
    const { value, ...rest } = item;
    if (value === WAIT_DEPOSITORY_FULL_TEXT) {
      return {
        value: WAIT_DEPOSITORY_TEXT,
        ...rest,
      };
    }
    return item;
  });
}

// 将提交审核改为提交终审
export function convertCurrentStepTypes(data) {
  return _.map(data, (item) => {
    const { value, ...rest } = item;
    if (value === '提交审核') {
      return {
        value: '提交终审',
        ...rest,
      };
    }
    return item;
  });
}

// 获取不同刷单次数对应的禁用天数（大于等于3次，90天；2次 30天；1次，7天）
export function getDisableDayNum(brushNum) {
  if (brushNum === NUM_ONE) {
    return SEVEN_DAY;
  }
  if (brushNum === NUM_TWO) {
    return THIRTY_DAY;
  }
  if (!(brushNum < NUM_THREE)) {
    return NINETY_DAY;
  }
  return NODATA;
}

// 处理数值
export function transNumber(value) {
  return _.isNumber(value) ? value : NODATA;
}

// 判断刷单提示语
export function getBrushTip(brushInfoData = {}) {
  const {
    brushNum,
    resumeDayNum,
  } = brushInfoData;
  // 多少天后恢复
  const transResumeDayNum = transNumber(resumeDayNum);
  // 第几次刷单
  const timeTip = brushNum > NUM_ONE ? `第${brushNum}次` : '';
  // 禁用天数
  const disableDayNum = getDisableDayNum(transNumber(brushNum));
  const prevTip = `根据系统检测，该账户${timeTip}存在高频率刷单行为，影响系统安全`;
  return `${prevTip}，将禁用该功能${disableDayNum}天，于${transResumeDayNum}天后恢复使用`;
}

// 根据刷单行为判断是否展示无数据(刷单次数大于0并且剩余禁止天数大于0)
export function judgeShowBrushEmptyPage(brushInfoData = {}) {
  const {
    brushNum,
    resumeDayNum,
  } = brushInfoData;
  return brushNum > 0 && resumeDayNum > 0;
}
