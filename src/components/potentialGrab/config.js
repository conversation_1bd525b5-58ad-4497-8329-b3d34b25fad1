/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-03-06 15:41:30
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-04-27 09:35:54
 * @Description: 潜客抢单列表配置文件
 */
import {
  UNBELONG_CUST,
  REGISTER_CUST,
} from '@/routes/potentialCustomer/config';

// 列间距20
export const SPACE_20 = {
  width: 20,
};
// 列间距25
export const SPACE_25 = {
  width: 25,
};
// 开户潜客的Table列
export const OPEN_ACCOUNT_COLUMNS = [
  {
    key: 'createdTime',
    dataIndex: 'createdTime',
    title: '创建时间',
    width: 138,
    fixed: 'left',
  },
  {
    key: 'lastUpdatedTime',
    dataIndex: 'lastUpdatedTime',
    title: '最近一次操作时间',
    width: 138,
    fixed: 'left',
  },
  {
    key: 'phone',
    dataIndex: 'phone',
    title: '手机号码',
    width: 83,
    fixed: 'left',
  },
  {
    key: 'phoneLocation',
    dataIndex: 'phoneLocation',
    title: '手机号归属地',
    width: 84,
  },
  {
    key: 'name',
    dataIndex: 'name',
    title: '姓名',
    width: 56,
  },
  {
    key: 'custId',
    dataIndex: 'custId',
    title: '客户号',
    width: 99,
  },
  {
    key: 'idnumber',
    dataIndex: 'idnumber',
    title: '身份证号',
    width: 56,
  },
  {
    key: 'appliedTimes',
    dataIndex: 'appliedTimes',
    title: '申领次数',
    width: 56,
  },
  {
    key: 'channel',
    dataIndex: 'channel',
    title: '渠道名称',
    width: 174,
  },
  {
    key: 'currentStep',
    dataIndex: 'currentStep',
    title: '当前步骤',
    width: 84,
  },
  {
    key: 'isSubmitApproval',
    dataIndex: 'isSubmitApproval',
    title: '是否提交审核',
    width: 84,
  },
  {
    key: 'approvalStatus',
    dataIndex: 'approvalStatus',
    title: '审核状态',
    width: 134,
  },
  {
    key: 'productName',
    dataIndex: 'productName',
    title: '套餐',
    width: 150,
  },
  {
    key: 'applicationAccount',
    dataIndex: 'applicationAccount',
    title: '申请账户',
    width: 140,
  },
  {
    key: 'depostoryBank',
    dataIndex: 'depostoryBank',
    title: '存管银行',
    width: 56,
  },
  {
    key: 'hasServiceRecord',
    dataIndex: 'hasServiceRecord',
    title: '服务记录',
    width: 56,
    fixed: 'right',
  },
  {
    key: 'grabOrders',
    dataIndex: 'grabOrders',
    title: '我要抢单',
    width: 56,
    fixed: 'right',
  },
];

// 注册潜客的Table列
export const REGISTER_COLUMNS = [
  {
    key: 'registerTime',
    dataIndex: 'registerTime',
    title: '注册时间',
    width: 138,
  },
  {
    key: 'recentLoginTime',
    dataIndex: 'recentLoginTime',
    title: '最近登录时间',
    width: 138,
  },
  {
    key: 'phone',
    dataIndex: 'phone',
    title: '手机号码',
    width: 83,
  },
  {
    key: 'phoneLocation',
    dataIndex: 'phoneLocation',
    title: '手机号归属地',
    width: 84,
  },
  {
    key: 'userName',
    dataIndex: 'userName',
    title: '用户名',
    width: 110,
  },
  {
    key: 'custBehaviorLead',
    dataIndex: 'custBehaviorLead',
    title: '客户行为线索',
    // width: 284,
  },
  {
    key: 'registerSource',
    dataIndex: 'registerSource',
    title: '注册来源',
    width: 160,
  },
  {
    key: 'hasServiceRecord',
    dataIndex: 'hasServiceRecord',
    title: '服务记录',
    width: 56,
  },
  {
    key: 'grabOrders',
    dataIndex: 'grabOrders',
    title: '我要抢单',
    width: 56,
  },
];
// 开户潜客【是否已被申领过】筛选下拉值
export const IS_APPLIED_TYPES = [
  {
    code: '',
    value: '不限',
  },
  {
    code: 'Y',
    value: '是',
  },
  {
    code: 'N',
    value: '否',
  },
];

// 开户潜客【所在地区】筛选下拉值
export const IS_SAMECITY_TYPES = [
  {
    code: '',
    value: '不限',
  },
  {
    code: 'Y',
    value: '同城',
  },
  {
    code: 'N',
    value: '非同城',
  },
];
// 降序
export const DESC = 'desc';
// 升序
export const ASC = 'asc';
// 开户潜客类型
export const OPEN_ACCOUNT_TYPE = '1';
// 注册潜客类型
export const REGISTER_TYPE = '2';
// 营业部用户
export const YINGYEBU_CODE = '4';
// 营业部管理用户
export const YINGYEBU_ORG_CODE = 'org';
// 抢单到达上限
export const LIMIT_CODE = '40001';
// 被别人抢了
export const SLOW_CODE = '40000';
// 默认第一页
export const PAGE_NUM = 1;
// 每页大小默认20
export const PAGE_SIZE = 10;

// 一键派单-选择接收人列表标题
export const CAST_PERSONNEL_COLUMNS = [
  {
    key: 'empName',
    title: '服务经理',
    dataIndex: 'empName',
    width: 115,
  },
  {
    key: 'distribNum',
    title: '已申领数',
    dataIndex: 'distribNum',
    width: 56,
  },
  {
    key: 'convSuccNum',
    title: '已转化数',
    dataIndex: 'convSuccNum',
    width: 56,
  },
  {
    key: 'convSuccRate',
    title: '成功率',
    dataIndex: 'convSuccRate',
    width: 56,
  },
];
// 时间格式，时分秒
export const DATE_FORMAT_ALL = 'YYYY-MM-DD HH:mm:ss';
// 选择框列的宽度
export const SELECTION_COLUMN_WIDTH = 33;

// 点击我的潜客跳转页面Tab需要带的key
export const MY_POTENTIAL_JUMP = {
  [OPEN_ACCOUNT_TYPE]: UNBELONG_CUST,
  [REGISTER_TYPE]: REGISTER_CUST,
};

// 等待建立三方存管需在前端转换展示名字
export const WAIT_DEPOSITORY_TEXT = '待建三方存管';
export const WAIT_DEPOSITORY_FULL_TEXT = '等待建立三方存管';
// 潜客抢单A类员工可用, 营销人员不可用
// 营销人员界定为oa中经纪人类型为客户经理'207090'、一般型经纪人'207060'、目标型经纪人'207100'
export const MARKET_USER_TYPE_MAP = ['207090', '207060', '207100'];

// 刷单次数
const NUM_ONE = 1;
const NUM_TWO = 2;
const NUM_THREE = 3;

// 刷单次数对应的禁用天数
const SEVEN_DAY = 7;
const THIRTY_DAY = 30;
const NINETY_DAY = 90;

// 无数据
const NODATA = '--';

export {
  NUM_ONE,
  NUM_TWO,
  NUM_THREE,
  SEVEN_DAY,
  THIRTY_DAY,
  NINETY_DAY,
  NODATA,
};
