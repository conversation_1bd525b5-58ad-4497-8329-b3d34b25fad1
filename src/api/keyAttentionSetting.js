/*
 * @Description: 重点关注标签设置 api 文件
 * @Author: <PERSON><PERSON>ianShu-K0180193
 * @Date: 2020-07-13 16:14:16
 * @Last Modified by: <PERSON><PERSON>ian<PERSON>hu-K0180193
 * @Last Modified time: 2020-07-13 16:14:40
 */

export default function keyAttentionSetting(api) {
  return {
    // 获取重点关注标签列表
    queryKeyAttention: (query) => api.post('/groovynoauth/fsp/index/queryKeyAttention', query),
    // 查询供替换的重点关注标签数据
    queryKeyAttentionForReplace: (query) => api.post('/groovynoauth/fsp/index/queryKeyAttentionForReplace', query),
    // 替换重点关注
    replaceKeyAttention: (query) => api.post('/groovynoauth/fsp/index/replaceKeyAttention', query),
  };
}
