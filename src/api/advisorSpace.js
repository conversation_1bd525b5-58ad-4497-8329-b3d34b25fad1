/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2018-09-11 14:05:05
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2018-09-21 17:49:47
 * @Descripter: 投顾空间相关接口
 */

import _ from 'lodash';

function fixDictoryKeys(dict) {
  const { resultData, resultData: { smartFrontHallList } } = dict;
  // 因为返回值没有label和value,label需要通过siteName和roomName拼接
  return {
    ...dict,
    resultData: {
      ...resultData,
      smartFrontHallList: _.map(smartFrontHallList, item => ({ ...item, label: `${item.siteName}${item.roomName}`, value: item.roomNo })),
    },
  };
}

export default function advisorSpace(api) {
  return {
    // 申请单列表
    getApplicationList: query => api.post('/groovynoauth/fsp/biz/advisorSpace/queryApplicationList', query),
    // 智慧前厅列表
    getRoomList: query => api.post('/groovynoauth/fsp/biz/advisorSpace/querySmartFrontHallList', query).then(fixDictoryKeys),
    // 申请单详情
    getApplicationDetail: query => api.post('/groovynoauth/fsp/biz/advisorSpace/queryApplicationDetail', query),
    // 新建申请单
    saveApplictaion: query => api.post('/groovynoauth/fsp/biz/advisorSpace/save', query),
    // 获取参与人
    getParticipantList: query => api.post('/groovynoauth/fsp/biz/advisorSpace/getParticipantList', query),
    // 取消预订
    cancelReservation: query => api.post('/groovynoauth/fsp/biz/advisorSpace/cancelReservation', query),
    // 获取已预订时间段
    getOrderPeriod: query => api.post('/groovynoauth/fsp/biz/advisorSpace/getOrderPeriod', query),
  };
}
