/**
 * @Description: 佣金配置管理
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-07-27 16:54:01
 */
export default function commissionConfig(api) {
  return {
    // 查询佣金城市地域配置列表接口
    queryCommissionCitySettings: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/queryCommissionCitySettings', query),
    // 删除单条佣金城市地域配置
    deleteCommissionCitySetting: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/deleteCommissionCitySetting', query),
    // 新增单条佣金城市地域配置
    saveCommissionCitySetting: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/saveCommissionCitySetting', query),
    // 编辑单条佣金城市地域配置
    updateCommissionCitySetting: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/updateCommissionCitySetting', query),
    // 校验单条佣金城市地域配置
    validateCommissionCitySetting: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/validateCommissionCitySetting', query),
    // 查询省市数据
    queryProvinceCity: (query) => api.post('/groovynoauth/fsp/common/queryNextAddrByCode', query),
    // 查询营业部列表
    queryDepartmentList: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/queryDepartmentList', query),
    // 全品种佣金参数管理-获取业务品种接口
    queryComRateList: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/queryComRateList', query),
    // 佣金范围配置查询列表接口
    queryCommissionRateRangeList: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/queryCommissionRateRangeList', query),
    // 佣金范围配置新增接口
    saveCommissionRateRange: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/saveCommissionRateRange', query),
    // 佣金范围配置删除接口
    deleteCommissionRateRange: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/deleteCommissionRateRange', query),
    // 佣金范围配置编辑接口
    updateCommissionRateRange: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/updateCommissionRateRange', query),
    // 佣金范围配置新增校验接口
    validateCommissionRateRange: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/validateCommissionRateRange', query),
    // 佣金率参数条件配置查询列表接口
    queryCommissionRateParamsList: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/queryCommissionRateParamsList', query),
    // 佣金率参数条件配置新增接口
    saveCommissionRateParams: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/saveCommissionRateParams', query),
    // 佣金率参数条件配置删除接口
    deleteCommissionRateParams: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/deleteCommissionRateParams', query),
    // 佣金率参数条件配置编辑接口
    updateCommissionRateParams: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/updateCommissionRateParams', query),
    // 佣金率参数条件配置新增校验接口
    validateCommissionRateParams: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/validateCommissionRateParams', query),
    // 获取佣金率信息接口
    queryCommissionRateData: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/queryCommissionRateData', query),
    // 获取场景下拉选项接口
    queryComSceneList: (query) => api.post('/groovynoauth/fsp/biz/commissionConfig/queryComSceneList', query),
  };
}
