/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-01 09:37:12
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-04-06 13:17:16
 * @Description: 联合服务渠道潜客api文件
 */

export default function channelPotential(api) {
  return {
    // 查询列表
    queryPotentialtCustList: (query) => api.post('/groovynoauth/fsp/potentialcust/queryPotentialCustList', query),
    // 删除列表数据
    deletePotentialCust: (query) => api.post('/groovynoauth/fsp/potentialcust/deletePotentialChannel', query),
    // 查渠道分公司
    queryChannelBranch: (query) => api.post('/groovynoauth/fsp/emp/org/queryEmpOrgTree', query),
    // 根据关键字查员工
    queryEmpList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
    // 联合服务员工信息配置
    queryEmpOfChannelOrg: (query) => api.post('/groovynoauth/fsp/potentialcust/queryEmpOfChannelOrg', query),
    // 开户渠道信息配置
    queryOpenChannelList: (query) => api.post('/groovynoauth/fsp/potentialcust/queryOpenChannelList', query),
    // 新增编辑
    addOrEditCustChannel: (query) => api.post('/groovynoauth/fsp/potentialcust/addOrEditCustChannel', query),
    // 详情
    queryPotentialCustDetail: (query) => api.post('/groovynoauth/fsp/potentialcust/queryPotentialCustDetail', query),

  };
}
