/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-04-27 15:59:16
 * @LastEditors: yuan<PERSON>jie
 * @LastEditTime: 2020-05-14 18:23:58
 * @Description: 投顾直播间接口
 */

export default function live(api) {
  return {
    // 获取用户基本信息
    queryEmpBaseInfo: (query) => api.post('/groovynoauth/fsp/emp/infomanagement/queryEmpBaseInfo', query),
    // 创建直播间-提交创建申请
    submitAapplication: (query) => api.post('/groovynoauth/fsp/live/createLive', query),
    // 创建直播间-获取直播分类列表
    queryTypeList: (query) => api.post('/groovynoauth/fsp/live/queryTypeList', query),
    // 创建直播间-搜索产品
    queryProductList: (query) => api.post('/groovynoauth/fsp/live/queryTGLiveProductList', query),
    // 创建直播间-获取直播间详情
    queryApplication: (query) => api.post('/groovynoauth/fsp/live/queryApplication', query),
    // 我的直播间-搜索直播
    searchLive: (query) => api.post('/groovynoauth/fsp/live/searchLive', query),
    // 我的直播间-获取直播列表
    queryLiveList: (query) => api.post('/groovynoauth/fsp/live/queryLiveList', query),
    // 我的直播间-禁用直播
    disableLive: (query) => api.post('/groovynoauth/fsp/live/disableLive', query),
    // 我的直播间-公开直播
    openLive: (query) => api.post('/groovynoauth/fsp/live/openLive', query),
    // 我的直播间-切换直播间投顾会话入口是否展示
    toggleImEntry: (query) => api.post('/groovynoauth/fsp/live/toggleImEntry', query),
    // 我的直播间-获取H5分享链接
    queryLiveShareInfo: (query) => api.post('/groovynoauth/fsp/live/queryLiveShareInfo', query),
    // 我的直播间-搜索渠道号
    searchChannel: (query) => api.post('/groovynoauth/fsp/order/commission/queryChannelFromCustPartner', query),
    // 获取分享和站外推广url
    shareLive: (query) => api.post('/groovynoauth/fsp/live/shareLive', query),
    innerShareLive: (query) => api.post('/groovynoauth/fsp/live/innerShareLive', query),
    // 获取服务器时间
    queryServerDate: (query) => api.post('/groovynoauth/fsp/live/queryServerDate', query),
    // 切换站外评论，或者关闭直播
    closeOrChangeLive: (query) => api.post('/groovynoauth/fsp/live/closeOrChangeLive', query),
    // 获取消息
    getMessage: (query) => api.post('/fspa/live/manage/070501', query, { isFullUrl: true }),
    // 发送消息
    sendMessage: (query) => api.post('/fspa/live/manage/070503', query, { isFullUrl: true }),
    // 获取直播流量数据
    queryLiveFlowData: (query) => api.post('/groovynoauth/fsp/live/queryLiveFlowData', query),
    // 获取直播流量数据-点击按时间/场次查看详情的下钻列表数据
    queryLiveFlowDetail: (query) => api.post('/groovynoauth/fsp/live/queryLiveFlowDetail', query),
    // 流量数据二级下钻-点击客户详情-直播观看客户
    queryLiveFlowViewDetail: (query) => api.post('/groovynoauth/fsp/live/queryLiveFlowViewDetail', query),
    // 流量数据二级下钻-点击客户详情-直播新开客户
    queryLiveFlowNewOpenDetail: (query) => api.post('/groovynoauth/fsp/live/queryLiveFlowNewOpenDetail', query),
    // 流量数据二级下钻-点击客户详情-直播开户断点
    queryLiveFlowUnOpenDetail: (query) => api.post('/groovynoauth/fsp/live/queryLiveFlowUnOpenDetail', query),
    // 流量数据二级下钻-点击客户详情-直播产品客户
    queryLiveFlowBuyProductDetail: (query) => api.post('/groovynoauth/fsp/live/queryLiveFlowBuyProductDetail', query),
    // 获取直播运营数据
    queryOperationData: (query) => api.post('/groovynoauth/fsp/live/queryOperationData', query),
    // 获取直播运营数据-我的客户下钻数据
    queryLiveOpeMyCustList: (query) => api.post('/groovynoauth/fsp/live/queryLiveOpeMyCustList', query),
    // 获取实时预约人数
    queryReserveTotalNum: (query) => api.post('/groovynoauth/fsp/live/queryReserveTotalNum', query),
    queryLiveStatus: (query) => api.post('/groovynoauth/fsp/live/queryLiveStatus', query),
    // 获取推广的数据
    queryLiveContentEmpConfigInfo: (query) => api.post('/groovynoauth/fsp/live/queryLiveContentEmpConfigInfo', query),
    // 修改视频源地址
    changeResStreamUrl: (query) => api.post('/groovynoauth/fsp/live/updateResSteamUrl', query),
  };
}
