/*
  * @Author: <PERSON><PERSON><PERSON><PERSON>
  * @Date: 2020-03-04 18:38:12
 * @Last Modified by: weiting
 * @Last Modified time: 2022-07-04 17:41:45
  * @description 潜客抢单的Api
  */

export default function potentialGrab(api) {
  return {
    // 查询开户潜客列表
    queryOpenPotentialCustWaitPickList: (query) => api.post('/groovynoauth/fsp/potentialcust/queryOpenPotentialCustWaitPickList', query),
    // 查询注册潜客列表
    queryRegisterPotentialCustWaitPickList: (query) => api.post('/groovynoauth/fsp/potentialcust/queryRegisterPotentialCustWaitPickList', query),
    // 潜客抢单
    pickPotentialCust: (query) => api.post('/groovynoauth/fsp/potentialcust/pickPotentialCust', query),
    // 查询用户刷单信息
    queryBrushInfo: (query) => api.post('/groovynoauth/fsp/potentialcust/queryBrushInfo', query),
    // 查询渠道名称/姓名列表数据
    queryOpenPotentialCustListByChannelAndName: (query) => api.post('/groovynoauth/fsp/potentialcust/queryOpenPotentialCustListByChannelAndName', query),
    // 查询渠道列表数据
    queryOpenPotentialCustChannels: (query) => api.post('/groovynoauth/fsp/potentialcust/queryOpenPotentialCustChannels', query),
  };
}
