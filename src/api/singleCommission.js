// 单佣金调整相关接口
export default function singleCommission(api) {
  return {
    // 跳转到360视图界面必须的参数（场景：单佣金调整，新建，选择客户时，若改客户有未完成订单，会弹框提醒，点击确定会跳转到360视图界面）
    queryCustDetailInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCustBrifeInfo', query),
    // 单佣金调整页面-客户搜索
    querySingleCustomer: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCustInfo', query),
    // 单佣金调整中的其他佣金费率选项
    queryOtherCommissionOptions: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommissionRateDictionary', query),
    // 佣金调整校验客户如果有被驳回订单，终止订单
    terminalOrderFlow: (query) => api.post('/groovynoauth/fsp/order/commission/terminalOrderFlow', query),
    // 佣金调整校验客户如果有新建订单，修改订单状态
    changeOrderStatus: (query) => api.post('/groovynoauth/fsp/order/commission/changeOrderStatus', query),
    // 查询单佣金调整客户的当前股基佣金率
    queryCustCommission: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/cust/pc/CommissionCustInfo/queryCustRealTimeCommissison', query, { isFullUrl: true }),
    // 单佣金调整新建页面中的目标股基佣金率
    querySingleGJCommissionRate: (query) => api.post('/groovynoauth/fsp/biz/singlecommissonchg/querySingleCommisionNew', query),
    // 查询单佣金调整中的产品列表信息
    querySingleCommissionProductList: (query) => api.post('/groovynoauth/fsp/biz/singlecommissonchg/queryProDuctInfoNew', query),
    // 查询用户选择的产品三匹配信息
    queryThreeMatchInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryThreeMatchInfo', query),
    // 保存单佣金订单
    saveSingleCommission: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/save/pc/CommissionSave/saveCommonCommission', query, { isFullUrl: true }),
    // 单佣金调整新建页面客户检验
    validateCustomer: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/queryCustRiskInfo', query, { isFullUrl: true }),
    // 查询驳回后修改的详情页面
    querySingleDetail4Update: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySingleCommissionOrderInfoForUpdate', query, { isFullUrl: true }),
    // 获取附件信息
    getAttachment: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),
    // 查询异地限价以及是否同城
    queryCustLocalInfo: (query) => api.post('/groovynoauth/fsp/biz/singlecommissonchg/queryCustLocalInfo', query),
    // 存量两融客户对齐佣金场景校验接口
    checkRzRqCustCommissions: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/RzrqCustCommissionQuery/checkRzRqCustCommissions', query, { isFullUrl: true }),
    // 校验客户信息
    validateCustInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/cust/pc/CommissionCustInfo/validateCustInfo', query, { isFullUrl: true }),
    // 校验是否涨乐选投顾
    validateZLPickCustomer: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/validateZLPickCustomer', query, { isFullUrl: true }),
    // 查询经服已填写的佣金信息
    queryProductBrokerageCommissionData: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/EconAgreementCommissionQuery/queryCommissionSettingInfo', query, { isFullUrl: true }),
    // 更新经服佣金设置待办状态
    updateSetCommissionTodoStatus: (query) => api.post('/fspa/uicrm-bff-aorta/groovy/unauth/productAccountIntroduction/flow/terminalCommissionSettingTodoFlow', query, { isFullUrl: true }),
    // 查询佣金类型
    queryCommissionSubType: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/queryCommissionSubType', query, { isFullUrl: true }),
    // 单佣金调整详情页面基础数据接口
    querySingleDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySingleCommissionOrderInfo', query, { isFullUrl: true }),
    // 查询【佣金模式】的新投顾签约有哪些需要禁用的佣金率
    queryDisableContractRatioType: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/queryDisableContractRatioType', query, { isFullUrl: true }),
    // 全账户提佣校验
    validateUnifiedCommissionRaise: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/validateUnifiedCommissionRaise', query, { isFullUrl: true }),
  };
}
