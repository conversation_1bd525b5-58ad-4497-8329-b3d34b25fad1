/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2018-11-20 15:53:09
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-21 14:00:19
 * @Description: 客户360-投资能力分析相关api
 */

export default function detailInvestAnalyze(api) {
  return {
    // 提交pdf编辑信息
    addAccountAnalysisReportRecord: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/addAccountAnalysisReportRecord', query),
    // 请求生成pdf
    submitAccountAnalysisReportRecordPDF: query => api.get('/bfe/pdf/submit', query, { isSSRServer: true }),
    // 查询pdf信息
    queryAccountAnalysisReportDetail: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryAccountAnalysisReportDetail', query),
    // 获取客户盈利能力
    queryProfitAbility: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryProfitAbility', query),
    // 获取账号分析可选的时间周期
    queryPeriodTime: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryPeriodTime', query),
    // 获取投资账户特征
    queryInvestmentFeatureLabels: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryInvestmentFeatureLabels', query),
    // 获取账户资产变动
    queryAssetChangeState: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryAssetChangeState', query),
    // 获取账户资产变动图表
    queryAssetChangeReport: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryAssetChangeReport', query),
    // 获取账户收益走势图表数据
    queryProfitTrendReport: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryProfitTrendReport', query),
    // 获取个股收益明细
    queryEachStockIncomeDetails: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryEachStockIncomeDetails', query),
    // 获取brinson归因分析
    queryAttributionAnalysis: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryAttributionAnalysis', query),
    // 获取期末资产配置数据
    queryEndTermAssetConfig: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryEndTermAssetConfig', query),
    // 获取资产配置变动走势
    queryAssetConfigTrend: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryAssetConfigTrendInfo', query),
    // 获取账户回撤数据
    queryAccountRetracementComparison: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryAccountRetracementComparison', query),
    // 获取账户风险收益对比
    queryRiskBenefitComparison: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryRiskBenefitComparison', query),
    // 获取产品收益明细
    queryProductIncomeDetail: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryProductIncomeDetail', query),
    // 获取投资账户分析报告历史列表
    queryAccountAnalysisReportsList: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/queryAccountAnalysisReportsList', query),
    // 删除资账户分析报告
    deleteAccountAnalysisReport: query => api.post('/groovynoauth/fsp/cust/accountAnalysis/deleteAccountAnalysisReport', query),
  };
}
