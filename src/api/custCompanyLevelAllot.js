/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 客户公司级客户分配
 * @Date: 2019-07-02 13:50:18
 * @Last Modified by: <PERSON><PERSON><PERSON>K<PERSON>
 * @Last Modified time: 2019-07-02 15:26:14
 */

export default function custCompanyLevelAllot(api) {
  return {
    // 新建中查询辅服务经理列表
    queryEmpList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryAllEmpList', query),
    // 保存
    saveData: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/saveCompanyLevelCustAssign', query),
    // 右侧详情
    queryDetailInfo: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryCompanyLevelAssignDetail', query),
    // 右侧详情中客户列表
    queryDetailCustList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryCompanyLevelAssignDetailCustList', query),
    // 客户校验
    checkCustInfo: (query) => api.post('/fspa/aorta/user/api/desktop/relation/companylevel/pc/CompanyLevelValidate/checkCustomer', query, { isFullUrl: true }),
    // 跨境服务关系校验
    checkCustCityLimit: (query) => api.post('/fspa/aorta/user/api/desktop/relation/companylevel/pc/CompanyLevelValidate/checkManager', query, { isFullUrl: true }),
  };
}
