/*
 * @Description:
 * @Author: l<PERSON>zhenzhen
 * @Date: 2019-08-15 14:05:43
 * @Last Modified by: l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-03-09 10:06:08
 */

export default function productOperatorManage(api) {
  return {
    // 获取经办人列表
    queryOperatorList: query => api.post('/groovynoauth/fsp/emp/operatorapply/queryOperatorApplyList', query),
    // 校验所选客户是否符合经办人要求
    validateCust: query => api.post('/groovynoauth/fsp/emp/operatorapply/validateEmpInfo', query),
    // 搜索模糊查询客户
    searchCustList: query => api.post('/groovynoauth/fsp/emp/operatorapply/searchOperatorList', query),
    // 查询最后一个经办人信息
    queryLastOperatorInfo: query => api.post('/groovynoauth/fsp/emp/operatorapply/queryLastOperatorInfo', query),
    // 新建经办人提交
    submiteOperator: query => api.post('/groovynoauth/fsp/emp/operatorapply/submitBusApply', query),
    // 获取组织机构树完整版
    getUpOrgTree: query => api.post('/groovynoauth/fsp/emp/org/queryEmpOrgTree', query, { noEmpId: true }),
    // 查询审批人信息
    queryApproveInfo: query => api.post('/groovynoauth/fsp/empActiviti/queryAssigns', query),
    // 新建提交之后发起流程
    startFlow: query => api.post('/groovynoauth/fsp/empActiviti/startFlow', query),
  };
}
