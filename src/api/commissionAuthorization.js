/*
 * @Author: yanfaping
 * @Date: 2024-09-02 11:13:58
 * @LastEditors: yanfaping
 * @LastEditTime: 2025-01-15 15:07:14
 * @Description: 佣金授权申请相关接口
 */
export default function commissionAuthorization(api) {
  return {
    // 查询申请类型字典
    querySubType: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/AuthCommissionQuery/querySubType', query, { isFullUrl: true }),
    // 查询佣金费率下拉选项字典接口
    queryCommissionRateDictionary: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommissionRateDictionary', query),
    // 文件解析接口
    parseExcel: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/AuthBatchValid/parseExcel', query, { isFullUrl: true }),
    // 文件解析进度
    getValidProgress: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/AuthBatchValid/getValidProgress', query, { isFullUrl: true }),
    // 文件解析结果
    queryValidResult: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/AuthBatchValid/queryValidResult', query, { isFullUrl: true }),
    // 查询附件
    getAttachmentList: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),
    // 删除客户
    deleteImportCust: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/AuthBatchValid/deleteImportedCust', query, { isFullUrl: true }),
    // 删除附件
    deleteAttachment: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileDeleteForSpecial', query, { isFullUrl: true }),
    // 提交申请
    submitAuthCommissionApply: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/save/pc/CommissionSave/saveAuthCommission', query, { timeout: 20000, isFullUrl: true }),
    // 查询审批按钮及下一步审批人
    queryFlowButtons: (query) => api.post('/fspa/aorta/biz/api/desktop/workflow/common/pc/ActivitiFlowCommonQuery/queryFlowButton', query, { isFullUrl: true }),
    // 查询佣金授权详情
    queryAuthCommissionDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/AuthCommissionQuery/queryAuthAdjustDetail', query, { isFullUrl: true }),
    // 查询审批历史
    queryFlowHistory: (query) => api.post('/fspa/aorta/biz/api/desktop/workflow/common/pc/ActivitiFlowCommonQuery/queryFlowHistory', query, { isFullUrl: true }),
    // 审批流转
    doApprove: (query) => api.post('/fspa/aorta/biz/api/desktop/workflow/common/pc/ActivitiFlowCommonStartTask/doApprove', query, { isFullUrl: true }),
    // 查询客户列表
    queryCustInfoDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/AuthCommissionQuery/queryCustInfoDetail', query, { isFullUrl: true }),
  };
}
