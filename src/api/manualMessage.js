/*
 * @Author: lijingjing
 * @Date: 2020-07-21 09:58:15
 * @Description: 手工消息推送相关接口
 */

export default function manualMessage(api) {
  return {
    // 发送消息
    // 迁移前旧接口路径：/groovynoauth/fsp/message/manual/releaseNotice
    releaseNotice: (query) => api.post('/fspa/aorta/biz/api/desktop/notification/inbox/pc/manualNoticeService/releaseNotice', query, { isFullUrl: true }),
    // 导入名单
    importEmpList: (query) => api.post('/groovynoauth/fsp/message/manual/importEmpList', query),
    // 查询导入名单进度结果信息
    queryImportProcess: (query) => api.post('/groovynoauth/fsp/message/manual/queryImportProcess', query),
    // 新建/编辑消息接口
    // 迁移前旧接口路径：/groovynoauth/fsp/message/manual/saveManualMsgInfo
    saveManualMsgInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/notification/inbox/pc/manualNoticeService/saveManualMsgInfo', query, { isFullUrl: true }),
    // 查看消息详情
    // 迁移前旧接口路径：/groovynoauth/fsp/message/manual/queryManualMessageDetail
    queryMessageDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/notification/inbox/pc/manualNoticeService/queryManualMessageDetail', query, { isFullUrl: true }),
    // 查询岗位类型列表
    queryJobTypeList: (query) => api.post('/groovynoauth/fsp/message/manual/queryJobTypeList', query),
    // 查询手工消息栏目类型
    queryManualColumnList: (query) => api.post('/groovynoauth/fsp/message/manual/queryManualColumnList', query),
    // 查询推送规模人数
    queryPushNumber: (query) => api.post('/groovynoauth/fsp/message/manual/queryPushNumber', query),
    // 手工消息推送-列表页查询
    // 迁移前旧接口路径：/groovynoauth/fsp/message/manual/queryManualNotificationList
    queryManualNotificationList: (query) => api.post('/fspa/aorta/biz/api/desktop/notification/inbox/pc/manualNoticeService/queryManualNotificationList', query, { isFullUrl: true }),
    // 手工消息推送-获取人群字典
    queryReceiverDictionary: (query) => api.post('/groovynoauth/fsp/message/manual/queryReceiverDictionary', query),
    // 手工消息推送-删除消息
    // 迁移前旧接口路径：/groovynoauth/fsp/message/manual/deleteManualNotification
    deleteManualNotification: (query) => api.post('/fspa/aorta/biz/api/desktop/notification/inbox/pc/manualNoticeService/deleteManualNotification', query, { isFullUrl: true }),
    // 根据关键字查员工信息
    queryEmpList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
  };
}
