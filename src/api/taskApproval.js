/*
 * @Description: smart 任务相关接口
 * @Author: LiuJianShu-K0180193
 * @Date: 2020-06-04 00:43:21
 * @Last Modified by: sunweibin
 * @Last Modified time: 2020-07-21 11:10:33
 */
export default function taskApproval(api) {
  return {
    // 提交审批记录申请
    submitApplication: (query) => api.post('/groovynoauth/fsp/campaign/taskapprove/addApply', query),
    // 查询审批列表
    queryApplicationList: (query) => api.post('/groovynoauth/fsp/campaign/taskapprove/queryApplyList', query),
    // 查询审批详情
    queryDetail: (query) => api.post('/groovynoauth/fsp/campaign/taskapprove/queryApplyDetail', query),
    // 查询审批按钮
    queryFlowButton: (query) => api.post('/groovynoauth/fsp/campaign/taskapprove/queryFlowButton', query),
    // 更新审批记录单
    updateApply: (query) => api.post('/groovynoauth/fsp/campaign/taskapprove/updateApply', query),
    // 流程接口
    doApprove: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/doApprove', query),
    // 获取问卷调查填写结果
    queryQuestionnaireResult: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryQuestionnaireResult', query),
  };
}
