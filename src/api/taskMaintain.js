/*
 * @Description: 运维管理任务维护接口
 * @Author: sunweibin-K0100008
 * @Date: 2019-07-17 16:44:00
 */

export default function taskMaintain(api) {
  return {
    // 运维管理-任务维护-首页申请单列表
    queryApplyList: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/queryApplyList', query),
    // 运维管理-任务维护-首页申请单详情
    queryDetail: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/queryApplyDetail', query),
    // 运维管理-任务维护-根据任务ID或者名称搜索任务列表
    queryTasksByKeyword: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/queryTaskByKeyword', query),
    // 运维管理-任务维护-获取审批历史
    queryApprovalHistory: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/queryFlowHistory', query),
    // 运维管理-任务维护-获取附件列表
    getAttachmentList: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),
    // 运维管理-任务维护-获取下一步审批人和按钮
    getNextStepInfo: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/queryNextStepInfo', query),
    // 运维管理-任务维护-新建/驳回后修改-保存修改信息
    saveApplyInfo: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/saveTaskMaintain', query),
    // 运维管理-任务维护-新建流程
    creatNewFlow: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/startFlow', query),
    // 运维管理-任务维护-驳回后修改走流程或者是审批流程
    doApproval: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/doApprove', query),
    // 运维管理-任务维护-新建-任务详情
    queryTaskDetail: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/queryTaskDetail', query),
    // 运维管理-任务维护-检查任务是否可以修改/删除
    checkTask: (query) => api.post('/groovynoauth/fsp/campaign/taskMaintain/checkTask', query),
    // 运维管理-任务维护-检查是否交易日
    checkIsTradingDay: (query) => api.post('/groovynoauth/fsp/campaign/smart/queryIsTradingDay', query),
    // SMART任务审批-获取审批历史
    queryTaskApprovalHistory: (query) => api.post('/groovynoauth/fsp/campaign/taskapprove/queryFlowHistory', query),
  };
}
