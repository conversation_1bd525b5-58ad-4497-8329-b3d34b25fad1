/*
 * @Author: wa<PERSON><PERSON><PERSON>
 * @Date: 2018-11-05 17:55:20
 * @Last Modified by: sunweibin
 * @Last Modified time: 2019-06-04 11:02:20
 */
export default function detailServiceRelationship(api) {
  return {
    // 查询新版客户360详情下的服务关系中服务团队
    queryCustServiceTeam: query => api.post('/groovynoauth/fsp/cust/custdetail/queryCustServiceTeam', query),
    // 查询新版客户360详情下的服务关系中介绍信息
    queryCustDevInfo: query => api.post('/groovynoauth/fsp/cust/custdetail/queryCustDevInfo', query),
    // 查询新版客户360详情下的服务关系中服务历史
    queryCustServiceHistory: query => api.post('/groovynoauth/fsp/cust/custdetail/queryCustServiceHistory', query),
    // 查询客户转销户历史数据
    queryCustSwitchAndCloseHistory: query => api.post('/groovynoauth/fsp/cust/custdetail/queryCustSwitchAndCloseHistory', query),
    // 查询服务权重历史
    queryWeightHistory: query => api.post('/groovynoauth/fsp/cust/custdetail/queryWeightHistory', query),
  };
}
