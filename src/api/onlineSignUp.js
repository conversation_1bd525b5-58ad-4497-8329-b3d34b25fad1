/**
 * @Author: tiant<PERSON>(K0181209)
 * @Date: 2019-09-10 14:38:38
 * @Last Modified by: tiantian(K0181209)
 * @Last Modified time: 2019-09-20 13:42:08
 * @Descripter: 线上签约接口
 */

export default function onlineSignUp(api) {
  return {
    // Home左侧列表
    getDespiteSignList: query => api.post('/groovynoauth/fsp/biz/investcontract/queryContractList', query),
    // 附件
    getAttachmentList: query => api.post('/groovynoauth/fsp/biz/investcontract/queryAttachList', query),
    // 获取右侧列表详情信息(包含基本信息 + 其他佣金率)
    getDetailInfo: query => api.post('/groovynoauth/fsp/biz/investcontract/queryContractDetail', query),
    // 产品信息-当前订购产品
    getProductInfo: query => api.post('/groovynoauth/fsp/biz/investcontract/queryContractProdItems', query),
    // 产品信息-所有产品
    getProductInfoAll: query => api.post('/groovynoauth/fsp/biz/investcontract/queryContractProdItems', query),
    // 签约历史记录
    getSignHistoryList: query => api.post('/groovynoauth/fsp/biz/investcontract/queryAgrChgHistory', query),
    // 扣费流水
    getDeductionWaterList: query => api.post('/groovynoauth/fsp/biz/investcontract/queryCustAgrFeeList', query),
    // 其他佣金率
    getCommissionRate: query => api.post('/groovynoauth/fsp/biz/chgcommission/queryCommissionRate', query),
    // 审批记录
    getFlowHistory: query => api.post('/groovynoauth/fsp/agreeflow/queryFlowHistory', query),
    // 产品信息字典
    getProdDictionary: query => api.post('/groovynoauth/fsp/biz/investcontract/prodDictionary', query),
    // 终止协议
    terminateAgreement: query => api.post('/groovynoauth/fsp/biz/investcontract/terminateAgreement', query),
    // 获取部门
    getCustRange: query => api.post('/groovynoauth/fsp/biz/investcontract/queryOrgTree', query),

  };
}
