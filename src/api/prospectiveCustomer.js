/*
  * @Author: <PERSON><PERSON>xiaow<PERSON>
  * @Date: 2019-09-24 18:38:12
 * @Last Modified by: xiexiaow<PERSON>
 * @Last Modified time: 2019-10-21 19:50:27
  * @description 潜客商机的Api
  */

export default function prospectiveCustomer(api) {
  return {
    // 查询潜客商机字典列表
    queryDictList: (query) => api.post('/groovynoauth/fsp/potentialcust/queryPotentialDict', query),
    // 查询开户潜客列表
    queryOpenAccountList: (query) => api.post('/groovynoauth/fsp/potentialcust/queryOpenPotentialCustList', query),
    // 查询注册潜客列表
    queryRegisterList: (query) => api.post('/groovynoauth/fsp/potentialcust/queryRegisterPotentialCustList', query),
    // 获取附件列表
    queryAttachmentList: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),
    // 查询服务记录列表
    queryServiceRecord: (query) => api.post('/groovynoauth/fsp/potentialcust/queryPotentialCustServiceRecordList', query),
    // 查询分派员工列表
    queryRecipientList: (query) => api.post('/groovynoauth/fsp/service/potentialCust/queryAssignerList', query),
    // 一键派单
    allotCustToEmp: (query) => api.post('/groovynoauth/fsp/potentialcust/allotCustToEmp', query),
  };
}
