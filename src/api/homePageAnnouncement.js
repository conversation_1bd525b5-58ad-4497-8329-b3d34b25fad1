/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Descripter: 平台参数设置-首页内容-首页公告
 * @Date: 2019-08-13
 * @Last Modified by: xie<PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-08-16 13:50:52
 */

export default function homePageAnnouncement(api) {
  return {
    // 首页公告查询
    queryAnnouncement: query => api.post('/groovynoauth/fsp/announcement/queryAnnouncement', query),
    // 首页公告增删改
    saveAnnouncement: query => api.post('/groovynoauth/fsp/announcement/saveAnnouncement', query),
  };
}
