/**
 * 目标客户池模块的接口
 */

// 此处针对修改字典里面委托他人任务处理的字段从key-value修改为value-label对应关系
import _ from 'lodash';

function fixDictoryKeys(dict) {
  const { resultData, resultData: { deputeStatusDictList: deputeList } } = dict;
  // 因为初始的委托他人任务状态字典列表中的字段名为 key, value
  return {
    ...dict,
    resultData: {
      ...resultData,
      deputeStatusDictList: _.map(deputeList, (item) => ({
        value: item.key,
        label: item.value
      })),
    },
  };
}

export default function customerPool(api) {
  return {
    // 经营指标新增客户指标区域接口
    getCustCount: (query) => api.post('/groovynoauth/fsp/emp/kpi/queryNewCustCount3', query),
    // 获取资讯中心统一接口
    getInformation: (query) => api.post('/groovynoauth/fsp/info/infoCenter/queryInfo', query),
    // 获取客户池沪深归集率 和 业务开通指标（经营指标）
    getManagerIndicators: (query) => api.post('/groovynoauth/fsp/emp/kpi/queryManageKPIs', query),
    // 客户分析接口
    getCustAnalyticsIndicators: (query) => api.post('/groovynoauth/fsp/index/queryCustAnalyticsKPIs', query),
    // 获取客户池投顾绩效
    getPerformanceIndicators: (query) => api.post('/groovynoauth/fsp/emp/kpi/queryPerformanceKPIs', query),
    // 获取组织机构树完整版
    getCustRangeAll: (query) => api.post('/groovynoauth/fsp/emp/org/queryEmpOrgTree', query, { noEmpId: true }),

    // 按权限获取组织机构树
    getCustRangeByAuthority: (query) => api.post('/groovynoauth/fsp/emp/org/queryEmpPostnsOrgTree2', query),

    // 查询待办流程记录列表
    getToDoList: (query) => api.post('/groovynoauth/fsp/emp/workflow/queryWorkFlowTaskList', query),
    // 发起紫金币活动
    createMarketTask: (query) => api.post('/groovynoauth/fsp/cust/custlist/marktingStart', query, { timeout: 30000 }),
    // 我的申请
    getApplyList: (query) => api.post('/groovynoauth/fsp/emp/workflow/queryStartProcessTaskList', query),
    // 我的审批
    getApproveList: (query) => api.post('/groovynoauth/fsp/emp/workflow/queryParticipateProcessTaskList', query),
    // 发起人下拉框
    getInitiator: (query) => api.post('/groovynoauth/fsp/emp/workflow/queryOriginValues', query),

    // 获取客户池经营指标
    // getManageIndicators: (query) => api.post('/groovynoauth/fsp/emp/kpi/queryEmpKPIs', query),

    // 统计周期
    getStatisticalPeriod: (query) => api.get('/groovynoauth/fsp/dictionary', query, { noEmpId: true }).then(fixDictoryKeys),

    // (首页总数)
    getQueryNumbers: (query) => api.post('/groovynoauth/fsp/emp/todealwith/queryNumbers', query),

    // 今日可做任务总数
    getMotTaskCount: (query) => api.post('/groovynoauth/fsp/emp/notification/queryMotTaskCount', query),

    // 客户列表
    getCustomerList: (query) => api.post('/groovynoauth/fsp/cust/custlist/queryCustList3', query),

    // 获取机构客户列表
    getInstitutionCustList: (query) => api.post('/groovynoauth/fsp/cust/custlist/queryInstiCustList', query),

    // 客户列表中的6个月的收益数据
    getCustIncome: (query) => api.post('/groovynoauth/fsp/cust/custlist/queryCustIncome', query),

    // 默认推荐词及热词推荐列表
    getHotWds: (query) => api.post('/groovynoauth/fsp/cust/custlabel/queryHotWds3', query),

    // 联想的推荐热词列表
    getHotPossibleWds: (query) => api.post('/groovynoauth/fsp/cust/custlabel/queryPossibleHotWds3', query),

    // 客户分组列表信息
    customerGroupList: (query) => api.post('/groovynoauth/fsp/cust/custgroup/queryCustGroupList', query),

    // 添加客户到现有分组
    saveCustGroupList: (query) => api.post('/groovynoauth/fsp/cust/custgroup/saveCustGroupList2', query),

    // 添加客户到新建分组
    createCustGroup: (query) => api.post('/groovynoauth/fsp/cust/custgroup/createCustGroup2', query),

    // 客户分组批量导入客户解析客户列表
    queryBatchCustList: (query) => api.post('/groovynoauth/fsp/cust/custgroup/queryBatchCust', query),

    // 发起任务校验接口（获取批次Id）
    queryValidateBatchId: (query) => api.post('/groovynoauth/fsp/cust/task/validateCustomerList', query),

    // 查询校验进度
    queryValidateProgress: (query) => api.post('/groovynoauth/fsp/cust/task/queryCommonValidateProgress', query),

    // 自建任务提交
    createTask: (query) => api.post('/groovynoauth/fsp/cust/task/createTask', query),

    // 查询指定日期是否为交易日
    queryIsTradingDay: (query) => api.post('/groovynoauth/fsp/campaign/smart/queryIsTradingDay', query),

    // 自建任务编辑后，重新提交
    updateTask: (query) => api.post('/groovynoauth/fsp/cust/task/updateTask', query),

    // 自建任务编辑后，重新提交新接口
    updateTaskNew: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/create/pc/self/update', query, { isFullUrl: true }),

    // 客户列表页添加服务记录
    addCommonServeRecord: (query) => api.post('/groovynoauth/fsp/cust/service/addCommonServeRecord3', query),

    // 净创收数据
    queryKpiIncome: (query) => api.post('/groovynoauth/fsp/emp/kpi/queryNetIncome', query),

    // 查询机构与个人联系方式
    queryCustContact: (query) => api.post('/groovynoauth/fsp/cust/custlist/fspQueryCustContact', query),

    // 查询最近五次服务记录
    queryRecentServiceRecord: (query) => api.post('/groovynoauth/fsp/cust/service/queryRecentServiceRecord', query),

    // 关注与取消关注
    followCust: (query) => api.post('/groovynoauth/fsp/cust/custgroup/followCust', query),

    // 分组维度，客户分组列表
    queryCustomerGroupList: (query) => api.post('/groovynoauth/fsp/cust/custgroup/queryCustGroupList', query),

    // 某一个分组下面的客户列表
    queryGroupCustomerList: (query) => api.post('/groovynoauth/fsp/cust/custgroup/queryGroupCustList', query),

    // 查询客户列表，用于分组详情里面的客户搜索，联想
    queryPossibleCustList: (query) => api.post('/groovynoauth/fsp/biz/custList', query),

    // 操作分组（编辑、新增客户分组）
    operateGroup: (query) => api.post('/groovynoauth/fsp/cust/custgroup/operateGroup2', query),

    // 删除分组
    deleteGroup: (query) => api.post('/groovynoauth/fsp/cust/custgroup/deleteGroup', query),

    // 删除分组下的客户
    deleteCustomerFromGroup: (query) => api.post('/groovynoauth/fsp/cust/custgroup/operateCust', query),

    // 360服务记录查询
    queryServeRecords: (query) => api.post('/groovynoauth/fsp/cust/task/queryServeRecords', query),

    // 查询某客户的服务人员待选择列表
    getSearchServerPersonelList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),

    queryAllServiceRecord: (query) => api.post('/groovynoauth/fsp/cust/service/queryAllChannelServiceRecord', query),
    // 获取是否有权限下载MOT服务记录录音权限
    getLoadMotURLPermisson: (query) => api.post('/groovynoauth/fsp/cust/service/checkLoadPermisson', query),

    // 预览客户细分数据
    previewCustFile: (query) => api.post('/groovynoauth/fsp/cust/custlist/previewCustFile', query),

    // 查询审批人列表
    queryFlowStepInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/review/pc/queryFlowStepInfo/queryFlowItem', query, { isFullUrl: true }),

    queryLabelPeople: (query) => api.post('/groovynoauth/fsp/cust/task/queryLabelPeople', query),

    queryLabelInfo: (query) => api.post('/groovynoauth/fsp/cust/task/queryLabelInfo2', query),

    queryTagList: (query) => api.post('/groovynoauth/fsp/cust/custlabel/queryAllLabelsInfo', query),
    // 订购组合
    queryJxGroupProduct: (query) => api.post('/groovynoauth/fsp/product/finprod/queryJxGroupProduct', query),
    // 任务列表-任务详情基本信息
    queryBasicInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/query/pc/queryBasicInfo/query', query, { isFullUrl: true }),

    // 文件下载文件列表数据
    ceFileList: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),

    // 生成问卷模板id
    generateTemplateId: (query) => api.post('/groovynoauth/fsp/assess/common/saveTemplate', query),

    // 查询一级指标数据
    queryIndicatorData: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryTraceIndexDic', query),

    // 查询目标类型
    queryTargetTypeData: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryTargetTypeData', query),

    // 查询产品接口
    queryProduct: (query) => api.post('/groovynoauth/fsp/product/finprod/queryFinProductList', query),
    // 查询产品详情接口
    queryFinProductByCode: (query) => api.post('/groovynoauth/fsp/product/finprod/queryFinProductByCode', query),
    // 查询优惠券接口
    queryCouponList: (query) => api.post('/groovynoauth/fsp/cust/custcoupon/queryCustCoupons', query),

    // 查询产品绑定优惠券接口
    queryCouponProductList: (query) => api.post('/groovynoauth/fsp/cust/custcoupon/queryCustCouponRelPrdt', query),

    // 查询所勾选的客户是否可以设置标签以及设置自定义标签
    queryCanSetCustlabel: (query) => api.post('/groovynoauth/fsp/cust/custlabel/isAssistServiceManager', query),

    // 查询客户是否是我名下的客户
    isCustServedByPostn: (query) => api.post('/groovynoauth/fsp/cust/task/isCustServedByPostn', query),

    // 审批流程获取按钮
    queryApprovalBtn: (query) => api.post('/groovynoauth/fsp/flow/queryApprovalBtn', query),

    // 提交审批流程
    submitApproval: (query) => api.post('/groovynoauth/fsp/flow/submitApproval', query),
    // 查询持仓产品详情
    queryHoldingProduct: (query) => api.post('/groovynoauth/fsp/cust/custbriefinfo/queryCustHoldingProducts', query),

    // 首页查询所有可用客户标签列表
    queryCustLabelList: (query) => api.post('/groovynoauth/fsp/cust/custlabel/queryAllLabelsInfoByType', query),

    // 首页查询所有可用客户标签列表
    queryHoldingSecurityRepetition: (query) => api.post('/groovynoauth/fsp/product/portfolioInfoProd/queryCustHoldList', query),

    // 客户列表中查询持仓行业过滤器的数据
    queryIndustryList: (query) => api.post('/groovynoauth/fsp/info/infoCenter/queryIndustry', query),

    // 查询持仓行业详情信息
    queryHoldingIndustryDetail: (query) => api.post('/groovynoauth/fsp/cust/custbriefinfo/queryCustHoldingProductDetails', query),

    // 查询优惠券详情信息
    queryCouponDetail: (query) => api.post('/groovynoauth/fsp/cust/custcoupon/queryCouponDetail', query),

    // 查询自定义标签
    queryDefinedLabelsInfo: (query) => api.post('/groovynoauth/fsp/cust/custlabel/queryDefinedLabelsInfo', query),

    // 查询是否触发服务评价
    queryCommEvaTriggerStatus: (query) => api.post('/groovynoauth/fsp/cust/service/queryCommEvaTriggerStatus', query),

    // 发送触发服务评价的结果
    sendCommEvaluate: (query) => api.post('/groovynoauth/fsp/cust/service/sendCommEvaluate2', query),

    // 更新触发状态
    updServeTrigger: (query) => api.post('/groovynoauth/fsp/cust/service/updServeTrigger', query),

    // 选评放弃评价
    cancelEvaluateTrigger: (query) => api.post('/groovynoauth/fsp/cust/service/cancelEvaluateTrigger', query),

    // 查询开户渠道
    queryAccountOpenChannel: (query) => api.post('/groovynoauth/fsp/cust/custlist/queryAccountOpenChannel', query),

    // 查询开户合作方
    queryAccountOpenCompany: (query) => api.post('/groovynoauth/fsp/cust/custlist/queryAccountOpenCompany', query),

    // 查询持仓金融产品类别列表
    queryFinancialProductList: (query) => api.post('/groovynoauth/fsp/cust/custlist/queryFinancialProductList', query),

    // 终止待办接口
    stopWorkFlowTask: (query) => api.post('/groovynoauth/fsp/emp/workflow/stopWorkFlowTask', query),
    // 新客户列表-发起任务-校验客户
    validateNewCustomerList: (query) => api.post('/groovynoauth/fsp/cust/incite/validateNewCustomerList', query),
    // 我的客群-发起任务-校验客户
    validateCustomerGroup: (query) => api.post('/groovynoauth/fsp/cust/incite/validateCustomerGroup', query),
    // smart任务管理-发起任务-选择我的客群
    queryMyGroupList: (query) => api.post('/groovynoauth/fsp/campaign/smart/queryCustGroupList', query),
    // 自建任务新流程-发起任务-校验客户
    validateCust: (query) => api.post('/groovynoauth/fsp/campaign/smart/validateCust', query),
    // 查询新流程校验进度
    queryNewValidateProgress: (query) => api.post('/groovynoauth/fsp/campaign/smart/queryValidateProgress', query),
    // 根据客群id集合查询我的客群人数
    queryCustCountByGroupIdList: (query) => api.post('/groovynoauth/fsp/campaign/smart/queryCustCountByGroupIdList', query),
    // 自建任务提交新接口
    createTaskNew: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/create/pc/self/create', query, { isFullUrl: true }),
    // 任务提示新接口
    queryTaskTips: (query) => api.post('/groovynoauth/fsp/cust/task/queryTaskTips', query),
  };
}
