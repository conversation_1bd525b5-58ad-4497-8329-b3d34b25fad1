/**
 * @Description: 超额快取
 * @Author: l<PERSON>zhenzhen
 * @Date: 2020-04-01 09:37:16
 * @Last Modified by: l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-04-08 11:08:39
 */
export default function excessCache(api) {
  return {
    // 查询超额快取右侧详情（基本信息，拟稿人信息，附件）
    queryDetailInfo: (query) => api.post('/groovynoauth/fsp/excesscache/queryExcessCacheDetail', query),
    // 查询审批历史
    queryFlowHistory: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowHistory', query),
    // 发起流程
    startFlow: (query) => api.post('/groovynoauth/fsp/activitiflow/startFlow', query),
    // 获取附件列表
    getAttachmentList: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),
    // 查询下一步审批人
    queryButtonList: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowButton', query),
    // 搜索查询客户
    queryCustList: (query) => api.post('/groovynoauth/fsp/common/queryCustList', query),
    // 根据所选客户查询客户详情
    searchCustInfo: (query) => api.post('/groovynoauth/fsp/excesscache/searchCustInfo', query),
    // 新建-提交
    saveExcessCache: (query) => api.post('/groovynoauth/fsp/excesscache/saveExcessCache', query),
  };
}
