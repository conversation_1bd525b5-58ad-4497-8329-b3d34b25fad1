/*
 * @Author: 016187
 * @Date: 2019-12-19 09:58:15
 * @LastEditors  : 016187
 * @LastEditTime : 2019-12-31 16:31:18
 * @Description: 财富管理室预定（会议室预定）接口
 */

// 开始时间升序
const SORT_ASC = 'beginTime,asc';
// 开始时间降序
const SORT_DESC = 'beginTime,desc';

export default function meetingRoom(api) {
  return {
    // 查询重点关注
    queryMeetingList: (query) => api.post('/groovynoauth/fsp/bookMeeting/queryMeetingList', { ...query, sort: SORT_ASC }),
    // 查询全部预定列表
    queryAllMeetingList: (query) => api.post('/groovynoauth/fsp/bookMeeting/queryAllMeetingList', { ...query, sort: SORT_DESC }),
    // 查询会议室管理
    queryMeetingRooms: (query) => api.post('/groovynoauth/fsp/bookMeeting/queryMeetingRooms', { ...query, sort: SORT_DESC }),
    // 提交会议室预定信息
    bookMeeting: (query) => api.post('/groovynoauth/fsp/bookMeeting/bookMeeting', query, { ignoreCatch: true }),
    // 根据预定id查询详情
    queryMeetingDetail: (query) => api.post('/groovynoauth/fsp/bookMeeting/queryMeetingDetail', query),
    // 根据预定id取消会议
    cancelMeeting: (query) => api.post('/groovynoauth/fsp/bookMeeting/cancelMeeting', query),
    // 查询会议室预定信息 - 预定页面使用
    queryRoomBookInfo: (query) => api.post('/groovynoauth/fsp/bookMeeting/queryRoomBookInfo', query),
    // 根据会议室id查询详情 - 预定页面使用
    queryRoomDeatil: (query) => api.post('/groovynoauth/fsp/bookMeeting/queryRoomDeatil', query),
    // 查询同事
    queryEmpList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
    // 完成提醒
    meetingRemind: (query) => api.post('/groovynoauth/fsp/bookMeeting/meetingRemind', query),
    // 获取当前员工可预订会议部门信息
    queryScheduledMeetPlaces: (query) => api.post('/groovynoauth/fsp/bookMeeting/queryScheduledMeetPlaces', query),
  };
}
