/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 客户360-投资者评估相关api
 * @Date: 2018-11-07 10:00:46
 * @Last Modified by: <PERSON><PERSON><PERSON>K<PERSON>
 * @Last Modified time: 2019-01-09 15:46:30
 */
export default function detailInvestorAssess(api) {
  return {
    // 查询涉税信息
    queryTaxInformation: query => api.post('/groovynoauth/fsp/cust/custdetail/queryTaxInformation', query),
    // 更新涉税信息
    updateTaxInformation: query => api.post('/groovynoauth/fsp/cust/custdetail/updateTaxInformation', query),
    // 查询异常交易信息
    queryUnusualTransaction: query => api.post('/groovynoauth/fsp/cust/custdetail/queryUnusualTransaction', query),
    // 查询信用业务
    queryCreditBusiness: query => api.post('/groovynoauth/fsp/cust/custdetail/queryCreditBusiness', query),
    // 查询恶意投诉
    queryMaliciousComplaint: query => api.post('/groovynoauth/fsp/cust/custdetail/queryMaliciousComplaint', query),
    // 查询适当性记录
    queryAppropriatenessRecord: query => api.post('/groovynoauth/fsp/cust/custdetail/queryAppropriatenessRecord', query),
    // 查询核查信息详情
    queryCheckInformation: query => api.post('/groovynoauth/fsp/cust/custdetail/queryCheckInformation', query),
    // 核查信息新建
    addCheckInformation: query => api.post('/groovynoauth/fsp/cust/custdetail/addCheckInformation', query),
    // 查询重点监控账户-列表
    queryKeyMonitoringAccount: query => api.post('/groovynoauth/fsp/cust/custdetail/queryKeyMonitoringAccountList', query),
    // 查询重点监控账户-核查信息
    queryVerification: query => api.post('/groovynoauth/fsp/cust/custdetail/queryVerification', query),
    // 查询uuId
    getUUID: query => api.post('/file/getUUID', query),
  };
}
