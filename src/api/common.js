/**
 * @Description: 公用的接口
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-10-25 15:32:30
 */
export default function common(api) {
  return {
    // 获取部门
    getCustRange: (query) => api.post('/groovynoauth/fsp/queryOrgInfo', query),
    // 员工职责与职位
    getEmpInfo: (query) => api.post('/groovynoauth/emp/queryEmpResInfo', query),
    // 获取用户有权限查看的菜单
    getMenus: (query) => api.post('/groovynoauth/fsp/newMenu', query),
    getMenus2: (query) => api.post('/fspa/aorta/user/api/desktop/emp/menu/pc/menu/queryMain', query, { isFullUrl: true }),
    // 用户切换岗位
    // changePost: query => api.post('/groovynoauth/fsp/emp/info/changePost', query),
    changePost: (query) => api.postFspData('/chgPstn', query, { ignoreCatch: true }),
    // 添加服务记录中 服务类型
    getServiceType: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryMissionList2', query),
    // 查询瞄准镜筛选条件
    getFiltersOfSightingTelescope: (query) => api.post('/groovynoauth/fsp/cust/custlist/queryFilters', query),
    // 增加通话记录接口，关联服务记录
    addCallRecord: (query) => api.post('/groovynoauth/fsp/emp/mobilebinding/addCallRecord', query),
    // 获取附件列表
    getAttachmentList: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),
    // 获取附件列表(走存储网关)
    getStorageAttachList: (query) => api.post('/storage/list', query),
    // 获取服务经理列表
    getEmpList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
    // 查询国家列表
    queryCountryList: (query) => api.post('/groovynoauth/fsp/common/queryCountryList', query),
    // 查询历史流程
    queryHistoryFlow: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowHistory', query),
    // 查询下一步按钮和审批人
    queryFlowButtonList: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowButton', query),
    // 发起流程
    startFlow: (query) => api.post('/groovynoauth/fsp/activitiflow/startFlow', query),
    // 查询客户列表
    queryCustList: (query) => api.post('/groovynoauth/fsp/cust/manager/queryCustList', query),
    // 保存机主信息
    saveOwnerInfo: (query) => api.post('/groovynoauth/fsp/cust/custdetail/saveOwnerInfo', query),
    // 公共的查客户接口-速度快
    queryCustomerList: (query) => api.post('/groovynoauth/fsp/common/queryMyCustList', query),
    // 新的查询客户列表接口
    queryCustomerListNew: (query) => api.post('/groovynoauth/fsp/common/queryCustList', query),
    // 驳回后修改发起流程
    doApprove: (query) => api.post('/groovynoauth/fsp/activitiflow/doApprove', query),
    // ecif字典接口
    queryEcifDict: (query) => api.post('/groovynoauth/fsp/common/queryEcifDict', query),
    // 搜索产品
    queryProductList: (query) => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryProd', query),
    // 查询审批人
    queryApprovalList: (query) => api.post('/groovynoauth/fsp/activitiflow/queryAssigns', query),
    // 查询投顾名下客户列表
    queryMngCustList: (query) => api.post('/groovynoauth/common/queryMngCustList', query),
    // 查询历史流程
    queryAgreementHistoryFlow: (query) => api.post('/groovynoauth/fsp/agreeflow/queryFlowHistory', query),
    // 查询下一步按钮和审批人
    queryAgreementFlowButtonList: (query) => api.post('/groovynoauth/fsp/agreeflow/queryFlowButton', query),
    // 发起流程
    startAgreementFlow: (query) => api.post('/groovynoauth/fsp/agreeflow/startflow', query),
    // 驳回后修改发起流程
    doAgreementApprove: (query) => api.post('/groovynoauth/fsp/agreeflow/doApprove', query),
    // 根据业务id获取附件列表
    getAttachmentListByBusiness: (query) => api.post('/s3file/common/s3FileList', query),
    // 组织架构树接口，支持按岗位查询职责与组织
    queryByPosition: (query) => api.post('/desktop/emp/info/pc/EmployeePosition/queryByPosition', query),
    // 获取权限
    batchQueryPermissionByResourceKeys: (query) => api.post('/desktop/common/permission/pc/AortaPermissionQuery/batchQueryPermissionByResourceKeys', query),
    // 判断是否激活公务号
    getOfficialSIMByEmpId: (query) => api.post('/fspa/aorta/user/api/desktop/emp/phone/all/EmpMobileInfo/getOfficialSIMByEmpId', query, { isFullUrl: true }),
  };
}
