/*
 * @Author: <PERSON><PERSON><PERSON>ow<PERSON>
 * @Date: 2020-03-02 15:46:58
 * @Last Modified by: x<PERSON>xiaow<PERSON>
 * @Last Modified time: 2020-03-04 09:40:39
 * @Description: 平台参数设置-潜客分发配置
 */

export default function potentialDistribution(api) {
  return {
    // 潜客分发配置查询
    queryPotentialDistribution: query => api.post('/groovynoauth/potentialcust/queryPotentialCustConfigList', query),
    // 潜客分发配置修改
    savePotentialDistribution: query => api.post('/groovynoauth/potentialcust/updatePotentialCustConfig', query),
  };
}
