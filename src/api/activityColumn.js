/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Descripter: 平台参数设置-首页内容-活动栏目
 * @Date: 2018-11-05 17:12:18
 * @Last Modified by: z<PERSON><PERSON><PERSON>e-k0171276
 * @Last Modified time: 2021-09-09 16:25:19
 */

export default function activityColumn(api) {
  return {
    // 活动栏目提交
    submitContent: (query) => api.post('/groovynoauth/fsp/activityColumn/saveContent', query),
    // 产品名称模糊搜索
    queryProdNameByKeyword: (query) => api.post('/groovynoauth/fsp/activityColumn/queryProdNameByKeyword', query),
  };
}
