/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-04-14 15:26:26
 * @LastEditors: luz<PERSON>yu
 * @LastEditTime: 2023-03-08 16:09:16
 * @Description: 公务号通话明细查询（原录音文件管理功能）接口
 */

export default function recordsManage(api) {
  return {
    // 查询录音文件列表
    queryRecordsList: (query) => api.post('/groovynoauth/voice/queryRecordsList', query),
    // 查询录音文件相关的客户
    queryRecordsCustList: (query) => api.post('/groovynoauth/voice/queryRecordsCustList', query),
    // 更新服务纪录关联关系
    updateRecordInfo: (query) => api.post('/groovynoauth/voice/updateRecordInfo', query),
  };
}
