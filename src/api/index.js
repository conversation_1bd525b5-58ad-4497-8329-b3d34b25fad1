import apiCreator from '../utils/apiCreator';
import commonAPI from './common';
import reportAPI from './report';
import feedbackAPI from './feedback';
import permissionAPI from './permission';
import commissionAPI from './commission';
import commissionConfigAPI from './commissionConfig';
import singleCommissionAPI from './singleCommission';
import specialCommissionAPI from './specialCommission';
import unStandardCommissionAPI from './unStandardCommission';
import batchCommissionAPI from './batchCommission';
import autoCallCommissionAPI from './autoCallCommission';
import customerPoolAPI from './customerPool';
import contractAPI from './contract';
import channelsTypeProtocolAPI from './channelsTypeProtocol';
import seibelCommonAPI from './seibelCommon';
import performerViewAPI from './performerView';
import demoteAPI from './demote';
import relationAPI from './relation';
import customerFeedbackAPI from './customerFeedback';
import taskFeedbackAPI from './taskFeedback';
import mainPositionAPI from './mainPosition';
import morningBoradcastAPI from './morningBoradcast';
import preSaleQueryAPI from './preSaleQuery';
import stockAPI from './stock';
import pointsExchangeAPI from './pointsExchange';
import userCenterAPI from './userCenter';
import telephoneNumberManageAPI from './telephoneNumberManage';
import choicenessCombinationAPI from './choicenessCombination';
import investmentAdviceAPI from './investmentAdvice';
import operationCenterAPI from './operationCenter';
import custAllotAPI from './custAllot';
import messageCenterAPI from './messageCenter';
import stockOptionEvaluationAPI from './stockOptionEvaluation';
import latestViewAPI from './latestView';
import keyMonitorAccountAPI from './keyMonitorAccount';
import custRelationshipsAPI from './custRelationships';
import cancelAccountOLAPI from './cancelAccountOL';
import customerLabelAPI from './customerLabel';
import accountLimitAPI from './accountLimit';
import labelManagementAPI from './labelManagement';
import tempDeputeAPI from './tempDepute';
import newHomeAPI from './newHome';
import advisorSpaceAPI from './advisorSpace';
import customerDetailAPI from './customerDetail';
import detailServiceRelationAPI from './detailServiceRelationship';
import detailAccountInfoAPI from './detailAccountInfo';
import detailCustPropertyAPI from './detailCustProperty';
import detailProductOrderAPI from './detailProductOrder';
import activityColumnAPI from './activityColumn';
import homePageAnnouncementAPI from './homePageAnnouncement';
import taskAnalysisReportAPI from './taskAnalysisReport';
import detailDiscountCouponAPI from './detailDiscountCoupon';
import detailInvestAnalyzeAPI from './detailInvestAnalyze';
import detailBusinessHandAPI from './detailBusinessHand';
import detailContractManageAPI from './detailContractManage';
import detailInvestorAssessAPI from './detailInvestorAssess';
import orderManageAPI from './orderManage';
import taskManageAPI from './taskManage';
import workOrderManageAPI from './workOrderManage';
import assetAllocationAPI from './assetAllocation';
import serviceScenesAPI from './serviceScenes';
import scenarioAndLabelAPI from './scenarioAndLabel';
import reservationAPI from './reservation';
import custCompanyLevelAllotAPI from './custCompanyLevelAllot';
import acrossBranchCustAllotAPI from './acrossBranchCustAllot';
import weightSettingAPI from './weightSetting';
import customerMessageAPI from './customerMessage';
import onlineSignUpApi from './onlineSignUp';
import taskMaintainAPI from './taskMaintain';
import productPoolAPI from './productPool';
import productOperationAPI from './productOperation';
import productPushFlowAPI from './productPushFlow';
import prospectiveCustomerAPI from './prospectiveCustomer';
import onlineContractAPI from './onlineContract';
import meetingRoomApi from './meetingRoom';
import potentialGrabAPI from './potentialGrab';
import potentialCustomerAPI from './potentialCustomer';
import potentialDistributionAPI from './potentialDistribution';
import productOperatorManageAPI from './productOperatorManage';
import mobileStartADApi from './mobileStartAD';
import excessCacheAPI from './excessCache';
import transformTrackAPI from './transformTrack';
import recordsManageAPI from './recordsManage';
import taskApprovalAPI from './taskApproval';
import liveAPI from './live';
import futuresAnalyzeAPI from './futuresAnalyze';
import keyAttentionSettingAPI from './keyAttentionSetting';
import manualMessageAPI from './manualMessage';
import zeroDistanceAPI from './zeroDistance';
import channelPotentialAPI from './channelPotential';
import headquartersCustAllotAPI from './headquartersCustAllot';
import custDistributeAPI from './custDistribute';
import branchCentralAllotAPI from './branchCentralAllot';
import commissionNotifiesAPI from './commissionNotifies';
import qualifyInvestorAPI from './qualifyInvestor';
import customerAutoCirculationAPI from './customerAutoCirculation';
import serviceRadiusSettingAPI from './serviceRadiusSetting';
import cancelLabelApplyAPI from './cancelLabelApply';
import cancelLabelApplyMultiAPI from './cancelLabelApplyMulti';
import hotActivitiesConfigAPI from './hotActivitiesConfig';
import customizePosterAPI from './customizePoster';
import grayscaleAPI from './grayscale';
import assetConfigAPI from './assetConfig';
import recordsManageViewAPI from './recordsManageView';
import cooperationWeightAPI from './cooperationWeight';
import optionCommissionAPI from './optionCommission';
import reservedPhoneAPI from './reservedPhone';
import commissionAuthorizationAPI from './commissionAuthorization';

const api = apiCreator();

const exported = {
  // 暴露api上的几个底层方法: get / post
  ...api,

  // ========== 公用接口
  common: commonAPI(api),

  // ========== 客户资源池
  customerPool: customerPoolAPI(api),

  // ========== 执行者视图
  performerView: performerViewAPI(api),

  // ========== 绩效视图
  report: reportAPI(api),

  // ========== 反馈管理
  feedback: feedbackAPI(api),

  // ========== seibel 通用接口
  seibel: seibelCommonAPI(api),

  // ========== 权限申请私有接口
  permission: permissionAPI(api),

  // ========== 合作合约相关接口
  contract: contractAPI(api),

  // ========== 通道类型协议相关接口
  channelsTypeProtocol: channelsTypeProtocolAPI(api),

  // ========== 佣金调整的数据接口end
  commission: commissionAPI(api),
  // ========== 佣金配置管理的数据接口 end
  commissionConfig: commissionConfigAPI(api),

  // ========== 单佣金相关独立新页面佣金调整的数据接口end
  singleCommission: singleCommissionAPI(api),
  // ========== 特殊佣金相关独立新页面佣金调整的数据接口end
  specialCommission: specialCommissionAPI(api),
  // ========== 【批量佣金调整】相关独立新页面佣金调整的数据接口end
  batchCommission: batchCommissionAPI(api),
  // ========== 【自动外呼调佣申请】相关独立新页面佣金调整的数据接口end
  autoCallCommission: autoCallCommissionAPI(api),
  // ========== 【非标客户特殊佣金调整】相关独立新页面佣金调整的数据接口end
  unStandardCommission: unStandardCommissionAPI(api),

  // ========== 汇报关系树页面
  relation: relationAPI(api),

  // ========== 设置主职位接口
  mainPosition: mainPositionAPI(api),

  // ========== 降级客户接口
  demote: demoteAPI(api),

  // ========== 客户反馈
  customerFeedback: customerFeedbackAPI(api),

  // ========== 任务反馈
  taskFeedback: taskFeedbackAPI(api),

  // ========== 晨报
  morningBoradcast: morningBoradcastAPI(api),

  // ========== 售前适当性查询
  preSaleQuery: preSaleQueryAPI(api),

  // ========== 个股资讯
  stock: stockAPI(api),

  // ========== 积分兑换历史查询
  pointsExchange: pointsExchangeAPI(api),

  // ========== 用户中心
  userCenter: userCenterAPI(api),

  // ========== 公务手机和电话卡号管理
  telephoneNumberManage: telephoneNumberManageAPI(api),

  // ========== 精选组合
  choicenessCombination: choicenessCombinationAPI(api),

  // ========== 投资建议模板
  investmentAdvice: investmentAdviceAPI(api),

  // ========== 平台参数-运营中心
  operationCenter: operationCenterAPI(api),

  // ========== 客户分配分公司客户分配
  custAllot: custAllotAPI(api),

  // ========== 消息通知提醒
  messageCenter: messageCenterAPI(api),

  // ========== 股票期权评估申请
  stockOptionEvaluation: stockOptionEvaluationAPI(api),

  // ========== 最新观点
  latestView: latestViewAPI(api),

  // ========== 消息通知提醒
  keyMonitorAccount: keyMonitorAccountAPI(api),

  // ========== 客户关联关系
  custRelationships: custRelationshipsAPI(api),

  // ========== 线上销户
  cancelAccountOL: cancelAccountOLAPI(api),

  // ========== 客户自定义标签
  customerLabel: customerLabelAPI(api),
  // ========== 账户限制管理
  accountLimit: accountLimitAPI(api),
  // ========== 管理标签页面
  labelManagement: labelManagementAPI(api),
  // ========== 临时委托他人处理任务
  tempDepute: tempDeputeAPI(api),
  // ========== 丰富首页内容
  newHome: newHomeAPI(api),
  // ========== 投顾空间申请
  advisorSpace: advisorSpaceAPI(api),
  // ========== 新版客户360详情API
  customerDetail: customerDetailAPI(api),
  // ========== 新版客户360详情下账户信息Tab组件的API
  detailAccountInfo: detailAccountInfoAPI(api),
  // ========== 新版客户360详情下服务关系的API
  detailServiceRelationship: detailServiceRelationAPI(api),
  // ========== 新版客户360详情下客户属性Tab组件的API
  detailCustProperty: detailCustPropertyAPI(api),
  // ========== 新版客户360详情下产品订单Tab组件的API
  detailProductOrder: detailProductOrderAPI(api),
  // ========== 平台参数设置-首页内容-活动栏目
  activityColumn: activityColumnAPI(api),
  // ========== 平台参数设置-首页内容-首页公告
  homePageAnnouncement: homePageAnnouncementAPI(api),
  // ========== SMART任务相关运营报表
  taskAnalysisReport: taskAnalysisReportAPI(api),
  // ========== 新版客户360详情下理财优惠券Tab组件的API
  detailDiscountCoupon: detailDiscountCouponAPI(api),
  // ========== 客户360-投资能力分析相关api
  detailInvestAnalyze: detailInvestAnalyzeAPI(api),
  // ========== 新版客户360详情下业务办理的API
  detailBusinessHand: detailBusinessHandAPI(api),
  // ========== 新版客户360-合约管理-协议
  detailContractManage: detailContractManageAPI(api),
  // ========== 新版客户360-投资者评估
  detailInvestorAssess: detailInvestorAssessAPI(api),
  // ========== 统计查询-订单管理
  orderManage: orderManageAPI(api),
  // ========== 任务中心-服务记录管理
  taskManage: taskManageAPI(api),
  // ========== 嵌入其他项目的资产分布接口
  assetAllocation: assetAllocationAPI(api),
  // ========== 平台参数设置-服务场景规则设置
  serviceScenes: serviceScenesAPI(api),
  // ========== 任务管理-服务评价参数管理
  scenarioAndLabel: scenarioAndLabelAPI(api),
  // 预约服务记录
  reservation: reservationAPI(api),
  // ========== 留言反馈-客户留言
  customerMessage: customerMessageAPI(api),
  // ========== 客户公司级分配
  custCompanyLevelAllot: custCompanyLevelAllotAPI(api),
  // =========== 跨分公司客户分配
  acrossBranchCustAllot: acrossBranchCustAllotAPI(api),
  // =========== 权重设置
  weightSetting: weightSettingAPI(api),
  // 线上签约
  onlineSignUp: onlineSignUpApi(api),
  // =========== 运维管理-任务维护
  taskMaintain: taskMaintainAPI(api),
  // 分公司产品池维护
  productPool: productPoolAPI(api),
  // CRM工单管理
  workOrderManage: workOrderManageAPI(api),
  // 涨乐产品运营
  productOperation: productOperationAPI(api),
  // 涨乐运营-新建产品推送
  productPushFlow: productPushFlowAPI(api),
  // 客户中心-潜客商机
  prospectiveCustomer: prospectiveCustomerAPI(api),
  // 新建线上投顾签约
  onlineContract: onlineContractAPI(api),
  // 会议室预定
  meetingRoom: meetingRoomApi(api),
  // 客户中心-潜在客户
  potentialCustomer: potentialCustomerAPI(api),
  // 平台参数设置-潜客分发配置
  potentialDistribution: potentialDistributionAPI(api),
  // 运维管理-销售经办人管理
  productOperatorManage: productOperatorManageAPI(api),
  // 潜客抢单
  potentialGrab: potentialGrabAPI(api),
  // 移动启动广告配置
  mobileStartAD: mobileStartADApi(api),
  // 超额快取
  excessCache: excessCacheAPI(api),
  // 潜在客户-转化追踪
  transformTrack: transformTrackAPI(api),
  // 公务号通话明细查询（原录音文件管理功能）
  recordsManage: recordsManageAPI(api),
  // smart 任务审批
  taskApproval: taskApprovalAPI(api),
  // 投顾直播间
  live: liveAPI(api),
  // 期货账户-账户分析
  futuresAnalyze: futuresAnalyzeAPI(api),
  // 重点关注设置
  keyAttentionSetting: keyAttentionSettingAPI(api),
  // 手工消息推送
  manualMessage: manualMessageAPI(api),
  // 总分零距离
  zeroDistance: zeroDistanceAPI(api),
  // 联合服务渠道潜客
  channelPotential: channelPotentialAPI(api),
  // 总部客户分配
  headquartersCustAllot: headquartersCustAllotAPI(api),
  // 分公司集中分配
  custDistribute: custDistributeAPI(api),
  // 分公司集中客户分配
  branchCentralAllot: branchCentralAllotAPI(api),
  // 服务订购消息提醒
  commissionNotifies: commissionNotifiesAPI(api),
  // 合格投资者
  qualifyInvestor: qualifyInvestorAPI(api),
  // 参与客户自动流转申请
  customerAutoCirculation: customerAutoCirculationAPI(api),
  // 投顾服务半径设置
  serviceRadiusSetting: serviceRadiusSettingAPI(api),
  // 取消客户选投顾标识申请
  cancelLabelApply: cancelLabelApplyAPI(api),
  // 多客户取消客户选投顾标识申请
  cancelLabelApplyMulti: cancelLabelApplyMultiAPI(api),
  // 热门活动
  hotActivitiesConfig: hotActivitiesConfigAPI(api),
  // 自定义海报
  customizePoster: customizePosterAPI(api),
  // 灰度建设
  grayscale: grayscaleAPI(api),
  // 资产配置
  assetConfig: assetConfigAPI(api),
  // 公务号通话管理视图
  recordsManageView: recordsManageViewAPI(api),
  // 合作权重
  cooperationWeight: cooperationWeightAPI(api),
  // 期权佣金调整
  optionCommission: optionCommissionAPI(api),
  // 留资手机号线索派发
  reservedPhone: reservedPhoneAPI(api),
  // 佣金授权申请
  commissionAuthorization: commissionAuthorizationAPI(api)
};

export default exported;

export const {
  common,
  customerPool,
  performerView,
  report,
  feedback,
  seibel,
  permission,
  contract,
  channelsTypeProtocol,
  commission,
  commissionConfig,
  singleCommission,
  specialCommission,
  batchCommission,
  autoCallCommission,
  relation,
  mainPosition,
  demote,
  customerFeedback,
  taskFeedback,
  morningBoradcast,
  preSaleQuery,
  stock,
  pointsExchange,
  userCenter,
  telephoneNumberManage,
  choicenessCombination,
  investmentAdvice,
  operationCenter,
  custAllot,
  messageCenter,
  stockOptionEvaluation,
  latestView,
  keyMonitorAccount,
  custRelationships,
  cancelAccountOL,
  customerLabel,
  accountLimit,
  labelManagement,
  tempDepute,
  newHome,
  advisorSpace,
  customerDetail,
  detailServiceRelationship,
  detailAccountInfo,
  detailCustProperty,
  detailProductOrder,
  activityColumn,
  homePageAnnouncement,
  taskAnalysisReport,
  detailDiscountCoupon,
  detailInvestAnalyze,
  detailBusinessHand,
  detailContractManage,
  detailInvestorAssess,
  orderManage,
  taskManage,
  assetAllocation,
  serviceScenes,
  scenarioAndLabel,
  reservation,
  customerMessage,
  custCompanyLevelAllot,
  acrossBranchCustAllot,
  weightSetting,
  onlineSignUp, // 线上签约
  taskMaintain,
  productPool,
  workOrderManage,
  productOperation,
  productPushFlow,
  prospectiveCustomer,
  onlineContract,
  meetingRoom,
  potentialCustomer,
  potentialDistribution,
  productOperatorManage,
  potentialGrab,
  mobileStartAD,
  excessCache,
  transformTrack,
  recordsManage,
  live,
  post,
  taskApproval,
  futuresAnalyze,
  keyAttentionSetting,
  manualMessage,
  zeroDistance,
  channelPotential,
  headquartersCustAllot,
  custDistribute,
  branchCentralAllot,
  commissionNotifies,
  qualifyInvestor,
  customerAutoCirculation,
  serviceRadiusSetting,
  cancelLabelApply,
  hotActivitiesConfig,
  customizePoster,
  unStandardCommission,
  grayscale,
  assetConfig,
  recordsManageView,
  cooperationWeight,
  optionCommission,
  reservedPhone,
  cancelLabelApplyMulti,
  commissionAuthorization
} = exported;
