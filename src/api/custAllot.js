/**
 * @Description: 分公司客户分配
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-05-23 09:37:16
 * @Last Modified by: LiuJianShu-K0180193
 * @Last Modified time: 2019-07-18 10:34:37
 */
export default function custAllot(api) {
  return {
    // 获取详情信息--xzx
    queryDetailInfo: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryBranchAndDepartCustAssignDetail', query),
    // 下一步按钮和下一步审批人
    queryButtonList: (query) => api.post('/groovynoauth/fsp/activitiflow/departAndBranchCustAssignQueryButton', query),
    // 查询服务经理列表
    queryManageList: (query) => api.post('/groovynoauth/fsp/cust/manager/queryPriPostn', query),
    // 查询服务经理列表(新 2024.2.27新增，后续会替换原先queryManageList接口)
    queryManageList2: (query) => api.post('/fspa/aorta/user/api/desktop/relation/common/all/serviceRelation/queryMajorRelation', query, { isFullUrl: true }),
    // 提交客户分配
    saveChange: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/saveBranchAndDepartCustAssign', query),
    // 走流程接口
    doApprove: (query) => api.post('/groovynoauth/fsp/cust/manager/doApprove', query),
    // 批量划转的消息提醒数据--xzx
    queryNotifiesList: (query) => api.post('/groovynoauth/fsp/cust/manager/queryNotifiesList', query),
    // 查询辅助服务经理列表
    queryServiceManageList: (query) => api.post('/groovynoauth/fsp/cust/manager/queryServiceManageList', query),
    // 批量添加客户校验权重信息
    queryCustomerWeightAssignCount: (query) => api.post('/groovynoauth/fsp/cust/manager/queryCustomerWeightAssignCount', query),
    // 营业部校验客户
    syncValidDepartCustomers: (query) => api.post('/fspa/aorta/user/api/desktop/relation/departassign/pc/DepartAssign/validate', query, { isFullUrl: true }),
    // 分公司校验客户
    syncValidBranchCustomerList: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchassign/pc/BranchAssign/validate', query, { isFullUrl: true }),
    // 查询已校验的客户列表
    queryValidateCustList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryValidCustList', query),
    // 查询设置权重的客户个数
    queryInTransitWeightAssign: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryInTransitWeightAssign', query),
    // 查询详情中的客户列表
    queryDetailCustList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryBranchAndDepartAssignCustList', query),
    // 通知提醒用客户列表
    queryDetailCustListForEmp: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryBranchAndDepartAssignCustListForEmp', query),
    // 查询提交的客户数据中是否有投顾
    validBranchAndDepartCustTGConfirm: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/validBranchAndDepartCustTGConfirm', query),
    // 查询原跨分公司分配页面中的客户列表
    queryCrossBranchCustList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryCrossBranchCustList', query),
    // 查询某客户的服务人员待选择列表
    getSearchServerPersonelList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
    // 分公司、营业部导入客户后，校验服务经理是否可以分配（分公司客户分配功能改造需求新增）
    validateManage: (query) => api.post('/groovynoauth/fsp/cust/manager/validateManage', query),
  };
}
