/*
 * @Description: 服务预约接口文件
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>-K0180193
 * @Date: 2019-05-22 16:00:53
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>-K0180193
 * @Last Modified time: 2019-05-22 16:05:29
 */

export default function reservation(api) {
  return {
    // 查询预约列表
    queryList: query => api.post('/groovynoauth/fsp/reservation/queryList', query),
    // 新建预约服务
    addReservation: query => api.post('/groovynoauth/fsp/reservation/addReservation', query),
    // 编辑预约服务
    editReservation: query => api.post('/groovynoauth/fsp/reservation/editReservation', query),
    // 发送预约服务信息
    sendMessage: query => api.post('/groovynoauth/fsp/reservation/sendMessage', query),
    // 查询本人名下的客户
    queryMyCustomerList: query => api.post('/groovynoauth/fsp/common/queryMyCustList', query),
  };
}
