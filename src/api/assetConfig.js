// 资产配置 api
export default function assetConfig(api) {
  return {
    // 删除资产配置列表
    deleteAssetConfigData: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCmd/removeAssetConfigData', query),
    // 获取资产配置列表
    listAssetConfigData: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationQuery/listAssetConfigData', query),
    // 根据关键字查询客户列表
    queryAssetConfigCustList: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationQuery/listAssetConfigCust', query),
    // 根据关键字查询创建者
    queryConfigAssertCreatorList: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationQuery/listConfigAssertCreator', query),
    // 查询机构树
    listDataviewOrgTreeByEmpId: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationQuery/listDataviewOrgTreeByEmpId', query),
    // 市场观点
    getMarketOpinion: (query) => api.post('/desktop/content/assetallocation/pc/AnalysisReportQuery/listMarketOpinion', query),
    // 获取资产配置步骤1-客户KYC
    getCustKyc: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCustKycQuery/getCustKyc', query),
    // 获取资产配置步骤1-客户KYC配置客户的信息
    getCustMess: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCustKycQuery/getCustMess', query),
    // 保存资产配置步骤1-客户KYC
    saveCustKyc: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCustKycCmd/saveCustKyc', query),
    // 获取周期数据
    getAnalysisPeriod: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCustKycQuery/getAnalysisPeriod', query),
    // 获取资产配置步骤1-客户KYC查询客户列表数据
    queryAllCustList: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCustKycQuery/listAllCust', query),
    // 获取资产配置步骤1-客户KYC查询客户偏好数据
    getCustPreference: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCustKycQuery/getCustPreference', query),
    // 获取资产配置步骤1-客户KYC查询客户偏好数据-默认不传值
    getDefaultCustPreference: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCustKycQuery/getDefaultCustPreference', query),
    // 获取资产配置-账户分析大类列表
    listModules: (query) => api.post('/desktop/content/assetallocation/pc/AnalysisReportQuery/listModules', query),
    // 获取资产配置-账户分析模块详情
    getAnalysisReport: (query) => api.post('/desktop/content/assetallocation/pc/AnalysisReportQuery/getAnalysisReport', query),
    // 获取大类标题
    getBigTit: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCustKycQuery/getBigTit', query),
    // 获取产品筛选信息
    getProductFilters: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationPrdtQuery/listProductFilters', query),
    // 模糊搜索产品信息
    getProductListByKeyword: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationPrdtQuery/listProductByKeyword', query),
    // 获取产品信息
    getProductData: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationPrdtQuery/listProductData', query),
    // 第四步-配置建议-保存产品配置
    saveAssetConfig: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCmd/saveAssetConfig', query),
    // 第五步-生成报告-生成报告接口
    updateGenerateReport: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCmd/updateGenerateReport', query),
    // 第四步-保存产品配置
    saveAssetProductWeight: (query) => api.post('/desktop/content/assetallocation/pc/FunctionModuleQuery/saveAssetProductWeight', query),
    // 保存产品配置
    updatePeiZhiJianYiChanPinMingXi: (query) => api.post('/desktop/content/assetallocation/pc/AnalysisReportQuery/updatePeiZhiJianYiChanPinMingXi', query),
    // 获取当前账户是否有新建的权限
    checkEmpCanCreateNewAssetAllocation: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationQuery/checkEmpCanCreateNewAssetAllocation', query),
    // 查询流程按钮及审批人
    listFlowButtonsAndApprovers: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationFlowQuery/listFlowButtonsAndApprovers', query),
    // 第五步-提交审批
    startAssetAllocationAuditProcess: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationCmd/startAssetAllocationAuditProcess', query),
    // 查询审批详情
    getApproveAssetDetail: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationFlowQuery/getApproveAssetDetail', query),
    // 查询审批历史
    listFlowHistory: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationFlowQuery/listFlowHistory', query),
    // 走流程
    doApprove: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationFlowCmd/doApprove', query),
    // 查询资产配置相关信息
    getAssetConfigInfo: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationFlowQuery/getAssetConfigInfo', query),
    // 查询资产配置相关字典
    getAssetAllocationBizStateDict: (query) => api.post('/paasgw/aorta-business-operations/AcontentDictQueryService/getAssetAllocationBizStateDict', query),
    // 下载pdf
    checkAssetAllocationDownloadLimit: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationFlowCmd/checkAssetAllocationDownloadLimit', query),
    // 重新生成pdf
    retryGenerateFinalPdfReport: (query) => api.post('/paasgw/aorta-business-operations/AssetAllocationFlowCmdService/retryGenerateFinalPdfReport', query),
    // 立即触发
    doSign: (query) => api.post('/desktop/content/assetallocation/pc/AssetAllocationFlowCmd/doSign', query),
    // 获取资产配置-账户分析模块详情
    newGetAnalysisReport: (query) => api.post('/fspa/strategy/api/desktop/abo/zjadvisor/assetallocationv2/pc/AnalysisReportV2Query/getAnalysisReport', query, { isFullUrl: true }),
  };
}
