/**
 * @Description: 线上签约新建相关
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-05-23 09:37:16
 * @Last Modified by: LiuJianShu-K0180193
 * @Last Modified time: 2019-07-18 10:34:37
 */
export default function onlineContract(api) {
  return {
    // 根据关键字查询客户
    queryCustList: query => api.post('/groovynoauth/fsp/common/queryMyCustList', query),
    // 前置校验接口
    custValidate: query => api.post('/groovynoauth/fsp/biz/investcontract/custValidate', query),
    // 校验客户是否有在途佣金调整订单
    commissionValidate: query => api.post('/groovynoauth/fsp/biz/investcontract/commissionValidate', query),
    // 查询客户信息接口
    queryCustInfo: query => api.post('/groovynoauth/fsp/biz/investcontract/queryCustInfo', query),
    // 查询协议模板
    queryTemplateList: query => api.post('/groovynoauth/fsp/biz/investcontract/queryTemplateList', query),
    // 查询可选的股基佣金率
    queryGJCommissionRate: query => api.post('/groovynoauth/fsp/biz/chgcommsion/querySingelCommionJustRate', query),
    // 查询其他的佣金率选项
    queryOtherCommissionRate: query => api.post('/groovynoauth/fsp/biz/chgcommsion/queryOtherCommissionRate', query),
    // 查询可选的产品列表
    queryProdList: query => api.post('/groovynoauth/fsp/biz/investcontract/queryProdList', query),
    // 产品三匹配
    validCustAndProd: query => api.post('/groovynoauth/fsp/biz/investcontract/validCustAndProd', query),
    // 查询提交按钮
    queryFlowButton: query => api.post('/groovynoauth/fsp/agreeflow/queryFlowButton', query),
    // 保存接口
    saveContract: query => api.post('/groovynoauth/fsp/biz/investcontract/saveContract', query),
    // 更新接口
    updateContract: query => api.post('/groovynoauth/fsp/biz/investcontract/updateContract', query),
    // 发起流程接口
    startFlow: query => api.post('/groovynoauth/fsp/agreeflow/startflow', query),
    // 查询字典接口
    queryProdDictionary: query => api.post('/groovynoauth/fsp/biz/investcontract/prodDictionary', query),
    // 审批流程
    doApprove: query => api.post('/groovynoauth/fsp/agreeflow/doApprove', query),
    // 产品校验
    validProduct: query => api.post('/groovynoauth/fsp/biz/investcontract/validOrderedPrdt', query),
  };
}
