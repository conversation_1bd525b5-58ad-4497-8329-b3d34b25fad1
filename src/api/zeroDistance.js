/*
 * @Author: <PERSON><PERSON>xiaow<PERSON>
 * @Date: 2020-10-12 16:41:48
 * @Last Modified by: sunweibin
 * @Last Modified time: 2022-07-29 14:54:16
 * @Description: 总分零距离api文件
 */

export default function zeroDistance(api) {
  return {
    // 查询公告列表
    queryZeroDistanceList: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/queryAnnouncementList', query),
    // 查询公告详情
    queryZeroDistanceDetail: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/queryAnnouncementDetail', query),
    // 保存公告
    saveAnnouncement: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/saveAnnouncement', query),
    // 查询岗位类型接口
    queryJobTypeList: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/queryJobTypeList', query),
    // 获取组织机构树完整版
    getCustRangeAll: (query) => api.post('/groovynoauth/fsp/emp/org/queryEmpOrgTree', query, { noEmpId: true }),
    // 查组织机构树
    queryEmpOrgTree: (query) => api.post('/groovynoauth/fsp/emp/org/queryEmpOrgTree', query),
    // 删除、下架公告
    deleteZeroDistanceNotice: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/operateAnnouncement', query),
    // 公告二次提醒
    // 迁移前旧接口路径：/groovynoauth/fsp/sigma/zerodistance/processNotify
    reminderZeroDistanceNotice: (query) => api.post('/fspa/aorta/operation/api/desktop/announcement/notify/pc/AnnouncementCommonCmd/processNotify', query, { isFullUrl: true }),
    // 查询批注添加列表
    queryCommentsList: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/queryCommentsList', query),
    // 批注删除
    deleteAprrove: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/deleteAnnotation', query),
    // 根据关键字查员工
    queryEmpList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
    // 查询审批历史
    queryFlowHistory: (query) => api.post('/groovynoauth/ibcp/flow/queryFlowHistory', query),
    // 查询审批按钮
    queryFlowButton: (query) => api.post('/groovynoauth/ibcp/flow/queryFlowButton', query),
    // 发起流程
    startFlow: (query) => api.post('/groovynoauth/ibcp/flow/startFlow', query),
    // 走流程
    doApprove: (query) => api.post('/groovynoauth/ibcp/flow/doApprove', query),
    // 查询批注/物料信息
    queryNotationDetail: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/queryAnnotationDetail', query),
    // 添加/编辑批注
    saveNotationInfo: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/saveAnnotationInfo', query),
    // 导入名单
    importEmpList: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/importEmpList', query),
    // 查询导入名单进度结果信息
    queryImportProcess: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/queryImportProcess', query),
    // 审批提示语
    queryApprovalPrompt: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/queryApprovalPrompt', query),
    // 模糊查询【产品营销计划】列表
    queryProductSalesByKeyword: (query) => api.post('/groovynoauth/fsp/sigma/zerodistance/queryProductSalesByKeyword', query),
  };
}
