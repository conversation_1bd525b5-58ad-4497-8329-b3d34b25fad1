/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2018-06-05 16:24:22
 * @Last Modified by: sunweibin
 * @Last Modified time: 2019-11-28 17:16:06
 */
export default function stockOptionEvaluation(api) {
  return {
    // 右侧详情基本信息
    getDetailInfo: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/queryApplicationDetail', query),
    // 获取附件列表
    getAttachmentList: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),
    // 上传附件
    uploadAttachment: (query) => api.post('/file/ceFileUpload2', query),
    // 新建页面查询下一步审批人
    queryNextApproval: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/queryNextApproval', query),
    // 下一步按钮和下一步审批人
    getButtonList: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/queryNextStepInfo', query),
    // 下载附件
    downloadAttachment: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/download', query, { isFullUrl: true }),
    // 删除附件
    deleteAttachment: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileDeleteForSpecial', query, { isFullUrl: true }),
    // 更新接口（新建和修改）
    updateBindingFlow: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/update', query),
    // 走流程接口
    doApprove: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/doApprove', query),
    // 验证提交数据接口
    validateData: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/validate', query),
    // 选择客户获取用户信息
    getCustInfo: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/custInfo', query),
    // 查询本营业部客户
    getBusCustList: (query) => api.post('/groovynoauth/fsp/biz/departmentsCustList', query),
    // 基本信息的多个select数据
    getSelectMap: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/typeMap', query),
    // 受理营业部变更查询
    queryAcceptOrg: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/queryAcceptOrg', query),
    // 查询深市、沪市
    queryZdNumber: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/getZdNumber', query),
    // 获取投资经历评估不满足提示显示条件
    getInvestFlag: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/getInvestFlag', query),
    // 检查客户是否能够申请股票期权评估
    checkCustCanApplyStockOptionEval: (query) => api.post('/groovynoauth/fsp/biz/stockOptionApplication/checkCustCanApplyStockOptionEval', query),
  };
}
