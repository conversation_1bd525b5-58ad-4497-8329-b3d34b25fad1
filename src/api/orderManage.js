/*
 * @Author: sun<PERSON>bin
 * @Date: 2019-03-05 10:42:08
 * @Last Modified by: <PERSON><PERSON><PERSON>ow<PERSON>
 * @Last Modified time: 2024-04-09 09:19:49
 * @description 订单管理的Api
 */

export default function newHome(api) {
  return {
    // 查询订单管理页面字典
    queryOrderDict: (query) => api.post('/groovynoauth/fsp/emp/order/queryOrderDict', query),
    // 查询交易订单列表
    queryTranscationOrderList: (query) => api.post('/groovynoauth/fsp/emp/order/queryTransOrderList', query),
    // 查询历史交易订单列表
    queryHistoryTranscationOrderList: (query) => api.post('/fspa/aorta/biz/api/desktop/transaction/order/pc/QueryTransOrderList/queryHistoryTransOrderList',
      query,
      { isFullUrl: true }),
    // 查询今日交易订单列表
    queryTodayTranscationOrderList: (query) => api.post('/fspa/aorta/biz/api/desktop/transaction/order/pc/QueryTransOrderList/queryRealTimeTransOrderList',
      query,
      { isFullUrl: true }),
    // 交易订单导出接口Key查询
    queryOrderExportKey: (query) => api.post('/groovynoauth/fsp/emp/order/queryOrderExportKey', query),
    // 查询服务订单列表
    queryServiceOrderList: (query) => api.post('/groovynoauth/fsp/emp/order/queryServiceOrderList', query),
    // 查询服务订单详情
    queryServiceOrderDetail: (query) => api.post('/groovynoauth/fsp/cust/prodorder/queryServiceOrderDetail', query),
    // 查询客户列表
    queryCustList: (query) => api.post('/groovynoauth/fsp/emp/order/queryCustList', query),
    // 获取组织机构树
    getDepartmentList: (query) => api.post('/fspa/aorta/user/api/desktop/emp/org/pc/orgTree/queryMergedHQOrgTree',
      query,
      { isFullUrl: true }),
    // 查询产品列表
    queryProductList: (query) => api.post('/groovynoauth/fsp/emp/order/queryProductList', query),
    // 查询服务经理
    queryEmpList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
    // 查询服务经理名下是否有客户
    queryMyCustList: (query) => api.post('/groovynoauth/fsp/common/queryMyCustList', query),
    // 查询近三个交易日
    lastNTradingDays: (query) => api.post('/fspa/aorta/user/api/desktop/bigsearch/search/pc/TradingDays/lastNTradingDays', query, { isFullUrl: true }),
  };
}
