/*
 * @Author: lijingjing
 * @Date: 2019-04-23 11:00:59
 * @description 服务场景参数设置的API
 */

export default function serviceScenes(api) {
  return {
    // 模糊匹配事件名称
    queryEventNameByKeyword: query => api.post('/groovynoauth/fsp/cust/service/queryEventNameByKeyword', query),
    // MOT数据列表查询
    queryMotServiceList: query => api.post('/groovynoauth/fsp/serviceScenes/queryMotServiceList', query),
    // 下拉框一级二级分类列表
    queryTaskTypeDict: query => api.post('/groovynoauth/fsp/serviceScenes/queryTaskTypeDict', query),
    // 获取客户反馈列表数据
    queryFeedBackList: query => api.post('/groovynoauth/fsp/serviceScenes/queryRelaFeeBack', query),
    // 新增反馈模板
    addFeedBack: query => api.post('/groovynoauth/fsp/serviceScenes/addFeedBackByRelaVal', query),
    // 删除反馈模板
    delFeedBack: query => api.post('/groovynoauth/fsp/serviceScenes/delFeedBackByRelaVal', query),
    // 获取客户反馈类型列表
    queryFeedBackType: query => api.post('/groovynoauth/fsp/campaign/mot/queryFeedbackList', query),
    // 获取自建任务列表
    querySelfServiceList: query => api.post('/groovynoauth/fsp/serviceScenes/querySelfServiceList', query),
    // 获取服务留痕列表
    queryMarkServiceList: query => api.post('/groovynoauth/fsp/serviceScenes/queryMarkServiceList', query),
    // 获取潜客服务留痕列表
    queryPotentialServiceList: query => api.post('/groovynoauth/fsp/serviceScenes/queryPotentialServiceList', query),
    // 触发评价编辑
    editTrigger: query => api.post('/groovynoauth/fsp/serviceScenes/editTriggerByRelaVal', query),
    // 查询服务评价模板列表
    queryEvaluationTemplateList: query => api.post('/groovynoauth/fsp/serviceScenes/queryServEvalTemp', query),
    // 查询可用标签下拉列表
    queryServEvalLabel: query => api.post('/groovynoauth/fsp/serviceScenes/queryServEvalLabel', query),
    // 服务评价编辑
    editServEvalTemp: query => api.post('/groovynoauth/fsp/serviceScenes/editServEvalTemp', query),
    // 同步到其他模板
    syncEvalTemp: query => api.post('/groovynoauth/fsp/serviceScenes/synchroEvalTemp', query),
    // 切换是否强制手机执行
    switchPhoneExecute: query => api.post('/groovynoauth/fsp/serviceScenes/switchPhoneExecute', query),
    // 切换是否必传附件
    switchIsAttachmentRequired: query => api.post('/groovynoauth/fsp/serviceScenes/switchIsAttachmentRequired', query),
    // 获取调查问卷模板(问题列表)
    queryQuestionnaire: query => api.post('/groovynoauth/fsp/campaign/mot/queryQuestionnaire', query),
    // 获取填写完成的问卷调查内容
    queryQuestionnaireResult: query => api.post('/groovynoauth/fsp/campaign/mot/queryQuestionnaireResult', query),
  };
}
