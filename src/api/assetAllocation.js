/**
 * @Description: 资产分布 api
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2019-04-17 14:50:58
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-05-09 18:41:47
 */
export default function assetAllocation(api) {
  return {
    // 获取大类资产分布
    queryBigAssetAllocation: (query) => api.post('/groovynoauth/fsp/cust/custasset/queryLargeCateAssetDistribution', query),
    // 获取产品类别资产分布数据
    queryAssetTypeAllocation: (query) => api.post('/groovynoauth/fsp/cust/custasset/queryProdCateAssetDistribution', query),
    // 查询收益走势对比数据
    queryProfitRateInfo: (query) => api.post('/groovynoauth/fsp/cust/custasset/queryCustPofitRateChart', query),
  };
}
