// 部门协作收入申报
export default function departCooperationV2(api) {
  return {
    // 查询历史流程
    queryFlowHistory: (query) => api.post('/fspa/institution/api/uicrm/departCooperationV2/flow/DepartCoopQueryFlowHistory', query, { baseURL: '' }),
    // 查询下一步按钮和审批人
    queryFlowButton: (query) => api.post('/fspa/institution/api/uicrm/departCooperationV2/flow/DepartCoopQueryFlowButton', query, { baseURL: '' }),
    // 发起流程
    startFlow: (query) => api.post('/fspa/institution/api/uicrm/bpmnflow/startFlow', query, { baseURL: '' }),
    // 查询审批的协作项目列表
    listApproveApplication: (query) => api.post('/fspa/institution/api/uicrm/departCooperationV2/listApproveApplication', query, { baseURL: '' }),
    // 查询分发申请单详情
    queryApplicationDetail: (query) => api.post('/fspa/institution/api/uicrm/departCooperationV2/queryApplicationDetail', query, { baseURL: '' }),
    // 新增项目审批
    doNewProjectApprove: (query) => api.post('/fspa/institution/api/uicrm/departCooperationV2/flow/DepartCoopNewProjectFlowServiceCmd', query, { baseURL: '' }),
    // 存续项目审批
    doRenewProjectApprove: (query) => api.post('/fspa/institution/api/uicrm/departCooperationV2/flow/DepartCoopRenewProjectFlowServiceCmd', query, { baseURL: '' }),
  };
}
