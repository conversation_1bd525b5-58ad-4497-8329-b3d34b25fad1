export default function cancelLabelApply(api) {
  return {
    // 获取客户审批详情数据
    queryDetailInfo: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/queryApplicationDetail', query, { baseURL: '' }),
    // 获取审批按钮、审批人数据
    queryFlowButton: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/queryFlowButton', query, { baseURL: '' }),
    // 继续审批流程
    doApprove: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/doApprove', query, { baseURL: '' }),
    // 获取客户列表
    queryApproveCustList: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/queryCustListPaged', query, { baseURL: '' }),
  };
}
