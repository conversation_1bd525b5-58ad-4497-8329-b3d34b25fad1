/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-04 11:51:07
 * @LastEditors: yanfaping
 * @LastEditTime: 2024-10-24 15:02:02
 * @Description: 审批页面的api
 */

import apiCreator from '@/utils/apiCreatorAxios';
import commonAPI from './common';
import commissionAPI from './commission';
import custDistributeAPI from './custDistribute';
import cancelLabelApplyAPI from './cancelLabelApply';
import cancelLabelApplyMultiAPI from './cancelLabelApplyMulti';
import cooperationWeightAPI from './cooperationWeight';
import optionCommissionAPI from './optionCommission';
import departCooperationAPI from './departCooperation';
import departCooperationAPIV2 from './departCooperationV2';
import commissionAuthorizationAPI from './commissionAuthorization';

const api = apiCreator();
const common = commonAPI(api);
const commission = commissionAPI(api);
const custDistribute = custDistributeAPI(api);
const cancelLabelApply = cancelLabelApplyAPI(api);
const cooperationWeight = cooperationWeightAPI(api);
const optionCommission = optionCommissionAPI(api);
const departCooperation = departCooperationAPI(api);
const departCooperationV2 = departCooperationAPIV2(api);
const cancelLabelApplyMulti = cancelLabelApplyMultiAPI(api);
const commissionAuthorization = commissionAuthorizationAPI(api);

export {
  // 暴露api上的几个底层方法: get / post
  api,
  // 审批通用接口
  common,
  // 佣金相关接口
  commission,
  // 分公司分配
  custDistribute,
  // 取消客户选投顾标识申请
  cancelLabelApply,
  // 多客户-取消客户选投顾标识申请
  cancelLabelApplyMulti,
  // 合作权重
  cooperationWeight,
  // 期权佣金申请
  optionCommission,
  // 部门协作收入申报
  departCooperation,
  // 部门协作收入申报二期
  departCooperationV2,
  // 佣金授权申请
  commissionAuthorization,
};
