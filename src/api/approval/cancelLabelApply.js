/*
 * @Description: 取消客户选投顾标识申请--审批页接口
 * @Author: yuduo.zhang
 * @Date: 2022-02-24 09:54:08
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-02-24 10:06:47
 */

export default function cancelLabelApply(api) {
  return {
    // 获取客户审批详情数据
    queryDetailInfo: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/queryCancelApplicationDetail', query),
    // 获取审批轨迹数据
    queryFlowHistory: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/queryFlowHistory', query),
    // 获取审批按钮、审批人数据
    queryFlowButton: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/queryFlowButton', query),
    // 继续审批流程
    doApprove: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/doApprove', query),
  };
}
