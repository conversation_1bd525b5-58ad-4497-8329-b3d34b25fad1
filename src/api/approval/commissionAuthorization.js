/*
 * @Author: yanfaping
 * @Date: 2024-09-02 11:13:58
 * @LastEditors: yanfaping
 * @LastEditTime: 2024-12-02 17:33:04
 * @Description: 佣金授权申请相关接口
 */
export default function commissionAuthorization(api) {
  return {
    // 查询佣金授权详情
    queryAuthCommissionDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/AuthCommissionQuery/queryAuthAdjustDetail', query, { baseURL: '' }),
    // 查询客户列表
    queryCustInfoDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/AuthCommissionQuery/queryCustInfoDetail', query, { baseURL: '' }),
    // 查询审批按钮及下一步审批人
    queryFlowButtons: (query) => api.post('/fspa/aorta/biz/api/desktop/workflow/common/pc/ActivitiFlowCommonQuery/queryFlowButton', query, { baseURL: '' }),
    // 查询审批历史
    queryFlowHistory: (query) => api.post('/fspa/aorta/biz/api/desktop/workflow/common/pc/ActivitiFlowCommonQuery/queryFlowHistory', query, { baseURL: '' }),
    // 审批流转
    doApprove: (query) => api.post('/fspa/aorta/biz/api/desktop/workflow/common/pc/ActivitiFlowCommonStartTask/doApprove', query, { baseURL: '' }),
    // 查询会签人列表
    queryCountersignerList: (query) => api.post('/fspa/aorta/biz/api/desktop/workflow/common/pc/ActivitiFlowCommonQuery/candidateList', query, { baseURL: '' }),
    // 保存会签标记
    saveCountersignFlag: (query) => api.post('/fspa/aorta/biz/api/desktop/workflow/common/pc/ActivitiFlowCommonStartTask/saveCountersignFlag', query, { baseURL: '' }),
  };
}
