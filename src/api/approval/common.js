/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-04 13:14:10
 * @LastEditors: yuan<PERSON>jie
 * @LastEditTime: 2021-03-04 13:18:41
 * @Description: 审批页面通用接口
 */

export default function common(api) {
  return {
    // 查询历史流程
    queryHistoryFlow: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowHistory', query),
    // 查询下一步按钮和审批人
    queryFlowButtonList: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowButton', query),
    // 驳回后修改发起流程
    doApprove: (query) => api.post('/groovynoauth/fsp/activitiflow/doApprove', query),
    // 获取附件列表(走存储网关)
    getStorageAttachList: (query) => api.post('/storage/list', query),
    // 获取附件列表
    getAttachmentList: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { baseURL: '' }),
  };
}
