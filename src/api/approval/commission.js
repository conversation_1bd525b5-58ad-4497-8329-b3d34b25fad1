/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-04 13:24:33
 * @LastEditors: yuan<PERSON>jie
 * @LastEditTime: 2021-03-04 13:27:21
 * @Description: 审批页面佣金接口
 */

export default function commission(api) {
  return {
    // 特殊佣金调整-订单基本信息
    querySpecialOrderDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySpecialCommissionDetail', query, { baseURL: '' }),
    // 特殊佣金调整-订单的佣金费率
    querySpecialOrderCommissions: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySpecialChgCommissionRatioDetail', query, { baseURL: '' }),
    // 单佣金调整 - 订单基本信息
    querySingleCommChgOrderInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySingleCommissionOrderInfo', query, { baseURL: '' }),
    // 批量佣金调整 - 订单基本信息
    queryBatchOrderInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryBatchCommChgOrderInfo', query),
    // 批量佣金调整 - 订单下的客户列表信息
    queryBatchCustList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommChgOrderCusts', query),
    // 批量佣金调整 - 判断批次号内客户外呼是否均已回复
    checkbatchOutBoundCallOfAnswer: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/checkbatchOutBoundCallOfAnswer', query),
    // 批量佣金调整、资讯订阅、资讯退订 - 查询产品信息
    queryOrderProds: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommChgOrderProds', query),
    // 批量佣金调整-审批时修改申请单数据
    updateBatchJustCommionInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/updateBatchJustCommionInfo', query),
    // 自动外呼调佣申请-订单基本信息
    queryCommsionComboDetail: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommsionComboDetail', query),
    // 自动外呼调佣申请-获取审批按钮
    queryApprovalBtns: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryApprovalBtns', query),
    // 自动外呼调佣申请-获取审批人列表
    queryApprovalList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryApprovalList', query),
    // 自动外呼调佣申请-审批页面保存
    saveCommsionCombo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/saveCommsionCombo', query),
    // 非标客户特殊佣金调整-查询订单基本信息
    queryUnStandardCommsionDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/queryNonStandardCommissionDetail', query, { baseURL: '' }),
    // 非标客户特殊佣金调整-查询订单的佣金费率
    queryUnStandardCommsionRatioDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySpecialChgCommissionRatioDetail', query, { baseURL: '' }),
    // 非标客户特殊佣金调整-查询客户承诺达截止日期
    queryCustPromiseDeadline: (query) => api.post('/groovynoauth/fsp/biz/unStandardCommsion/queryCustPromiseDeadline', query),
  };
}
