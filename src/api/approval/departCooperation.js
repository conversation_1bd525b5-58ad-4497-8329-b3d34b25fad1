// 部门协作收入申报
export default function departCooperation(api) {
  return {
    // 查询历史流程
    queryFlowHistory: (query) => api.post('/fspa/institution/api/uicrm/bpmnflow/queryFlowHistory', query, { baseURL: '' }),
    // 查询下一步按钮和审批人
    queryFlowButton: (query) => api.post('/fspa/institution/api/uicrm/bpmnflow/queryFlowButton', query, { baseURL: '' }),
    // 发起流程
    startFlow: (query) => api.post('/fspa/institution/api/uicrm/bpmnflow/startFlow', query, { baseURL: '' }),
    // 流程审批
    doApprove: (query) => api.post('/fspa/institution/api/uicrm/bpmnflow/doApprove', query, { baseURL: '' }),
    // 查询审批的协作项目列表
    listApproveApplication: (query) => api.post('/fspa/institution/api/uicrm/departcooperation/listApproveApplication', query, { baseURL: '' }),
    // 查询分发申请单详情
    queryApplicationDetail: (query) => api.post('/fspa/institution/api/uicrm/departcooperation/queryApplicationDetail', query, { baseURL: '' }),
  };
}
