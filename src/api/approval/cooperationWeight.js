// 合作权重
export default function cooperationWeight(api) {
  return {
    // 查询历史流程
    queryHistoryFlow: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowHistory', query),
    // 查询下一步按钮和审批人
    queryFlowButtonList: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowButton', query),
    // 发起流程
    startFlow: (query) => api.post('/groovynoauth/fsp/activitiflow/startFlow', query),
    // 流程审批
    doApprove: (query) => api.post('/groovynoauth/fsp/activitiflow/doApprove', query),
    // 查询列表数据
    queryProcessList: (query) => api.post('/desktop/business/weightassign/pc/WeightAssignQuery/queryProcessList', query),
    // 查询详情数据
    queryProcessDetail: (query) => api.post('/desktop/business/weightassign/pc/WeightAssignQuery/queryProcessDetail', query),
    // 查询详情客户列表数据
    queryProcessCustomer: (query) => api.post('/desktop/business/weightassign/pc/WeightAssignQuery/queryProcessCustomer', query),
    // 新建时可添加的客户列表数据
    queryCoopWeightCustomer: (query) => api.post('/desktop/business/weightassign/pc/WeightAssignQuery/queryCoopWeightCustomer', query),
    // 校验添加的客户
    validateCustomer: (query) => api.post('/desktop/business/weightassign/pc/WeightAssignCmd/validateCustomer', query),
    // 新建时可添加的服务经理列表数据
    searchEmpInfo: (query) => api.post('/desktop/emp/info/pc/EmpInfoQuery/searchEmpInfo', query),
    // 新建/编辑合作权重
    saveProcess: (query) => api.post('/desktop/business/weightassign/pc/WeightAssignCmd/saveProcess', query),
  };
}
