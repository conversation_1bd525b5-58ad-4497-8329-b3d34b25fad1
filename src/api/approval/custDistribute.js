/**
 * @Description: 分公司集中分配
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2021-07-15 17:37:03
 * @Last Modified by: l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-07-15 17:37:03
 */
export default function custDistribute(api) {
  return {
    // 查询右侧详情
    queryBranchAltogetherDetail: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/queryApplicationDetail', query, { baseURL: '' }),
    // 查询右侧详情的客户列表
    queryBranchAltogetherDetailCustList: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/queryCustListPaged', query, { baseURL: '' }),
  };
}
