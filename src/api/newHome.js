/**
 * @Description: 丰富首页内容 接口文件
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-09-12 15:38:06
 * @Last Modified by: ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-08-18 11:01:24
 */
export default function newHome(api) {
  return {
    // 重点关注
    queryKeyAttention: (query) => api.post('/groovynoauth/fsp/index/queryKeyAttention', query),
    // 重点关注标签-已开通/潜客
    entryPointer: (query) => api.post('/fspa/aorta/user/api/desktop/cust/specialZone/pc/SmartAlgorithmTrading/entryPointer', query, { isFullUrl: true }),
    // 猜你感兴趣
    queryGuessYourInterests: (query) => api.post('/groovynoauth/fsp/index/queryGuessYourInterests', query),
    // 产品日历
    queryProductCalendar: (query) => api.post('/groovynoauth/fsp/index/queryProdCalendar', query),
    // 获取组合推荐
    queryIntroCombination: (query) => api.post('/groovynoauth/fsp/index/queryIntroCombination', query),
    // 首席观点
    queryChiefView: (query) => api.post('/groovynoauth/fsp/info/infoCenter/queryInfo', query),
    // 首页任务概览数据
    queryNumbers: (query) => api.post('/groovynoauth/fsp/emp/todealwith/queryNumbers', query),
    // 获取数据最大时间
    getInitialData: (query) => api.post('/groovynoauth/jxzb/queryMaxDataDt', query),
    // 获取首页大图
    queryHomeBigImage: (query) => api.post('/groovynoauth/fsp/queryHomeBigImage', query),
    // 获取首页内容速览数据
    queryNumbers2: (query) => api.post('/groovynoauth/fsp/queryNumbers2', query),
    // 客户列表
    getCustomerList: (query) => api.post('/groovynoauth/fsp/cust/custlist/homePageCustList', query),
    // 获取最新的一条未读消息
    // 迁移前旧接口路径：/groovynoauth/fsp/emp/notification/queryLatestUnreadNotice
    queryLatestUnreadNotice: (query) => api.post('/fspa/aorta/biz/api/desktop/notification/inbox/pc/notificationService/queryLatestUnreadNotice', query, { isFullUrl: true }),
    // 获取热点客群列表
    queryHotCustGroupLabelList: (query) => api.post('/groovynoauth/fsp/cust/custgroup/queryPubliGroupListInUserView', query),
    // 获取重点标签详情
    getFilterMeta: (query) => api.post('/desktop/cust/label/all/LabelPropertyInfo/getFilterMeta', query),
    // 获取数观财富概览展示数据
    queryWealthDataSummaryWithChart: (query) => api.post('/fspa/aorta/user/api/desktop/emp/performance/pc/PerformanceKanban/queryWealthDataSummaryWithChart', query, { isFullUrl: true }),
    // 获取业务数据概览展示数据
    queryBizDataSummaryWithChart: (query) => api.post('/fspa/aorta/user/api/desktop/emp/performance/pc/PerformanceKanban/queryBizDataSummaryWithChart', query, { isFullUrl: true }),
    // 获取主岗及岗位下拉选项数据
    queryDepartmentData: (query) => api.post('/fspa/aorta/user/api/desktop/emp/performance/pc/PerformanceKanban/queryDepartmentData', query, { isFullUrl: true }),
    // 获取交易日信息
    getThisYearTradeDates: (query) => api.post('/fspa/aorta/user/api/desktop/emp/performance/pc/PerformanceKanban/getThisYearTradeDates', query, { isFullUrl: true }),
  };
}
