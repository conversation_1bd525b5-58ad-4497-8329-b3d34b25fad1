/*
 * @Description: 产品池 api
 * @Author: LiuJianShu-********
 * @Date: 2019-07-30 16:31:52
 * @Last Modified by: LiuJianShu-********
 * @Last Modified time: 2019-08-05 10:24:29
 */

export default function productPool(api) {
  return {
    // 获取产品池列表
    queryList: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryProdListFromPool', query),
    // 删除产品
    deleteProduct: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/delProdListFromPool', query),
    // 查询产品
    queryProduct: query => api.post('/groovynoauth/fsp/biz/queryProd', query),
    // 校验产品
    validateProduct: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/validProdInfo', query),
    // 查询进度
    queryProgress: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryValidateProgress', query),
    // 查询校验后的产品列表
    queryValidateProductList: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryValidProdList', query),
    // 敏感词校验
    querySensitiveWords: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/checkSensitiveWords', query),
    // 保存产品池
    saveApplication: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/saveApplication', query),
    // 审批详情产品池列表
    queryNotifiesList: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryProdApplicationDetail', query),
    // 获取按钮、审批人
    queryFlowButtonList: query => api.post('/groovynoauth/fsp/activitiflow/queryPrdtApplyFlowButton', query),
  };
}
