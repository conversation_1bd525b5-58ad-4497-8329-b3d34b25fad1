// 特殊佣金调整相关接口
export default function specialCommission(api) {
  return {
    // 跳转到360视图界面必须的参数（场景：单佣金调整，新建，选择客户时，若改客户有未完成订单，会弹框提醒，点击确定会跳转到360视图界面）
    queryCustDetailInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCustBrifeInfo', query),
    // 特殊佣金调整-查询客户
    queryCustList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/querySpecialCommsionCustInfo', query),
    // 特殊佣金调整-其它佣金率字典
    queryOtherRatioDict: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/querOtherCommissionRatioDict', query),
    // 特殊佣金调整-股基佣金率
    queryStockCommissionRateDict: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryStockCommissionRateDict', query),
    // 特殊佣金调整-客户当前其它佣金率值
    queryCurrentOtherCommissionRate: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCurrentOtherCommissionRate', query),
    // 特殊佣金调整-客户在途佣金调整效验
    specialValidateCustomer: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/checkCustChgCommissionInProcess', query, { isFullUrl: true }),
    // 佣金调整校验客户如果有被驳回订单，终止订单
    terminalOrderFlow: (query) => api.post('/groovynoauth/fsp/order/commission/terminalOrderFlow', query),
    // 佣金调整校验客户如果有新建订单，修改订单状态
    changeOrderStatus: (query) => api.post('/groovynoauth/fsp/order/commission/changeOrderStatus', query),
    // 查询客户的当前股基佣金率
    queryCustCommission: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/cust/pc/CommissionCustInfo/queryCustRealTimeCommissison', query, { isFullUrl: true }),
    // 查询异地限价以及是否同城
    queryCustLocalInfo: (query) => api.post('/groovynoauth/fsp/biz/singlecommissonchg/queryCustLocalInfo', query),
    // 特殊佣金调整-保存提交
    saveSpecialCommionInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/save/pc/CommissionSave/saveSpecialCommission', query, { isFullUrl: true }),
    // 特殊佣金调整-订单基本信息
    querySpecialOrderDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySpecialCommissionDetail', query, { isFullUrl: true }),
    // 特殊佣金调整-订单的佣金费率
    querySpecialOrderCommissions: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySpecialChgCommissionRatioDetail', query, { isFullUrl: true }),
    // 查询佣金费率下拉选项字典接口
    queryCommissionRateDictionary: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommissionRateDictionary', query),
    // 存量两融客户对齐佣金场景校验接口
    checkRzRqCustCommissions: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/RzrqCustCommissionQuery/checkRzRqCustCommissions', query, { isFullUrl: true }),
    // 校验客户信息
    validateCustInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/cust/pc/CommissionCustInfo/validateCustInfo', query, { isFullUrl: true }),
    // 校验是否涨乐选投顾
    validateZLPickCustomer: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/validateZLPickCustomer', query, { isFullUrl: true }),
    // 查询经服已填写的佣金信息
    queryProductBrokerageCommissionData: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/EconAgreementCommissionQuery/queryCommissionSettingInfo', query, { isFullUrl: true }),
    // 更新经服佣金设置待办状态
    updateSetCommissionTodoStatus: (query) => api.post('/fspa/uicrm-bff-aorta/groovy/unauth/productAccountIntroduction/flow/terminalCommissionSettingTodoFlow', query, { isFullUrl: true }),
    // 查询【佣金模式】的新投顾签约有哪些需要禁用的佣金率
    queryDisableContractRatioType: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/queryDisableContractRatioType', query, { isFullUrl: true }),
    // 全账户提佣校验
    validateUnifiedCommissionRaise: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/validateUnifiedCommissionRaise', query, { isFullUrl: true }),
  };
}
