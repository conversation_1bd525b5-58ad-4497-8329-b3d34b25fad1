/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 跨分公司客户分配api
 * @Date: 2019-06-18 15:51:35
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-06-19 15:45:08
 */

export default function acrossBranchCustAllot(api) {
  return {
    // 获取详情信息
    queryDetailInfo: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryCustAssignDetail', query),
    // 校验客户
    checkCustInfo: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/checkAssignCust', query),
    // 保存数据
    saveData: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/saveAssignCustData', query),
    // 查询详情中的客户列表
    queryDetailCustList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryDetailCustList', query),
    // 获取组织机构树
    queryEmpOrgTreeNew: (query) => api.post('/groovynoauth/fsp/emp/org/queryEmpOrgTreeNew', query),
  };
}
