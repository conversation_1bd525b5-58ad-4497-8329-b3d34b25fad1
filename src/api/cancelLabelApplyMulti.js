/*
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-08-13 13:15:09
 * @LastEditors: tiannengyu
 * @LastEditTime: 2024-08-19 18:11:44
 * @FilePath: /performance_report/src/api/cancelLabelApplyMulti.js
 * @Description:取消客户选投顾标识申请-多客户
 */
export default function cancelLabelApplyMulti(api) {
  return {
    // 获取部门下拉选数据
    queryOrgInfo: (query) => api.post('/fspa/aorta/user/api/desktop/emp/org/all/orgTree/queryStandardOrgTree', query, { isFullUrl: true }),
    // 获取左侧客户审批列表
    queryList: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/queryApplicationList', query, { isFullUrl: true }),
    // 获取右侧客户审批详情数据
    queryApproveDetail: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/queryApplicationDetail', query, { isFullUrl: true }),
    // 获取客户列表
    queryApproveCustList: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/queryCustListPaged', query, { isFullUrl: true }),
    // 获取审批按钮、审批人数据
    queryFlowButton: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/queryFlowButton', query, { isFullUrl: true }),
    // 提交保存
    saveData: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/saveApplicationData', query, { isFullUrl: true }),
    // 驳回修改时保存
    updateData: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/updateApplicationData', query, { isFullUrl: true }),
    // 发起审批流程
    startFlow: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/startFlow', query, { isFullUrl: true }),
    // 删除刚提交保存的数据
    deleteData: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/deleteDataWhenFailed', query, { isFullUrl: true }),
    // 继续审批流程
    doApprove: (query) => api.post('/fspa/aorta/user/api/desktop/relation/batchcancel/pc/batchcancel/doApprove', query, { isFullUrl: true }),
    // 轮询-查询解析进度
    queryProgress: (query) => api.get('/fspa/aorta/dmz/api/batchData/queryProcessBarOnlyCache', query, { isFullUrl: true }),
    // 查询解析进度最终结果
    queryProgressDetail: (query) => api.get('/fspa/aorta/dmz/api/batchData/queryProcessBar', query, { isFullUrl: true }),
    // 查询解析结果
    querybatchDataResult: (query) => api.post('/fspa/aorta/dmz/api/batchData/queryBatchFileDataRecord', query, { isFullUrl: true }),
  };
}
