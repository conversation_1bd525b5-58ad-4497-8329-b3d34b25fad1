/**
 * @Author: yanfaping
 * @Date: 2021-10-19 10:16:30
 * @Last Modified by: yanfaping
 * @Last Modified time: 2021-10-19 10:16:30
 * @description 热门活动-接口
 */

export default function investmentMarket(api) {
  return {
    // 查询活动物料列表接口
    queryActivitiesMaterialList: (query) => api.post('/groovynoauth/fsp/biz/salesOperations/queryActivitiesMaterialList', query),
    // 活动物料更新接口
    updateActivitiesMaterial: (query) => api.post('/groovynoauth/fsp/biz/salesOperations/updateActivitiesMaterial', query),
    // 活动物料新增接口
    saveActivitiesMaterial: (query) => api.post('/groovynoauth/fsp/biz/salesOperations/saveActivitiesMaterial', query),
    // 查询其他运营列表接口
    queryOtherOperationList: (query) => api.post('/groovynoauth/fsp/biz/salesOperations/queryOtherOperationList', query),
    // 其他运营更新接口
    saveOtherOperation: (query) => api.post('/groovynoauth/fsp/biz/salesOperations/saveOtherOperation', query),
    // 查询内容中心列表接口
    queryActivitiesContentList: (query) => api.post('/groovynoauth/fsp/biz/salesOperations/queryActivitiesContentList', query),
    // 查询私享池运作报告链接接口
    queryPrivatePoolLink: (query) => api.post('/groovynoauth/fsp/biz/salesOperations/queryPrivatePoolLink', query),
    // 私享池运作报告链接更新接口
    updatePrivatePoolLink: (query) => api.post('/groovynoauth/fsp/biz/salesOperations/updatePrivatePoolLink', query),
    // 内容中心-校验接口
    validateContent: (query) => api.post('/groovynoauth/fsp/biz/salesOperations/validateContent', query),
    // 图片附件上传接口
    imageUpload: (query) => api.post('/storage/imageUpload', query),
    // 视频附件上传接口
    videoUpload: (query) => api.post('/storage/videoUpload', query),
  };
}
