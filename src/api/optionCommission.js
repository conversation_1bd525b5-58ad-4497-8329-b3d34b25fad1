/**
 * @Description: 期权佣金调整 接口文件
 * @Author: yanfaping
 * @Date: 2023-04-18 13:38:40
 * @Last Modified by:  yanfaping
 * @Last Modified time: 2023-04-18 13:38:40
 */
export default function optionCommission(api) {
  return {
    // 查询期权佣金调整申请列表
    queryApplicationList: (query) => api.post('/desktop/option/commission/pc/OptionCommission/queryApplicationList', query),
    // 查询期权佣金详情信息
    queryOptionCommDetail: (query) => api.post('/desktop/option/commission/pc/OptionCommission/queryOptionCommDetail', query),
    // 客户校验接口(含在途、是否开通期权账户，是否做了风险测评，是否为境内自然人)
    checkCustOptionCommInProcess: (query) => api.post('/desktop/option/commission/pc/OptionCommission/checkCustOptionCommInProcess', query),
    // 查询客户信息
    queryOptionComCustInfo: (query) => api.post('/desktop/option/commission/pc/OptionCommission/queryOptionComCustInfo', query),
    // 查询佣金费率下拉选项字典接口
    queryCommissionRateDictionary: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommissionRateDictionary', query),
    // 查询原佣金档
    queryCustOriginCommission: (query) => api.post('/desktop/option/commission/pc/OptionCommission/queryCustOriginCommission', query),
    // 新建/驳回修改-提交接口
    saveOptionCommissionInfo: (query) => api.post('/desktop/option/commission/pc/OptionCommission/saveOptionCommissionInfo', query),
  };
}
