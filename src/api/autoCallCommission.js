/*
 * @Author: sun<PERSON><PERSON>
 * @Date: 2021-07-05 10:59:52
 * @Last Modified by: sunweibin
 * @Last Modified time: 2021-07-05 16:38:51
 * @description 自动外呼调佣申请
 */

export default function autoCallCommission(api) {
  return {
    // 自动外呼调佣申请（智能调佣）-查询渠道号
    queryChannelList: (query) => api.post('/groovynoauth/fsp/order/commission/queryChannelFromCustPartner', query),
    // 自动外呼调佣申请（智能调佣）-查询普通股基费率
    queryNormalStockRateList: (query) => api.post('/groovynoauth/fsp/order/commission/queryCommonStockBaseRate', query),
    // 自动外呼调佣申请（智能调佣）-查询审批人
    queryApprovalList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryApprovalList', query),
    // 自动外呼调佣申请保存接口
    saveCommsionCombo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/saveCommsionCombo', query),
    // 获取其他佣金费率选项
    queryOtherCommissionOptions: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommissionRateDictionary', query),
  };
}
