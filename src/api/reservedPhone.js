/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-06 09:39:33
 * @Last Modified by: ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-09-05 09:15:26
 * @Description: 留资手机号线索派发
 */

export default function reservedPhone(api) {
  return {
    // 查询手机号列表
    queryPotentialRetentionInfoList: (query) => api.post('/groovynoauth/cust/queryPotentialRetentionInfoList', query),
    // 查看备注列表
    queryPotentialRetentionRemarkList: (query) => api.post('/groovynoauth/cust/queryPotentialRetentionRemarkList', query),
    // 添加备注
    addPotentialRetentionRemark: (query) => api.post('/groovynoauth/cust/addPotentialRetentionRemark', query),
    // 获取标签线索
    queryLabelInfo: (query) => api.post('/desktop/cust/potential/all/PotentialQuery/queryLabelInfo', query),
    // 获取外呼记录
    queryActivityInfo: (query) => api.post('/desktop/cust/potential/all/PotentialQuery/queryActivityInfo', query),
  };
}
