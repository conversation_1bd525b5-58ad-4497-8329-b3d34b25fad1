/*
 * Description: 分公司集中分配
 * @Author: z<PERSON>heng
 * @Date: 2021-07-13 13:53:18
 * @Last Modified by:
 * @Last Modified time:
 */

export default function branchCentralAllot(api) {
  return {
    // 查询下一步按钮和审批人
    queryFlowButton: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowButton', query),
    // 走流程接口
    doApprove: (query) => api.post('/groovynoauth/fsp/cust/manager/doApprove', query),
    // 查询提交的客户数据中是否有投顾
    queryCustBranchAltogetherAssignWeightList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryCustBranchAltogetherAssignWeightList', query),
    // 新建-查询异步校验进度
    queryValidateProgress: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryValidateProgress', query),
    // 新建-校验分配客户列表客户是否有投顾服务分成权重
    queryTgCustExcel: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryTgCustExcel', query),
    // 校验客户接口
    syncValidBranchAltogetherCustAssign: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/syncValidBranchAltogetherCustAssign', query, { timeout: 60000 }),
    // 查询已校验的客户列表
    queryValidBranchAltogetherCustList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryValidBranchAltogetherCustList', query),
    // 保存
    saveBranchAltogetherCustAssign: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/saveBranchAltogetherCustAssign', query),
    // 获取详情信息
    queryBranchAltogetherDetail: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryBranchAltogetherDetail', query),
    // 查询详情中的客户列表
    queryBranchAltogetherDetailCustList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryBranchAltogetherDetailCustList', query),
    // 轮询-查询解析进度
    queryProgress: (query) => api.post('/paasgw/business-log/IBatchInfoQueryService/queryProcessDataBarOnlyCacheFacade', query),
    // 查询解析进度最终结果
    queryProgressDetail: (query) => api.post('/paasgw/business-log/IBatchInfoQueryService/queryProcessBarFacade', query),
    // 查询解析结果
    querybatchDataResult: (query) => api.post('/paasgw/business-log/IBatchInfoQueryService/queryBatchFileDataRecordFacade', query),
    // 查询客户列表（用于驳回修改等）
    queryCustListPaged: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/queryCustListPaged', query, { isFullUrl: true }),
    // 新建保存接口
    saveApplicationData: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/saveApplicationData', query, { timeout: 60000, isFullUrl: true }),
    // 驳回修改保存
    updateApplicationData: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/updateApplicationData', query, { isFullUrl: true }),
    // 新的权重校验
    checkWeight: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/checkWeight', query, { timeout: 20000, isFullUrl: true }),
    // 新的详情接口
    queryApplicationDetail: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/queryApplicationDetail', query, { isFullUrl: true }),
    // 查询文件导入数量阈值
    queryBatchFileReadConfig: (query) => api.post('/fspa/aorta/dmz/api/batchData/queryBatchFileReadConfig', query, { isFullUrl: true }),
    // 校验导入数据是否过期
    checkImportDataExpired: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/checkExpired', query, { isFullUrl: true }),
  };
}
