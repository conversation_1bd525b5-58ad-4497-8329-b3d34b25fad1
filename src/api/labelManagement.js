/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-08-03 12:53:30
 * @Last Modified by: l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-03-11 16:28:20
 */


export default function labelManagement(api) {
  return {
    // 获取标签列表
    queryLabelList: query => api.post('/groovynoauth/fsp/cust/custlabel/queryLabelList', query),
    // 单条标签删除
    deleteLabel: query => api.post('/groovynoauth/fsp/cust/custlabel/cancelCustLabel', query),
    // 新建或编辑标签
    operateLabel: query => api.post('/groovynoauth/fsp/cust/custlabel/signBatchCustLabels', query),
    // 给分组内客户打标签
    signLabelForGroupCust: query => api.post('/groovynoauth/fsp/cust/custlabel/signGroupCustLabel', query),
    // 标签名重名校验
    checkDuplicationName: query => api.post('/groovynoauth/fsp/cust/custlabel/checkDuplicationName', query),
    // 查询分组列表
    queryCustGroupList: query => api.post('/groovynoauth/fsp/cust/custgroup/queryCustGroupList', query),
    // 查询分组下的客户
    queryGroupCustList: query => api.post('/groovynoauth/fsp/cust/custgroup/queryGroupCustList', query),
    // 分组转标签
    group2Label: query => api.post('/groovynoauth/fsp/cust/custlabel/signGroupCustLabel', query),
    // 查询标签下的客户
    queryLabelCust: query => api.post('/groovynoauth/fsp/cust/custlabel/queryEmpCustsByLabelId', query),
    // 删除标签下的客户
    deleteLabelCust: query => api.post('/groovynoauth/fsp/cust/custlabel/deleteCustSignedLabel', query),
    // 查询列表转标签列表数据
    queryListToLabelListData: query => api.post('/groovynoauth/fsp/cust/custgroup/queryCustListList', query),
    // 查询列表转标签所选列表下的客户数据
    queryListToLabelCustData: query => api.post('/groovynoauth/fsp/cust/custgroup/queryListCustList', query),
    // 列表转标签提交
    signListToLabel: query => api.post('/groovynoauth/fsp/cust/custlabel/signListToLabel', query),
  };
}
