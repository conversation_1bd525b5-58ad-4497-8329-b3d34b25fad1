/**
 * 批量佣金接口
 */

export default function commission(api) {
  return {
    // 跳转到360视图界面必须的参数（场景：单佣金调整，新建，选择客户时，若改客户有未完成订单，会弹框提醒，点击确定会跳转到360视图界面）
    queryCustDetailInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCustBrifeInfo', query),
    // 批量佣金调整Home右侧详情
    getCommissionDetail: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryBatchCommChgOrderInfo', query),
    // 查询批量佣金调整详情页面中查看单个用户的审批记录
    querySingleCustApprovalRecord: (query) => api.post('/groovynoauth/fsp/flow/queryFlowHistory', query),
    // 根据目标股基佣金率查询目标产品列表
    queryProductList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryProduct', query),
    // 查询审批人员
    queryAprovalUserList: (query) => api.post('/groovynoauth/fsp/flow/queryAprovalUser', query),
    // 检验客户是否可以调整
    validateCustInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/validateBatCommChgCust', query),
    // 佣金调整校验客户如果有被驳回订单，终止订单
    terminalOrderFlow: (query) => api.post('/groovynoauth/fsp/order/commission/terminalOrderFlow', query),
    // 佣金调整校验客户如果有新建订单，修改订单状态
    changeOrderStatus: (query) => api.post('/groovynoauth/fsp/order/commission/changeOrderStatus', query),
    // 提交批量佣金调整
    submitBatchCommission: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/saveBatchJustCommionInfo', query),
    // 提交单佣金调整申请
    submitSingleCommission: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/saveSingleJustCommionInfo', query),
    // 查询咨询订阅/退订详情接口
    queryConsultDetail: (query) => api.post('/groovynoauth/fsp/biz/mailsubscription/querySubscriptionOrderInfo', query),
    // 获取附件信息
    getAttachment: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),
    // 根据用户输入的数值查询目标股基佣金率的码值
    queryGJCommissionRate: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/querySingelCommionJustRate', query),
    // 查询客户可以订阅的资讯产品
    queryConsultSubscribeProductList: (query) => api.post('/groovynoauth/fsp/biz/mailsubscription/queryMailSubscriptionProds', query),
    // 查询客户可以退订的资讯服务
    queryConsultUnSubProductList: (query) => api.post('/groovynoauth/fsp/biz/mailunsubscription/queryMailUnsubscriptionProds', query),
    // 新增资讯订阅申请或者资讯退订
    newConsultApply: (query) => api.post('/groovynoauth/fsp/biz/mailsubscription/saveMailSubscriptionInfo', query),
    // 查询单佣金调整中的产品列表信息
    querySingleCommissionProductList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryProDuctInfo', query),
    // 查询用户选择的产品三匹配信息
    queryThreeMatchInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryThreeMatchInfo', query),
    // 单佣金调整中的其他佣金费率选项
    queryOtherCommissionOptions: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryOtherCommissionRate', query),
    // 单佣金调整页面 客户查询
    querySingleCustomer: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCustInfo', query),
    // 资讯订阅与退订客户列表查询接口
    querySubscriptionCustomer: (query) => api.post('/groovynoauth/fsp/biz/mailsubscription/queryMailSubscriptionCusts', query),
    // 单佣金调整详情页面 当前审批步骤接口
    queryCurrentStep: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryFlowCurrentStepInfo', query),
    // 咨讯订阅和退订详情页面 当前审批步骤接口
    querySubscriStep: (query) => api.post('/groovynoauth/fsp/biz/mailsubscription/queryMailFlowCurrentStepInfo', query),
    // 单佣金调整详情页面 基础数据接口
    querySingleDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySingleCommissionOrderInfo', query, { isFullUrl: true }),
    // 单佣金调整新建页面中的目标股基佣金率
    querySingleGJCommissionRate: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/querySingleCommision', query),
    // 单佣金调整新建页面客户检验
    validateCustomer: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCustRiskInfo', query),
    // 单佣金调整的驳回修改,提交后，结转下个流程
    updateFlowStatus: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/updateFlowStatus', query),
    // 查询驳回后修改的页面按钮列表
    queryAprovalBtns: (query) => api.post('/groovynoauth/fsp/flow/queryAprovalBtns', query),
    // 咨讯订阅客户风险测评、偏好品种、投资期限校验接口
    checkCustomer: (query) => api.post('/groovynoauth/fsp/biz/mailsubscription/queryMailCustRiskInfo', query),
    // 查询驳回后修改的详情页面
    querySingleDetail4Update: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySingleCommissionOrderInfoForUpdate', query, { isFullUrl: true }),
    // 查询单佣金调整客户的当前股基佣金率
    queryCustCommission: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCustCommission', query),
    // 自动外呼调佣申请保存接口
    saveCommsionCombo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/saveCommsionCombo', query),
    // 自动外呼调佣申请（智能调佣）-查询审批人
    queryApprovalList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryApprovalList', query),
    // 自动外呼调佣申请（智能调佣）-查询普通股基费率
    queryNormalStockRateList: (query) => api.post('/groovynoauth/fsp/order/commission/queryCommonStockBaseRate', query),
    // 自动外呼调佣申请（智能调佣）-查询渠道号
    queryChannelList: (query) => api.post('/groovynoauth/fsp/order/commission/queryChannelFromCustPartner', query),
    // 自动外呼调佣申请（智能调佣）右侧详情
    queryCommsionComboDetail: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommsionComboDetail', query),
    // 自动外呼调佣申请（智能调佣）查询按钮
    queryComboApprovalBtns: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryApprovalBtns', query),
    // 自动外呼调佣申请（智能调佣）查询详情中客户列表
    queryComboDetailCustList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryAntoChgCommCustInfoList', query),
    // 自动外呼调佣申请（智能调佣）查询当前用户下的客户列表
    queryMyCustList: (query) => api.post('/groovynoauth/fsp/common/queryMyCustList', query),
    // 自动外呼调佣申请（智能调佣）-终止调拥外呼任务
    terminateCommsionCombo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/terminateCommsionCombo', query),
    // 校验添加的客户
    syncValidateCustomerList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/syncValidateCustomerList', query),
    // 查询导入客户的校验进度
    queryValidateProgress: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryValidateProgress', query),
    // 查询校验后的客户列表
    queryValidateCustomerList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryValidateCustomerList', query),
    // 查询详情中的客户列表
    queryDetailCustomerList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommChgOrderCusts', query),
    // 针对批量智能调佣判断配额是否满足与当前时间是否为交易日判断接口
    applyForSmartQuota: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/applyForSmartQuota', query),
    // 特殊佣金调整-订单基本信息
    querySpecialOrderDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySpecialCommissionDetail', query, { isFullUrl: true }),
    // 特殊佣金调整-订单的佣金费率
    querySpecialOrderCommissions: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySpecialChgCommissionRatioDetail', query, { isFullUrl: true }),
    // 特殊佣金调整-保存提交
    saveSpecialCommionInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/save/pc/CommissionSave/saveSpecialCommission', query, { isFullUrl: true }),
    // 特殊佣金调整-其它佣金率字典
    querOtherCommissionRatioDict: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/querOtherCommissionRatioDict', query),
    // 特殊佣金调整-客户当前其它佣金率
    queryCurrentOtherCommissionRate: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCurrentOtherCommissionRate', query),
    // 特殊佣金调整-股基佣金率
    queryStockCommissionRateDict: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryStockCommissionRateDict', query),
    // 特殊佣金调整-查询客户
    querySpecialCommsionCustInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/querySpecialCommsionCustInfo', query),
    // 特殊佣金调整-客户在途佣金调整效验
    specialValidateCustomer: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/checkCustChgCommissionInProcess', query),
    // 批量佣金调整详情-客户信息--搜索客户
    queryCustList: (query) => api.post('/groovynoauth/fsp/common/queryCustList', query),
    // 查询消息提醒中的客户列表
    queryAdjustCommissionCustList: (query) => api.post('/groovynoauth/fsp/biz/queryAdjustCommissionCustList', query),
    // 获取附件列表(走存储网关)
    getStorageAttachList: (query) => api.post('/storage/list', query),
    // 非标客户特殊佣金调整-校验当前营业部信息
    checkCurrentDepartment: (query) => api.post('/groovynoauth/fsp/biz/unStandardCommsion/checkCurrentDepartment', query),
    // 非标客户特殊佣金调整-查询订单基本信息
    queryUnStandardCommsionDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/queryNonStandardCommissionDetail', query, { isFullUrl: true }),
    // 非标客户特殊佣金调整-查询订单的佣金费率
    queryUnStandardCommsionRatioDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySpecialChgCommissionRatioDetail', query, { isFullUrl: true }), // 非标客户特殊佣金调整-查询订单的佣金费率
    // 佣金授权详情
    queryAuthCommissionDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/AuthCommissionQuery/queryAuthAdjustDetail', query, { isFullUrl: true }),
    // 佣金授权申请-客户列表
    queryCustInfoDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/AuthCommissionQuery/queryCustInfoDetail', query, { isFullUrl: true }),
    // 查询佣金授权申请-审批历史
    queryWorkFlowHistory: (query) => api.post('/fspa/aorta/biz/api/desktop/workflow/common/pc/ActivitiFlowCommonQuery/queryFlowHistory', query, { isFullUrl: true }),
  };
}
