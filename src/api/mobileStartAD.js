/*
 * @Author: li-ke
 * @Date: 2020-02-12 22:35:31
 * @Last Modified by: li-ke
 * @Last Modified time: 2020-02-12 22:35:31
 */
export default function mobileStartAD(api) {
  return {
    // 获取移动启动广告配置信息
    getStartADData: query => api.post('/groovynoauth/fsp/mobads/getStartADData', query),
    // 获取图片尺寸列表
    getImageSizeList: query => api.post('/groovynoauth/fsp/mobads/queryBootAdScreenConfig', query),
    // 更新移动启动广告配置
    uploadStartAD: query => api.post('/groovynoauth/fsp/mobads/updateBootAdsConfig', query),
  };
}
