/*
 * @Description: 服务场景规则设计 和 标签维护 api
 * @Author: LiuJianShu-********
 * @Date: 2019-04-24 11:02:00
 * @Last Modified by: <PERSON><PERSON>ianShu-********
 * @Last Modified time: 2019-04-25 10:49:36
 */

export default function scenarioAndLabel(api) {
  return {
    // 标签维护-服务评价列表
    queryCommentList: query => api.post('/groovynoauth/fsp/service/evaluate/queryEvaluateLabelList', query),
    // 标签维护-服务评价编辑
    editCommentLabel: query => api.post('/groovynoauth/fsp/service/evaluate/editEvaluateLabel', query),
    // 标签维护-客户反馈列表
    queryFeedbakList: query => api.post('/groovynoauth/fsp/campaign/mot/queryFeedbackList', query),
    // 标签维护-客户反馈新增
    addFeedbackLabel: query => api.post('/groovynoauth/fsp/campaign/mot/addFeedback', query),
    // 标签维护-客户反馈编辑
    editFeedbackLabel: query => api.post('/groovynoauth/fsp/campaign/mot/modifyFeedback', query),
    // 标签维护-反馈任务关联的信息列表
    queryInfoListByFeedbackId: query => api.post('/groovynoauth/fsp/campaign/mot/queryInfoListByFeedbackId', query),
  };
}
