/**
 * 涨乐运营-产品推介相关接口
 */

export default function productPushFlow(api) {
  return {
    // 获取分公司产品池产品接口
    queryBranchProductList: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryBranchProductList', query),

    // 获取场外金融产品产品数据接口
    queryOffSiteProductList: query => api.post('/groovynoauth/fsp/biz/queryProd', query),

    // 获取场外金融产品产品表格数据接口
    queryOffSiteProductTableList: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryOffSiteFinPrdt', query),

    // 查询校验后的客户列表
    queryValidateCustList: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryChooseCustList', query),

    // 查询客户列表发起的产品推介的客户列表
    querySelfValidateCustList: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryValidCustList', query),

    // 保存选择的客户和产品
    submitCustAndProduct: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/validateAndSaveCustAndProduct', query),

    // 查询产品保存进度
    queryProductSaveProcess: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryValidateCustAndProductProgress', query),

    // 获取分公司code
    queryBranchCodeByCust: query => api.post('/groovynoauth/fsp/biz/matbranchprodpool/queryBranchCodeByCust', query),
  };
}
