// 非标客户特殊佣金调整相关接口
export default function unStandardCommission(api) {
  return {
    // 跳转到360视图界面必须的参数（场景：单佣金调整，新建，选择客户时，若改客户有未完成订单，会弹框提醒，点击确定会跳转到360视图界面）
    queryCustDetailInfo: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCustBrifeInfo', query),
    // 查询客户当前目标股基佣金率
    queryCustCommission: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/cust/pc/CommissionCustInfo/queryCustRealTimeCommissison', query, { isFullUrl: true }),
    // 查询客户本地限价信息以及是否同城
    queryCustLocalInfo: (query) => api.post('/groovynoauth/fsp/biz/singlecommissonchg/queryCustLocalInfo', query),
    // 校验客户如果有被驳回订单，终止订单
    terminalOrderFlow: (query) => api.post('/groovynoauth/fsp/order/commission/terminalOrderFlow', query),
    // 校验客户如果有新建订单，修改订单状态
    changeOrderStatus: (query) => api.post('/groovynoauth/fsp/order/commission/changeOrderStatus', query),
    // 根据关键字搜索客户
    queryCustList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/querySpecialCommsionCustInfo', query),
    // 查询其他佣金品种字典
    queryOtherRatioDict: (query) => api.post('/groovynoauth/fsp/biz/unStandardCommsion/querOtherCommissionRatioDict', query),
    // 查询目标股基佣金费率选项值
    queryStockCommissionRateDict: (query) => api.post('/groovynoauth/fsp/biz/unStandardCommsion/queryStockCommissionRateDict', query),
    // 查询客户当前的其他佣金费率值
    queryCurrentOtherCommissionRate: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCurrentOtherCommissionRate', query),
    // 查询订单基本信息
    queryUnStandardCommsionDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/queryNonStandardCommissionDetail', query, { isFullUrl: true }),
    // 查询订单的股基佣金、其他佣金费率
    queryUnStandardCommsionRatioDetail: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/querySpecialChgCommissionRatioDetail', query, { isFullUrl: true }),
    // 校验当前营业部信息
    checkCurrentDepartment: (query) => api.post('/groovynoauth/fsp/biz/unStandardCommsion/checkCurrentDepartment', query),
    // 校验业务品种费率是否承诺周期校验范围内
    checkCommInCommitmentPeriod: (query) => api.post('/groovynoauth/fsp/biz/unStandardCommsion/checkCommInCommitmentPeriod', query),
    // 校验客户是否在跟踪期内
    checkCustIsTrackingPeriod: (query) => api.post('/groovynoauth/fsp/biz/unStandardCommsion/checkCustIsTrackingPeriod', query),
    // 校验客户在途佣金调整
    validateCustomer: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/checkCustChgCommissionInProcess', query, { isFullUrl: true }),
    // 保存提交申请
    saveUnStandardCommsionCommionInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/save/pc/CommissionSave/saveNonStandardCommission', query, { isFullUrl: true }),
    // 查询佣金费率下拉选项字典接口
    queryCommissionRateDictionary: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommissionRateDictionary', query),
    // 存量两融客户对齐佣金场景校验接口
    checkRzRqCustCommissions: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/RzrqCustCommissionQuery/checkRzRqCustCommissions', query, { isFullUrl: true }),
    // 校验客户信息
    validateCustInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/cust/pc/CommissionCustInfo/validateCustInfo', query, { isFullUrl: true }),
    // 校验是否涨乐选投顾
    validateZLPickCustomer: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/validateZLPickCustomer', query, { isFullUrl: true }),
    // 查询经服已填写的佣金信息
    queryProductBrokerageCommissionData: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/EconAgreementCommissionQuery/queryCommissionSettingInfo', query, { isFullUrl: true }),
    // 更新经服佣金设置待办状态
    updateSetCommissionTodoStatus: (query) => api.post('/fspa/uicrm-bff-aorta/groovy/unauth/productAccountIntroduction/flow/terminalCommissionSettingTodoFlow', query, { isFullUrl: true }),
    // 查询【佣金模式】的新投顾签约有哪些需要禁用的佣金率
    queryDisableContractRatioType: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/query/pc/CommissionQuery/queryDisableContractRatioType', query, { isFullUrl: true }),
    // 全账户提佣校验
    validateUnifiedCommissionRaise: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/validateUnifiedCommissionRaise', query, { isFullUrl: true }),
  };
}
