/*
 * @Description:
 * @Author: <PERSON><PERSON>ian<PERSON>hu-********
 * @Date: 2019-08-15 14:05:43
 * @Last Modified by: LiuJianShu-********
 * @Last Modified time: 2019-08-16 14:43:24
 */

export default function productOperation(api) {
  const apiPrefix = '/groovynoauth/fsp/biz/matbranchprodpool';
  return {
    // 获取左侧列表
    queryList: query => api.post(`${apiPrefix}/queryPushHisAppList`, query),
    // 获取右侧详情
    queryDetail: query => api.post(`${apiPrefix}/queryPushHisAppDetail`, query),
    // 查询右侧详情中产品信息列表
    queryDetailProductList: query => api.post(`${apiPrefix}/queryPushHisProdList`, query),
    // 查询右侧详情中目标客户列表
    queryDetailCustList: query => api.post(`${apiPrefix}//queryPushHisCustList`, query),
    // 查询客户购买明细
    queryCustPurchaseDetails: query => api.post(`${apiPrefix}/queryPushHisPurchaseList`, query),
    // 更新详情中的产品信息
    updateProductList: query => api.post(`${apiPrefix}/updPushProdInfo`, query),
  };
}
