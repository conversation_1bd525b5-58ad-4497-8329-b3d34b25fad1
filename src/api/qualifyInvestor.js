/*
 * @Author: mi<PERSON><PERSON><PERSON>
 * @Date: 2021-10-18 17:04:39
 * @Last Modified by: mi<PERSON><PERSON><PERSON>
 * @Last Modified time:  2021-10-18 17:04:39
 * @description 合格投资者认定
 */

export default function qualifyInvestor(api) {
  return {
    // 查询合格投资者左侧列表
    queryInvestorList: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryInvestorList', query),
    // 合格投资者审批
    startQualifyInvestorPostProcess: (query) => api.post('/groovynoauth/fsp/cust/custdetail/startQualifyInvestorPostProcess', query),
    // 合格投资者详情
    queryInvestorDetails: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryInvestorDetails', query),
  };
}
