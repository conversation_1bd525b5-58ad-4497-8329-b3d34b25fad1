/*
 * @Author: l<PERSON><PERSON>
 * @Date: 2022-06-05 11:12:08
 * @description：灰度建设
 */

export default function grayscale(api) {
  return {
    // 保存常用分组
    saveGroup: (query) => api.post('/groovynoauth/fsp/common/grayscale/saveGroup', query),
    // 获取所有分组
    getGroups: (query) => api.post('/groovynoauth/fsp/common/grayscale/getGroups', query),
    // 删除菜单
    deleteGrayFunction: (query) => api.post('/groovynoauth/fsp/common/grayscale/deleteGrayFunction', query),
    // 更新菜单(技术)
    saveGrayFunction: (query) => api.post('/groovynoauth/fsp/common/grayscale/submitGrayFunctionConfig', query),
    // 更新菜单(业务)
    saveGrayEmployeeList: (query) => api.post('/groovynoauth/fsp/common/grayscale/saveGrayEmployeeList', query),
    // 获取分组详情
    getGroupDetail: (query) => api.post('/groovynoauth/fsp/common/grayscale/getGroupDetail', query),
    // 获取灰度菜单
    getGrayFunctionList: (query) => api.post('/groovynoauth/fsp/common/grayscale/getGrayFunctionList', query),
    // 获取灰度菜单详情
    getGrayFunctionDetail: (query) => api.post('/groovynoauth/fsp/common/grayscale/getGrayFunctionDetail', query),
    // 保存左侧菜单名称
    addGrayFunction: (query) => api.post('/groovynoauth/fsp/common/grayscale/addGrayFunction', query),
    // 判断是否是技术人员
    isSuperAdmin: (query) => api.post('/groovynoauth/fsp/common/grayscale/isSuperAdmin', query),
    // 员工搜索
    queryEmpList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
  };
}
