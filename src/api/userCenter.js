/**
 * @Description: 用户中心模块相关接口
 * @Author: xia<PERSON><PERSON><PERSON><PERSON>g
 * @Date: 2018-04-11 14:47:50
 */

export default function taskFeedback(api) {
  return {
    // 查询用户标签
    queryAllLabels: query => api.post('/groovynoauth/fsp/emp/label/queryAllLabels', query),
    // 添加标签
    addLabel: query => api.post('/groovynoauth/fsp/emp/label/addLabel', query),
    // 修改标签
    updateLabel: query => api.post('/groovynoauth/fsp/emp/label/updateLabel', query),
    // 删除标签
    deleteLabel: query => api.post('/groovynoauth/fsp/emp/label/deleteLabel', query),
  };
}
