/*
 * @Description: 参与客户自动流转申请
 * @Author: yuduo.zhang
 * @Date: 2021-09-06 14:55:28
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2021-09-29 13:36:13
 */

export default function customerAutoCirculation(api) {
  return {
    // 获取服务经理列表
    queryEmpList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
    // 获取主页列表
    queryList: (query) => api.post('/fspa/aorta/user/api/desktop/emp/radius/pc/serviceRadius/queryQuotaList', query, { isFullUrl: true }),
    // 获取投顾有效状态的流转类型
    queryApplyType: (query) => api.post('/fspa/aorta/user/api/desktop/emp/radius/pc/serviceRadius/queryApplyType', query, { isFullUrl: true }),
    // 获取当前最大承接客户数
    queryServiceTop: (query) => api.post('/fspa/aorta/user/api/desktop/emp/radius/pc/serviceRadius/queryServiceTop', query, { isFullUrl: true }),
    // 新建一条客户流转申请
    createAutoCirculation: (query) => api.post('/fspa/aorta/user/api/desktop/emp/radius/pc/serviceRadius/createQuota', query, { isFullUrl: true }),
    // 终止一条客户流转申请
    stopAutoCirculation: (query) => api.post('/fspa/aorta/user/api/desktop/emp/radius/pc/serviceRadius/stopQuota', query, { isFullUrl: true }),
  };
}
