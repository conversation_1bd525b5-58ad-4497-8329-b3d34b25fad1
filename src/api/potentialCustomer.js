/*
 * @Description: 潜在客户的 api 文件
 * @Author: Liu<PERSON>ianShu-K0180193
 * @Date: 2020-03-03 17:29:56
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-03-11 11:19:10
 */

export default function potentialCustomer(api) {
  return {
    // 获取开发营业部
    getCustopenaccount: (query) => api.post('/groovynoauth/potentialcust/queryOrgTree', query),
    // 开户断点列表
    queryCustOpeningAccountList: (query) => api.post('/groovynoauth/potentialcust/queryCustOpeningAccountList', query),
    // 获得介绍人
    getQueryProcessAssigner: (query) => api.post('/groovynoauth/potentialcust/queryProcessAssigner', query),
    // 获得详情接口
    getAccountFlowDetail: (query) => api.post('/groovynoauth/potentialcust/queryOpeningAccountFlowDetail', query),
    // 获得开户渠道
    getOpenChannelList: (query) => api.post('/groovynoauth/potentialcust/queryOpenChannelList', query),
    // 查询抢单潜客列表
    queryOpenAccountList: (query) => api.post('/groovynoauth/fsp/potentialcust/queryNotBelongOpenPotentialCustList', query),
    // 查询注册潜客列表
    queryRegisterList: (query) => api.post('/groovynoauth/fsp/potentialcust/queryRegisterCustList', query),
    // 抢单
    grabOrder: (query) => api.post('/groovynoauth/fsp/potentialcust/pickPotentialCust', query),
    // 获取客户反馈列表数据
    queryFeedBackList: (query) => api.post('/groovynoauth/fsp/serviceScenes/queryRelaFeeBack', query),
    // 添加服务记录
    addServiceRecord: (query) => api.post('/groovynoauth/fsp/potentialcust/addPotentialCustServiceRecord', query),
    // 查询服务记录列表
    queryServiceRecord: (query) => api.post('/groovynoauth/fsp/potentialcust/queryPotentialCustServiceRecordList', query),
    // 获取附件列表
    queryAttachmentList: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileList', query, { isFullUrl: true }),
    // 删除附件
    deleteAttachment: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileDeleteForSpecial', query, { isFullUrl: true }),
    // 已归属潜客（3.0开户）列表
    queryPotentialList: (query) => api.post('/fspa/aorta/user/api/desktop/potential/v3/all/PotentialList/queryPotentialList', query, { isFullUrl: true }),
    // 已归属潜客（3.0开户）- 系统执行列表
    queryDataByBizId: (query) => api.post('/fspa/aorta/user/api/desktop/potential/v3/all/PotentialDetail/queryDataByBizId', query, { isFullUrl: true }),
  };
}
