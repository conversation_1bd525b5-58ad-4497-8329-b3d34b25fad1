/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 客户360-客户属性相关api
 * @Date: 2018-11-07 10:00:46
 * @Last Modified by: <PERSON><PERSON>enKang
 * @Last Modified time: 2019-06-06 15:17:34
 */
export default function detailCustProperty(api) {
  return {
    // 获取客户属性信息
    queryCustomerProperty: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryCustPropertyInfo', query),
    // 获取客户属性联系信息
    queryCustContactInfo: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryCustContactInfo', query),
    // 获取涨乐财富通U会员信息
    queryZLUmemberInfo: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryZLUMemberInfo', query),
    // 获取涨乐财富通U会员等级变更记录
    queryZLUmemberLevelChangeRecords: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryZLUMemberChangeList', query),
    // 获取紫金积分会员信息
    queryZjPointMemberInfo: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryZjPointMemberInfo', query),
    // 获取紫金积分会员积分兑换流水
    queryZjPointExchangeFlow: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryZjPointExchangeFlow', query),
    // 编辑个人客户、机构客户的基本信息
    updateCustBasicInfo: (query) => api.post('/groovynoauth/fsp/cust/custdetail/modifyBasicInfo', query),
    // 查询个人客户联系方式数据
    queryPersonalContactWay: (query) => api.post('/fspa/aorta/user/api/desktop/cust/detail/contact/pc/ContactWay/queryContactWayForPerson', query, { isFullUrl: true }),
    // 查询机构客户联系方式数据
    queryOrgContactWay: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryContactWayForOrg', query),
    // 修改个人客户的联系方式中的请勿发短信，请勿打电话
    changePhoneInfo: (query) => api.post('/groovynoauth/fsp/cust/custdetail/changePersonalContactWayPhone', query),
    // 新增，修改个人客户联系方式电话信息
    updatePerPhone: (query) => api.post('/groovynoauth/fsp/cust/custdetail/operateContactWayForPhone', query),
    // 新增，修改个人客户联系方式地址信息
    updatePerAddress: (query) => api.post('/groovynoauth/fsp/cust/custdetail/operateContactWayForAddress', query),
    // 新增，修改个人客户联系方式其他信息
    updatePerOther: (query) => api.post('/groovynoauth/fsp/cust/custdetail/operateContactWayForOther', query),
    // 新增，修改机构客户联系方式电话信息
    updateOrgPhone: (query) => api.post('/groovynoauth/fsp/cust/custdetail/operateOrgContactWayForPhone', query),
    // 新增，修改机构客户联系方式地址信息，与个人客户的地址信息修改接口路径是同一个，为了后面好区分
    updateOrgAddress: (query) => api.post('/groovynoauth/fsp/cust/custdetail/operateContactWayForAddress', query),
    // 删除个人客户、机构客户的联系方式
    delContact: (query) => api.post('/groovynoauth/fsp/cust/custdetail/deleteContact', query),
    // 查询个人客户、机构客户的财务信息
    queryFinanceDetail: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryFinanceDetail', query),
    // 编辑个人客户的财务信息
    updatePerFinaceData: (query) => api.post('/groovynoauth/fsp/cust/custdetail/updatePerFinaceData', query),
    // 编辑机构客户的财务信息
    updateOrgFinaceData: (query) => api.post('/groovynoauth/fsp/cust/custdetail/updateOrgFinaceData', query),
    // 获取关系信息Tab 职务数据
    queryJobList: (query) => api.post('/groovynoauth/fsp/cust/custrelation/queryDutyDictionary', query),
    // 获取关系信息Tab 个人客户/机构客户关系信息表格数据
    queryCustRelationInfo: (query) => api.post('/groovynoauth/fsp/cust/custrelation/queryRelationInfo', query),
    // 新增/编辑 个人客户、机构客户的关系信息
    operateCustRelationInfo: (query) => api.post('/groovynoauth/fsp/cust/custrelation/operateCustRelationInfo', query),
    // 删除 个人客户、机构客户的关系信息
    deleteCustRelationInfo: (query) => api.post('/groovynoauth/fsp/cust/custrelation/deleteCustRelationInfo', query),
    // 获取关系信息Tab  个人客户/机构客户 关联账户表格数据
    queryAssociateAccount: (query) => api.post('/groovynoauth/fsp/cust/custrelation/queryAssociateAccount', query),
    // 新增/编辑 个人客户、机构客户的关联账户
    operateAssociateAccount: (query) => api.post('/groovynoauth/fsp/cust/custrelation/operateAssociateAccount', query),
    // 删除 个人客户、机构客户的关联账户
    deleteAssociateAccount: (query) => api.post('/groovynoauth/fsp/cust/custrelation/deleteAssociateAccount', query),
    // 查询营业部下个人客户经纪客户号
    queryPersonCustId: (query) => api.post('/groovynoauth/fsp/cust/custrelation/queryPersonCustList', query),
    // 根据经纪客户号 查询 姓名和服务营业部
    queryNameServiceDepart: (query) => api.post('/groovynoauth/fsp/cust/custrelation/queryNameServiceDepart', query),
    // 查询投资偏好数据
    queryInvestPreference: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryInvestPreference', query),
    // 查询机主核验信息状态
    queryOwnerInfo: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryOwnerInfo', query),
    // 获取免打扰信息详情
    queryDisturbanceInfo: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryDisturbanceInfo', query),
    // 提交免打扰信息
    updateDisturbanceInfo: (query) => api.post('/groovynoauth/fsp/cust/custdetail/updateDisturbanceInfo', query),
  };
}
