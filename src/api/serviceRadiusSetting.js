/*
 * @Description: 投顾服务半径设置
 * @Author: yuduo.zhang
 * @Date: 2021-09-06 14:53:53
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2021-09-17 15:06:01
 */

export default function serviceRadiusSetting(api) {
  return {
    // 获取主页列表
    queryList: (query) => api.post('/fspa/aorta/user/api/desktop/emp/radius/pc/serviceRadius/queryConfigList', query, { isFullUrl: true }),
    // 保存单条服务半径上限
    saveServiceTop: (query) => api.post('/fspa/aorta/user/api/desktop/emp/radius/pc/serviceRadius/saveConfig', query, { isFullUrl: true }),
    // 清除单条数据
    clearServiceRadiusRow: (query) => api.post('/fspa/aorta/user/api/desktop/emp/radius/pc/serviceRadius/clearConfigData', query, { isFullUrl: true }),
  };
}
