/**
 * @Author: liji<PERSON>jing
 * @Descripter: 自定义海报配置
 * @Date: 2022-04-25 17:09:53
 * @Last Modified by: weiting
 * @Last Modified time: 2022-05-05 14:17:51
 */

export default function customizePoster(api) {
  return {
    // 获取生日/节日海报列表
    queryPosterList: (query) => api.post('/groovynoauth/feedCenter/poster/queryPosterList', query),
    // 编辑生日/节日海报列表接口
    editPosterList: (query) => api.post('/groovynoauth/feedCenter/poster/editPosterList', query),
    // 获取节日列表接口
    queryFestivalList: (query) => api.post('/groovynoauth/feedCenter/poster/queryFestivalList', query),
    // 新建/编辑节日
    editFestival: (query) => api.post('/groovynoauth/feedCenter/poster/editFestival', query),
    // 删除节日接口
    deleteFestival: (query) => api.post('/groovynoauth/feedCenter/poster/deleteFestival', query),
  };
}
