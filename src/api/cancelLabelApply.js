/*
 * @Description: 取消客户选投顾标识申请
 * @Author: yuduo.zhang
 * @Date: 2022-02-21 17:37:28
 * @Last Modified by: yuduo.zhang
 * @Last Modified time: 2022-02-28 15:20:31
 */

export default function cancelLabelApply(api) {
  return {
    // 获取全国客户列表
    queryAllCustList: (query) => api.post('/groovynoauth/fsp/common/queryAllCustList', query),
    // 获取部门下拉选数据
    queryOrgInfo: (query) => api.post('/groovynoauth/fsp/biz/queryOrgInfo', query),
    // 获取左侧客户审批列表
    queryList: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/queryCancelApplicationList', query),
    // 获取右侧客户审批详情数据
    queryApproveDetail: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/queryCancelApplicationDetail', query),
    // 获取审批轨迹数据
    queryFlowHistory: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/queryFlowHistory', query),
    // 新建时查经纪客户号获取客户信息
    queryCustInfo: (query) => api.post('/fspa/aorta/user/api/desktop/relation/singlecancel/pc/singlecancel/queryCustRelationshipInfo', query, { isFullUrl: true }),
    // 获取审批按钮、审批人数据
    queryFlowButton: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/queryFlowButton', query),
    // 提交保存
    saveData: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/saveCancelCustDrivingData', query),
    // 发起审批流程
    startFlow: (query) => api.post('/groovynoauth/fsp/activitiflow/startFlow', query),
    // 删除刚提交保存的数据
    deleteData: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/deleteDataWhenFailed', query),
    // 继续审批流程
    doApprove: (query) => api.post('/groovynoauth/fsp/biz/cancelpickassign/doApprove', query),
  };
}
