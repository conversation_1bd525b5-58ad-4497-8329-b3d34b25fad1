/*
 * @Description: 潜在客户-转化追踪的 api
 * @Author: LiuJianShu-K0180193
 * @Date: 2020-04-14 17:01:01
 * @Last Modified by: LiuJianShu-K0180193
 * @Last Modified time: 2020-04-14 17:19:08
 */
export default function transformTrack(api) {
  return {
    // 投顾视角
    // 顶部汇总指标
    querySummaryIndicators: query => api.post('/groovynoauth/fsp/potentialcust/querySummaryIndicators', query),
    // 个人潜客转化效果
    queryPersonTransformEffect: query => api.post('/groovynoauth/fsp/potentialcust/queryPersonTransformEffect', query),
    // 潜客月度走势图（员工层）
    queryEmpTrendData: query => api.post('/groovynoauth/fsp/potentialcust/queryEmpTrendData', query),
    // 个人转化龙虎榜
    queryEmpRankingTop3: query => api.post('/groovynoauth/fsp/potentialcust/queryEmpRankingTop3', query),
    // 管理层视角
    // 潜客数据汇总
    queryPotentialCustSummaryData: query => api.post('/groovynoauth/fsp/potentialcust/queryPotentialCustSummaryData', query),
    // 管理层整体转化效果
    queryOverallTransformEffect: query => api.post('/groovynoauth/fsp/potentialcust/queryOverallTransformEffect', query),
    // 潜客月度走势图（管理层）
    queryManageTrendData: query => api.post('/groovynoauth/fsp/potentialcust/queryManageTrendData', query),
    // 分公司转化龙虎榜
    queryBranchRankingTop3: query => api.post('/groovynoauth/fsp/potentialcust/queryBranchRankingTop3', query),
    // 客户转化记录tab页表格数据
    queryCustomerTransList: query => api.post('/groovynoauth/fsp/potentialcust/queryCustomerTransList', query),
  };
}
