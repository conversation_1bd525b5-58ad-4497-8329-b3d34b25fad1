/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2023-03-08 15:19:21
 * @LastEditors: luz<PERSON>yu
 * @LastEditTime: 2023-03-29 11:27:00
 * @Description: 公务号通话管理视图接口
 */

export default function recordsManageView(api) {
  return {
    // 总览分析-公务号使用总览
    queryOfficialAccountNums: (query) => api.post('/desktop/emp/official/pc/QueryVoiceTotal/queryVoiceUsedTotal', query),
    // 总览分析-员工使用情况
    queryEmpUseInfoList: (query) => api.post('/desktop/emp/official/pc/QueryVoiceTotal/queryEmpUseInfoList', query),
    // 通话明细列表
    queryVoiceUsedTotal: (query) => api.post('/desktop/emp/official/pc/QueryVoiceDetail/queryVoiceUsedTotal', query),
    // 查询圈客权限
    getPermissionByParam: (query) => api.post('/desktop/cust/permission/all/PermissionQuery/getPermissionByParam', query),
    // 查询录音文件相关的客户
    queryRecordsCustListMgr: (query) => api.post('/groovynoauth/voice/queryRecordsCustListMgr', query),
  };
}
