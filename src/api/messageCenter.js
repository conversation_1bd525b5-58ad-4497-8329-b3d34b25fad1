/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2018-05-22 22:35:31
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2018-05-25 11:10:56
 */
export default function messageCenter(api) {
  return {
    // 获取消息通知提醒列表
    // 迁移前旧接口路径：/groovynoauth/fsp/emp/notification/queryNotificationMsg
    getRemindMessageList: (query) => api.post('/fspa/aorta/biz/api/desktop/notification/inbox/pc/notificationService/queryNotificationMsg', query, { isFullUrl: true }),
    // 更新消息已读状态
    // 迁移前旧接口路径：/groovynoauth/fsp/emp/notification/updateNotificationStatus
    updateNotificationStatus: (query) => api.post('/fspa/aorta/biz/api/desktop/notification/inbox/pc/notificationService/updateNotificationStatusPc', query, { isFullUrl: true }),
    // 消息中心左侧消息菜单
    // 迁移前旧接口路径：/groovynoauth/fsp/emp/notification/queryMsgMenu
    queryMsgMenu: (query) => api.post('/fspa/aorta/biz/api/desktop/notification/inbox/pc/notificationService/queryMsgMenu', query, { isFullUrl: true }),
  };
}
