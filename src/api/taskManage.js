/*
 * @Author: liji<PERSON>jing
 * @Date: 2019-04-08 11:00:59
 * @description 服务记录管理的API
 */

export default function newHome(api) {
  return {
    // 查询服务管理记录列表
    queryServiceManageList: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/serverecord/pc/serveRecord/queryMotServiceRecord', query, { isFullUrl: true }),
    // 查询服务策略详情
    queryServicePolicyDetail: (query) => api.post('/groovynoauth/fsp/cust/service/queryServicePolicyDetail', query),
    // 模糊匹配事件名称
    queryEventNameByKeyword: (query) => api.post('/groovynoauth/fsp/cust/service/queryEventNameByKeyword', query),
    // 查询机构树
    queryServiceRecordOrgTree: (query) => api.post('/groovynoauth/fsp/cust/service/queryServiceRecordOrgTree', query),
    // 查询当前用户下的客户列表
    queryMyCustList: (query) => api.post('/groovynoauth/fsp/common/queryMyCustList', query),
  };
}
