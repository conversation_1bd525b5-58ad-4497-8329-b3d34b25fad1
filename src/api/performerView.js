/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 执行者视图模块的接口
 * @Date: 2018-08-20 13:15:28
 * @Last Modified by: x<PERSON>xiaow<PERSON>
 * @Last Modified time: 2024-04-03 18:05:34
 */

export default function performerView(api) {
  return {
    // 视图的公共列表接口（创建者、执行者、管理者）
    queryTaskList: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryMOTMissions2', query),
    // 执行者视图的详情基本信息
    queryTaskDetailBasicInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/query/pc/queryMissionDetail/queryMissionDetail', query, { isFullUrl: true }),
    // 智能外呼委托时间
    queryExternalCallTime: (query) => api.post('/groovynoauth/fsp/campaign/smart/queryExternalCallTime', query),
    // 智能外呼委托时间（新）
    queryExternalCallTimeInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/smartcall/pc/queryCallTimeInfo/queryCallInfo', query, { isFullUrl: true }),
    // 发起智能外呼
    applyExternalCall: (query) => api.post('/groovynoauth/fsp/campaign/smart/applyExternalCall', query),
    // 发起智能外呼(新)
    applyNewExternalCall: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/smartcall/pc/applyCall/applyCallPc', query, { isFullUrl: true }),
    // NOTE: SWB 2024-08-23 替换成 biz-bff 接口
    queryTargetCust: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/query/pc/queryMissionCust/queryEmpMissionCust', query, { isFullUrl: true }),
    // 执行者视图的目标客户的详情
    queryTargetCustDetail: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryCustDetail', query),
    queryCustExtraDetail: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryCustExtraDetail', query),
    // 执行视图下添加服务记录
    addMotServeRecord: (query) => api.post('/groovynoauth/fsp/cust/service/addMotServeRecord2', query),
    // 上传文件需要先上传uuid
    queryCustUuid: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryCustUuid', query),
    // 删除文件
    ceFileDelete: (query) => api.post('/fspa/aorta/dmz/api/storage/s3/fileDeleteForSpecial', query, { isFullUrl: true }),
    // 获取任务简报
    getMissionBrief: (query) => api.post('/groovynoauth/fsp/campaign/mot/getMissionBrief', query),
    // 预览客户明细
    previewCustDetail: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryCustListOfMission', query),
    // 管理者视图查询任务的详细信息
    queryMngrMissionDetailInfo: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/query/pc/queryMissionDetail/queryMngMissionInfo', query, { isFullUrl: true }),
    // 管理者视图客户反馈一二级
    countFlowFeedBack: (query) => api.post('/groovynoauth/fsp/campaign/mot/countFlowFeedBack', query),
    // 管理者视图任务实施进度
    countFlowStatus: (query) => api.post('/groovynoauth/fsp/campaign/mot/countFlowStatus', query),
    // 添加服务记录中 服务类型
    getServiceType: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryMissionList22ForTask', query),
    // 问卷调查
    getTempQuesAndAnswer: (query) => api.post('/groovynoauth/fsp/assess/common/queryTempQuesAndAnswer', query),
    // 问卷调查保存答案
    saveAnswersByType: (query) => api.post('/groovynoauth/fsp/assess/common/saveAnswersByType', query),
    // 任务反馈统计接口
    countAnswersByType: (query) => api.post('/groovynoauth/fsp/assess/common/countAnswersByType', query),
    // 任务反馈已反馈总数统计
    countExamineeByType: (query) => api.post('/groovynoauth/fsp/assess/common/countExamineeByType', query),
    // 导出
    exportCustListExcel: (query) => api.post('/groovynoauth/fsp/assess/common/exportCustListExcel', query),
    // 执行者视图查客户
    queryCustomer: (query) => api.post('/groovynoauth/fsp/cust/custlist/queryMssnCustsByExecutorId', query),
    // 任务列表管理者视图下任务实施简报的生成
    createMotReport: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/serverecord/pc/exportExcel/createFeedBackExcelFile', query, { isFullUrl: true }),
    // 获取生成报告信息
    queryMOTServeAndFeedBackExcel: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/serverecord/pc/exportExcel/queryExportInfo', query, { isFullUrl: true }),
    // 获取去重后的客户数量
    queryDistinctCustomerCount: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryDistinctCustListOfMission', query),
    // 获取服务经理维度任务数据
    getCustManagerScope: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryEmpListOfMission', query),
    // 查询涨乐财富通服务方式下给予客户选择的客户反馈选项
    queryCustFeedbackList: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryCustOptionsByTaskType', query),
    // 查询涨乐财富通服务方式下的审批人
    queryApproval: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryNextApproval', query),
    // 查询服务结果进度
    queryExecutorFlowStatus: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryExecutorFlowStatus', query),
    // 查询我执行的任务-服务结果-已完成服务评价客户
    queryStarPieData: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryExecutorEvaluateStar', query),
    // 查询服务结果客户反馈
    queryExecutorFeedBack: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryExecutorFeedBack', query),
    // 查询客户明细
    queryExecutorDetail: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryExecutorDetail', query),
    // 服务经理维度查询客户明细
    previewCustDetailByScope: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryCustListDetailOfMission', query),
    // 获取服务经理维度去重后的客户数量
    queryDistinctCustListDetailOfMission: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryDistinctCustListDetailOfMission', query),
    // 根据任务类型获取任务绑定的投资建议模板列表
    getTemplateList: (query) => api.post('/groovynoauth/fsp/campaign/investAdvice/queryTemplateListByType', query),
    // 翻译投资建议模板
    translateTemplate: (query) => api.post('/groovynoauth/fsp/campaign/investAdvice/replaceCustIndexPlaceHoders', query),
    // 获取客户名下其他代办任务
    getOtherTaskList: (query) => api.post('/desktop/sales/smartMission/mot/SmartMssnCustTaskQuery/listCustTaskForPC', query),
    // 查询可以分配任务的人员列表
    queryAllotEmpList: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryDispatchableEmpList', query),
    // 选择了人员后将任务分配给该人员
    dispatchTaskToEmp: (query) => api.post('/groovynoauth/fsp/campaign/mot/dispatchToEmpDirectly', query),
    // 针对 MOT 回访类型任务添加服务记录接口
    addMotReturnVisitServiceRecord: (query) => api.post('/groovynoauth/fsp/cust/service/addMotTGVisitServeRecord', query),
    // 批量添加服务记录
    saveBatchAddServiceRecord: (query) => api.post('/groovynoauth/fsp/cust/service/addBatchedMotServeRecord', query),
    // 查询触发服务状态
    queryFireEvaluateStatus: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryTaskEvaTriggerStatus', query),
    // 保存触发服务状态
    saveFireEvaluateStatus: (query) => api.post('/groovynoauth/fsp/campaign/mot/sendTaskEvaluate2', query),
    // 取消服务评价
    cancelEvaluateTrigger: (query) => api.post('/groovynoauth/fsp/cust/service/cancelEvaluateTrigger', query),
    // 查询有关服务评价的客户明细
    queryEvaluateCustDetail: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryExecutorEvalCustDetail', query),
    // 我部门的任务-查询已完成服务评价的星级饼图
    queryControllerStarPie: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryManagerEvaluateStar', query),
    // 我部门的任务-查询已完成服务评价的客户明细
    queryControllerEvaluateCustDetail: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryCustListOfMissionForEvaluate', query),
    // 查询机主核验信息状态
    queryOwnerInfo: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryOwnerInfo', query),
    // 选择客户反馈的时候校验
    validateMOTChgComm: (query) => api.post('/groovynoauth/fsp/cust/service/validateMOTChgComm', query),
    // 任务实时查询自主完成客户人数
    queryMotAutoCustNum: (query) => api.post('/groovynoauth/fsp/campaign/mot/queryMotAutoCustNum', query),
    // 查询外呼配置列表
    queryExternalCallConfigInfo: (query) => api.post('/groovynoauth/fsp/campaign/smart/queryExternalCallConfigInfo', query),
    // 编辑外呼配置列表
    editExternalCallConfigInfo: (query) => api.post('/groovynoauth/fsp/campaign/smart/editExternalCallConfigInfo', query),
    // 根据任务id查询服务目标
    queryServeTarget: (query) => api.post('/groovynoauth/fsp/motConfiguration/queryServeTarget', query),
    // 获取当前客户下的录音文件列表
    queryCustVoiceList: (query) => api.post('/groovynoauth/fsp/motConfiguration/queryCustVoiceList', query),
    // 查询可关联的客群列表
    querySmartCallCustGroupList: (query) => api.post('/desktop/sales/smartMission/mot/SmartCallFilterQuery/querySmartCallCustGroupList', query),
    // 获取智能外呼过滤配置
    querySmartCallFilterConfig: (query) => api.post('/desktop/sales/smartMission/mot/SmartCallFilterQuery/querySmartCallFilterConfig', query),
    // 设置智能外呼过滤配置
    setSmartCallFilterConfig: (query) => api.post('/desktop/sales/smartMission/mot/SmartCallFilterCommand/setSmartCallFilterConfig', query),
    // 查询客群对应的客户列表
    queryCustListByGroup: (query) => api.post('/desktop/sales/smartMission/mot/SmartCallFilterQuery/queryCustListByGroup', query),
    // 执行撤销外呼
    doCancelCall: (query) => api.post('/desktop/sales/smartMission/mot/SmartCallRepealCommand/doCancelCall', query),
    // 查询今日剩余可撤销次数和全选人数
    getNumValidateInfo: (query) => api.post('/desktop/sales/smartMission/mot/SmartCallRepealQuery/getNumValidateInfo', query, { noEmpId: true }),
    // 查询撤销外呼列表
    listCallingCustInfo: (query) => api.post('/desktop/sales/smartMission/mot/SmartCallRepealQuery/listCallingCustInfo', query),
    // 查询个人客户联系方式数据
    queryPersonalContactWay: (query) => api.post('/fspa/aorta/user/api/desktop/cust/detail/contact/pc/ContactWay/queryContactWayForPerson', query, { isFullUrl: true }),
    // 查询机构客户联系方式数据
    queryOrgContactWay: (query) => api.post('/groovynoauth/fsp/cust/custdetail/queryContactWayForOrg', query),
    // 查找任务列表
    queryEvent: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/subscribe/pc/eventSubscribe/queryEvent', query, { isFullUrl: true }),
    // 查看我的订阅
    queryMySubEvent: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/subscribe/pc/eventSubscribe/queryMySubEvent', query, { isFullUrl: true }),
    // 查看历史订阅
    queryHisSubEvent: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/subscribe/pc/eventSubscribe/queryHisSubEvent', query, { isFullUrl: true }),
    // 订阅任务
    subEvent: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/subscribe/pc/eventSubscribe/subEvent', query, { isFullUrl: true }),
    // 取消、重新订阅任务
    cancelMySub: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/subscribe/pc/eventSubscribe/cancelMySub', query, { isFullUrl: true }),
    // 删除已下架任务
    deleteOffline: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/subscribe/pc/eventSubscribe/deleteOffline', query, { isFullUrl: true }),
    // 查看订阅消息列表
    queryNote: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/subscribe/pc/eventSubscribe/queryNote', query, { isFullUrl: true }),
    // 今日取消查看订阅消息
    cancelNotify: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/subscribe/pc/eventSubscribe/cancelNotify', query, { isFullUrl: true }),
    // 查看订阅消息
    readNotify: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/subscribe/pc/eventSubscribe/readNotify', query, { isFullUrl: true }),
    // 查看企微支持任务数
    queryWxMssnCount: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/wx/pc/wxmission/queryWxMssnCount', query, { isFullUrl: true }),
    // 今日取消查看企微支持任务数
    cancelWxMssnNotify: (query) => api.post('/fspa/aorta/biz/api/desktop/smartmission/wx/pc/wxmission/cancelWxMssnNotify', query, { isFullUrl: true }),
    // 搜索任务负责人
    getSearchCoAssigneesList: (query) => api.post('/groovynoauth/fsp/biz/privateCustApplication/queryEmpList', query),
  };
}
