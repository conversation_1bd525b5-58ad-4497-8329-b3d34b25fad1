/**
 * @Description: 分公司集中分配
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 17:37:03
 * @Last Modified by: l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-07-12 17:37:03
 */
export default function custDistribute(api) {
  return {
    // 查询右侧详情
    queryBranchAltogetherDetail: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/queryApplicationDetail', query, { isFullUrl: true }),
    // 查询右侧详情的客户列表
    queryBranchAltogetherDetailCustList: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/queryCustListPaged', query, { isFullUrl: true }),
    // 搜索查询客户
    queryCustList: (query) => api.post('/groovynoauth/fsp/common/queryCustList', query),
    // 查询分公司机构树
    queryEmpOrgTree: (query) => api.post('/groovynoauth/fsp/emp/org/queryEmpOrgTree', query),
    // 查询左侧列表(2025-01-07 分公司集中分配接入大文件上传超时优化（新增提交失败状态），左侧列表替换新接口)
    getApplyList: (query) => api.post('/fspa/aorta/user/api/desktop/relation/branchaltogether/pc/branchaltogether/queryApplicationList', query, { isFullUrl: true }),
  };
}
