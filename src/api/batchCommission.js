/**
 * 批量佣金接口
 */

export default function batchCommission(api) {
  return {
    // 通过用户输入的关键字，获取可申请的客户列表
    getCanApplyCustList: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/cust/pc/CommissionCustInfo/queryCustInfo', query, { isFullUrl: true }),
    // 校验添加的客户
    syncValidateCustomerList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/syncValidateCustomerList', query),
    // 佣金调整校验客户如果有被驳回订单，终止订单
    terminalOrderFlow: (query) => api.post('/groovynoauth/fsp/order/commission/terminalOrderFlow', query),
    // 佣金调整校验客户如果有新建订单，修改订单状态
    changeOrderStatus: (query) => api.post('/groovynoauth/fsp/order/commission/changeOrderStatus', query),
    // 查询校验后的客户列表信息
    queryValidateCustomerList: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryValidateCustomerList', query),
    // 查询导入客户的校验进度
    queryValidateProgress: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryValidateProgress', query),
    // 针对批量智能调佣判断配额是否满足与当前时间是否为交易日判断接口
    applyForSmartQuota: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/applyForSmartQuota', query),
    // 提交批量佣金调整
    submitBatchCommission: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/saveBatchJustCommionInfo', query),
    // 获取其他佣金费率选项
    queryOtherCommissionOptions: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryCommissionRateDictionary', query),
    // 全账户提佣校验
    validateUnifiedCommissionRaise: (query) => api.post('/fspa/aorta/biz/api/desktop/commission/validate/pc/CommissionValidate/validateUnifiedCommissionRaise', query, { isFullUrl: true }),
  };
}
