/*
 * @Description: 权重设置的接口文件
 * @Author: Liu<PERSON>ian<PERSON>hu-K0180193
 * @Date: 2019-06-12 14:29:51
 * @Last Modified by: <PERSON><PERSON>ian<PERSON>hu-K0180193
 * @Last Modified time: 2019-06-14 10:07:40
 */

export default function weightSetting(api) {
  return {
    // 查询列表
    queryList: (query) => api.post('/groovynoauth/fsp/biz/weightassign/queryApplicationList', query),
    // 查询详情
    queryDetail: (query) => api.post('/groovynoauth/fsp/biz/weightassign/queryApplicationDetail', query),
    // 查询详情中的客户列表
    queryDetailCustomerList: (query) => api.post('/groovynoauth/fsp/biz/weightassign/queryAssignDetail', query),
    // 查询历史流程
    queryHistoryFlow: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowHistory', query),
    // 查询下一步按钮和审批人
    queryFlowButtonList: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowButton', query),
    // NOTE: SWB 2025-07-29 【服务权重】查询下一步按钮和审批人
    queryFlowButtonListNew: (query) => api.post('/fspa/aorta/user/api/desktop/relation/weightassign/pc/WeightAssign/queryFlowButton', query, { isFullUrl: true }),
    // 查询校验后的客户详细信息
    queryValidateCustomerList: (query) => api.post('/groovynoauth/fsp/biz/weightassign/queryValidateCustomerList', query),
    // 校验客户
    syncValidate: (query) => api.post('/fspa/aorta/user/api/desktop/relation/weightassign/pc/WeightAssignValidate/syncValidateWeightAssignList', query, { isFullUrl: true }),
    // 异步校验客户
    asyncValidate: (query) => api.post('/groovynoauth/fsp/biz/weightassign/asyncValidateCustomerList', query),
    // 查询进度
    queryProgress: (query) => api.post('/groovynoauth/fsp/biz/chgcommsion/queryValidateProgress', query),
    // 提交数据
    saveApplication: (query) => api.post('/fspa/aorta/user/api/desktop/relation/weightassign/pc/WeightAssign/saveApplication', query, { isFullUrl: true }),
    // 更新数据
    updateApplication: (query) => api.post('/groovynoauth/fsp/biz/weightassign/updateApplication', query),
    // 发起流程
    startFlow: (query) => api.post('/groovynoauth/fsp/activitiflow/startFlow', query),
    // 流程审批
    doApprove: (query) => api.post('/groovynoauth/fsp/activitiflow/doApprove', query),
  };
}
