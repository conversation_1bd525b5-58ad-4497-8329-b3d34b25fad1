/*
 * Description: 总部客户分配
 * @Author: LiuJianShu-K0180193
 * @Date: 2021-06-21 13:53:18
 * @Last Modified by: LiuJianShu-K0180193
 * @Last Modified time: 2021-06-22 15:52:14
 */

export default function headquartersCustAllot(api) {
  return {
    // 查询下一步按钮和审批人
    queryFlowButton: (query) => api.post('/groovynoauth/fsp/activitiflow/queryFlowButton', query),
    // 走流程接口
    doApprove: (query) => api.post('/groovynoauth/fsp/cust/manager/doApprove', query),
    // 查询提交的客户数据中是否有投顾
    validBranchAndDepartCustTGConfirm: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/validBranchAndDepartCustTGConfirm', query),
    // 总部分配时，校验客户接口
    syncValidHeadquartersCustAssign: (query) => api.post('/fspa/aorta/user/api/desktop/relation/headassign/pc/HeadAssign/validate', query, { isFullUrl: true }),
    // 查询设置权重的客户个数
    queryInTransitWeightAssign: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryInTransitWeightAssign', query),
    // 查询已校验的客户列表
    queryHeadquartersValidCustList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryHeadquartersValidCustList', query),
    // 保存
    saveHeadquartersCustAssign: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/saveHeadquartersCustAssign', query),
    // 获取详情信息
    queryHeadquartersCustAssignDetail: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryHeadquartersCustAssignDetail', query),
    // 详情中的客户列表
    queryHeadquartersCustList: (query) => api.post('/groovynoauth/fsp/biz/customerAssign/queryHeadquartersCustList', query),
  };
}
