/**
 * 历史对比排名柱状图数据
 */
exports.response = function (req, res) {
  return {
    code: '0',
    msg: 'OK',
    resultData: {
      historyCardRecordVo: {
        current_page: '1',
        total_num: '30',
        data: {
          indiModel: {
            key: 'effCustNum',
            name: '有效客户数',
            value: null,
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: null,
          },
          orgModel: [
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ323263',
              level2Name: '无锡分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '1',
              rank_current: '1',
              rank_contrast: '12',
              rand_change: '11',
              changeRate: null,
              indiModelList: null,
            },
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ323270',
              level2Name: '盐城分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '2',
              rank_current: '2',
              rank_contrast: '24',
              rand_change: '22',
              changeRate: null,
              indiModelList: null,
            },
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ323288',
              level2Name: '江西分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '3',
              rank_current: '3',
              rank_contrast: '26',
              rand_change: '23',
              changeRate: null,
              indiModelList: null,
            },
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ323264',
              level2Name: '河南分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '4',
              rank_current: '4',
              rank_contrast: '18',
              rand_change: '14',
              changeRate: null,
              indiModelList: null,
            },
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ001041116',
              level2Name: '辽宁分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '5',
              rank_current: '5',
              rank_contrast: '15',
              rand_change: '10',
              changeRate: null,
              indiModelList: null,
            },
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ323267',
              level2Name: '安徽分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '6',
              rank_current: '6',
              rank_contrast: '27',
              rand_change: '21',
              changeRate: null,
              indiModelList: null,
            },
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ001041108',
              level2Name: '北京分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '7',
              rank_current: '7',
              rank_contrast: '5',
              rand_change: '-2',
              changeRate: null,
              indiModelList: null,
            },
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ323265',
              level2Name: '福建分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '8',
              rank_current: '8',
              rank_contrast: '29',
              rand_change: '21',
              changeRate: null,
              indiModelList: null,
            },
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ001041093',
              level2Name: '南京分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '9',
              rank_current: '9',
              rank_contrast: '1',
              rand_change: '-8',
              changeRate: null,
              indiModelList: null,
            },
            {
              level1Id: 'ZZ001041',
              level1Name: '经纪及财富管理部',
              level2Id: 'ZZ323268',
              level2Name: '山东分公司',
              level3Id: null,
              level3Name: null,
              level4Id: null,
              level4Name: null,
              value: '0',
              rank: '10',
              rank_current: '10',
              rank_contrast: '21',
              rand_change: '11',
              changeRate: null,
              indiModelList: null,
            },
          ],
        },
      },
    },
  };
};
