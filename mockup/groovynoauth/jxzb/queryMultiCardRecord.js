/**
 * 初始化返回报表下所有分类以及分类下所有Echarts图表信息
 */

exports.response = function (req, res) {
  return {
      code: '0',
      msg: 'OK',
      resultData: [
        {
          key: 'custAmountDetail',
          name: '客户指标明细',
          description: null,
          data: [
            {
              indiModel: {
                key: 'effCustNum',
                name: '有效客户数',
                value: null,
                unit: '户',
                description: '统计周期期末为有效的客户数',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323288',
                  level2Name: '江西分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041116',
                  level2Name: '辽宁分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323267',
                  level2Name: '安徽分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323265',
                  level2Name: '福建分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323268',
                  level2Name: '山东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'InminorCustNum',
                name: '高净值客户数',
                value: null,
                unit: '户',
                description: '是否零售客户标志为非零售客户',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323288',
                  level2Name: '江西分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041116',
                  level2Name: '辽宁分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323267',
                  level2Name: '安徽分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323265',
                  level2Name: '福建分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323268',
                  level2Name: '山东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'newCustNum',
                name: '新增客户数',
                value: null,
                unit: '户',
                description: '统计周期内新增且成为有效户的客户数',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: 'Y',
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '4082',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '4081',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '1',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '3587',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '3582',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '5',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '3430',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '3424',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '6',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323289',
                  level2Name: '西北分公司（筹）',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '3211',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '3208',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '3',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '2236',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '2230',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '6',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '2200',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '2185',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '15',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323274',
                  level2Name: '湖南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '2160',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '2158',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '2',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '2012',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '2012',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323262',
                  level2Name: '徐州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '1619',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '1613',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '6',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '1597',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'pNewCustNum',
                      name: '个人',
                      value: '1593',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewCustNum',
                      name: '一般机构',
                      value: '4',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'oNewPrdtCustNum',
                      name: '产品机构',
                      value: '0',
                      unit: '户',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '新开客户数',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          key: 'asetDetail',
          name: '资产指标明细',
          description: null,
          data: [
            {
              indiModel: {
                key: 'allTotAset',
                name: '总资产',
                value: null,
                unit: '元',
                description: '所有客户普通账户和信用账户内资产总和',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323288',
                  level2Name: '江西分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041116',
                  level2Name: '辽宁分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323267',
                  level2Name: '安徽分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323265',
                  level2Name: '福建分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323268',
                  level2Name: '山东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'shzNpRate',
                name: '沪深归集率',
                value: null,
                unit: '%',
                description: '客户在沪深市场归属华泰的资产总额/所有资产总额',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323288',
                  level2Name: '江西分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041116',
                  level2Name: '辽宁分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323267',
                  level2Name: '安徽分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323265',
                  level2Name: '福建分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323268',
                  level2Name: '山东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'gjzCustAset',
                name: '高净值客户总资产',
                value: null,
                unit: '元',
                description: '客户性质为高净值客户名下普通账户和信用账户内资产总和',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323288',
                  level2Name: '江西分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041116',
                  level2Name: '辽宁分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323267',
                  level2Name: '安徽分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323265',
                  level2Name: '福建分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323268',
                  level2Name: '山东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            }
          ]
        },
        {
          key: 'transAmountDetail',
          name: '交易指标明细',
          description: null,
          data: [
            {
              indiModel: {
                key: 'zhTranAmt',
                name: '综合交易量',
                value: null,
                unit: '元',
                description: '统计期内各类交易的总和，包括：基础股基交易、场内货币市场基金交易、债券ETF交易',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: 'Y',
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '132525511141.7105',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '21362122828.59',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '140236855.1',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '111023151458.0205',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '93371232744.9785',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '4679407569.34',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '17196608.16',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '88674628567.4785',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '73941374122.3994',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '1345871853.48',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '2181384.04',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '72593320884.8794',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '59351336944.1574',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '4700555736.47',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '32190887.6',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '54618590320.0874',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '47052424518.2652',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '1222240388.74',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '4488409.63',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '45825695719.8952',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '46651991637.7835',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '506946540.14',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '525640.9',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '46144519456.7435',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '41309479579.3921',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '1065350846.89',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '451673',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '40243677059.5021',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '40424584360.7469',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '465133443.98',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '821092.3',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '39958629824.4669',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041118',
                  level2Name: '浙江分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '38149429330.7224',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '2909559622.69',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '55924.2',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '35239813783.8324',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '35077910182.349',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfHbTranAmt',
                      name: '场内货币市场基金',
                      value: '1451673362.09',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'kfZqTranAmt',
                      name: '债券ETF',
                      value: '1361061.2',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'tradTranAmtCopy',
                      name: '基础股基',
                      value: '33624875759.059',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '综合交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                }
              ]
            },
            {
              indiModel: {
                key: 'tradTranAmt',
                name: '基础股基交易量',
                value: null,
                unit: '元',
                description: '统计期内股基交易量总和，包括范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: 'Y',
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '111023151458.0205',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '75337525329.9605',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '27062167345.53',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '8623458782.53',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '88674628567.4785',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '65120179753.9585',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '16668208091.01',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '6886240722.51',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '72593320884.8794',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '47911263126.8194',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '17008433416.74',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '7673624341.32',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '54618590320.0874',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '42433765067.9574',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '8319897647.05',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '3864927605.08',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '46144519456.7435',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '31946301701.7835',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '9961277402.3',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '4236940352.66',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '45825695719.8952',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '33463381975.1652',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '8599091891.76',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '3763221852.97',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '40243677059.5021',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '28157907872.7521',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '8306125362.3',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '3779643824.45',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '39958629824.4669',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '29347862142.8269',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '7580302957.81',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '3030464723.83',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041118',
                  level2Name: '浙江分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '35239813783.8324',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '25951179908.8824',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '6736320909.31',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '2552312965.64',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '33624875759.059',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'ptGjTranAmt',
                      name: '普通',
                      value: '24374977279.229',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'dbGjTranAmt',
                      name: '担保',
                      value: '6785507722.38',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'xyGjTranAmt',
                      name: '信用',
                      value: '2464390757.45',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '基础股基交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                }
              ]
            },
            {
              indiModel: {
                key: 'hskhTranAmt',
                name: '港股通交易量',
                value: null,
                unit: '元',
                description: '普通账户沪港通与深港通交易量总和',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: 'Y',
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '1038965418.14',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '883457990.94',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '155507427.2',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '979555960.49',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '712843732.82',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '266712227.67',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '776830753.93',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '613409458.32',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '163421295.61',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '588695516.21',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '265011137.12',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '323684379.09',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '375872387.37',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '236616992.39',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '139255394.98',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041118',
                  level2Name: '浙江分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '276815955.18',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '186956199.73',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '89859755.45',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '232451207.57',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '186912453.72',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '45538753.85',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '194909284.14',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '177163477.98',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '17745806.16',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '170172796.21',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '134396780.5',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '35776015.71',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '155855966.14',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'hTranAmt',
                      name: '沪港通',
                      value: '116464166.7',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'skhTranAmt',
                      name: '深港通',
                      value: '39391799.44',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '港股通交易量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          key: 'productDetail',
          name: '产品销售指标明细',
          description: null,
          data: [
            {
              indiModel: {
                key: 'curPrdtTranAmt',
                name: '产品当期销量',
                value: null,
                unit: '元',
                description: '统计周期内产品销售金额之和，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: 'Y',
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '6692414488.91',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '520139967.54',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '164774521.37',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '6001500000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '6000000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '2040652791.6',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '1856437529.11',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '179115262.49',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '5100000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '1009296314.83',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '550778643.61',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '356557671.22',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '101960000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '999893104.15',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '205633618.34',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '556409485.81',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '163550000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '74300000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '721434593.37',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '270279840.73',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '450954752.64',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '200000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '715713048.56',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '491674139.25',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '219078909.31',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '4960000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '630640798.69',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '168435930.24',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '290904868.45',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '171300000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '529962750.91',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '315263243.14',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '214299507.77',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '400000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323262',
                  level2Name: '徐州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '458018336.54',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '201789154.09',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '224599182.45',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '31630000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '439323396.93',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranAmt',
                      name: '公募',
                      value: '295651894.71',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranAmt',
                      name: '紫金',
                      value: '127371502.22',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'otcTranAmt',
                      name: 'OTC',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranAmt',
                      name: '私募',
                      value: '16300000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品当期销量',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                }
              ]
            },
            {
              indiModel: {
                key: 'avgMktVal',
                name: '产品日均市值',
                value: null,
                unit: '元',
                description: '统计周期期期间产品日均市值，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: 'Y',
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '15901795723.89150625',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '3770353986.9353125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '2730576273.1128125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '9196909862.73563125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '203955601.10775',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '13510138307.8019',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '5278455125.126',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '2410608727.7995625',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '5618458932.968025',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '202615521.9083125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '9239765078.330725',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '2350612115.3744375',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '2796465285.10075',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '3506465658.1836625',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '586222019.671875',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '7077840437.1500875',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '4955182281.5974375',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '1855608206.8146875',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '192639576.5039625',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '74410372.234',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '4882672775.704825',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '1390867480.753625',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '2227765994.34875',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '632187688.7535125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '631851611.8489375',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '4840737160.53890625',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '1842470048.2200625',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '2818480821.6020625',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '99560637.45959375',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '80225653.2571875',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '3641542149.96709375',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '1815536050.82625',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '1476675272.7315',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '95235194.78765625',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '254095631.6216875',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '3487332029.21991875',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '888570702.8841875',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '1592083811.4113125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '471978831.50573125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '534698683.4186875',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323261',
                  level2Name: '常州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '2840831787.3327625',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '548954947.5805',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '1164739293.124875',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '573753316.81395',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '553384229.8134375',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '2528504597.56256875',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'avgFundMktVal',
                      name: '公募',
                      value: '296761729.1725625',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgFinaMktVal',
                      name: '紫金',
                      value: '1972710444.9408125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgOtcMktVal',
                      name: 'OTC',
                      value: '222593265.77238125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'avgPriFundMktVal',
                      name: '私募',
                      value: '36439157.6768125',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '日均保有市值',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                }
              ]
            },
            {
              indiModel: {
                key: 'nowTtfMktVal',
                name: '天天发时点保有量',
                value: null,
                unit: '元',
                description: '统计时点天天发的保有市值',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323288',
                  level2Name: '江西分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041116',
                  level2Name: '辽宁分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323267',
                  level2Name: '安徽分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323265',
                  level2Name: '福建分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323268',
                  level2Name: '山东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            }
          ]
        },
        {
          key: 'newBusinessDetail',
          name: '开通业务明细',
          description: null,
          data: [
            {
              indiModel: {
                key: 'rzrqBusiCurr',
                name: '融资融券',
                value: null,
                unit: '户',
                description: '统计周期新开通融资融券客户数量',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '70',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '70',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '57',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '57',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '55',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '40',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323261',
                  level2Name: '常州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '38',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '30',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '28',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '25',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'cybBusiCurr',
                name: '创业板',
                value: null,
                unit: '户',
                description: '统计周期新开通创业板客户数量',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '997',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '854',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '725',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '541',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '538',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '525',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '443',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '441',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323262',
                  level2Name: '徐州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '436',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '414',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'ttfBusiCurr',
                name: '天天发',
                value: null,
                unit: '户',
                description: '统计周期新开通天天发客户数量',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '1013',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '950',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '898',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '892',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '624',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '613',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '508',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323262',
                  level2Name: '徐州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '498',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '484',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041116',
                  level2Name: '辽宁分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '471',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'hgtBusiCurr',
                name: '沪港通',
                value: null,
                unit: '户',
                description: '统计周期新开通沪港通客户数量',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '145',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '140',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '87',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '59',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '55',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '51',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '43',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '42',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '42',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '37',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'sgtBusiCurr',
                name: '深港通',
                value: null,
                unit: '户',
                description: '统计周期新开通深港通客户数量',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '187',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '162',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '113',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '68',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '67',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '60',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '56',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '46',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '46',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '43',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'gpqqBusiCurr',
                name: '股票期权',
                value: null,
                unit: '户',
                description: '统计周期新开通股票期权客户数量',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '45',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '22',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '17',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '16',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '15',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323261',
                  level2Name: '常州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '14',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '10',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '10',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '9',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323262',
                  level2Name: '徐州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '8',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            }
          ]
        },
        {
          key: 'pureIncomeDetail',
          name: '收入指标明细',
          description: null,
          data: [
            {
              indiModel: {
                key: 'tranPurRakeCopy',
                name: '净佣金收入',
                value: null,
                unit: '元',
                description: '交易净佣金收入=（股基+沪港通+深港通+股票期权+债劵+三板+其他）净佣金之和',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '22122070.3861',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '21843061.8237',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '16608249.642',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '14032356.555',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '13666755.9813',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '10638286.7605',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '10625724.9662',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '8645400.6074',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '8339895.5771',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323262',
                  level2Name: '徐州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '7477340.2076',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'totCrdtIntCopy',
                name: '净利息收入',
                value: null,
                unit: '元',
                description: '融资净利息+融券净利息+股票质押净利息+融资打新净利息+小额贷净利息+限制性股票融资净利息+股权激励行权融资净利息',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '16501650.8236',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041166',
                  level2Name: '深圳分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '15874735.127',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '12758037.9086',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '11741771.3605',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '11009338.967',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041120',
                  level2Name: '四川分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '6936635.7441',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '6685422.4286',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '5581991.3686',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '5518596.0674',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041118',
                  level2Name: '浙江分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '4265651.2422',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'totTranInt',
                name: '产品净手续费收入',
                value: null,
                unit: '元',
                description: '统计期内【公募+紫金（剔除天天发940018、940028、940038）+OTC+私募】产品（场外认购+场外申购+场内认购）产生手续费净收入',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: 'Y',
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '50469.73',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '469.73',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '50000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '25163.89',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '23663.89',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '1500',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041111',
                  level2Name: '湖北分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '12353.64',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '8603.64',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '3750',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041112',
                  level2Name: '广东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '8879.43',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '5729.43',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '3150',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323265',
                  level2Name: '福建分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '8758.22',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '8758.22',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323261',
                  level2Name: '常州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '7854.37',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '1854.37',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '6000',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '7851.26',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '6351.26',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '1500',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041118',
                  level2Name: '浙江分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '7693.29',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '943.29',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '6750',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '6389.09',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '6389.09',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041106',
                  level2Name: '南通分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '3411.5',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: [
                    {
                      key: 'kfTranInt',
                      name: '公募',
                      value: '3411.5',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'taTranInt',
                      name: '紫金',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    },
                    {
                      key: 'smTranInt',
                      name: '私募',
                      value: '0',
                      unit: '元',
                      description: null,
                      categoryKey: null,
                      isBelongsSummury: null,
                      hasChildren: null,
                      parentKey: null,
                      parentName: '产品净手续费收入',
                      children: null,
                      isAggressive: null,
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          key: 'hignCustServiceDetail',
          name: '服务指标明细',
          description: null,
          data: [
            {
              indiModel: {
                key: 'gjzServiceCompPercent',
                name: '高净值客户服务覆盖率',
                value: null,
                unit: '%',
                description: '已服务高净值客户数/高净值客户总数',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323288',
                  level2Name: '江西分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041116',
                  level2Name: '辽宁分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323267',
                  level2Name: '安徽分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323265',
                  level2Name: '福建分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323268',
                  level2Name: '山东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'gjzMotCompletePercent',
                name: '必做MOT任务完成率',
                value: null,
                unit: '%',
                description: '必做已完成任务数/必做MOT任务总数',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323267',
                  level2Name: '安徽分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.9855555555555555555555555555555555555556',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.9454805174192524402825172605348781842711',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041103',
                  level2Name: '苏州分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.9159185082872928176795580110497237569061',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.9041193181818181818181818181818181818182',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323288',
                  level2Name: '江西分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.8926116838487972508591065292096219931271',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.8834900731452455590386624869383490073145',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323273',
                  level2Name: '江阴分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.8801571709233791748526522593320235756385',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041107',
                  level2Name: '上海分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.8676242236024844720496894409937888198758',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323289',
                  level2Name: '西北分公司（筹）',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.8653712699514226231783483691880638445524',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323269',
                  level2Name: '淮安分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0.859356376638855780691299165673420738975',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                }
              ]
            },
            {
              indiModel: {
                key: 'gjzCustInfoCompletePercent',
                name: '高净值客户信息完备率',
                value: null,
                unit: '%',
                description: '信息完善率得分*50%+信息有效率得分*50%',
                categoryKey: null,
                isBelongsSummury: null,
                hasChildren: null,
                parentKey: null,
                parentName: null,
                children: null,
                isAggressive: null,
              },
              orgModel: [
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323263',
                  level2Name: '无锡分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '1',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323270',
                  level2Name: '盐城分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '2',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323288',
                  level2Name: '江西分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '3',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323264',
                  level2Name: '河南分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '4',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041116',
                  level2Name: '辽宁分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '5',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323267',
                  level2Name: '安徽分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '6',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041108',
                  level2Name: '北京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '7',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323265',
                  level2Name: '福建分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '8',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ001041093',
                  level2Name: '南京分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '9',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
                {
                  level1Id: 'ZZ001041',
                  level1Name: '经纪及财富管理部',
                  level2Id: 'ZZ323268',
                  level2Name: '山东分公司',
                  level3Id: null,
                  level3Name: null,
                  level4Id: null,
                  level4Name: null,
                  value: '0',
                  rank: '10',
                  rank_current: null,
                  rank_contrast: null,
                  rand_change: null,
                  changeRate: null,
                  indiModelList: null,
                },
              ],
            },
          ],
        },
      ],
    };
};
