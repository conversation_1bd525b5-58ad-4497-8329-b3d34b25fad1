/**
 * 指标库
 */

exports.response = function (req, res) {
  return {
    code: '0',
    msg: 'OK',
    resultData: {
      summury: [
        {
          indicatorCategoryDto: {
            categoryKey: 'custAmountDetail',
            categoryName: '客户指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'effCustNum',
              name: '有效客户数',
              value: null,
              unit: '户',
              description: '统计周期期末为有效的客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'InminorCustNum',
              name: '高净值客户数',
              value: null,
              unit: '户',
              description: '是否零售客户标志为非零售客户',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'newCustNum',
              name: '新增客户数',
              value: null,
              unit: '户',
              description: '统计周期内新增且成为有效户的客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'pNewCustNum',
                  name: '个人',
                  value: null,
                  unit: '户',
                  description: '在统计周期内开户，且客户性质为个人的客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'newCustNum',
                  parentName: '新开客户数',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oNewCustNum',
                  name: '一般机构',
                  value: null,
                  unit: '户',
                  description: '在统计周期内开户，且客户性质为一般机构的客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'newCustNum',
                  parentName: '新开客户数',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oNewPrdtCustNum',
                  name: '产品机构',
                  value: null,
                  unit: '户',
                  description: '在统计周期内开户，且客户性质为产品机构户的客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'newCustNum',
                  parentName: '新开客户数',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'totCustNum',
              name: '总客户数',
              value: null,
              unit: '户',
              description: '非销户的客户总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'pCustNum',
              name: '个人客户数',
              value: null,
              unit: '户',
              description: '客户性质为个人且状态为非销户客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'oCustNum',
              name: '机构客户数',
              value: null,
              unit: '户',
              description: '客户性质为机构客户且状态为非销户客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'oNoPrdtCustNum',
                  name: '一般',
                  value: null,
                  unit: '户',
                  description: '除产品户之外的机构客户',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'oCustNum',
                  parentName: '机构客户数',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'oPrdtCustNum',
                  name: '产品',
                  value: null,
                  unit: '户',
                  description: '客户性质为机构且机构类型为产品户，账户状态正常的客户。产品类型不为“基金公司特定客户资产管理产品（保险）”、“全国社保基金”、“地方社保基金”、“保险产品”、“保险资产管理产品”、“企业年金计划”、“养老金产品”、“银行理财产品”、上市公司员工持股计划”“其他”的客户，剔除资管客户',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'oCustNum',
                  parentName: '机构客户数',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'minorCustNum',
              name: '零售客户数',
              value: null,
              unit: '户',
              description: '是否零售客户标志为零售客户',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'asetDetail',
            categoryName: '资产指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'pCustAset',
              name: '个人客户总资产',
              value: null,
              unit: '元',
              description: '客户性质为个人客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'oNoPrdtCustAset',
              name: '一般机构户总资产',
              value: null,
              unit: '元',
              description: '客户性质为一般机构客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'oPrdtCustAset',
              name: '产品机构户总资产',
              value: null,
              unit: '元',
              description: '客户性质为产品客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzCustAset',
              name: '高净值客户总资产',
              value: null,
              unit: '元',
              description: '客户性质为高净值客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'mktZq',
              name: '债券市值',
              value: null,
              unit: '元',
              description: '类别为“债券”的所有证券总市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'fundMktVal',
              name: '公募基金市值',
              value: null,
              unit: '元',
              description: '类别为“开放式基金”的所有证券总市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'finaMktVal',
              name: '紫金产品市值（剔除天天发）',
              value: null,
              unit: '元',
              description: '紫金产品市值，不含天天发',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'priFundMktVal',
              name: '证券投资类私募市值',
              value: null,
              unit: '元',
              description: '所有私募基金市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'otcMktVal',
              name: 'OTC产品市值',
              value: null,
              unit: '元',
              description: 'OTC产品市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'optMktVal',
              name: '期权市值',
              value: null,
              unit: '元',
              description: '个股期权的权益，计算公式：市价*数量*合约单位',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'avgBal',
              name: '日均保证金余额',
              value: null,
              unit: '元',
              description: '统计期间客户普通账户、信用账户、期权账户中“当前余额”的日均数据',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'purFinAmt',
              name: '净存取',
              value: null,
              unit: '元',
              description: '转入资金和转出资金的差值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'new_cust_aset',
              name: '新开客户总资产',
              value: null,
              unit: '元',
              description: '在统计周期内开户的所有客户（含销户客户）的期末总资产',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'allTotAsetDis',
              name: '资产明细',
              value: null,
              unit: '元',
              description: '限售市值+流通股市值+产品市值+负债+现金资产+天天发市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'mktLmt',
                  name: '限售股',
                  value: null,
                  unit: '元',
                  description: '股票性质为受限股的所有股票市值，B股转换为人民币计算',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'ttfMktVal',
                  name: '天天发',
                  value: null,
                  unit: '元',
                  description: '天天发产品市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'debVal',
                  name: '负债',
                  value: null,
                  unit: '元',
                  description: '所有负债类资产总额，包含：小额贷负债、融资打新负债、股票质押式融资负债、融资融券负债、正回购负债',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'bal',
                  name: '现金',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户以及期权账户中“当前余额”总和',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'mktVal',
                  name: '产品',
                  value: null,
                  unit: '元',
                  description: '产品类资产总市值，包括：公募基金、紫金产品（不含天天发）、证券投资类私募、OTC',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'cirMktValCopy',
                  name: '流通股',
                  value: null,
                  unit: '元',
                  description: '流通股资产之和，包含：A股市值(主板+中小板+创业板)、B股市值、封闭式基金市值、沪港通、深港通、传统三板市值、新三板市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'allTotAset',
              name: '总资产',
              value: null,
              unit: '元',
              description: '所有客户普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'shNpRate',
              name: '沪市归集率',
              value: null,
              unit: '%',
              description: '客户在沪市归属华泰的资产总额/所有资产总额',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'szNpRate',
              name: '深市归集率',
              value: null,
              unit: '%',
              description: '客户在深市归属华泰的资产总额/所有资产总额',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'shzNpRate',
              name: '沪深归集率',
              value: null,
              unit: '%',
              description: '客户在沪深市场归属华泰的资产总额/所有资产总额',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'transAmountDetail',
            categoryName: '交易指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'zhTranAmt',
              name: '综合交易量',
              value: null,
              unit: '元',
              description: '统计期内各类交易的总和，包括：基础股基交易、场内货币市场基金交易、债券ETF交易',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'kfHbTranAmt',
                  name: '场内货币市场基金',
                  value: null,
                  unit: '元',
                  description: '统计期内普通账户、信用账户沪深交易所场内交易型开放式货币基金的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'zhTranAmt',
                  parentName: '综合交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'kfZqTranAmt',
                  name: '债券ETF',
                  value: null,
                  unit: '元',
                  description: '统计期内普通账户、信用账户沪深交易所债券ETF的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'zhTranAmt',
                  parentName: '综合交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'tradTranAmtCopy',
                  name: '基础股基',
                  value: null,
                  unit: '元',
                  description: '统计期内股基交易量总和，包括范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'zhTranAmt',
                  parentName: '综合交易量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'tradTranAmt',
              name: '基础股基交易量',
              value: null,
              unit: '元',
              description: '统计期内股基交易量总和，包括范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'ptGjTranAmt',
                  name: '普通',
                  value: null,
                  unit: '元',
                  description: '统计期内普通账户产生的股基交易量。股基范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tradTranAmt',
                  parentName: '基础股基交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'dbGjTranAmt',
                  name: '担保',
                  value: null,
                  unit: '元',
                  description: '统计期内信用账户产生的担保股基交易量。股基范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tradTranAmt',
                  parentName: '基础股基交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'xyGjTranAmt',
                  name: '信用',
                  value: null,
                  unit: '元',
                  description: '统计期内信用账户产生的信用股基交易量。股基范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tradTranAmt',
                  parentName: '基础股基交易量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'hskhTranAmt',
              name: '港股通交易量',
              value: null,
              unit: '元',
              description: '普通账户沪港通与深港通交易量总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'hTranAmt',
                  name: '沪港通',
                  value: null,
                  unit: '元',
                  description: '普通账户沪港通的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'hskhTranAmt',
                  parentName: '港股通交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'skhTranAmt',
                  name: '深港通',
                  value: null,
                  unit: '元',
                  description: '普通账户深港通的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'hskhTranAmt',
                  parentName: '港股通交易量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'threeTranAmt',
              name: '三板交易量',
              value: null,
              unit: '元',
              description: '普通账户新三板与老三板交易量总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'newThreeTranAmt',
                  name: '新三板',
                  value: null,
                  unit: '元',
                  description: '普通账户新三板交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'threeTranAmt',
                  parentName: '三板交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oldThreeTranAmt',
                  name: '老三板',
                  value: null,
                  unit: '元',
                  description: '普通账户老三板交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'threeTranAmt',
                  parentName: '三板交易量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'tranAmtOpt',
              name: '股票期权交易量',
              value: null,
              unit: '元',
              description: '期权账户交易量',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'productDetail',
            categoryName: '产品销售指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'curPrdtTranAmt',
              name: '产品当期销量',
              value: null,
              unit: '元',
              description: '统计周期内产品销售金额之和，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'kfTranAmt',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计期内开放式基金的销售金额，包括：场外（认购+申购）、场内认购',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'taTranAmt',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计周期内【场外（认购+申购）+场内认购】紫金产品销售金额，不含天天发（940018、940028、940038）',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'otcTranAmt',
                  name: 'OTC',
                  value: null,
                  unit: '元',
                  description: '统计周期内OTC 产品销售金额，包括场外（认购+申购）、场内认购',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'smTranAmt',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计周期内【场外（认购+申购）+场内认购】非公募产品（总部或分支机构引并销售的私募产品、基金专户产品）销售金额',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'nowMktVal',
              name: '时点保有市值',
              value: null,
              unit: '元',
              description: '统计周期期末产品时点市值，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'nowFundMktVal',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计周期期末公募基金的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'nowFinaMktVal',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计周期期末紫金产品剔除天天发的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'nowOtcMktVal',
                  name: 'OTC',
                  value: null,
                  unit: '元',
                  description: '统计周期期末OTC产品的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'nowPriFundMktVal',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计周期期末证券投资类私募产品的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'avgMktVal',
              name: '产品日均市值',
              value: null,
              unit: '元',
              description: '统计周期期期间产品日均市值，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'avgFundMktVal',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计周期期间公募基金的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
                {
                  key: 'avgFinaMktVal',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计周期期间紫金产品剔除天天发的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
                {
                  key: 'avgOtcMktVal',
                  name: 'OTC',
                  value: null,
                  unit: '元',
                  description: '统计周期期间OTC产品的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
                {
                  key: 'avgPriFundMktVal',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计周期期间证券投资类私募产品的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                }
              ],
              isAggressive: '4',
            },
            {
              key: 'nowTtfMktVal',
              name: '天天发时点保有量',
              value: null,
              unit: '元',
              description: '统计时点天天发的保有市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'avgTtfMktVal',
              name: '天天发日均保有量',
              value: null,
              unit: '元',
              description: '统计周期期间天天发的日均保有市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '4',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'newBusinessDetail',
            categoryName: '开通业务明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'ljOpenBusi',
              name: '累计开通业务',
              value: null,
              unit: '户',
              description: '目前开通以下业务的客户数量（不去重），业务包含：融资融券、天天发、创业板、财富通登录、股票期权、OTC、沪港通、深港通、股票质押、新三板',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'rzrqBusi',
                  name: '融资融券',
                  value: null,
                  unit: '户',
                  description: '目前开通融资融券客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'ttfBusi',
                  name: '天天发',
                  value: null,
                  unit: '户',
                  description: '目前开通天天发客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'gpqqBusi',
                  name: '股票期权',
                  value: null,
                  unit: '户',
                  description: '目前开通股票期权客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'hgtBusi',
                  name: '沪港通',
                  value: null,
                  unit: '户',
                  description: '目前开通沪港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'sgtBusi',
                  name: '深港通',
                  value: null,
                  unit: '户',
                  description: '目前开通深港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'cybBusi',
                  name: '创业板',
                  value: null,
                  unit: '户',
                  description: '目前开通创业板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'zlcftBusi',
                  name: '财富通登录',
                  value: null,
                  unit: '户',
                  description: '截止当前累计登录财富通的客户数（1次即算）',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'otcBusi',
                  name: 'OTC',
                  value: null,
                  unit: '户',
                  description: '目前开通OTC客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'gpzyBusi',
                  name: '股票质押',
                  value: null,
                  unit: '户',
                  description: '目前开通股票质押客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'xsbBusi',
                  name: '新三板',
                  value: null,
                  unit: '户',
                  description: '目前开通新三板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'dqOpenBusi',
              name: '当期开通业务',
              value: null,
              unit: '户',
              description: '统计周期内所有新开业务的客户数（不去重），新开业务包含：融资融券、天天发、创业板、财富通登录、股票期权、OTC、沪港通、深港通、股票质押、新三板',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'rzrqBusiCurr',
                  name: '融资融券',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通融资融券客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'gpzyBusiCurr',
                  name: '股票质押',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通股票质押客户数量 ',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'hgtBusiCurr',
                  name: '沪港通',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通沪港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'gpqqBusiCurr',
                  name: '股票期权',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通股票期权客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'ttfBusiCurr',
                  name: '天天发',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通天天发客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'otcBusiCurr',
                  name: 'OTC',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通OTC客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'cybBusiCurr',
                  name: '创业板',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通创业板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'xsbBusiCurr',
                  name: '新三板',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通新三板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'zlcftBusiCurr',
                  name: '财富通登录',
                  value: null,
                  unit: '户',
                  description: '统计周期登录财富通的客户数（1次即算）',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'sgtBusiCurr',
                  name: '深港通',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通深港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'pureIncomeDetail',
            categoryName: '收入指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'tranPurRake',
              name: '净佣金收入明细',
              value: null,
              unit: '元',
              description: '交易净佣金收入=（股基+沪港通+深港通+股票期权+债劵+三板+其他）净佣金之和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'gjPurRake',
                  name: '股基',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户沪深交易所证券的净佣金，不包含债券ETF、三板、债券、沪港通净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'shhkPurRake',
                  name: '沪港通',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户沪港通的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'szhkPurRake',
                  name: '深港通',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户深港通的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'zqPurRake',
                  name: '债券',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户沪深交易所债券的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'sbPurRake',
                  name: '三板',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户三板的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'optPurRake',
                  name: '股票期权',
                  value: null,
                  unit: '元',
                  description: '期权账户净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'otherPurRake',
                  name: '其它',
                  value: null,
                  unit: '元',
                  description: '除股基净佣金、沪港通净佣金、深港通净佣金、股票期权净佣金、债劵净佣金、三板净佣金以外的其他净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'allIncomeAmt',
              name: '净收入',
              value: null,
              unit: '元',
              description: '统计期内整体客户金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'pIncomeAmt',
                  name: '个人',
                  value: null,
                  unit: '元',
                  description: '统计期内客户性质为个人的，金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allIncomeAmt',
                  parentName: '净收入',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oIncomeAmt',
                  name: '机构',
                  value: null,
                  unit: '元',
                  description: '统计期内客户性质为机构的，金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allIncomeAmt',
                  parentName: '净收入',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'gjzIncomeAmt',
              name: '高净值客户净收入',
              value: null,
              unit: '元',
              description: '统计期内客户标识为非零售的，金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'totTranInt',
              name: '产品净手续费收入',
              value: null,
              unit: '元',
              description: '统计期内【公募+紫金（剔除天天发940018、940028、940038）+OTC+私募】产品（场外认购+场外申购+场内认购）产生手续费净收入',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'kfTranInt',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计期内公募产品（场外认购+场外申购+场内认购）产生手续费净收入',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totTranInt',
                  parentName: '产品净手续费收入',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'taTranInt',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计期内紫金产品（场外认购+场外申购+场内认购）产生手续费净收入',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totTranInt',
                  parentName: '产品净手续费收入',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'smTranInt',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计期内证券投资类私募产品（场外认购+场外申购+场内认购）产生手续费净收入',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totTranInt',
                  parentName: '产品净手续费收入',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'totCrdtInt',
              name: '净利息收入明细',
              value: null,
              unit: '元',
              description: '融资净利息+融券净利息+股票质押净利息+融资打新净利息+小额贷净利息+限制性股票融资净利息+股权激励行权融资净利息',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'fina_int',
                  name: '融资',
                  value: null,
                  unit: '元',
                  description: '每日客户应计融资利息-每日融资余额*当日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'stc_int',
                  name: '融券',
                  value: null,
                  unit: '元',
                  description: '融券收入取自流动性平台',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'srp_int',
                  name: '股票质押融资',
                  value: null,
                  unit: '元',
                  description: '股票质押融资合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'xed_int',
                  name: '小额贷',
                  value: null,
                  unit: '元',
                  description: '小额贷合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'rsfdebit_int',
                  name: '限制性股票融资',
                  value: null,
                  unit: '元',
                  description: '限制性股票融资合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'finrzsopt_int',
                  name: '股权激励行权融资',
                  value: null,
                  unit: '元',
                  description: '股权激励行权融资合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'totCrdtIntCopy',
              name: '净利息收入',
              value: null,
              unit: '元',
              description: '融资净利息+融券净利息+股票质押净利息+融资打新净利息+小额贷净利息+限制性股票融资净利息+股权激励行权融资净利息',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'tranPurRakeCopy',
              name: '净佣金收入',
              value: null,
              unit: '元',
              description: '交易净佣金收入=（股基+沪港通+深港通+股票期权+债劵+三板+其他）净佣金之和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'hignCustServiceDetail',
            categoryName: '服务指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'gjzCustNum',
              name: '高净值客户总数',
              value: null,
              unit: '户',
              description: '标识为非零售，账户状态非销户的高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'gjzCustNumSingle',
                  name: '个人',
                  value: null,
                  unit: '户',
                  description: '客户性质为个人且标识为非零售，账户状态非销户的高净值客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzCustNum',
                  parentName: '高净值客户总数',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'gjzCustNumOrgan',
                  name: '机构',
                  value: null,
                  unit: '户',
                  description: '客户性质为机构且标识为非零售，账户状态非销户的高净值客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzCustNum',
                  parentName: '高净值客户总数',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'gjzConfigRiskNum',
              name: '配置2种风险属性标的高净值客户数',
              value: null,
              unit: '户',
              description: '统计期内同时持有两种风险等级以上投资标的的高净值客户数，其中股票、期权为高风险标的，剔除天天发',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzFeeConfigPercent',
              name: '资产配置高净值覆盖率',
              value: null,
              unit: '%',
              description: '配置2种风险属性标的高净值客户数/高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzJxzNum',
              name: '高净值客户净新增数',
              value: null,
              unit: '户',
              description: '本考核期新增的高净值客户数-上一考核期末的高净值客户在本考核期内降级为零售客户且资产降幅超过同期市场指数跌幅的客户数量',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzJxzRate',
              name: '高净值客户净增长率',
              value: null,
              unit: '%',
              description: '(本考核期新增的高净值客户数-上一考核期末的高净值客户在本考核期内降级为零售客户且资产降幅超过同期市场指数跌幅的客户数量）/上一考核期末高净值客户数*100% ',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzServCompNum',
              name: '已服务高净值客户数',
              value: null,
              unit: '户',
              description: '统计期内“服务沟通记录中的类型为“客户信息核实+服务营销+理财建议+通知提醒+异动确认+温馨关怀+销户跟踪+投诉处理+销售活动”，服务经理执行且完成“打勾”的记录数+ MOT活动中服务经理执行的且状态已经为“完成”的记录数+直销渠道营销活动响应状态为“完成”的记录数，同一周期客户去重统计',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'gjzServCompNumSingle',
                  name: '个人',
                  value: null,
                  unit: '户',
                  description: '统计期内“服务沟通记录中的类型为“客户信息核实+服务营销+理财建议+通知提醒+异动确认+温馨关怀+销户跟踪+投诉处理+销售活动”，服务经理执行且完成“打勾”的记录数+ MOT活动中服务经理执行的且状态已经为“完成”的记录数+直销渠道营销活动响应状态为“完成”的个人客户记录数，同一周期客户去重统计',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzServCompNum',
                  parentName: '已服务高净值客户数',
                  children: null,
                  isAggressive: '3',
                },
                {
                  key: 'gjzServCompNumOrgan',
                  name: '机构',
                  value: null,
                  unit: '户',
                  description: '统计期内“服务沟通记录中的类型为“客户信息核实+服务营销+理财建议+通知提醒+异动确认+温馨关怀+销户跟踪+投诉处理+销售活动”，服务经理执行且完成“打勾”的记录数+ MOT活动中服务经理执行的且状态已经为“完成”的记录数+直销渠道营销活动响应状态为“完成”的机构客户记录数，同一周期客户去重统计',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzServCompNum',
                  parentName: '已服务高净值客户数',
                  children: null,
                  isAggressive: '3',
                }
              ],
              isAggressive: '3',
            },
            {
              key: 'gjzServiceCompPercent',
              name: '高净值客户服务覆盖率',
              value: null,
              unit: '%',
              description: '已服务高净值客户数/高净值客户总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzServiceCompPercentSingle',
              name: '高净值个人客户服务覆盖率',
              value: null,
              unit: '%',
              description: '已服务高净值个人客户数/高净值个人客户总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzServiceCompPercentOrgan',
              name: '高净值机构客户服务覆盖率',
              value: null,
              unit: '%',
              description: '已服务高净值机构客户数/高净值机构客户总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzMotCompletePercent',
              name: '必做MOT任务完成率',
              value: null,
              unit: '%',
              description: '必做已完成任务数/必做MOT任务总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'gjzMotTotNum',
              name: '必做MOT任务总数',
              value: null,
              unit: '个',
              description: '统计期内高净值客户的必做MOT任务数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'gjzMotCompleteNum',
              name: '必做已完成任务数',
              value: null,
              unit: '个',
              description: '统计期内高净值客户的必做MOT任务数完成任务数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'gjzCustInfoCompletePercent',
              name: '高净值客户信息完备率',
              value: null,
              unit: '%',
              description: '信息完善率得分*50%+信息有效率得分*50%',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzInfoCompPercent',
              name: '高净值客户信息完善率',
              value: null,
              unit: '%',
              description: '存量高净值客户信息完善率得分*70%+新开高净值客户信息完善率得分*30%。',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzStockInfoCompPercent',
              name: '存量高净值客户信息完善率',
              value: null,
              unit: '%',
              description: '统计时点当年前开户的标记为“非零售\'客户，手机维护率（30%）+地址维护率（20%）+风险偏好维护率（30%）+电子邮件维护率（20%）',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzNewInfoCompPercent',
              name: '新开高净值客户信息完善率',
              value: null,
              unit: '%',
              description: '统计时点当年新开户的标记为“非零售\'客户，手机维护率（30%）+地址维护率（20%）+风险偏好维护率（30%）+电子邮件维护率（20%）',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzRiskPreferEffRate',
              name: '高净值客户信息有效率',
              value: null,
              unit: '%',
              description: '风险偏好有效的高净值客户数/维护了风险偏好的高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzConfigReportNum',
              name: '资产配置报告高净值客户完成数',
              value: null,
              unit: '户',
              description: '统计期内完成资产配置报告的高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            }
          ]
        }
      ],
      detail: [
        {
          indicatorCategoryDto: {
            categoryKey: 'custAmountDetail',
            categoryName: '客户指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'effCustNum',
              name: '有效客户数',
              value: null,
              unit: '户',
              description: '统计周期期末为有效的客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'InminorCustNum',
              name: '高净值客户数',
              value: null,
              unit: '户',
              description: '是否零售客户标志为非零售客户',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'newCustNum',
              name: '新增客户数',
              value: null,
              unit: '户',
              description: '统计周期内新增且成为有效户的客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'pNewCustNum',
                  name: '个人',
                  value: null,
                  unit: '户',
                  description: '在统计周期内开户，且客户性质为个人的客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'newCustNum',
                  parentName: '新开客户数',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oNewCustNum',
                  name: '一般机构',
                  value: null,
                  unit: '户',
                  description: '在统计周期内开户，且客户性质为一般机构的客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'newCustNum',
                  parentName: '新开客户数',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oNewPrdtCustNum',
                  name: '产品机构',
                  value: null,
                  unit: '户',
                  description: '在统计周期内开户，且客户性质为产品机构户的客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'newCustNum',
                  parentName: '新开客户数',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'totCustNum',
              name: '总客户数',
              value: null,
              unit: '户',
              description: '非销户的客户总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'pCustNum',
              name: '个人客户数',
              value: null,
              unit: '户',
              description: '客户性质为个人且状态为非销户客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'oCustNum',
              name: '机构客户数',
              value: null,
              unit: '户',
              description: '客户性质为机构客户且状态为非销户客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'oNoPrdtCustNum',
                  name: '一般',
                  value: null,
                  unit: '户',
                  description: '除产品户之外的机构客户',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'oCustNum',
                  parentName: '机构客户数',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'oPrdtCustNum',
                  name: '产品',
                  value: null,
                  unit: '户',
                  description: '客户性质为机构且机构类型为产品户，账户状态正常的客户。产品类型不为“基金公司特定客户资产管理产品（保险）”、“全国社保基金”、“地方社保基金”、“保险产品”、“保险资产管理产品”、“企业年金计划”、“养老金产品”、“银行理财产品”、上市公司员工持股计划”“其他”的客户，剔除资管客户',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'oCustNum',
                  parentName: '机构客户数',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'minorCustNum',
              name: '零售客户数',
              value: null,
              unit: '户',
              description: '是否零售客户标志为零售客户',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'asetDetail',
            categoryName: '资产指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'pCustAset',
              name: '个人客户总资产',
              value: null,
              unit: '元',
              description: '客户性质为个人客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'oNoPrdtCustAset',
              name: '一般机构户总资产',
              value: null,
              unit: '元',
              description: '客户性质为一般机构客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'oPrdtCustAset',
              name: '产品机构户总资产',
              value: null,
              unit: '元',
              description: '客户性质为产品客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzCustAset',
              name: '高净值客户总资产',
              value: null,
              unit: '元',
              description: '客户性质为高净值客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'mktZq',
              name: '债券市值',
              value: null,
              unit: '元',
              description: '类别为“债券”的所有证券总市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'fundMktVal',
              name: '公募基金市值',
              value: null,
              unit: '元',
              description: '类别为“开放式基金”的所有证券总市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'finaMktVal',
              name: '紫金产品市值（剔除天天发）',
              value: null,
              unit: '元',
              description: '紫金产品市值，不含天天发',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'priFundMktVal',
              name: '证券投资类私募市值',
              value: null,
              unit: '元',
              description: '所有私募基金市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'otcMktVal',
              name: 'OTC产品市值',
              value: null,
              unit: '元',
              description: 'OTC产品市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'optMktVal',
              name: '期权市值',
              value: null,
              unit: '元',
              description: '个股期权的权益，计算公式：市价*数量*合约单位',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'avgBal',
              name: '日均保证金余额',
              value: null,
              unit: '元',
              description: '统计期间客户普通账户、信用账户、期权账户中“当前余额”的日均数据',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'purFinAmt',
              name: '净存取',
              value: null,
              unit: '元',
              description: '转入资金和转出资金的差值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'new_cust_aset',
              name: '新开客户总资产',
              value: null,
              unit: '元',
              description: '在统计周期内开户的所有客户（含销户客户）的期末总资产',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'allTotAsetDis',
              name: '资产明细',
              value: null,
              unit: '元',
              description: '限售市值+流通股市值+产品市值+负债+现金资产+天天发市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'mktLmt',
                  name: '限售股',
                  value: null,
                  unit: '元',
                  description: '股票性质为受限股的所有股票市值，B股转换为人民币计算',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'ttfMktVal',
                  name: '天天发',
                  value: null,
                  unit: '元',
                  description: '天天发产品市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'debVal',
                  name: '负债',
                  value: null,
                  unit: '元',
                  description: '所有负债类资产总额，包含：小额贷负债、融资打新负债、股票质押式融资负债、融资融券负债、正回购负债',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'bal',
                  name: '现金',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户以及期权账户中“当前余额”总和',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'mktVal',
                  name: '产品',
                  value: null,
                  unit: '元',
                  description: '产品类资产总市值，包括：公募基金、紫金产品（不含天天发）、证券投资类私募、OTC',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'cirMktValCopy',
                  name: '流通股',
                  value: null,
                  unit: '元',
                  description: '流通股资产之和，包含：A股市值(主板+中小板+创业板)、B股市值、封闭式基金市值、沪港通、深港通、传统三板市值、新三板市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'allTotAset',
              name: '总资产',
              value: null,
              unit: '元',
              description: '所有客户普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'shNpRate',
              name: '沪市归集率',
              value: null,
              unit: '%',
              description: '客户在沪市归属华泰的资产总额/所有资产总额',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'szNpRate',
              name: '深市归集率',
              value: null,
              unit: '%',
              description: '客户在深市归属华泰的资产总额/所有资产总额',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'shzNpRate',
              name: '沪深归集率',
              value: null,
              unit: '%',
              description: '客户在沪深市场归属华泰的资产总额/所有资产总额',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'transAmountDetail',
            categoryName: '交易指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'zhTranAmt',
              name: '综合交易量',
              value: null,
              unit: '元',
              description: '统计期内各类交易的总和，包括：基础股基交易、场内货币市场基金交易、债券ETF交易',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'kfHbTranAmt',
                  name: '场内货币市场基金',
                  value: null,
                  unit: '元',
                  description: '统计期内普通账户、信用账户沪深交易所场内交易型开放式货币基金的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'zhTranAmt',
                  parentName: '综合交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'kfZqTranAmt',
                  name: '债券ETF',
                  value: null,
                  unit: '元',
                  description: '统计期内普通账户、信用账户沪深交易所债券ETF的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'zhTranAmt',
                  parentName: '综合交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'tradTranAmtCopy',
                  name: '基础股基',
                  value: null,
                  unit: '元',
                  description: '统计期内股基交易量总和，包括范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'zhTranAmt',
                  parentName: '综合交易量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'tradTranAmt',
              name: '基础股基交易量',
              value: null,
              unit: '元',
              description: '统计期内股基交易量总和，包括范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'ptGjTranAmt',
                  name: '普通',
                  value: null,
                  unit: '元',
                  description: '统计期内普通账户产生的股基交易量。股基范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tradTranAmt',
                  parentName: '基础股基交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'dbGjTranAmt',
                  name: '担保',
                  value: null,
                  unit: '元',
                  description: '统计期内信用账户产生的担保股基交易量。股基范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tradTranAmt',
                  parentName: '基础股基交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'xyGjTranAmt',
                  name: '信用',
                  value: null,
                  unit: '元',
                  description: '统计期内信用账户产生的信用股基交易量。股基范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tradTranAmt',
                  parentName: '基础股基交易量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'hskhTranAmt',
              name: '港股通交易量',
              value: null,
              unit: '元',
              description: '普通账户沪港通与深港通交易量总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'hTranAmt',
                  name: '沪港通',
                  value: null,
                  unit: '元',
                  description: '普通账户沪港通的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'hskhTranAmt',
                  parentName: '港股通交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'skhTranAmt',
                  name: '深港通',
                  value: null,
                  unit: '元',
                  description: '普通账户深港通的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'hskhTranAmt',
                  parentName: '港股通交易量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'threeTranAmt',
              name: '三板交易量',
              value: null,
              unit: '元',
              description: '普通账户新三板与老三板交易量总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'newThreeTranAmt',
                  name: '新三板',
                  value: null,
                  unit: '元',
                  description: '普通账户新三板交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'threeTranAmt',
                  parentName: '三板交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oldThreeTranAmt',
                  name: '老三板',
                  value: null,
                  unit: '元',
                  description: '普通账户老三板交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'threeTranAmt',
                  parentName: '三板交易量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'tranAmtOpt',
              name: '股票期权交易量',
              value: null,
              unit: '元',
              description: '期权账户交易量',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'productDetail',
            categoryName: '产品销售指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'curPrdtTranAmt',
              name: '产品当期销量',
              value: null,
              unit: '元',
              description: '统计周期内产品销售金额之和，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'kfTranAmt',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计期内开放式基金的销售金额，包括：场外（认购+申购）、场内认购',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'taTranAmt',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计周期内【场外（认购+申购）+场内认购】紫金产品销售金额，不含天天发（940018、940028、940038）',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'otcTranAmt',
                  name: 'OTC',
                  value: null,
                  unit: '元',
                  description: '统计周期内OTC 产品销售金额，包括场外（认购+申购）、场内认购',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'smTranAmt',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计周期内【场外（认购+申购）+场内认购】非公募产品（总部或分支机构引并销售的私募产品、基金专户产品）销售金额',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'nowMktVal',
              name: '时点保有市值',
              value: null,
              unit: '元',
              description: '统计周期期末产品时点市值，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'nowFundMktVal',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计周期期末公募基金的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'nowFinaMktVal',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计周期期末紫金产品剔除天天发的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'nowOtcMktVal',
                  name: 'OTC',
                  value: null,
                  unit: '元',
                  description: '统计周期期末OTC产品的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'nowPriFundMktVal',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计周期期末证券投资类私募产品的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'avgMktVal',
              name: '产品日均市值',
              value: null,
              unit: '元',
              description: '统计周期期期间产品日均市值，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'avgFundMktVal',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计周期期间公募基金的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
                {
                  key: 'avgFinaMktVal',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计周期期间紫金产品剔除天天发的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
                {
                  key: 'avgOtcMktVal',
                  name: 'OTC',
                  value: null,
                  unit: '元',
                  description: '统计周期期间OTC产品的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
                {
                  key: 'avgPriFundMktVal',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计周期期间证券投资类私募产品的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                }
              ],
              isAggressive: '4',
            },
            {
              key: 'nowTtfMktVal',
              name: '天天发时点保有量',
              value: null,
              unit: '元',
              description: '统计时点天天发的保有市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'avgTtfMktVal',
              name: '天天发日均保有量',
              value: null,
              unit: '元',
              description: '统计周期期间天天发的日均保有市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '4',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'newBusinessDetail',
            categoryName: '开通业务明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'ljOpenBusi',
              name: '累计开通业务',
              value: null,
              unit: '户',
              description: '目前开通以下业务的客户数量（不去重），业务包含：融资融券、天天发、创业板、财富通登录、股票期权、OTC、沪港通、深港通、股票质押、新三板',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'rzrqBusi',
                  name: '融资融券',
                  value: null,
                  unit: '户',
                  description: '目前开通融资融券客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'ttfBusi',
                  name: '天天发',
                  value: null,
                  unit: '户',
                  description: '目前开通天天发客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'gpqqBusi',
                  name: '股票期权',
                  value: null,
                  unit: '户',
                  description: '目前开通股票期权客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'hgtBusi',
                  name: '沪港通',
                  value: null,
                  unit: '户',
                  description: '目前开通沪港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'sgtBusi',
                  name: '深港通',
                  value: null,
                  unit: '户',
                  description: '目前开通深港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'cybBusi',
                  name: '创业板',
                  value: null,
                  unit: '户',
                  description: '目前开通创业板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'zlcftBusi',
                  name: '财富通登录',
                  value: null,
                  unit: '户',
                  description: '截止当前累计登录财富通的客户数（1次即算）',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'otcBusi',
                  name: 'OTC',
                  value: null,
                  unit: '户',
                  description: '目前开通OTC客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'gpzyBusi',
                  name: '股票质押',
                  value: null,
                  unit: '户',
                  description: '目前开通股票质押客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'xsbBusi',
                  name: '新三板',
                  value: null,
                  unit: '户',
                  description: '目前开通新三板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'dqOpenBusi',
              name: '当期开通业务',
              value: null,
              unit: '户',
              description: '统计周期内所有新开业务的客户数（不去重），新开业务包含：融资融券、天天发、创业板、财富通登录、股票期权、OTC、沪港通、深港通、股票质押、新三板',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'rzrqBusiCurr',
                  name: '融资融券',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通融资融券客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'gpzyBusiCurr',
                  name: '股票质押',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通股票质押客户数量 ',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'hgtBusiCurr',
                  name: '沪港通',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通沪港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'gpqqBusiCurr',
                  name: '股票期权',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通股票期权客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'ttfBusiCurr',
                  name: '天天发',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通天天发客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'otcBusiCurr',
                  name: 'OTC',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通OTC客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'cybBusiCurr',
                  name: '创业板',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通创业板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'xsbBusiCurr',
                  name: '新三板',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通新三板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'zlcftBusiCurr',
                  name: '财富通登录',
                  value: null,
                  unit: '户',
                  description: '统计周期登录财富通的客户数（1次即算）',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'sgtBusiCurr',
                  name: '深港通',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通深港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'pureIncomeDetail',
            categoryName: '收入指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'tranPurRake',
              name: '净佣金收入明细',
              value: null,
              unit: '元',
              description: '交易净佣金收入=（股基+沪港通+深港通+股票期权+债劵+三板+其他）净佣金之和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'gjPurRake',
                  name: '股基',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户沪深交易所证券的净佣金，不包含债券ETF、三板、债券、沪港通净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'shhkPurRake',
                  name: '沪港通',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户沪港通的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'szhkPurRake',
                  name: '深港通',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户深港通的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'zqPurRake',
                  name: '债券',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户沪深交易所债券的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'sbPurRake',
                  name: '三板',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户三板的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'optPurRake',
                  name: '股票期权',
                  value: null,
                  unit: '元',
                  description: '期权账户净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'otherPurRake',
                  name: '其它',
                  value: null,
                  unit: '元',
                  description: '除股基净佣金、沪港通净佣金、深港通净佣金、股票期权净佣金、债劵净佣金、三板净佣金以外的其他净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'allIncomeAmt',
              name: '净收入',
              value: null,
              unit: '元',
              description: '统计期内整体客户金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'pIncomeAmt',
                  name: '个人',
                  value: null,
                  unit: '元',
                  description: '统计期内客户性质为个人的，金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allIncomeAmt',
                  parentName: '净收入',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oIncomeAmt',
                  name: '机构',
                  value: null,
                  unit: '元',
                  description: '统计期内客户性质为机构的，金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allIncomeAmt',
                  parentName: '净收入',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'gjzIncomeAmt',
              name: '高净值客户净收入',
              value: null,
              unit: '元',
              description: '统计期内客户标识为非零售的，金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'totTranInt',
              name: '产品净手续费收入',
              value: null,
              unit: '元',
              description: '统计期内【公募+紫金（剔除天天发940018、940028、940038）+OTC+私募】产品（场外认购+场外申购+场内认购）产生手续费净收入',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'kfTranInt',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计期内公募产品（场外认购+场外申购+场内认购）产生手续费净收入',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totTranInt',
                  parentName: '产品净手续费收入',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'taTranInt',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计期内紫金产品（场外认购+场外申购+场内认购）产生手续费净收入',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totTranInt',
                  parentName: '产品净手续费收入',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'smTranInt',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计期内证券投资类私募产品（场外认购+场外申购+场内认购）产生手续费净收入',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totTranInt',
                  parentName: '产品净手续费收入',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'totCrdtInt',
              name: '净利息收入明细',
              value: null,
              unit: '元',
              description: '融资净利息+融券净利息+股票质押净利息+融资打新净利息+小额贷净利息+限制性股票融资净利息+股权激励行权融资净利息',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'fina_int',
                  name: '融资',
                  value: null,
                  unit: '元',
                  description: '每日客户应计融资利息-每日融资余额*当日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'stc_int',
                  name: '融券',
                  value: null,
                  unit: '元',
                  description: '融券收入取自流动性平台',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'srp_int',
                  name: '股票质押融资',
                  value: null,
                  unit: '元',
                  description: '股票质押融资合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'xed_int',
                  name: '小额贷',
                  value: null,
                  unit: '元',
                  description: '小额贷合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'rsfdebit_int',
                  name: '限制性股票融资',
                  value: null,
                  unit: '元',
                  description: '限制性股票融资合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'finrzsopt_int',
                  name: '股权激励行权融资',
                  value: null,
                  unit: '元',
                  description: '股权激励行权融资合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                }
              ],
              isAggressive: '2',
            },
            {
              key: 'totCrdtIntCopy',
              name: '净利息收入',
              value: null,
              unit: '元',
              description: '融资净利息+融券净利息+股票质押净利息+融资打新净利息+小额贷净利息+限制性股票融资净利息+股权激励行权融资净利息',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'tranPurRakeCopy',
              name: '净佣金收入',
              value: null,
              unit: '元',
              description: '交易净佣金收入=（股基+沪港通+深港通+股票期权+债劵+三板+其他）净佣金之和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            }
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'hignCustServiceDetail',
            categoryName: '服务指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'gjzCustNum',
              name: '高净值客户总数',
              value: null,
              unit: '户',
              description: '标识为非零售，账户状态非销户的高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'gjzCustNumSingle',
                  name: '个人',
                  value: null,
                  unit: '户',
                  description: '客户性质为个人且标识为非零售，账户状态非销户的高净值客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzCustNum',
                  parentName: '高净值客户总数',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'gjzCustNumOrgan',
                  name: '机构',
                  value: null,
                  unit: '户',
                  description: '客户性质为机构且标识为非零售，账户状态非销户的高净值客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzCustNum',
                  parentName: '高净值客户总数',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'gjzConfigRiskNum',
              name: '配置2种风险属性标的高净值客户数',
              value: null,
              unit: '户',
              description: '统计期内同时持有两种风险等级以上投资标的的高净值客户数，其中股票、期权为高风险标的，剔除天天发',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzFeeConfigPercent',
              name: '资产配置高净值覆盖率',
              value: null,
              unit: '%',
              description: '配置2种风险属性标的高净值客户数/高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzJxzNum',
              name: '高净值客户净新增数',
              value: null,
              unit: '户',
              description: '本考核期新增的高净值客户数-上一考核期末的高净值客户在本考核期内降级为零售客户且资产降幅超过同期市场指数跌幅的客户数量',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzJxzRate',
              name: '高净值客户净增长率',
              value: null,
              unit: '%',
              description: '(本考核期新增的高净值客户数-上一考核期末的高净值客户在本考核期内降级为零售客户且资产降幅超过同期市场指数跌幅的客户数量）/上一考核期末高净值客户数*100% ',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzServCompNum',
              name: '已服务高净值客户数',
              value: null,
              unit: '户',
              description: '统计期内“服务沟通记录中的类型为“客户信息核实+服务营销+理财建议+通知提醒+异动确认+温馨关怀+销户跟踪+投诉处理+销售活动”，服务经理执行且完成“打勾”的记录数+ MOT活动中服务经理执行的且状态已经为“完成”的记录数+直销渠道营销活动响应状态为“完成”的记录数，同一周期客户去重统计',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'gjzServCompNumSingle',
                  name: '个人',
                  value: null,
                  unit: '户',
                  description: '统计期内“服务沟通记录中的类型为“客户信息核实+服务营销+理财建议+通知提醒+异动确认+温馨关怀+销户跟踪+投诉处理+销售活动”，服务经理执行且完成“打勾”的记录数+ MOT活动中服务经理执行的且状态已经为“完成”的记录数+直销渠道营销活动响应状态为“完成”的个人客户记录数，同一周期客户去重统计',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzServCompNum',
                  parentName: '已服务高净值客户数',
                  children: null,
                  isAggressive: '3',
                },
                {
                  key: 'gjzServCompNumOrgan',
                  name: '机构',
                  value: null,
                  unit: '户',
                  description: '统计期内“服务沟通记录中的类型为“客户信息核实+服务营销+理财建议+通知提醒+异动确认+温馨关怀+销户跟踪+投诉处理+销售活动”，服务经理执行且完成“打勾”的记录数+ MOT活动中服务经理执行的且状态已经为“完成”的记录数+直销渠道营销活动响应状态为“完成”的机构客户记录数，同一周期客户去重统计',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzServCompNum',
                  parentName: '已服务高净值客户数',
                  children: null,
                  isAggressive: '3',
                }
              ],
              isAggressive: '3',
            },
            {
              key: 'gjzServiceCompPercent',
              name: '高净值客户服务覆盖率',
              value: null,
              unit: '%',
              description: '已服务高净值客户数/高净值客户总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzServiceCompPercentSingle',
              name: '高净值个人客户服务覆盖率',
              value: null,
              unit: '%',
              description: '已服务高净值个人客户数/高净值个人客户总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzServiceCompPercentOrgan',
              name: '高净值机构客户服务覆盖率',
              value: null,
              unit: '%',
              description: '已服务高净值机构客户数/高净值机构客户总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '3',
            },
            {
              key: 'gjzMotCompletePercent',
              name: '必做MOT任务完成率',
              value: null,
              unit: '%',
              description: '必做已完成任务数/必做MOT任务总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'gjzMotTotNum',
              name: '必做MOT任务总数',
              value: null,
              unit: '个',
              description: '统计期内高净值客户的必做MOT任务数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'gjzMotCompleteNum',
              name: '必做已完成任务数',
              value: null,
              unit: '个',
              description: '统计期内高净值客户的必做MOT任务数完成任务数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'gjzCustInfoCompletePercent',
              name: '高净值客户信息完备率',
              value: null,
              unit: '%',
              description: '信息完善率得分*50%+信息有效率得分*50%',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzInfoCompPercent',
              name: '高净值客户信息完善率',
              value: null,
              unit: '%',
              description: '存量高净值客户信息完善率得分*70%+新开高净值客户信息完善率得分*30%。',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzStockInfoCompPercent',
              name: '存量高净值客户信息完善率',
              value: null,
              unit: '%',
              description: '统计时点当年前开户的标记为“非零售\'客户，手机维护率（30%）+地址维护率（20%）+风险偏好维护率（30%）+电子邮件维护率（20%）',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzNewInfoCompPercent',
              name: '新开高净值客户信息完善率',
              value: null,
              unit: '%',
              description: '统计时点当年新开户的标记为“非零售\'客户，手机维护率（30%）+地址维护率（20%）+风险偏好维护率（30%）+电子邮件维护率（20%）',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzRiskPreferEffRate',
              name: '高净值客户信息有效率',
              value: null,
              unit: '%',
              description: '风险偏好有效的高净值客户数/维护了风险偏好的高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzConfigReportNum',
              name: '资产配置报告高净值客户完成数',
              value: null,
              unit: '户',
              description: '统计期内完成资产配置报告的高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
          ],
        },
      ],
      core: [
        {
          indicatorCategoryDto: {
            categoryKey: 'custAmountDetail',
            categoryName: '客户指标明细',
            parentCateKey: null,
            level: '2',
            remark: null,
          },
          detailIndicators: [
            {
              key: 'effCustNum',
              name: '有效客户数',
              value: null,
              unit: '户',
              description: '统计周期期末为有效的客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'InminorCustNum',
              name: '高净值客户数',
              value: null,
              unit: '户',
              description: '是否零售客户标志为非零售客户',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'newCustNum',
              name: '新增客户数',
              value: null,
              unit: '户',
              description: '统计周期内新增且成为有效户的客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'pNewCustNum',
                  name: '个人',
                  value: null,
                  unit: '户',
                  description: '在统计周期内开户，且客户性质为个人的客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'newCustNum',
                  parentName: '新开客户数',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oNewCustNum',
                  name: '一般机构',
                  value: null,
                  unit: '户',
                  description: '在统计周期内开户，且客户性质为一般机构的客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'newCustNum',
                  parentName: '新开客户数',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oNewPrdtCustNum',
                  name: '产品机构',
                  value: null,
                  unit: '户',
                  description: '在统计周期内开户，且客户性质为产品机构户的客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'newCustNum',
                  parentName: '新开客户数',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'totCustNum',
              name: '总客户数',
              value: null,
              unit: '户',
              description: '非销户的客户总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'pCustNum',
              name: '个人客户数',
              value: null,
              unit: '户',
              description: '客户性质为个人且状态为非销户客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'oCustNum',
              name: '机构客户数',
              value: null,
              unit: '户',
              description: '客户性质为机构客户且状态为非销户客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'oNoPrdtCustNum',
                  name: '一般',
                  value: null,
                  unit: '户',
                  description: '除产品户之外的机构客户',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'oCustNum',
                  parentName: '机构客户数',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'oPrdtCustNum',
                  name: '产品',
                  value: null,
                  unit: '户',
                  description: '客户性质为机构且机构类型为产品户，账户状态正常的客户。产品类型不为“基金公司特定客户资产管理产品（保险）”、“全国社保基金”、“地方社保基金”、“保险产品”、“保险资产管理产品”、“企业年金计划”、“养老金产品”、“银行理财产品”、上市公司员工持股计划”“其他”的客户，剔除资管客户',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'oCustNum',
                  parentName: '机构客户数',
                  children: null,
                  isAggressive: '1',
                },
              ],
              isAggressive: '1',
            },
            {
              key: 'minorCustNum',
              name: '零售客户数',
              value: null,
              unit: '户',
              description: '是否零售客户标志为零售客户',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
          ],
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'asetDetail',
            categoryName: '资产指标明细',
            parentCateKey: null,
            level: '2',
            remark: null,
          },
          detailIndicators: [
            {
              key: 'pCustAset',
              name: '个人客户总资产',
              value: null,
              unit: '元',
              description: '客户性质为个人客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'oNoPrdtCustAset',
              name: '一般机构户总资产',
              value: null,
              unit: '元',
              description: '客户性质为一般机构客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'oPrdtCustAset',
              name: '产品机构户总资产',
              value: null,
              unit: '元',
              description: '客户性质为产品客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzCustAset',
              name: '高净值客户总资产',
              value: null,
              unit: '元',
              description: '客户性质为高净值客户名下普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'mktZq',
              name: '债券市值',
              value: null,
              unit: '元',
              description: '类别为“债券”的所有证券总市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'fundMktVal',
              name: '公募基金市值',
              value: null,
              unit: '元',
              description: '类别为“开放式基金”的所有证券总市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'finaMktVal',
              name: '紫金产品市值（剔除天天发）',
              value: null,
              unit: '元',
              description: '紫金产品市值，不含天天发',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'priFundMktVal',
              name: '证券投资类私募市值',
              value: null,
              unit: '元',
              description: '所有私募基金市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'otcMktVal',
              name: 'OTC产品市值',
              value: null,
              unit: '元',
              description: 'OTC产品市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'optMktVal',
              name: '期权市值',
              value: null,
              unit: '元',
              description: '个股期权的权益，计算公式：市价*数量*合约单位',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'avgBal',
              name: '日均保证金余额',
              value: null,
              unit: '元',
              description: '统计期间客户普通账户、信用账户、期权账户中“当前余额”的日均数据',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'purFinAmt',
              name: '净存取',
              value: null,
              unit: '元',
              description: '转入资金和转出资金的差值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'new_cust_aset',
              name: '新开客户总资产',
              value: null,
              unit: '元',
              description: '在统计周期内开户的所有客户（含销户客户）的期末总资产',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'allTotAsetDis',
              name: '资产明细',
              value: null,
              unit: '元',
              description: '限售市值+流通股市值+产品市值+负债+现金资产+天天发市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'mktLmt',
                  name: '限售股',
                  value: null,
                  unit: '元',
                  description: '股票性质为受限股的所有股票市值，B股转换为人民币计算',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'ttfMktVal',
                  name: '天天发',
                  value: null,
                  unit: '元',
                  description: '天天发产品市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'debVal',
                  name: '负债',
                  value: null,
                  unit: '元',
                  description: '所有负债类资产总额，包含：小额贷负债、融资打新负债、股票质押式融资负债、融资融券负债、正回购负债',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'bal',
                  name: '现金',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户以及期权账户中“当前余额”总和',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'mktVal',
                  name: '产品',
                  value: null,
                  unit: '元',
                  description: '产品类资产总市值，包括：公募基金、紫金产品（不含天天发）、证券投资类私募、OTC',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'cirMktValCopy',
                  name: '流通股',
                  value: null,
                  unit: '元',
                  description: '流通股资产之和，包含：A股市值(主板+中小板+创业板)、B股市值、封闭式基金市值、沪港通、深港通、传统三板市值、新三板市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allTotAsetDis',
                  parentName: '资产明细',
                  children: null,
                  isAggressive: '1',
                },
              ],
              isAggressive: '1',
            },
            {
              key: 'allTotAset',
              name: '总资产',
              value: null,
              unit: '元',
              description: '所有客户普通账户和信用账户内资产总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'shNpRate',
              name: '沪市归集率',
              value: null,
              unit: '%',
              description: '客户在沪市归属华泰的资产总额/所有资产总额',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'szNpRate',
              name: '深市归集率',
              value: null,
              unit: '%',
              description: '客户在深市归属华泰的资产总额/所有资产总额',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'shzNpRate',
              name: '沪深归集率',
              value: null,
              unit: '%',
              description: '客户在沪深市场归属华泰的资产总额/所有资产总额',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
          ],
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'transAmountDetail',
            categoryName: '交易指标明细',
            parentCateKey: null,
            level: '2',
            remark: null,
          },
          detailIndicators: [
            {
              key: 'zhTranAmt',
              name: '综合交易量',
              value: null,
              unit: '元',
              description: '统计期内各类交易的总和，包括：基础股基交易、场内货币市场基金交易、债券ETF交易',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'kfHbTranAmt',
                  name: '场内货币市场基金',
                  value: null,
                  unit: '元',
                  description: '统计期内普通账户、信用账户沪深交易所场内交易型开放式货币基金的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'zhTranAmt',
                  parentName: '综合交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'kfZqTranAmt',
                  name: '债券ETF',
                  value: null,
                  unit: '元',
                  description: '统计期内普通账户、信用账户沪深交易所债券ETF的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'zhTranAmt',
                  parentName: '综合交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'tradTranAmtCopy',
                  name: '基础股基',
                  value: null,
                  unit: '元',
                  description: '统计期内股基交易量总和，包括范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'zhTranAmt',
                  parentName: '综合交易量',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'tradTranAmt',
              name: '基础股基交易量',
              value: null,
              unit: '元',
              description: '统计期内股基交易量总和，包括范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'ptGjTranAmt',
                  name: '普通',
                  value: null,
                  unit: '元',
                  description: '统计期内普通账户产生的股基交易量。股基范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tradTranAmt',
                  parentName: '基础股基交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'dbGjTranAmt',
                  name: '担保',
                  value: null,
                  unit: '元',
                  description: '统计期内信用账户产生的担保股基交易量。股基范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tradTranAmt',
                  parentName: '基础股基交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'xyGjTranAmt',
                  name: '信用',
                  value: null,
                  unit: '元',
                  description: '统计期内信用账户产生的信用股基交易量。股基范围：主板+B股+封基+创业板+中小板+场内交易的LOF、ETF+融资融券+根网套利系统中股基交易量，不含场内的交易型货币基金、债券ETF',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tradTranAmt',
                  parentName: '基础股基交易量',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'hskhTranAmt',
              name: '港股通交易量',
              value: null,
              unit: '元',
              description: '普通账户沪港通与深港通交易量总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'hTranAmt',
                  name: '沪港通',
                  value: null,
                  unit: '元',
                  description: '普通账户沪港通的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'hskhTranAmt',
                  parentName: '港股通交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'skhTranAmt',
                  name: '深港通',
                  value: null,
                  unit: '元',
                  description: '普通账户深港通的交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'hskhTranAmt',
                  parentName: '港股通交易量',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'threeTranAmt',
              name: '三板交易量',
              value: null,
              unit: '元',
              description: '普通账户新三板与老三板交易量总和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'newThreeTranAmt',
                  name: '新三板',
                  value: null,
                  unit: '元',
                  description: '普通账户新三板交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'threeTranAmt',
                  parentName: '三板交易量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oldThreeTranAmt',
                  name: '老三板',
                  value: null,
                  unit: '元',
                  description: '普通账户老三板交易量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'threeTranAmt',
                  parentName: '三板交易量',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'tranAmtOpt',
              name: '股票期权交易量',
              value: null,
              unit: '元',
              description: '期权账户交易量',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
          ],
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'productDetail',
            categoryName: '产品销售指标明细',
            parentCateKey: null,
            level: '2',
            remark: null,
          },
          detailIndicators: [
            {
              key: 'curPrdtTranAmt',
              name: '产品当期销量',
              value: null,
              unit: '元',
              description: '统计周期内产品销售金额之和，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'kfTranAmt',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计期内开放式基金的销售金额，包括：场外（认购+申购）、场内认购',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'taTranAmt',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计周期内【场外（认购+申购）+场内认购】紫金产品销售金额，不含天天发（940018、940028、940038）',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'otcTranAmt',
                  name: 'OTC',
                  value: null,
                  unit: '元',
                  description: '统计周期内OTC 产品销售金额，包括场外（认购+申购）、场内认购',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'smTranAmt',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计周期内【场外（认购+申购）+场内认购】非公募产品（总部或分支机构引并销售的私募产品、基金专户产品）销售金额',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'curPrdtTranAmt',
                  parentName: '产品当期销量',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'nowMktVal',
              name: '时点保有市值',
              value: null,
              unit: '元',
              description: '统计周期期末产品时点市值，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'nowFundMktVal',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计周期期末公募基金的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'nowFinaMktVal',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计周期期末紫金产品剔除天天发的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'nowOtcMktVal',
                  name: 'OTC',
                  value: null,
                  unit: '元',
                  description: '统计周期期末OTC产品的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'nowPriFundMktVal',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计周期期末证券投资类私募产品的时点市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'nowMktVal',
                  parentName: '时点保有市值',
                  children: null,
                  isAggressive: '1',
                },
              ],
              isAggressive: '1',
            },
            {
              key: 'avgMktVal',
              name: '产品日均市值',
              value: null,
              unit: '元',
              description: '统计周期期期间产品日均市值，包含：公募基金、紫金产品（剔除天天发）、OTC、证券投资类私募',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'avgFundMktVal',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计周期期间公募基金的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
                {
                  key: 'avgFinaMktVal',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计周期期间紫金产品剔除天天发的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
                {
                  key: 'avgOtcMktVal',
                  name: 'OTC',
                  value: null,
                  unit: '元',
                  description: '统计周期期间OTC产品的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
                {
                  key: 'avgPriFundMktVal',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计周期期间证券投资类私募产品的日均市值',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'avgMktVal',
                  parentName: '日均保有市值',
                  children: null,
                  isAggressive: '4',
                },
              ],
              isAggressive: '4',
            },
            {
              key: 'nowTtfMktVal',
              name: '天天发时点保有量',
              value: null,
              unit: '元',
              description: '统计时点天天发的保有市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'avgTtfMktVal',
              name: '天天发日均保有量',
              value: null,
              unit: '元',
              description: '统计周期期间天天发的日均保有市值',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '4',
            },
          ],
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'newBusinessDetail',
            categoryName: '开通业务明细',
            parentCateKey: null,
            level: '2',
            remark: null,
          },
          detailIndicators: [
            {
              key: 'ljOpenBusi',
              name: '累计开通业务',
              value: null,
              unit: '户',
              description: '目前开通以下业务的客户数量（不去重），业务包含：融资融券、天天发、创业板、财富通登录、股票期权、OTC、沪港通、深港通、股票质押、新三板',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'rzrqBusi',
                  name: '融资融券',
                  value: null,
                  unit: '户',
                  description: '目前开通融资融券客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'ttfBusi',
                  name: '天天发',
                  value: null,
                  unit: '户',
                  description: '目前开通天天发客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'gpqqBusi',
                  name: '股票期权',
                  value: null,
                  unit: '户',
                  description: '目前开通股票期权客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'hgtBusi',
                  name: '沪港通',
                  value: null,
                  unit: '户',
                  description: '目前开通沪港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'sgtBusi',
                  name: '深港通',
                  value: null,
                  unit: '户',
                  description: '目前开通深港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'cybBusi',
                  name: '创业板',
                  value: null,
                  unit: '户',
                  description: '目前开通创业板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'zlcftBusi',
                  name: '财富通登录',
                  value: null,
                  unit: '户',
                  description: '截止当前累计登录财富通的客户数（1次即算）',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'otcBusi',
                  name: 'OTC',
                  value: null,
                  unit: '户',
                  description: '目前开通OTC客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'gpzyBusi',
                  name: '股票质押',
                  value: null,
                  unit: '户',
                  description: '目前开通股票质押客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'xsbBusi',
                  name: '新三板',
                  value: null,
                  unit: '户',
                  description: '目前开通新三板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'ljOpenBusi',
                  parentName: '累计开通业务',
                  children: null,
                  isAggressive: '1',
                },
              ],
              isAggressive: '1',
            },
            {
              key: 'dqOpenBusi',
              name: '当期开通业务',
              value: null,
              unit: '户',
              description: '统计周期内所有新开业务的客户数（不去重），新开业务包含：融资融券、天天发、创业板、财富通登录、股票期权、OTC、沪港通、深港通、股票质押、新三板',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'rzrqBusiCurr',
                  name: '融资融券',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通融资融券客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'gpzyBusiCurr',
                  name: '股票质押',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通股票质押客户数量 ',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'hgtBusiCurr',
                  name: '沪港通',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通沪港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'gpqqBusiCurr',
                  name: '股票期权',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通股票期权客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'ttfBusiCurr',
                  name: '天天发',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通天天发客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'otcBusiCurr',
                  name: 'OTC',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通OTC客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'cybBusiCurr',
                  name: '创业板',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通创业板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'xsbBusiCurr',
                  name: '新三板',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通新三板客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'zlcftBusiCurr',
                  name: '财富通登录',
                  value: null,
                  unit: '户',
                  description: '统计周期登录财富通的客户数（1次即算）',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'sgtBusiCurr',
                  name: '深港通',
                  value: null,
                  unit: '户',
                  description: '统计周期新开通深港通客户数量',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'dqOpenBusi',
                  parentName: '当期开通业务',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
          ]
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'pureIncomeDetail',
            categoryName: '收入指标明细',
            parentCateKey: null,
            level: '2',
            remark: null
          },
          detailIndicators: [
            {
              key: 'tranPurRake',
              name: '净佣金收入明细',
              value: null,
              unit: '元',
              description: '交易净佣金收入=（股基+沪港通+深港通+股票期权+债劵+三板+其他）净佣金之和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'gjPurRake',
                  name: '股基',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户沪深交易所证券的净佣金，不包含债券ETF、三板、债券、沪港通净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'shhkPurRake',
                  name: '沪港通',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户沪港通的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'szhkPurRake',
                  name: '深港通',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户深港通的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'zqPurRake',
                  name: '债券',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户沪深交易所债券的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'sbPurRake',
                  name: '三板',
                  value: null,
                  unit: '元',
                  description: '普通账户、信用账户三板的净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'optPurRake',
                  name: '股票期权',
                  value: null,
                  unit: '元',
                  description: '期权账户净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'otherPurRake',
                  name: '其它',
                  value: null,
                  unit: '元',
                  description: '除股基净佣金、沪港通净佣金、深港通净佣金、股票期权净佣金、债劵净佣金、三板净佣金以外的其他净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'tranPurRake',
                  parentName: '净佣金收入明细',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'allIncomeAmt',
              name: '净收入',
              value: null,
              unit: '元',
              description: '统计期内整体客户金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'pIncomeAmt',
                  name: '个人',
                  value: null,
                  unit: '元',
                  description: '统计期内客户性质为个人的，金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allIncomeAmt',
                  parentName: '净收入',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'oIncomeAmt',
                  name: '机构',
                  value: null,
                  unit: '元',
                  description: '统计期内客户性质为机构的，金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'allIncomeAmt',
                  parentName: '净收入',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'gjzIncomeAmt',
              name: '高净值客户净收入',
              value: null,
              unit: '元',
              description: '统计期内客户标识为非零售的，金融产品净手续费+资本中介净利息（融券、资管资金融资）+交易净佣金',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'totTranInt',
              name: '产品净手续费收入',
              value: null,
              unit: '元',
              description: '统计期内【公募+紫金（剔除天天发940018、940028、940038）+OTC+私募】产品（场外认购+场外申购+场内认购）产生手续费净收入',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'kfTranInt',
                  name: '公募',
                  value: null,
                  unit: '元',
                  description: '统计期内公募产品（场外认购+场外申购+场内认购）产生手续费净收入',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totTranInt',
                  parentName: '产品净手续费收入',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'taTranInt',
                  name: '紫金',
                  value: null,
                  unit: '元',
                  description: '统计期内紫金产品（场外认购+场外申购+场内认购）产生手续费净收入',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totTranInt',
                  parentName: '产品净手续费收入',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'smTranInt',
                  name: '私募',
                  value: null,
                  unit: '元',
                  description: '统计期内证券投资类私募产品（场外认购+场外申购+场内认购）产生手续费净收入',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totTranInt',
                  parentName: '产品净手续费收入',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'totCrdtInt',
              name: '净利息收入明细',
              value: null,
              unit: '元',
              description: '融资净利息+融券净利息+股票质押净利息+融资打新净利息+小额贷净利息+限制性股票融资净利息+股权激励行权融资净利息',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'fina_int',
                  name: '融资',
                  value: null,
                  unit: '元',
                  description: '每日客户应计融资利息-每日融资余额*当日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'stc_int',
                  name: '融券',
                  value: null,
                  unit: '元',
                  description: '融券收入取自流动性平台',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'srp_int',
                  name: '股票质押融资',
                  value: null,
                  unit: '元',
                  description: '股票质押融资合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'xed_int',
                  name: '小额贷',
                  value: null,
                  unit: '元',
                  description: '小额贷合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'rsfdebit_int',
                  name: '限制性股票融资',
                  value: null,
                  unit: '元',
                  description: '限制性股票融资合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
                {
                  key: 'finrzsopt_int',
                  name: '股权激励行权融资',
                  value: null,
                  unit: '元',
                  description: '股权激励行权融资合约中，客户质押应计利息-质押金额*质押开仓日FTP价格',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'totCrdtInt',
                  parentName: '净利息收入明细',
                  children: null,
                  isAggressive: '2',
                },
              ],
              isAggressive: '2',
            },
            {
              key: 'totCrdtIntCopy',
              name: '净利息收入',
              value: null,
              unit: '元',
              description: '融资净利息+融券净利息+股票质押净利息+融资打新净利息+小额贷净利息+限制性股票融资净利息+股权激励行权融资净利息',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'tranPurRakeCopy',
              name: '净佣金收入',
              value: null,
              unit: '元',
              description: '交易净佣金收入=（股基+沪港通+深港通+股票期权+债劵+三板+其他）净佣金之和',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
          ],
        },
        {
          indicatorCategoryDto: {
            categoryKey: 'hignCustServiceDetail',
            categoryName: '服务指标明细',
            parentCateKey: null,
            level: '2',
            remark: null,
          },
          detailIndicators: [
            {
              key: 'gjzCustNum',
              name: '高净值客户总数',
              value: null,
              unit: '户',
              description: '标识为非零售，账户状态非销户的高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: 'Y',
              parentKey: null,
              parentName: null,
              children: [
                {
                  key: 'gjzCustNumSingle',
                  name: '个人',
                  value: null,
                  unit: '户',
                  description: '客户性质为个人且标识为非零售，账户状态非销户的高净值客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzCustNum',
                  parentName: '高净值客户总数',
                  children: null,
                  isAggressive: '1',
                },
                {
                  key: 'gjzCustNumOrgan',
                  name: '机构',
                  value: null,
                  unit: '户',
                  description: '客户性质为机构且标识为非零售，账户状态非销户的高净值客户数',
                  categoryKey: null,
                  isBelongsSummury: null,
                  hasChildren: null,
                  parentKey: 'gjzCustNum',
                  parentName: '高净值客户总数',
                  children: null,
                  isAggressive: '1',
                }
              ],
              isAggressive: '1',
            },
            {
              key: 'gjzMotCompletePercent',
              name: '必做MOT任务完成率',
              value: null,
              unit: '%',
              description: '必做已完成任务数/必做MOT任务总数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'gjzMotTotNum',
              name: '必做MOT任务总数',
              value: null,
              unit: '个',
              description: '统计期内高净值客户的必做MOT任务数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'gjzMotCompleteNum',
              name: '必做已完成任务数',
              value: null,
              unit: '个',
              description: '统计期内高净值客户的必做MOT任务数完成任务数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
            {
              key: 'gjzCustInfoCompletePercent',
              name: '高净值客户信息完备率',
              value: null,
              unit: '%',
              description: '信息完善率得分*50%+信息有效率得分*50%',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzInfoCompPercent',
              name: '高净值客户信息完善率',
              value: null,
              unit: '%',
              description: '存量高净值客户信息完善率得分*70%+新开高净值客户信息完善率得分*30%。',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzStockInfoCompPercent',
              name: '存量高净值客户信息完善率',
              value: null,
              unit: '%',
              description: '统计时点当年前开户的标记为“非零售\'客户，手机维护率（30%）+地址维护率（20%）+风险偏好维护率（30%）+电子邮件维护率（20%）',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzNewInfoCompPercent',
              name: '新开高净值客户信息完善率',
              value: null,
              unit: '%',
              description: '统计时点当年新开户的标记为“非零售\'客户，手机维护率（30%）+地址维护率（20%）+风险偏好维护率（30%）+电子邮件维护率（20%）',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzRiskPreferEffRate',
              name: '高净值客户信息有效率',
              value: null,
              unit: '%',
              description: '风险偏好有效的高净值客户数/维护了风险偏好的高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '1',
            },
            {
              key: 'gjzConfigReportNum',
              name: '资产配置报告高净值客户完成数',
              value: null,
              unit: '户',
              description: '统计期内完成资产配置报告的高净值客户数',
              categoryKey: null,
              isBelongsSummury: null,
              hasChildren: null,
              parentKey: null,
              parentName: null,
              children: null,
              isAggressive: '2',
            },
          ],
        },
      ],
    },
  };
};
