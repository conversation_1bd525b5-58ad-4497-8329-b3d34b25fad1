/**
 * 历史对比折线图mock数据
 */
exports.response = function (req, res) {
  return {
    code: '0',
    msg: 'OK',
    resultData: {
      current: [
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2936849',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '01',
            weekDay: '周二',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2934674',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '02',
            weekDay: '周三',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2936133',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '03',
            weekDay: '周四',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2932810',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '04',
            weekDay: '周五',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2932790',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '05',
            weekDay: '周六',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2932779',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '06',
            weekDay: '周日',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2937697',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '07',
            weekDay: '周一',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2938261',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '08',
            weekDay: '周二',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2939546',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '09',
            weekDay: '周三',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2936053',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '10',
            weekDay: '周四',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2924691',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1'
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '11',
            weekDay: '周五',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2924677',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '12',
            weekDay: '周六',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2924649',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '13',
            weekDay: '周日',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2934063',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '14',
            weekDay: '周一',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2934928',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '15',
            weekDay: '周二',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '0',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '08',
            day: '16',
            weekDay: '周三',
          },
        }
      ],
      previous: [
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2926985',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '16',
            weekDay: '周日',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2909065',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '17',
            weekDay: '周一',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2915801',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '18',
            weekDay: '周二',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2924760',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '19',
            weekDay: '周三',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2926618',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '20',
            weekDay: '周四',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2924139',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '21',
            weekDay: '周五',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2924125',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '22',
            weekDay: '周六',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2924117',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '23',
            weekDay: '周日',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2927996',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '24',
            weekDay: '周一',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2926186',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '25',
            weekDay: '周二',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2926834',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '26',
            weekDay: '周三',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2930805',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '27',
            weekDay: '周四',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2929650',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '28',
            weekDay: '周五',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2929631',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '29',
            weekDay: '周六',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2929602',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '30',
            weekDay: '周日',
          },
        },
        {
          indicatorMetaDto: {
            key: 'effCustNum',
            name: '有效客户数',
            value: '2936687',
            unit: '户',
            description: '统计周期期末为有效的客户数',
            categoryKey: null,
            isBelongsSummury: null,
            hasChildren: null,
            parentKey: null,
            parentName: null,
            children: null,
            isAggressive: '1',
          },
          timeModel: {
            year: '2017',
            month: '07',
            day: '31',
            weekDay: '周一',
          },
        },
      ],
    },
  };
};
