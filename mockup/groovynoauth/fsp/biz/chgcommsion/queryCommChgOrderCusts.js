/**
 * 批量佣金调整详细信息查询客户列表
 * @param  {[type]} req [description]
 * @param  {[type]} res [description]
 * @return {[type]}     [description]
 */
exports.response = function (req, res) {
  return {
    code: '0',
    msg: 'OK',
    resultData: [
      {
        batchNum: '308RY237WE00001',
        custId: '1-DF-7620',
        custType: 'per',
        econNum: '02000191',
        custName: '张三',
        custLevel: '钻石',
        openAccDept: '南京长江路证券营业部',
        status: '成功',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
      {
        batchNum: '308RY237WE00001',
        custId: '1-DF-7620',
        custType: 'per',
        econNum: '02000191',
        custName: '李四',
        custLevel: '钻石',
        openAccDept: '南京长江路证券营业部',
        status: '成功',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
      {
        batchNum: '308RY237WE00001',
        custId: '1-DF-7620',
        custType: 'per',
        econNum: '02000191',
        custName: '张三',
        custLevel: '钻石',
        openAccDept: '南京长江路证券营业部',
        status: '失败',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
      {
        batchNum: '308RY237WE00001',
        custId: '1-DF-7620',
        custType: 'per',
        econNum: '02000191',
        custName: '王二毛子',
        custLevel: '钻石',
        openAccDept: '南京长江路证券营业部',
        status: '成功',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
      {
        batchNum: '308RY237WE00001',
        custId: '1-DF-7620',
        custType: 'per',
        econNum: '02000191',
        custName: '黑寡妇',
        custLevel: '钻石',
        openAccDept: '南京长江路证券营业部',
        status: '成功',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
      {
        batchNum: '308RY237WE00001',
        custId: '1-DF-7620',
        custType: 'per',
        econNum: '02000191',
        custName: '超人',
        custLevel: '钻石',
        openAccDept: '南京长江路证券营业部',
        status: '成功',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
      {
        batchNum: '308RY237WE00001',
        custId: '1-DF-7620',
        custType: 'per',
        econNum: '02000191',
        custName: '钢铁侠',
        custLevel: '钻石',
        openAccDept: '南京长江路证券营业部',
        status: '成功',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
      {
        batchNum: '308RY237WE00001',
        custId: '1-DF-7620',
        custType: 'per',
        econNum: '02000191',
        custName: '牛郎',
        custLevel: '钻石',
        openAccDept: '南京长江路证券营业部',
        status: '失败',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
      {
        batchNum: '308RY237WE00001',
        custId: '1-DF-7620',
        custType: 'per',
        econNum: '02000191',
        custName: '张三',
        custLevel: '钻石',
        openAccDept: '南京长江路证券营业部',
        status: '成功',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
      {
        batchNum: '308RY237WE00001',
        custId: '1-EX-807',
        custType: 'per',
        econNum: '02004642',
        custName: '织女',
        custLevel: '金卡',
        openAccDept: '南京长江路证券营业部',
        status: '成功',
        flowCode: '3E90EB0B56C4DB45A7581863554414EC',
      },
    ],
  };
};
