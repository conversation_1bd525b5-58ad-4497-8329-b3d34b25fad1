exports.response = function (req, res) {
  return {
    "code": "0",
    "msg": "OK",
    "resultData": [
      {
          "row_Id": null,
          "empId": "123455",
          "empName": "小明",
          "isMain": "false",
          "occupation": "经纪及财富管理部",
          "occDivnNum": "ZZ001041",
          "jobTitle": "经营分析团队负责人"
      }, {
          "row_Id": null,
          "empId": "123450",
          "empName": "小费",
          "isMain": "false",
          "occupation": "经纪及财富管理部",
          "occDivnNum": "ZZ001041",
          "jobTitle": "经营分析团队负责人"
      }, {
          "row_Id": null,
          "empId": "124445",
          "empName": "小王",
          "isMain": "false",
          "occupation": "经纪及财富管理部",
          "occDivnNum": "ZZ001041",
          "jobTitle": "经营分析团队负责人"
      }, {
          "row_Id": null,
          "empId": "9875522",
          "empName": "隔壁老王",
          "isMain": "false",
          "occupation": "经纪及财富管理部",
          "occDivnNum": "ZZ001041",
          "jobTitle": "经营分析团队负责人"
      }, {
          "row_Id": null,
          "empId": "123123456",
          "empName": "小红",
          "isMain": "false",
          "occupation": "经纪及财富管理部",
          "occDivnNum": "ZZ001041",
          "jobTitle": "经营分析团队负责人"
      }, {
          "row_Id": null,
          "empId": "1234555",
          "empName": "小强",
          "isMain": "false",
          "occupation": "经纪及财富管理部",
          "occDivnNum": "ZZ001041",
          "jobTitle": "经营分析团队负责人"
      },
    ],
  };
}