/**
 * 合作合约、佣金调整、权限申请左侧列表项数据
 * @param  {[type]} req [description]
 * @param  {[type]} res [description]
 * @return {[type]}     [description]
 */
exports.response = function (req, res) {
  return {
    code: '0',
    msg: 'OK',
    resultData: {
      applicationBaseInfoList: [
        {
          id: 333,
          custType: 'per',
          subType: '0201',
        },
        {
          id: 334,
          custType: 'per',
          subType: '0203',
        },
        {
          id: 380,
          flowId: '37254B296C3B9D46AFD93F919D207566',
          title: '私密客户申请测试',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '01',
          custNumber: '1-3UUZ9JF',
          custName: '张三',
          createTime: '2017-09-20 17:12:38',
          modifyTime: null,
          remark: '备注备注备注备注hahaha备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
        {
          id: 379,
          flowId: '784348FB07296B40BB896461E8544676',
          title: '私密客户申请申请',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '02',
          custNumber: '1-3VRN4YA',
          custName: '张三',
          createTime: '2017-09-20 15:03:00',
          modifyTime: '2017-09-20 17:08:52',
          remark: '备注备注备注备注备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
        {
          id: 378,
          flowId: '784348FB07296B40BB896461E8544676',
          title: '私密客户申请申请',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '03',
          custNumber: '1-3VRN4YA',
          custName: '张三',
          createTime: '2017-09-20 15:03:00',
          modifyTime: '2017-09-20 17:08:52',
          remark: '备注备注备注备注备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
        {
          id: 377,
          flowId: '784348FB07296B40BB896461E8544676',
          title: '私密客户申请申请',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '04',
          custNumber: '1-3VRN4YA',
          custName: '张三',
          createTime: '2017-09-20 15:03:00',
          modifyTime: '2017-09-20 17:08:52',
          remark: '备注备注备注备注备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
        {
          id: 376,
          flowId: '784348FB07296B40BB896461E8544676',
          title: '私密客户申请申请',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '01',
          custNumber: '1-3VRN4YA',
          custName: '张三',
          createTime: '2017-09-20 15:03:00',
          modifyTime: '2017-09-20 17:08:52',
          remark: '备注备注备注备注备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
        {
          id: 375,
          flowId: '784348FB07296B40BB896461E8544676',
          title: '私密客户申请申请',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '02',
          custNumber: '1-3VRN4YA',
          custName: '张三',
          createTime: '2017-09-20 15:03:00',
          modifyTime: '2017-09-20 17:08:52',
          remark: '备注备注备注备注备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
        {
          id: 374,
          flowId: '784348FB07296B40BB896461E8544676',
          title: '私密客户申请申请',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '03',
          custNumber: '1-3VRN4YA',
          custName: '张三',
          createTime: '2017-09-20 15:03:00',
          modifyTime: '2017-09-20 17:08:52',
          remark: '备注备注备注备注备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
        {
          id: 373,
          flowId: '784348FB07296B40BB896461E8544676',
          title: '私密客户申请申请',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '04',
          custNumber: '1-3VRN4YA',
          custName: '张三',
          createTime: '2017-09-20 15:03:00',
          modifyTime: '2017-09-20 17:08:52',
          remark: '备注备注备注备注备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
        {
          id: 372,
          flowId: '784348FB07296B40BB896461E8544676',
          title: '私密客户申请申请',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '01',
          custNumber: '1-3VRN4YA',
          custName: '张三',
          createTime: '2017-09-20 15:03:00',
          modifyTime: '2017-09-20 17:08:52',
          remark: '备注备注备注备注备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
        {
          id: 371,
          flowId: '784348FB07296B40BB896461E8544676',
          title: '私密客户申请申请',
          empId: '002332',
          empName: '王华',
          orgId: 'ZZ001041',
          orgName: '经纪业务总部',
          type: '01',
          subType: '0103',
          status: '02',
          custNumber: '1-3VRN4YA',
          custName: '张三',
          createTime: '2017-09-20 15:03:00',
          modifyTime: '2017-09-20 17:08:52',
          remark: '备注备注备注备注备注',
          business1: [
            {
              empId: '002332',
              isMain: true,
            },
            {
              empId: '002727',
              isMain: false,
            },
          ],
          business2: '订购',
          business3: null,
        },
      ],
      page: {
        pageNum: 1,
        pageSize: 10,
        totalCount: 20,
        totalPage: 2,
      },
    },
  };
};
