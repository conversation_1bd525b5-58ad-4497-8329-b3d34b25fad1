exports.response = function (req, res) {
  return { "code": "0", "msg": "OK", "resultData": { "pageNum": 1, "pageSize": 10, "totalCount": 1, "totalPage": 1, "quesInfoList": [{ "quesId": "5", "quesValue": "此任务触发条件是否合理", "quesTypeCode": "0", "quesTypeValue": "单选", "optionInfoList": [{ "optionId": "51", "optionValue": "不合理" }, { "optionId": "51", "optionValue": "合理" }], "quesDesp": "我建议XXX" }, { "quesId": "5", "quesValue": "此任务触发条件是否合理", "quesTypeCode": "0", "quesTypeValue": "单选", "optionInfoList": [{ "optionId": "58", "optionValue": "不合理" }, { "optionId": "51", "optionValue": "不合理" }], "quesDesp": "我建议XXX" }, { "quesId": "5", "quesValue": "此任务触发条件是否合理", "quesTypeCode": "2", "quesTypeValue": "单选", "optionInfoList": [{ "optionId": "58", "optionValue": "合理" }, { "optionId": "58", "optionValue": "不合理" }], "quesDesp": "我建议XXX" }, { "quesId": "5", "quesValue": "此任务触发条件是否合理", "quesTypeCode": "2", "quesTypeValue": "单选", "optionInfoList": [{ "optionId": "58", "optionValue": "不合理" }, { "optionId": "51", "optionValue": "合理" }], "quesDesp": "我建议XXX" }, { "quesId": "5", "quesValue": "此任务触发条件是否合理", "quesTypeCode": "0", "quesTypeValue": "单选", "optionInfoList": [{ "optionId": "58", "optionValue": "合理" }, { "optionId": "58", "optionValue": "不合理" }], "quesDesp": "我建议XXX" }] } }
}