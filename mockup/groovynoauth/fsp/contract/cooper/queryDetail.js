exports.response = function (req, res) {
    return {
    "code": "0",
    "msg": "OK",
    "resultData": {
        "id": "1-435H8HH",
        "action": null,
        "createdBy": "1-OXZ5",
        "createdName": "王华",
        "lastUpdateBy": "1-OXZ5",
        "lastUpdateName": "王华",
        "initiator": "",
        "uuid": "1-A1EQ1D",
        "contractNum": "1-**********",
        "contractName": "LZTEST0905",
        "status": "01",
        "type": "Coop Contract",
        "subType": "Coop Contract",
        "workflowName": "合作合约",
        "workflowNode": "1111",
        "workflowCode": "***************",
        "divisionId": "1-5XNA",
        "divisionName": "南京长江路证券营业部",
        "contactId": "1-DX-4575",
        "accountId": "",
        "custId": null,
        "custType": null,
        "custName": "1-DX-4575",
        "econNum": "********",
        "startDt": "2017/09/01",
        "vailDt": "2017/09/05",
        "endDt": "2017/09/02",
        "context": "222",
        "description": "222",
        "assetAccnt": "",
        "terms": [
            {
                "key": "0",
                "termsName": "T91900",
                "paraName": "T91901",
                "paraVal": "0.9",
                "divIntegrationId": "ZZ001041054",
                "divName": "南京解放路证券营业部",
                "furturePromotion": "",
                "preCondition": "",
                "synBusSys": ""
            },
            {
                "key": "1",
                "termsName": "T92300",
                "paraName": "T92301",
                "paraVal": "0.4",
                "divIntegrationId": "ZZ001041054",
                "divName": "南京解放路证券营业部",
                "furturePromotion": "",
                "preCondition": "",
                "synBusSys": ""
            }
        ]
    }
}
}
