/**
 * 360服务记录查询
 */
exports.response = function (req, res) {
  return {
    "code":"0",
    "msg":"OK",
    "resultData" :[
      {
        "id": null,
        "serveTime": "2016/12/01 11:14:15",
        "taskName": "持续持有现金_未开通天天发",
        "taskDesc": "向客户推荐天天发。",
        "taskType": "MOT服务记录",
        "handlerType": "选做",
        "handlerTimeLimit": "2016/09/21-2017/07/29",
        "actor": null,
        "custFeedback": null,
        "feedbackTime": "2016/12/01 10:14:15",
        "serveStatus": "完成",
        "workResult": null,
        "serveChannel": null,
        "serveStrategy": "提示客户天天发开通带来的好处，并介绍固定收益率产品。",
        "serveRecord": "2016年12月01日10时与客户进行短信沟通不像你虚拟现实晋级赛",
        "activityContent": "MOT服务记录"
      },
      {
        "id": null,
        "serveTime": "2016/12/01 12:14:15",
        "taskName": "持续持有现金_未开通天天发",
        "taskDesc": "向客户推荐天天发。",
        "taskType": "MOT服务记录",
        "handlerType": "选做",
        "handlerTimeLimit": "2016/09/21-2017/07/29",
        "actor": null,
        "custFeedback": null,
        "feedbackTime": "2016/12/01 10:14:15",
        "serveStatus": "完成",
        "workResult": null,
        "serveChannel": null,
        "serveStrategy": "提示客户天天发开通带来的好处，并介绍固定收益率产品。",
        "serveRecord": "2016年12月01日10时与客户进行短信沟通不像你虚拟现实晋级赛",
        "activityContent": "MOT服务记录"
      },
        {
            "id": null,
            "serveTime": "2016/12/01 13:14:15",
            "taskName": "持续持有现金_未开通天天发",
            "taskDesc": "向客户推荐天天发。",
            "taskType": "MOT服务记录",
            "handlerType": "选做",
            "handlerTimeLimit": "2016/09/21-2017/07/29",
            "actor": null,
            "custFeedback": null,
            "feedbackTime": "2016/12/01 10:14:15",
            "serveStatus": "完成",
            "workResult": null,
            "serveChannel": null,
            "serveStrategy": "提示客户天天发开通带来的好处，并介绍固定收益率产品。",
            "serveRecord": "2016年12月01日10时与客户进行短信沟通不像你虚拟现实晋级赛",
            "activityContent": "MOT服务记录"
        },
        {
            "id": null,
            "serveTime": "2016/12/01 14:14:15",
            "taskName": "持续持有现金_未开通天天发",
            "taskDesc": "向客户推荐天天发。",
            "taskType": "MOT服务记录",
            "handlerType": "选做",
            "handlerTimeLimit": "2016/09/21-2017/07/29",
            "actor": null,
            "custFeedback": null,
            "feedbackTime": "2016/12/01 10:14:15",
            "serveStatus": "完成",
            "workResult": null,
            "serveChannel": null,
            "serveStrategy": "提示客户天天发开通带来的好处，并介绍固定收益率产品。",
            "serveRecord": "2016年12月01日10时与客户进行短信沟通不像你虚拟现实晋级赛",
            "activityContent": "MOT服务记录"
        },
        {
            "id": null,
            "serveTime": "2016/12/01 15:14:15",
            "taskName": "持续持有现金_未开通天天发",
            "taskDesc": "向客户推荐天天发。",
            "taskType": "MOT服务记录",
            "handlerType": "选做",
            "handlerTimeLimit": "2016/09/21-2017/07/29",
            "actor": null,
            "custFeedback": null,
            "feedbackTime": "2016/12/01 10:14:15",
            "serveStatus": "完成",
            "workResult": null,
            "serveChannel": null,
            "serveStrategy": "提示客户天天发开通带来的好处，并介绍固定收益率产品。",
            "serveRecord": "2016年12月01日10时与客户进行短信沟通不像你虚拟现实晋级赛",
            "activityContent": "MOT服务记录"
        },
        {
            "id": null,
            "serveTime": "2016/12/01 16:14:15",
            "taskName": "持续持有现金_未开通天天发",
            "taskDesc": "向客户推荐天天发。",
            "taskType": "MOT服务记录",
            "handlerType": "选做",
            "handlerTimeLimit": "2016/09/21-2017/07/29",
            "actor": null,
            "custFeedback": null,
            "feedbackTime": "2016/12/01 10:14:15",
            "serveStatus": "完成",
            "workResult": null,
            "serveChannel": null,
            "serveStrategy": "提示客户天天发开通带来的好处，并介绍固定收益率产品。",
            "serveRecord": "2016年12月01日10时与客户进行短信沟通不像你虚拟现实晋级赛",
            "activityContent": "MOT服务记录"
        },
        {
            "id": null,
            "serveTime": "2016/12/01 17:14:15",
            "taskName": "持续持有现金_未开通天天发",
            "taskDesc": "向客户推荐天天发。",
            "taskType": "MOT服务记录",
            "handlerType": "选做",
            "handlerTimeLimit": "2016/09/21-2017/07/29",
            "actor": null,
            "custFeedback": null,
            "feedbackTime": "2016/12/01 10:14:15",
            "serveStatus": "完成",
            "workResult": null,
            "serveChannel": null,
            "serveStrategy": "提示客户天天发开通带来的好处，并介绍固定收益率产品。",
            "serveRecord": "2016年12月01日10时与客户进行短信沟通不像你虚拟现实晋级赛",
            "activityContent": "MOT服务记录"
        },
        {
            "id": null,
            "serveTime": "2016/12/01 18:14:15",
            "taskName": "持续持有现金_未开通天天发",
            "taskDesc": "向客户推荐天天发。",
            "taskType": "MOT服务记录",
            "handlerType": "选做",
            "handlerTimeLimit": "2016/09/21-2017/07/29",
            "actor": null,
            "custFeedback": null,
            "feedbackTime": "2016/12/01 10:14:15",
            "serveStatus": "完成",
            "workResult": null,
            "serveChannel": null,
            "serveStrategy": "提示客户天天发开通带来的好处，并介绍固定收益率产品。",
            "serveRecord": "2016年12月01日10时与客户进行短信沟通不像你虚拟现实晋级赛",
            "activityContent": "MOT服务记录"
        }
    ]
  }
};
