exports.response = function (req, res) {
  return {
    "code": "0",
    "msg": "OK",
    "resultData": [
      {
        "taskName": "新规_新股上市提醒",
        "taskDesc": "新股上市提醒，新股上市提醒...",
        "taskType": "MOT任务",
        "handlerType": "Mission",
        "handlerTimeLimit": "2017/07/20-2017/07/23",
        "actor": "李四",
        "custFeedback": "继续持有继续持有",
        "feedbackTime": "2017/09/08",
        "serveStatus": null,
        "workResult": "预约下次",
        "serveChannel": null,
        "serveStrategy": null,
        "serveRecord": "沟通顺畅",
        "activityContent": null,
        "id": 'weqwqe21321',
        "serveTime": "2017/07/16 12:34:56",
      },
      {
        "taskName": "新规_新股上市提醒",
        "taskDesc": "新股上市提醒，新股上市提醒...",
        "taskType": "MOT任务",
        "handlerType": "Mission",
        "handlerTimeLimit": "2017/07/20-2017/07/23",
        "actor": "李四",
        "custFeedback": "继续持有继续持有",
        "feedbackTime": "2017/09/08",
        "serveStatus": null,
        "workResult": "预约下次",
        "serveChannel": null,
        "serveStrategy": null,
        "serveRecord": "沟通顺畅",
        "activityContent": null,
        "id": 'dfsadsa1231',
        "serveTime": "2017/07/16 08:34:56",
      },
      {
        "taskName": "新规_新股上市提醒",
        "taskDesc": "新股上市提醒，新股上市提醒...",
        "taskType": "MOT任务",
        "handlerType": "Mission",
        "handlerTimeLimit": "2017/07/20-2017/07/23",
        "actor": "李四",
        "custFeedback": "继续持有继续持有",
        "feedbackTime": "2017/09/08",
        "serveStatus": null,
        "workResult": "预约下次",
        "serveChannel": null,
        "serveStrategy": null,
        "serveRecord": "沟通顺畅",
        "activityContent": null,
        "id": 'fgfdgd455',
        "serveTime": "2017/07/18 12:34:56",
      },
      {
        "taskName": "新规_新股上市提醒",
        "taskDesc": "新股上市提醒，新股上市提醒...",
        "taskType": "MOT任务",
        "handlerType": "Mission",
        "handlerTimeLimit": "2017/07/20-2017/07/23",
        "actor": "李四",
        "custFeedback": "继续持有继续持有",
        "feedbackTime": "2017/09/08",
        "serveStatus": null,
        "workResult": "预约下次",
        "serveChannel": null,
        "serveStrategy": null,
        "serveRecord": "沟通顺畅",
        "activityContent": null,
        "id": 'sdfsd2355',
        "serveTime": "2017/07/19 12:34:56",
      },
      {
        "taskName": "新规_新股上市提醒",
        "taskDesc": "新股上市提醒，新股上市提醒...",
        "taskType": "MOT任务",
        "handlerType": "Mission",
        "handlerTimeLimit": "2017/07/20-2017/07/23",
        "actor": "李四",
        "custFeedback": "继续持有继续持有",
        "feedbackTime": "2017/09/08",
        "serveStatus": null,
        "workResult": "预约下次",
        "serveChannel": null,
        "serveStrategy": null,
        "serveRecord": "沟通顺畅",
        "activityContent": null,
        "id": 'dsadsa213',
        "serveTime": "2017/07/20 12:34:56",
      },
      {
          "taskName": "新规_新股上市提醒",
          "taskDesc": "新股上市提醒，新股上市提醒...",
          "taskType": "MOT任务",
          "handlerType": "Mission",
          "handlerTimeLimit": "2017/07/20-2017/07/23",
          "actor": "李四",
          "custFeedback": "继续持有继续持有",
          "feedbackTime": "2017/09/08",
          "serveStatus": null,
          "workResult": "预约下次",
          "serveChannel": null,
          "serveStrategy": null,
          "serveRecord": "沟通顺畅",
          "activityContent": null,
          "id": 'dsadsa213',
          "serveTime": "2017/07/20 12:34:56",
      },
      {
          "taskName": "新规_新股上市提醒",
          "taskDesc": "新股上市提醒，新股上市提醒...",
          "taskType": "MOT任务",
          "handlerType": "Mission",
          "handlerTimeLimit": "2017/07/20-2017/07/23",
          "actor": "李四",
          "custFeedback": "继续持有继续持有",
          "feedbackTime": "2017/09/08",
          "serveStatus": null,
          "workResult": "预约下次",
          "serveChannel": null,
          "serveStrategy": null,
          "serveRecord": "沟通顺畅",
          "activityContent": null,
          "id": 'dsadsa213',
          "serveTime": "2017/07/20 12:34:56",
      },
      {
          "taskName": "新规_新股上市提醒",
          "taskDesc": "新股上市提醒，新股上市提醒...",
          "taskType": "MOT任务",
          "handlerType": "Mission",
          "handlerTimeLimit": "2017/07/20-2017/07/23",
          "actor": "李四",
          "custFeedback": "继续持有继续持有",
          "feedbackTime": "2017/09/08",
          "serveStatus": null,
          "workResult": "预约下次",
          "serveChannel": null,
          "serveStrategy": null,
          "serveRecord": "沟通顺畅",
          "activityContent": null,
          "id": 'dsadsa213',
          "serveTime": "2017/07/20 12:34:56",
      },
      {
          "taskName": "新规_新股上市提醒",
          "taskDesc": "新股上市提醒，新股上市提醒...",
          "taskType": "MOT任务",
          "handlerType": "Mission",
          "handlerTimeLimit": "2017/07/20-2017/07/23",
          "actor": "李四",
          "custFeedback": "继续持有继续持有",
          "feedbackTime": "2017/09/08",
          "serveStatus": null,
          "workResult": "预约下次",
          "serveChannel": null,
          "serveStrategy": null,
          "serveRecord": "沟通顺畅",
          "activityContent": null,
          "id": 'dsadsa213',
          "serveTime": "2017/07/20 12:34:56",
      },
      {
          "taskName": "新规_新股上市提醒",
          "taskDesc": "新股上市提醒，新股上市提醒...",
          "taskType": "MOT任务",
          "handlerType": "Mission",
          "handlerTimeLimit": "2017/07/20-2017/07/23",
          "actor": "李四",
          "custFeedback": "继续持有继续持有",
          "feedbackTime": "2017/09/08",
          "serveStatus": null,
          "workResult": "预约下次",
          "serveChannel": null,
          "serveStrategy": null,
          "serveRecord": "沟通顺畅",
          "activityContent": null,
          "id": 'dsadsa213',
          "serveTime": "2017/07/20 12:34:56",
      },

    ]
  }
}