exports.response = function (req, res) {
    return {
      "code": "0",
      "msg": "OK",
      "resultData": {
        "id": "1-44NLLA0",
        "action": null,
        "subType": "紫金快车道协议",
        "agreementNum": "1-44NLLA0",
        "contactId": "1-28AF-249",
        "accountId": "",
        "custId": "1-28AF-249",
        "custType": "per",
        "econNum": "********",
        "startDt": "11/18/2017",
        "vailDt": "11/18/2018",
        "content": "重复性校验",
        "flowid": "9B03CEAC06F3C745BD88E549E04A181C",
        "appId": "14367",
        "empId": null,
        "createdDt": "11/17/2017 09:58:21",
        "contactName": "1-28AF-249",
        "accountName": "",
        "createdBy": "1-OXZ5",
        "lastUpdateBy": "1-OXZ5",
        "status": "02",
        "divisionId": "1-5XNA",
        "divisionName": "南京长江路证券营业部",
        "createdName": "王华",
        "lastUpdateName": "王华",
        "approver": "002332",
        "workflowName": "1",
        "workflowNode": "正常办结",
        "workflowCode": "1:9B03CEAC06F3C745BD88E549E04A181C,",
        "attachment": [
          {
            "uuid": "c2587b78-12eb-4c48-a7a5-3d6fc35dd02a",
            "attachmentType": "申请表",
            "attachmentComments": "14367"
          },
          {
            "uuid": "2c3561c5-6e94-4a54-961e-a6e21071ce03",
            "attachmentType": "影像资料",
            "attachmentComments": "14367"
          },
          {
            "uuid": "88b65659-a31e-460f-9839-ce3bd4090751",
            "attachmentType": "授权委托书",
            "attachmentComments": "14367"
          }
        ],
        "operationType": "协议订购",
        "templateId": "紫金快车道协议",
        "multiUsedFlag": "Y",
        "levelTenFlag": "Y",
        "item": [
          {
            "rk": null,
            "agrId": "1-44NLLA0",
            "prodRowId": null,
            "prodCode": "SP0079",
            "prodName": "紫金快车道",
            "prodType": "软件产品",
            "prodTypeName": "软件产品",
            "prodSubType": "交易软件",
            "prodSubTypeName": "交易软件",
            "commFlg": null,
            "informFlg": null,
            "packageFlg": null,
            "discountFlg": null,
            "price": "",
            "riskMatchFlag": "是",
            "termMatchFlag": "是",
            "varietyMatchFlag": "是",
            "confirmType": "适当性评估结果确认书"
          }
        ],
        "term": [
          {
            "rowId": null,
            "agrId": "1-44NLLA0",
            "seqNum": null,
            "termsName": "T21200",
            "terms": "冻结资产",
            "paraName": "T21201",
            "param": "冻结资产值",
            "typeCd": null,
            "descText": null,
            "furturePromotion": "是",
            "preCondition": "是",
            "xSynchBusSys": null,
            "paraVal": "100000"
          },
          {
            "rowId": null,
            "agrId": "1-44NLLA0",
            "seqNum": null,
            "termsName": "T19100",
            "terms": "去年股基交易量",
            "paraName": "T19101",
            "param": "去年股基交易量",
            "typeCd": null,
            "descText": null,
            "furturePromotion": "是",
            "preCondition": "是",
            "xSynchBusSys": null,
            "paraVal": "1000000"
          },
          {
            "rowId": null,
            "agrId": "1-44NLLA0",
            "seqNum": null,
            "termsName": "T20100",
            "terms": "年股基交易量",
            "paraName": "T20101",
            "param": "年股基交易量",
            "typeCd": null,
            "descText": null,
            "furturePromotion": "是",
            "preCondition": "是",
            "xSynchBusSys": null,
            "paraVal": "100000"
          },
          {
            "rowId": null,
            "agrId": "1-44NLLA0",
            "seqNum": null,
            "termsName": "T21300",
            "terms": "最低资产",
            "paraName": "T21301",
            "param": "最低资产值",
            "typeCd": null,
            "descText": null,
            "furturePromotion": "是",
            "preCondition": "是",
            "xSynchBusSys": null,
            "paraVal": "100000"
          },
          {
            "rowId": null,
            "agrId": "1-44NLLA0",
            "seqNum": null,
            "termsName": "T15100",
            "terms": "最终佣金",
            "paraName": "T15101",
            "param": "最终佣金值",
            "typeCd": null,
            "descText": null,
            "furturePromotion": "是",
            "preCondition": "是",
            "xSynchBusSys": null,
            "paraVal": "0.2"
          },
          {
            "rowId": null,
            "agrId": "1-44NLLA0",
            "seqNum": null,
            "termsName": "T21800",
            "terms": "续订股基净佣金",
            "paraName": "T21701",
            "param": "续订股基净佣金",
            "typeCd": null,
            "descText": null,
            "furturePromotion": "是",
            "preCondition": "是",
            "xSynchBusSys": null,
            "paraVal": "100000"
          },
          {
            "rowId": null,
            "agrId": "1-44NLLA0",
            "seqNum": null,
            "termsName": "T21100",
            "terms": "起始资产",
            "paraName": "T21101",
            "param": "起始资产值",
            "typeCd": null,
            "descText": null,
            "furturePromotion": "是",
            "preCondition": "是",
            "xSynchBusSys": null,
            "paraVal": "10000"
          }
        ],
        "cust": [
          {
            "ituId": null,
            "agrId": "1-44NLLA0",
            "custId": "1-LJ5T-890",
            "custName": "1-LJ5T-890",
            "econNum": "02040108",
            "subCustType": "个人客户",
            "perEconNum": "02040108",
            "perCustId": "1-LJ5T-890",
            "perCustName": "1-LJ5T-890",
            "orgEconNum": "",
            "orgCustId": "",
            "orgCustName": "",
            "custStatus": "开通完成",
            "custComments": ""
          }
        ]
      }
    }
}
