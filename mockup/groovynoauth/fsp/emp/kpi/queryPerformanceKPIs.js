/**
 * 投顾绩效指标
*/
exports.response = function (req, res) {
  return {
    "code":"0",
    "msg":"OK",
    "resultData":[
      {
        "key":"custNum",
        "name":"服务客户数",
        "value":null,
        "unit":"户",
        "description":"入岗投顾人员服务的客户数",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"totAset",
        "name":"托管总资产",
        "value":null,
        "unit":"元",
        "description":"入岗投顾人员名下客户总资产",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"signCustNum",
        "name":"签约客户数",
        "value":null,
        "unit":"户",
        "description":"入岗投顾统计期内名下签约的客户数",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"signCustTotAsset",
        "name":"签约总资产",
        "value":null,
        "unit":"元",
        "description":"入岗投顾统计期末时点签约客户的资产总额",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"newCustInAset",
        "name":"新开客户净转入资产",
        "value":null,
        "unit":"元",
        "description":"入岗投顾名下开发关系为本人且新开客户自开户起1年内在投顾名下每个统计期的净转入资产（资金+市值）",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"motCompletePercent",
        "name":"必做MOT任务完成率",
        "value":null,
        "unit":"%",
        "description":"入岗投顾统计期间触发的必做已完成任务数/必做MOT任务总数",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"serviceCompPercent",
        "name":"服务覆盖率",
        "value":null,
        "unit":"%",
        "description":"入岗投顾统计期间完成的期末客户中本人服务完成的客户人数/统计期期末名下客户数",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"configRiskNum",
        "name":"配置2种风险属性标的客户数",
        "value":null,
        "unit":"户",
        "description":"统计期内入岗投顾名下客户持仓同时具备两种及以上风险级别投资标的（不含天天发），其中股票、期权为高风险标的",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"infoCompPercent",
        "name":"客户信息完善率",
        "value":null,
        "unit":"%",
        "description":"投顾名下所有客户信息完善率",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"newCustNum",
        "name":"新增客户数",
        "value":null,
        "unit":"户",
        "description":"统计周期内新增且成为有效户的客户数",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"ttfBusi",
        "name":"天天发",
        "value":null,
        "unit":"户",
        "description":"目前开通天天发客户数量",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"累计开通业务",
        "children":null,
        "isAggressive":null
      }, {
        "key":"hgtBusi",
        "name":"沪港通",
        "value":null,
        "unit":"户",
        "description":"目前开通沪港通客户数量",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"累计开通业务",
        "children":null,
        "isAggressive":null
      }, {
        "key":"sgtBusi",
        "name":"深港通",
        "value":null,
        "unit":"户",
        "description":"目前开通深港通客户数量",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"累计开通业务",
        "children":null,
        "isAggressive":null
      }, {
        "key":"rzrqBusi",
        "name":"融资融券",
        "value":null,
        "unit":"户",
        "description":"目前开通融资融券客户数量",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"累计开通业务",
        "children":null,
        "isAggressive":null
      }, {
        "key":"xsbBusi",
        "name":"新三板",
        "value":null,
        "unit":"户",
        "description":"目前开通新三板客户数量",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"累计开通业务",
        "children":null,
        "isAggressive":null
      }, {
        "key":"gpqqBusi",
        "name":"股票期权",
        "value":null,
        "unit":"户",
        "description":"目前开通股票期权客户数量",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"累计开通业务",
        "children":null,
        "isAggressive":null
      }, {
        "key":"cybBusi",
        "name":"创业板",
        "value":null,
        "unit":"户",
        "description":"目前开通创业板客户数量",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"累计开通业务",
        "children":null,
        "isAggressive":null
      }, {
        "key":"shzNpRate",
        "name":"沪深归集率",
        "value":null,
        "unit":"%",
        "description":"客户在沪深市场归属华泰的资产总额/所有资产总额\n",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"tranPurRakeCopy",
        "name":"净佣金收入",
        "value":null,
        "unit":"元",
        "description":"交易净佣金收入=（股基+沪港通+深港通+股票期权+债劵+三板+其他）净佣金之和",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"totTranInt",
        "name":"产品净手续费收入",
        "value":null,
        "unit":"元",
        "description":"统计期内【公募+紫金（剔除天天发940018、940028、940038）+OTC+私募】产品（场外认购+场外申购+场内认购）产生手续费净收入",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"totCrdtIntCopy",
        "name":"净利息收入",
        "value":null,
        "unit":"元",
        "description":"融资净利息+融券净利息+股票质押净利息+融资打新净利息+小额贷净利息+限制性股票融资净利息+股权激励行权融资净利息",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":null,
        "children":null,
        "isAggressive":null
      }, {
        "key":"kfTranAmt",
        "name":"公募",
        "value":null,
        "unit":"元",
        "description":"统计期内开放式基金的销售金额，包括：场外（认购+申购）、场内认购",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"产品当期销量",
        "children":null,
        "isAggressive":null
      }, {
        "key":"smTranAmt",
        "name":"私募",
        "value":null,
        "unit":"元",
        "description":"统计周期内【场外（认购+申购）+场内认购】非公募产品（总部或分支机构引并销售的私募产品、基金专户产品）销售金额",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"产品当期销量",
        "children":null,
        "isAggressive":null
      }, {
        "key":"taTranAmt",
        "name":"紫金",
        "value":null,
        "unit":"元",
        "description":"统计周期内【场外（认购+申购）+场内认购】紫金产品销售金额，不含天天发（940018、940028、940038）",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"产品当期销量",
        "children":null,
        "isAggressive":null
      }, {
        "key":"otcTranAmt",
        "name":"OTC",
        "value":null,
        "unit":"元",
        "description":"统计周期内OTC 产品销售金额，包括场外（认购+申购）、场内认购",
        "categoryKey":null,
        "isBelongsSummury":null,
        "hasChildren":null,
        "parentKey":null,
        "parentName":"产品当期销量",
        "children":null,
        "isAggressive":null
      },
    ],
  }
}
