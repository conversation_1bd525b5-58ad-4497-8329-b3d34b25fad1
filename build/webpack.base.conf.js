const path = require('path')
const utils = require('./utils')
const config = require('../config')
const { RetryChunkLoadPlugin } = require('webpack-retry-chunk-load-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');


function resolve (dir) {
  return path.join(__dirname, '..', dir)
}

module.exports = {
  output: {
    // 设置 sourcemap 的前缀，以在多个webpack编译环境中区分不同工程的同位置文件.
    // ps: devtoolNamespace 通过 devtoolModuleFilenameTemplate 中占位符 namespace 来更改前缀；所以两者要同时设定
    devtoolNamespace: config.appName,
    devtoolModuleFilenameTemplate: 'webpack://[namespace]/[resource-path]?[loaders]',
  },
  resolve: {
    extensions: ['.jsx', '.ts', '.tsx', '.web.tsx', '.web.ts', '.web.jsx', '.web.js', '.js', '.less', '.css'],
    modules: [
      resolve('src'),
      'node_modules',
   ],
    alias: {
      '@/components': resolve('src/components'),
      '@/config': resolve('src/config'),
      '@/helper': resolve('src/helper'),
      '@/decorators': resolve('src/decorators'),
      '@/hooks': resolve('src/hooks'),
      '@/utils': resolve('src/utils'),
      '@/routes': resolve('src/routes'),
      'htsc/static': resolve('static'),
      '@/css': resolve('src/css'),
      '@/middlewares': resolve('src/middlewares'),
      '@/api': resolve('src/api'),
      '@/layouts': resolve('src/layouts'),
      '@/newUI': resolve('src/components/common/newUI'),
      '@ant-design/icons/lib/dist$': resolve('src/icons.js'),
    }
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        loader: 'ts-loader'
      },
      // {
      //   test: /\.json$/,
      //   loader: 'json-loader'
      // },
      {
        test: /\.(svg)$/i,
        loader: 'svg-sprite-loader',
        include: [
          // antd-mobile svg
          // require.resolve('antd-mobile').replace(/warn\.js$/, ''),
          path.resolve(__dirname, 'static/svg'),
        ]
      },
      {
        test: /\.(png|jpe?g|gif|msi|xlsx?|pdf|docx?)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10 * 1024,
          name: utils.assetsPath('img/[name].[hash:7].[ext]')
        }
      },
      {
        test: /\.(woff2?|eot|ttf|otf|svg)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: utils.assetsPath('fonts/[name].[hash:7].[ext]')
        }
      }
    ],
    noParse: [new RegExp('node_modules/localforage/dist/localforage.js')]
  },
  plugins: [
    new RetryChunkLoadPlugin({
      // optional stringified function to get the cache busting query string appended to the script src
      // if not set will default to appending the string `?cache-bust=true`
      cacheBust: `function() {
        return 'timestamp=' + Date.now();
      }`,
      // optional value to set request timeout in milliseconds
      timeout: 3000,
      // optional value to set the maximum number of retries to load the chunk. Default is 1
      maxRetries: 2,
      // optional list of chunks to which retry script should be injected
      // if not set will add retry script to all chunks that have webpack script loading
      // chunks: ['chunkName'],
      // optional code to be executed in the browser context if after all retries chunk is not loaded.
      // if not set - nothing will happen and error will be returned to the chunk loader.
      // lastResortScript: 'window.location.href=\'/500.html\';'
    }),
    new CopyWebpackPlugin([
      {
        from: path.join(path.dirname(require.resolve('pdfjs-dist/package.json')), 'cmaps'),
        to: 'cmaps/'
      },
    ]),
  ],
}
