var _ = require('lodash');
// 入口文件配置项
const entries = [
  {
    name: 'index',
    template: 'index',
    onlyInProductMode: false,
    entry: {
      app: './src/apps/index/app.js',
      standalone: './src/apps/index/app.standalone.js',
    },
  },
  {
    name: 'investAnalyze',
    template: 'index',
    onlyInProductMode: true,
    entry: {
      app: './src/apps/investAnalyze/app.js',
      standalone: './src/apps/investAnalyze/app.standalone.js',
    },
    externals: [
      'react',
      'react-dom',
      '@lego/bigbox-utils',
      'echarts',
      'canvas',
    ],
  },
  // 审批页面入口
  {
    name: 'approval',
    template: 'index',
    onlyInProductMode: false,
    entry: './src/apps/approval/app.standalone.js',
    externals: [
      'react',
      'react-dom',
      '@lego/bigbox-utils',
    ],
  },
  // pdf预览
  {
    name: 'pdfPreview',
    template: 'pdfPreview',
    onlyInProductMode: false,
    entry: './src/apps/pdfPreview/app.standalone.js',
    externals: [
      'react',
      'react-dom',
      '@lego/bigbox-utils',
    ],
  },
];

function getEntriesFromCommandline(options = { mode: 'production' }) {
  if (process.argv[2] && process.argv[2].indexOf('entry') > -1) {
    let entriesList = [];
    entriesList = process.argv[2].split('=')[1].split(':');
    if (entriesList.length === 1 && entriesList[0] === 'ALL') {
      return entries;
    }
    return entriesList
      .map(function(name) {
        const currentEntry = entries.find(function(item){ return item.name === name });
        if (!currentEntry) {
          console.error("your input entry is undefined. please check your input or your config");
          return false;
        }
        return currentEntry;
      })
      .filter(function(item){
        return !!item
      });
  }

  return options.mode === 'development' ? entries.filter(function(item) { return !item.onlyInProductMode }) : entries;
}


exports.generateEntries = function (entryItem, options = { mode: 'production' }) {
  if (_.isObject(entryItem.entry)) {
    // 入口为对外输出的页面
    return {
      [entryItem.name]: entryItem.entry.app,
      [entryItem.name + '_standalone']: entryItem.entry.standalone,
    };
  }
  return {
    [entryItem.name]: entryItem.entry,
  }
}

exports.getEntriesFromCommandline = getEntriesFromCommandline;
