// 根据多个入口生成html-webpack-plugin集合
// var HtmlWebpackPlugin = require('html-webpack-plugin');
// html头部配置项
var options = {
  lang: 'en',
  title: '华泰证券',
  meta: [
    {
      name: 'charset',
      content: 'utf-8'
    }
  ]
}

// prod配置项
var prodHTMLOptions = {
  ...options,
  minify: {
    removeComments: true,
    collapseWhitespace: true,
    removeAttributeQuotes: true
    // more options:
    // https://github.com/kangax/html-minifier#options-quick-reference
  },
  // necessary to consistently work with multiple chunks via CommonsChunkPlugin
  chunksSortMode: 'dependency'
};

exports.devHTMLOptions = options;
exports.prodHTMLOptions = prodHTMLOptions;