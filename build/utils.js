const path = require('path');
const crypto = require('crypto');
const isObject = require('lodash/isObject');
const config = require('../config');
const theme = require('../src/theme');
const chalk = require('chalk');
const isDev = process.env.NODE_ENV === 'development';
 
exports.logMessage = (message, level = 'info') => {
  const color =
    level === 'error'
      ? 'red'
      : level === 'warning'
        ? 'yellow'
        : level === 'info'
          ? 'blue'
          : 'white';
  console.log(`[${new Date().toISOString()}]`, chalk[color](message));
};

exports.compilerPromise = (name, compiler) => {
  return new Promise((resolve, reject) => {
    compiler.hooks.compile.tap(name, () => {
      logMessage(`[${name}] Compiling `);
    });
    compiler.hooks.done.tap(name, (stats) => {
      if (!stats.hasErrors()) {
        return resolve();
      }
      return reject(`Failed to compile ${name}`);
    });
  });
};

exports.assetsPath = function (_path) {
  const assetsSubDirectory = isDev
    ? config.dev.assetsSubDirectory
    : config.build.assetsSubDirectory;
  return path.posix.join(assetsSubDirectory, _path)
}
/** less配置 */
exports.getLessConfig = function () {
  return {
    modifyVars: theme,
    javascriptEnabled: true
  }
};

exports.getCSSLoaders = function (options) {
  let own = [];
  let nodeModules = [];

  options = options || {}

  let baseOptions = {
    minimize: !isDev,
    sourceMap: options.sourceMap,
    importLoaders: 1
  };

  let ownOptions = Object.assign({}, baseOptions);

  let cssModulesConfig = {
    modules: true,
    localIdentName: '[path][name]__[local]--[hash:base64:5]',
    getLocalIdent: (context, localIdentName, localName, options) => {
      const md5 = crypto.createHash('md5');
      const basename = path.basename(context.resourcePath, '.less');
      const dirname = path.basename(path.dirname(context.resourcePath));
      const hashValue = md5.update(context.resourcePath).digest('hex').substr(0, 5);

      if (isDev) {
        return dirname + '__' + basename + '__' + localName + '__' + hashValue;
      } else {
        return localName + '__' + hashValue;
      }
    }
  };

  if (!options.disableCSSModules) {
    ownOptions = Object.assign(
      ownOptions,
      cssModulesConfig
    );
  }

  let postcssOptions = {
    // Necessary for external CSS imports to work
    // https://github.com/facebookincubator/create-react-app/issues/2677
    ident: 'postcss',
    plugins: () => [
      require('postcss-flexbugs-fixes'),
      require('autoprefixer')({
        browsers: [
          '>1%',
          'last 4 versions',
          'Firefox ESR',
          'not ie < 10',
        ],
        flexbox: 'no-2009'
      })
   ]
  };

  own.push({
    loader: 'css-loader',
    options: ownOptions
  });
  nodeModules.push({
    loader: 'css-loader',
    options: baseOptions
  });

  // own.push('resolve-url-loadr');
  own.push({
    loader: 'postcss-loader',
    options: postcssOptions
  });

  return {
    own,
    nodeModules,
  };
}
