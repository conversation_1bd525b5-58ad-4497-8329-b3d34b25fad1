const _ = require('lodash');
const path = require('path');
const webpack = require('webpack');
const merge = require('webpack-merge');
const CopyWebpackPlugin = require('copy-webpack-plugin');
var WebpackPluginConcatJobid = require('@ht/webpack-plugin-concat-jobid')
const HtmlWebpackPlugin = require('html-webpack-plugin');
const LodashModuleReplacementPlugin = require('lodash-webpack-plugin');
const HtmlWebpackIncludeAssetsPlugin = require('html-webpack-include-assets-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const OptimizeCSSPlugin = require('optimize-css-assets-webpack-plugin');
const WebpackAssetsManifest = require('webpack-assets-manifest');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin');
const { RetryChunkLoadPlugin } = require('webpack-retry-chunk-load-plugin');
const { prodHTMLOptions } = require('./html');
const baseWebpackConfig = require('./webpack.base.conf');
const config = require('../config');
const utils = require('./utils');
const { getEntriesFromCommandline, generateEntries } = require('./entries');
const { getExternal, getExternalFiles } = require('./externals');

function resolve(dir) {
  return path.join(__dirname, '..', dir);
}

const entries = _.map(getEntriesFromCommandline(), (entry) => {
  if (_.isObject(entry.entry)) {
    return {
      ...entry,
      mode: 'app',
    };
  }
  return {
    ...entry,
    mode: 'host',
  };
});

const package = require('../package.json');
const {
  EMAS_BUILD_ID
} = process.env;


const cssLoaders = utils.getCSSLoaders({
  disableCSSModules: !config.cssModules,
  sourceMap: config.build.productionSourceMap
});

const umdPath = path.join(config.build.assetsSubDirectory, 'js', 'umd');

const lessConfig = utils.getLessConfig();

const webpackConfig = entries.map((entry) => {
  const tmpConfig = {
    mode: 'production',
    entry: generateEntries(entry),
    externals: getExternal(entry.externals),
    module: {
      rules: [
        {
          test: /\.jsx?$/,
          loader: 'babel-loader',
          include:
            [
              resolve('config/index.js'),
            ].concat(config.src).concat(config.htComponents),
          options: {
            configFile: resolve('babel.config.js'),
            cacheDirectory: true,
          }
        },
        {
          test: /\.css$/,
          include: config.src,
          use: [
            MiniCssExtractPlugin.loader,
          ].concat(cssLoaders.own)
        },
        {
          test: /\.less$/,
          include: config.src,
          use: [
            MiniCssExtractPlugin.loader,
          ].concat(cssLoaders.own).concat({
            loader: 'less-loader',
            options: lessConfig
          })
        },
        {
          test: /\.css$/,
          include: config.appNodeModules,
          use: [
            MiniCssExtractPlugin.loader,
          ].concat(cssLoaders.nodeModules)
        },
        {
          test: /\.less$/,
          include: config.appNodeModules,
          use: [
            MiniCssExtractPlugin.loader,
          ].concat(cssLoaders.nodeModules).concat({
            loader: 'less-loader',
            options: lessConfig
          })
        }
      ]
    },
    devtool: config.build.productionSourceMap ? '#source-map' : false,
    output: {
      path: config.build.assetsRoot,
      filename: utils.assetsPath('js/[name].[chunkhash].js'),
      chunkFilename: utils.assetsPath('js/[id].[chunkhash].js'),
      publicPath: config.build.assetsPublicPath,
      jsonpFunction: `${config.appName}_${entry.name}_jsonp`,
    },
    optimization: {
      namedModules: true,
      namedChunks: true,
      minimizer: [
        new UglifyJsPlugin({
          cache: true,
          parallel: true,
          sourceMap: config.build.productionSourceMap // set to true if you want JS source maps
        }),
        new OptimizeCSSPlugin({
          cssProcessorOptions: { safe: true, discardComments: { removeAll: true } },
        })
      ],
      splitChunks: {
        name: true,
        chunks: 'async',
        minChunks: 1,
        minSize: 300 * 1024,
        maxSize: 0,
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: (module => module.resource
              && /\.js$/.test(module.resource)
              && module.resource.indexOf(path.join(__dirname, '../node_modules')) === 0),
            chunks: 'async',
            // enforce: true,
            minSize: 300 * 1024,
            maxSize: 0,
          },
          styles: {
            minChunks: 1,
            name: 'css',
            test: /\.(css|scss|less)$/,
            enforce: true,
          }
        },
      },
      runtimeChunk: 'single'
    },
    plugins: [
      new webpack.DefinePlugin({
        PROJECT_JOBID:'"' + (EMAS_BUILD_ID || package.version) + '"',
      }),
      new WebpackPluginConcatJobid(),
      // http://vuejs.github.io/vue-loader/en/workflow/production.html

      // extract css into its own file
      new MiniCssExtractPlugin({
        ignoreOrder: true,
        filename: utils.assetsPath('css/[name].[contenthash].css')
      }),
      // Compress extracted CSS. We are using this plugin so that possible
      // duplicated CSS from different components can be deduped.
      new OptimizeCSSPlugin({
        cssProcessorOptions: {
          safe: true
        }
      }),
      new LodashModuleReplacementPlugin({
        collections: true,
        paths: true,
        caching: true,
        chaining: true,
        cloning: true,
        coercions: true,
        currying: true,
        deburring: true,
        exotics: true,
        flattening: true,
        guards: true,
        memoizing: true,
        metadata: true,
        placeholders: true,
        shorthands: true,
        unicode: true,
      }),
      // HtmlWebpackPlugin配置
      // generate dist index.html with correct asset hash for caching.
      // you can customize output by editing /index.html
      // see https://github.com/ampedandwired/html-webpack-plugin
      // new FriendlyErrorsPlugin(),
      new HtmlWebpackPlugin({
        filename: `${entry.name}.html`,
        inject: true,
        template: `${entry.template || 'index'}.html`,
        chunks: ['runtime', 'vendor', entry.mode === 'app' ? `${entry.name}_standalone` : entry.name],
        ...prodHTMLOptions,
      }),

      // copy custom static assets
      new CopyWebpackPlugin([
        {
          from: path.resolve(__dirname, '../static'),
          to: config.build.assetsSubDirectory,
          ignore: ['.*', 'font/iconfont/*']
        },
        ...getExternalFiles(entry.externals).map(
          external => ({
            from: external.path,
            to: path.posix.join(umdPath, external.to),
            flatten: true,
          }),
        ),
      ]),
      new HtmlWebpackIncludeAssetsPlugin({
        assets: _.map(
          getExternalFiles(entry.externals),
          external => path.posix.join(umdPath, external.to),
        ),
        append: false,
        publicPath: config.build.assetsPublicPath,
      }),
      new RetryChunkLoadPlugin({
        // optional stringified function to get the cache busting query string appended to the script src
        // if not set will default to appending the string `?cache-bust=true`
        cacheBust: `function() {
          return 'timestamp=' + Date.now();
        }`,
        // optional value to set request timeout in milliseconds
        timeout: 3000,
        // optional value to set the maximum number of retries to load the chunk. Default is 1
        maxRetries: 2,
        // optional list of chunks to which retry script should be injected
        // if not set will add retry script to all chunks that have webpack script loading
        // chunks: ['chunkName'],
        // optional code to be executed in the browser context if after all retries chunk is not loaded.
        // if not set - nothing will happen and error will be returned to the chunk loader.
        // lastResortScript: 'window.location.href=\'/500.html\';'
      }),
      new ScriptExtHtmlWebpackPlugin({
        custom: [
          {
            test: /\.js$/,
            attribute: 'onerror',
            value: '__loadScript(this, 1)'
          },
          {
            test: /\.js$/,
            attribute: 'type',
            value: 'text/javascript'
          },
          {
            test: /\.js.gz$/,
            attribute: 'onerror',
            value: '__loadScript(this, 1)'
          },
          {
            test: /\.js.gz$/,
            attribute: 'type',
            value: 'text/javascript'
          }
        ]
      }),  
      new webpack.ContextReplacementPlugin(/moment[\/\\]locale$/, /zh-cn/),
    ],
  };

  if (entry.mode === 'app') {
    tmpConfig.plugins.push(
      new WebpackAssetsManifest({
        output: `${entry.name}.json`,
        entrypoints: true,
        entrypointsKey: false,
        publicPath: true,
        transform(assets) {
          var finalJson = assets[entry.name];
          if (!finalJson.css) {
            finalJson.css = []
          }
          finalJson.version = (EMAS_BUILD_ID || package.version);
          return finalJson;
        },
      })
    );
  }

  if (config.build.productionGzip) {
    const CompressionWebpackPlugin = require('compression-webpack-plugin');
    tmpConfig.plugins.push(
      new CompressionWebpackPlugin({
        cache:true,
        compressionOptions:{},
        filename: '[path][base].gz',
        algorithm: 'gzip',
        test: new RegExp(`\\.(${config.build.productionGzipExtensions.join('|')})$`),
        threshold: 10240,
        minRatio: 0.8,
        deleteOriginalAssets: false
      }),
    );
  }

  if (config.build.bundleAnalyzerReport) {
    const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
    tmpConfig.plugins.push(new BundleAnalyzerPlugin());
  }

  return merge(baseWebpackConfig, tmpConfig);
});

module.exports = webpackConfig;
