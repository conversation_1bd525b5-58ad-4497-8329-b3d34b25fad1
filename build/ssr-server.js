const webpack = require('webpack');
const nodemon = require('nodemon');
const rimraf = require('rimraf');
const express = require('express');
const webpackConfig = require('./webpack.server.conf.js');
const { logMessage, compilerPromise } = require('./utils');

process.env.NODE_ENV = 'development';

function resolve(dir) {
  return path.join(__dirname, '..', dir)
}

const start = async () => {
  rimraf.sync(resolve('server'));
  const serverCompiler = webpack(webpackConfig);
  const serverPromise = compilerPromise('server', serverCompiler);

  const watchOptions = {
    ignored: /node_modules/,
  };

  serverCompiler.watch(watchOptions, (error, stats) => {
    if (!error && !stats.hasErrors()) {
      console.log(stats.toString(serverConfig.stats));
      return;
    }

    if (error) {
      logMessage(error, 'error');
    }

    if (stats.hasErrors()) {
      const info = stats.toJson();
      const errors = info.errors[0].split('\n');
      logMessage(errors[0], 'error');
      logMessage(errors[1], 'error');
      logMessage(errors[2], 'error');
    }
  });

  // wait until client and server is compiled
  try {
    await serverPromise;
  } catch (error) {
    logMessage(error, 'error');
  }

  const script = nodemon({
    script: "server/index.js",
    ignore: ['src', 'fspSrc', 'ssr', './*.*', 'build'],
  });

  script.on('restart', () => {
    logMessage('Server side app has been restarted.', 'warning');
  });

  script.on('quit', () => {
    console.log('Process ended');
    process.exit();
  });

  script.on('error', () => {
    logMessage('An error occured. Exiting', 'error');
    process.exit(1);
  });
};

start();