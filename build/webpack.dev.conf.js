var _ = require('lodash');
var path = require('path');
var utils = require('./utils');
var webpack = require('webpack');
var config = require('../config');
var merge = require('webpack-merge');
var HappyPack = require('happypack');
var HtmlWebpackPlugin = require('html-webpack-plugin');
var HtmlWebpackIncludeAssetsPlugin = require('html-webpack-include-assets-plugin');
var LodashModuleReplacementPlugin = require('lodash-webpack-plugin');
var HardSourceWebpackPlugin = require('hard-source-webpack-plugin');
var baseWebpackConfig = require('./webpack.base.conf');
var FriendlyErrorsPlugin = require('friendly-errors-webpack-plugin');
var WebpackAssetsManifest = require('webpack-assets-manifest');
const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin');
const { RetryChunkLoadPlugin } = require('webpack-retry-chunk-load-plugin');

var { getExternal, getExternalFiles } = require('./externals');
var { getEntriesFromCommandline, generateEntries } = require('./entries');
var { devHTMLOptions } = require('./html');

const package = require('../package.json');
const {
  EMAS_BUILD_ID
} = process.env;

function resolve(dir) {
  return path.join(__dirname, '..', dir)
}

var entries = _.map(getEntriesFromCommandline({ mode: 'development' }), (entry) => {
  if (_.isObject(entry.entry)) {
    return {
      ...entry,
      mode: 'app',
    }
  }
  return {
    ...entry,
    mode: 'host',
  }
});

var cssLoaders = utils.getCSSLoaders({
  disableCSSModules: !config.cssModules,
  sourceMap: config.dev.cssSourceMap
});

var lessConfig = utils.getLessConfig();

var webpackConfig = entries.map(function(entry) {
  var tmpConfig = {
    mode: 'development',
    entry: generateEntries(entry, { mode: 'development' }),
    output: {
      path: config.build.assetsRoot,
      filename: '[name].js',
      publicPath: config.dev.assetsPublicPath,
      jsonpFunction: `${config.appName}_${entry.name}_Jsonp`,
    },
    devServer: require('./dev-server').config,
    externals: getExternal(entry.externals),
    module: {
      rules: [
        {
          test: /\.jsx?$/,
          loader: 'happypack/loader?id=jsx',
          include: config.src.concat(config.htComponents)
        },
        {
          test: /\.css$/,
          include: config.src,
          use: ['style-loader'].concat(cssLoaders.own)
        },
        {
          test: /\.less$/,
          include: config.src,
          use: ['style-loader'].concat(cssLoaders.own).concat({
            loader: 'less-loader',
            options: lessConfig
          })
        },
        {
          test: /\.css$/,
          include: config.appNodeModules,
          use: ['style-loader'].concat(cssLoaders.nodeModules)
        },
        {
          test: /\.less$/,
          include: config.appNodeModules,
          use: ['style-loader'].concat(cssLoaders.nodeModules).concat({
            loader: 'less-loader',
            options: lessConfig
          })
        },
        {
          test: /\.less$/,
          include: config.crmComponents,
          use: ['style-loader'].concat(cssLoaders.nodeModules).concat({
            loader: 'less-loader',
            options: lessConfig
          })
        }
      ]
    },
    // cheap-module-eval-source-map is faster for development
    devtool: '#cheap-module-eval-source-map',
    stats: 'minimal',
    plugins: [
      new webpack.DefinePlugin({
        PROJECT_JOBID:'"' + (EMAS_BUILD_ID || package.version) + '"',
      }),  
      new webpack.HotModuleReplacementPlugin(),
      new webpack.NoEmitOnErrorsPlugin(),
      new HappyPack({
        id: 'jsx',
        threads: 4,
        loaders: [{
          loader: 'babel-loader',
          options: {
            configFile: resolve('babel.config.js'),
          }
        }],
      }),
      new LodashModuleReplacementPlugin({
        'collections': true,
        'paths': true,
        'caching': true,
        'chaining': true,
        'cloning': true,
        'coercions': true,
        'collections': true,
        'currying': true,
        'deburring': true,
        'exotics': true,
        'flattening': true,
        'guards': true,
        'memoizing': true,
        'metadata': true,
        'placeholders': true,
        'shorthands': true,
        'unicode': true,
      }),
      new FriendlyErrorsPlugin({
        compilationSuccessInfo: {
          messages: [`Entry(${entry.name}) compilation success`],
        },
      }),
      new HtmlWebpackPlugin({
        filename: `${entry.name}.html`,
        inject: true,
        template: `${entry.template || 'index'}.html`,
        chunks: [entry.mode === 'app' ? `${entry.name}_standalone`: entry.name],
        ...devHTMLOptions,
      }),
      new ScriptExtHtmlWebpackPlugin({
        custom: [
          {
            test: /\.js$/,
            attribute: 'onerror',
            value: '__loadScript(this, 1)'
          },
          {
            test: /\.js$/,
            attribute: 'type',
            value: 'text/javascript'
          },
          {
            test: /\.js.gz$/,
            attribute: 'onerror',
            value: '__loadScript(this, 1)'
          },
          {
            test: /\.js.gz$/,
            attribute: 'type',
            value: 'text/javascript'
          }
        ]
      }),
      new RetryChunkLoadPlugin({
        // optional stringified function to get the cache busting query string appended to the script src
        // if not set will default to appending the string `?cache-bust=true`
        cacheBust: `function() {
          return 'timestamp=' + Date.now();
        }`,
        // optional value to set request timeout in milliseconds
        timeout: 3000,
        // optional value to set the maximum number of retries to load the chunk. Default is 1
        maxRetries: 2,
        // optional list of chunks to which retry script should be injected
        // if not set will add retry script to all chunks that have webpack script loading
        // chunks: ['chunkName'],
        // optional code to be executed in the browser context if after all retries chunk is not loaded.
        // if not set - nothing will happen and error will be returned to the chunk loader.
        // lastResortScript: 'window.location.href=\'/500.html\';'
      }),
  
      new HtmlWebpackIncludeAssetsPlugin({
        assets: getExternalFiles(entry.externals).map(external => path.posix.join('umd', external.path)),
        append: false,
      }),
      new webpack.ContextReplacementPlugin(/moment[\/\\]locale$/, /zh-cn/),
    ],
  };

  if (config.dev.enableHardSourceCache) {
    tmpConfig.plugins.unshift(new HardSourceWebpackPlugin());
  }

  if (entry.mode === 'app') {
    tmpConfig.plugins.push(
      new WebpackAssetsManifest({
        output: `${entry.name}.json`,
        entrypoints: true,
        entrypointsKey: false,
        publicPath: true,
        transform(assets) {
          var finalJson = assets[entry.name];
          if (finalJson && !finalJson.css) {
            finalJson.css = [];
          }
          finalJson.version = (EMAS_BUILD_ID || package.version);
          return finalJson;
        },
      })
    )
  }

  return merge(baseWebpackConfig, tmpConfig);
});

module.exports = webpackConfig;

