/* eslint-disable import/no-extraneous-dependencies */

var map = require('lodash/map');
var pick = require('lodash/pick');
var filter = require('lodash/filter');
var find = require('lodash/find');
var isEmpty = require('lodash/isEmpty');
var path = require('path');

var externals = [
  {
    name: 'react',
    file: {
      dev: 'umd/react.development.js',
      prod: 'umd/react.production.min.js',
    },
  },
  {
    name: 'react-dom',
    file: {
      dev: 'umd/react-dom.development.js',
      prod: 'umd/react-dom.production.min.js',
    },
  },
  {
    name: '@lego/bigbox-utils',
    file: {
      dev: 'lib/bigboxUtils.js',
      prod: 'lib/bigboxUtils.min.js',
    },
  },
  {
    name: '@crm/feedbacks',
    file: {
      dev: 'lib/feedbacks.js',
      prod: 'lib/feedbacks.min.js',
    },
  },
  {
    name: 'echarts',
    file: {
      dev: 'dist/echarts.js',
      prod: 'dist/echarts.min.js',
    },
  },
  {
    name: 'echarts-liquidfill',
    file: {
      dev: 'dist/echarts-liquidfill.js',
      prod: 'dist/echarts-liquidfill.min.js',
    },
  },
  {
    name: 'xlsx',
    file: {
      dev: 'dist/xlsx.js',
      prod: 'dist/xlsx.min.js',
    },
  },
  {
    name: '@crm/app-utils',
    file: {
      dev: 'lib/appUtils.js',
      prod: 'lib/appUtils.min.js',
    },
  }
];

var external = {
  'react': 'React',
  'react-dom': 'ReactDOM',
  '@lego/bigbox-utils': 'bigboxUtils',
  'echarts': 'echarts',
  'xlsx': 'XLSX',
  '@crm/feedbacks': 'crmFeedback',
  'canvas': 'canvas',
  '@crm/app-utils': 'appUtils',
};

function getExternalFiles(packages) {
  let finalExternals = [
    {
      name: '@babel/polyfill',
      file: {
        dev: 'dist/polyfill.js',
        prod: 'dist/polyfill.min.js',
      },
    },
    ...externals,
  ];
  if (packages) {
    finalExternals = [
      {
        name: '@babel/polyfill',
        file: {
          dev: 'dist/polyfill.js',
          prod: 'dist/polyfill.min.js',
        },
      },
      ...filter(externals, item => find(packages, package => {
        if (item.name === 'echarts-liquidfill') {
          return package === 'echarts';
        }
        return package === item.name;
      })),
    ];
  }

  return map(
    finalExternals,
    (pkg) => {
      if (!isEmpty(pkg)) {
        var {
          name,
          file,
        } = pkg;
        // eslint-disable-next-line
        var packJSON = require(name + '/package.json');
        var version = packJSON.version;
        var fileName = process.env.NODE_ENV === 'development' ? file.dev : file.prod;
        var fileNameIndex = (fileName || packJSON.unpkg).indexOf('/');
        var outputName = (fileName || packJSON.unpkg).substring(fileNameIndex + 1);
        return {
          name,
          fileName,
          path: path.posix.join(
            'node_modules',
            name,
            (fileName || packJSON.unpkg),
          ),
          to: outputName.replace(/\.js$/, `_${version}.js`),
        };
      }
      // 非字符串情况待扩展，目前场景不需要支持
      return null;
    },
  );
}

function getExternal(packages) {
  return packages ? pick(external, packages) : external;
}

exports.getExternalFiles = getExternalFiles;
exports.getExternal = getExternal;
