const path = require('path');
const webpack = require('webpack');
const nodeExternals = require('webpack-node-externals');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const config = require('../config');
const utils = require('./utils');

function resolve(dir) {
  return path.join(__dirname, '..', dir)
}

const cssLoaders = utils.getCSSLoaders({
  disableCSSModules: !config.cssModules,
  sourceMap: config.build.productionSourceMap
});

const lessConfig = utils.getLessConfig();

function modulesCommonjs(regRex) {
  return function (context, request, callback) {
     if (regRex.test(request)) {
    return callback(null, 'commonjs ' + request);
  }
  callback();
  }
}

const externals = [modulesCommonjs(/^(canvas|express|isomorphic-fetch)$/)];

const serverConfig = {
  mode: process.env.NODE_ENV === 'development' ? 'development' : 'production',
  entry: ['./ssr/globals.js', './ssr/server/index.js'],
  target: 'node',
  externals,
  stats: {
    colors: true,
  },
  output: {
    path: resolve('server'),
    filename: 'index.js',
    publicPath: '/',
  },
  resolve: {
    extensions: ['.jsx', 'ts', 'tsx', '.web.tsx', '.web.ts', '.web.jsx', '.web.js', '.js', '.less', '.css'],
    modules: [
      resolve('src'),
      resolve('node_modules'),
   ],
    alias: {
      '@/components': resolve('src/components'),
      '@/config': resolve('src/config'),
      '@/helper': resolve('src/helper'),
      '@/decorators': resolve('src/decorators'),
      '@/utils': resolve('src/utils'),
      'htsc/static': resolve('static'),
    }
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        loader: 'ts-loader'
      },
      {
        test: /\.jsx?$/,
        loader: 'babel-loader',
        include: config.src.concat(config.htComponents, config.ssr),
        options: {
          configFile: resolve('babel.config.js'),
          cacheDirectory: true,
        }
      },
      {
        test: /\.(png|jpe?g|gif|msi|xlsx?|pdf|docx?)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: utils.assetsPath('img/[name].[hash:7].[ext]')
        }
      },
      {
        test: /\.(woff2?|eot|ttf|otf|svg)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: utils.assetsPath('fonts/[name].[hash:7].[ext]')
        }
      },
      {
        test: /\.css$/,
        include: config.src.concat(config.ssr),
        use: [
          MiniCssExtractPlugin.loader,
        ].concat(cssLoaders.own)
      },
      {
        test: /\.less$/,
        include: config.src.concat(config.ssr),
        use: [
          MiniCssExtractPlugin.loader,
        ].concat(cssLoaders.own).concat({
          loader: 'less-loader',
          options: lessConfig
        })
      },
      {
        test: /\.css$/,
        include: config.appNodeModules,
        use: [
          MiniCssExtractPlugin.loader,
        ].concat(cssLoaders.nodeModules)
      },
      {
        test: /\.less$/,
        include: config.appNodeModules,
        use: [
          MiniCssExtractPlugin.loader,
        ].concat(cssLoaders.nodeModules).concat({
          loader: 'less-loader',
          options: lessConfig
        })
      }
    ],
  },
  plugins: [
    new webpack.EnvironmentPlugin({
      NODE_ENV: 'development',
    }),
    new CopyWebpackPlugin([
      {
        from: resolve('static/css'),
        to: 'static/css',
      }
    ]),
    new MiniCssExtractPlugin({
      filename: utils.assetsPath('css/main.css'),
    }),
    new webpack.optimize.LimitChunkCountPlugin({
      maxChunks: 1
    }),
  ]
};

module.exports = serverConfig;
