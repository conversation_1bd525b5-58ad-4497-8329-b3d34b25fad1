user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    gzip on;
    gzip_min_length 1k;
    gzip_buffers 16 64k;
    gzip_http_version 1.1;
    gzip_comp_level 4;
    gzip_types text/plain application/javascript text/css;
    gzip_vary on;

    upstream mcrmapigatewaysit{
        server *************:8082;
    }

    upstream mcrmapigatewayuat{
        server *************:8082;
    }
    
    upstream mcrmapigatewaycp{
        server ************:8082;
    }

    upstream slxtgateway {
        server ***************:8940;
    }

    #include /etc/nginx/conf.d/*.conf;
	
	server {
        listen 8081;
       
        # mv dynamic request
        location ^~ /fspa/mcrm/api/ {
            proxy_pass http://mcrmapigatewaysit/mcrm/api/;
        }

        # 双录系统
        location ^~ /fspa/slxt/ {
            proxy_pass http://slxtgateway/slxt/;
            proxy_set_header contextPath /fspa/;
        }

        # mv static 
        location ^~ /fspa {
            alias /app/www/;
        }
    }

    server {
        listen 8082;
       
        # mv dynamic request
        location ^~ /fspa/mcrm/api/ {
            proxy_pass http://mcrmapigatewayuat/mcrm/api/;
        }

        # 双录系统
        location ^~ /fspa/slxt/ {
            proxy_pass http://slxtgateway/slxt/;
            proxy_set_header contextPath /fspa/;
        }

        # mv static 
        location ^~ /fspa {
            alias /app/www/;
        }
    }
    
    server {
        listen 8083;
       
        # mv dynamic request
        location ^~ /fspa/mcrm/api/ {
            proxy_pass http://mcrmapigatewaycp/mcrm/api/;
        }

        # 双录系统
        location ^~ /fspa/slxt/ {
            proxy_pass http://slxtgateway/slxt/;
            proxy_set_header contextPath /fspa/;
        }

        # mv static 
        location ^~ /fspa {
            alias /app/www/;
        }
    }
	
}